import { isEqualIgnoreNullOrUndefined, keepDecimal, uuid } from "./Utils";
import Editor from "./Editor";
import { ScriptType } from "./Constant";
import { ElementInParagraph, alignType } from "./Definition";
export interface FontStyle {
  family: string;
  height: number;

  bold?: boolean;
  italic?: boolean;

  underline?: boolean;
  dblUnderLine?: boolean;
  strikethrough?: boolean;
  characterSpacing?: number
  // 上标下标
  script?: ScriptType;

  color?: string; // TODO color type

  bgColor?: string | null;

  highLight?: string | null;// 高亮

  temp_word_color?: string;

  temp_word_bgColor?: string;

  temp_valid_color?: string;

  id?: string;

  align?: alignType
}

export default class Font {
  family: string;

  height: number;

  bold: boolean;

  italic: boolean;

  underline: boolean;

  dblUnderLine?: boolean;
  // 上标下标
  script: ScriptType;

  strikethrough: boolean;

  color: string;

  bgColor: string | null;

  highLight: string | null;

  temp_word_color?: string;

  temp_word_bgColor?: string;

  temp_valid_color?: string;

  characterSpacing?: number;// 字符间距

  id: string;

  align?: alignType;

  static handleMultipleSelectionChangeStyle(editor: Editor, style: Partial<FontStyle>) {
    const before_font = editor.contextState.getFontState();
    const font = editor.fontMap.add(Object.assign({}, before_font, style));
    let characters: ElementInParagraph[] = []; // 选区情况下将设置过样式的 character 都返回出去,让 pacs 设置 id ,字体缩放的时候跳过去
    if (!editor.selection.isCollapsed) {
      characters = [...editor.selection.selected_fields_chars?.all_chars];
      editor.changeFontStyleBySelection(style);
    } else {
      // 空段时改变字体大小行高也进行相应改变
      const focus_row = editor.selection.getFocusRow();
      if (focus_row && focus_row.children.length === 0 && style.height) {
        const new_font = { ...focus_row.paragraph.characters[0].font };
        new_font.height = Math.round(style.height);
        focus_row.paragraph.characters[0].font = editor.fontMap.add(new_font);
        focus_row.paragraph.updateChildren();
      }
      editor.contextState.setFontState(font);
      editor.contextState.setPathFont(font);
    }
    return characters;
  }

  static changeStyle (editor: Editor, style: Partial<FontStyle>) {
    // 分组如果锁定 则不能编辑
    if (!editor.operableOrNot(["cell", "group"])) return false;
    if (style.height) {
      // 修改字体保留三位小数
      style.height = keepDecimal(style.height, 3);
    }

    const characters: any = [];
    let { multipleSelected } = editor.selection;
    if (multipleSelected.length) {
      multipleSelected = JSON.parse(JSON.stringify(multipleSelected));
      editor.selection.clearSelectedInfo(false);
      for (let i = 0; i < multipleSelected.length; i++) {
        const current = multipleSelected[i];
        editor.selection.setSelectionByPath(current.start, current.end, "para_path", 0, false);
        const returnArr = this.handleMultipleSelectionChangeStyle(editor, style);
        returnArr && returnArr.length && (characters.push(...returnArr));
      }
      editor.selection.multipleSelected = multipleSelected;
    } else {
      this.handleMultipleSelectionChangeStyle(editor, style);
    }
    editor.update();
    editor.scroll_by_focus();
    editor.render();
    editor.event.emit("changeStyle", characters);
    return characters;
  }

  constructor (fontStyle: FontStyle, id: null | string = null) {
    if (id) {
      this.id = id;
    } else {
      this.id = uuid("font");
    }

    this.height = fontStyle.height;
    this.family = fontStyle.family;

    this.bold = Boolean(fontStyle.bold);
    this.italic = Boolean(fontStyle.italic);

    this.underline = Boolean(fontStyle.underline);

    this.dblUnderLine = Boolean(fontStyle.dblUnderLine);

    this.strikethrough = Boolean(fontStyle.strikethrough);

    this.script = fontStyle.script || ScriptType.NORMAL;

    this.color = fontStyle.color || "#000";

    this.bgColor = fontStyle.bgColor ? fontStyle.bgColor : null;

    this.highLight = fontStyle.highLight ? fontStyle.highLight : null;

    this.temp_word_color = fontStyle.temp_word_color;

    this.temp_word_bgColor = fontStyle.temp_word_bgColor;

    this.temp_valid_color = fontStyle.temp_valid_color;

    this.characterSpacing = fontStyle.characterSpacing;

    this.align = fontStyle.align;
  }

  /**
   * 字体文件对比
   * @param font
   * @param another
   * @param isComplete  是否是完全比较，即是否比较临时颜色
   */
  static isEqual (font: Font, another: FontStyle) {
    return font && another && isEqualIgnoreNullOrUndefined(font.family, another.family) &&
      isEqualIgnoreNullOrUndefined(Number(font.height), Number(another.height)) &&
      isEqualIgnoreNullOrUndefined(font.bold, another.bold) &&
      isEqualIgnoreNullOrUndefined(font.italic, another.italic) &&
      isEqualIgnoreNullOrUndefined(font.underline, another.underline) &&
      isEqualIgnoreNullOrUndefined(font.dblUnderLine, another.dblUnderLine) &&
      isEqualIgnoreNullOrUndefined(font.strikethrough, another.strikethrough) &&
      isEqualIgnoreNullOrUndefined(font.color, another.color) &&
      isEqualIgnoreNullOrUndefined(font.bgColor, another.bgColor) &&
      isEqualIgnoreNullOrUndefined(font.script, another.script) &&
      isEqualIgnoreNullOrUndefined(font.highLight, another.highLight) &&
      isEqualIgnoreNullOrUndefined(font.temp_word_color, another.temp_word_color) &&
      isEqualIgnoreNullOrUndefined(font.temp_word_bgColor, another.temp_word_bgColor) &&
      isEqualIgnoreNullOrUndefined(font.temp_valid_color, another.temp_valid_color) &&
      isEqualIgnoreNullOrUndefined(font.characterSpacing, another.characterSpacing) &&
      isEqualIgnoreNullOrUndefined(font.align, another.align);
  }

  get style (): FontStyle {
    return {
      family: this.family,
      height: this.height,
      bold: this.bold,
      italic: this.italic,
      underline: this.underline,
      dblUnderLine: this.dblUnderLine,
      characterSpacing: this.characterSpacing,
      strikethrough: this.strikethrough,
      color: this.color,
      bgColor: this.bgColor,
      script: this.script,
      highLight: this.highLight
    };
  }

  getCss (editor?: Editor, isRender?: boolean): string {
    return this.script === ScriptType.SUPERSCRIPT || this.script === ScriptType.SUBSCRIPT
      ? `${this.italic ? "italic " : ""}${this.bold ? (editor?.config?.font_weight_bold ?? 700) : (editor?.config?.font_weight_normal ?? 400)} ${this.height / 2
      }px ${isRender && editor?.config.editor_show_family ? editor?.config.editor_show_family : this.family}`
      : `${this.italic ? "italic " : ""}${this.bold ? (editor?.config?.font_weight_bold ?? 700) : (editor?.config?.font_weight_normal ?? 400)} ${this.height
      }px ${isRender && editor?.config.editor_show_family ? editor?.config.editor_show_family : this.family}`;
  }

  getDomStyle (): string {
    let css: any = "";
    if (this.family) {
      css += "font-family:" + this.family + ";";
    }
    if (this.italic) {
      css += "font-style:italic;";
    }
    if (this.bold) {
      css += "font-weight:bold;";
    }
    if (this.color) {
      css += "color:" + this.color + ";";
    }
    if (this.bgColor) {
      css += "background-color:" + this.bgColor + ";";
    }
    if (this.underline) {
      css += "text-decoration:underline;";
    }
    if (this.strikethrough) {
      css += "text-decoration:line-through;";
    }
    if (this.dblUnderLine) {
      css += "border-bottom:3px double black;";
    }

    if (this.script === ScriptType.SUPERSCRIPT) {
      css += "vertical-align:super;";
      css += "font-size:" + this.height + "px;";
    } else if (this.script === ScriptType.SUBSCRIPT) {
      css += "vertical-align:sub;";
      css += "font-size:" + this.height + "px;";
    } else {
      if (this.family && this.height) {
        css += "font-size:" + this.height + "px;";
      }
    }
    return css;
  }
  // get key(): string
  // {
  //   return `${this.family}-${this.height}-${Number(this.bold)}-${Number(this.italic)}-${Number(this.underline)}-${Number(this.strikethrough)}-${this.color}-${this.bgColor ? this.bgColor : '#fff'}`
  // }
}
