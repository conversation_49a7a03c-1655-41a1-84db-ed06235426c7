{"name": "msun-lib-editor-common", "version": "10.12.35", "description": "EMR editor", "module": "./dist/initEditorCommonBase.esm.js", "exports": {".": "./src/index.ts"}, "publishConfig": {"exports": {".": "./dist/initEditorCommonBase.esm.js"}}, "files": ["dist", "lib"], "projectType": "library", "scripts": {"start": "vite", "build": "vite build", "debug": "tsc && vite build --mode=debug", "preview": "vite preview", "pack": "npm run build && npm pack --force"}, "keywords": [], "author": "张超，薛再强", "license": "ISC", "dependencies": {"js-base64": "^3.7.3", "jr-qrcode": "^1.1.4", "jsbarcode": "^3.11.5"}, "type": "module", "devDependencies": {"@babel/core": "^7.14.3", "@babel/preset-env": "7.14.4", "@typescript-eslint/eslint-plugin": "^5.56.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^7.28.0", "eslint-config-prettier": "^8.3.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.23.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-promise": "^5.1.0", "prettier": "^2.3.1", "rimraf": "^3.0.2", "rollup-plugin-visualizer": "^5.9.2", "tslib": "^2.2.0", "typescript": "^4.9.5", "vite": "^4.4.5", "vite-plugin-babel": "^1.1.3", "vite-plugin-dts": "^1.7.1"}, "repository": {"type": "git", "url": "https://gitlab.msunhis.com/msunClound/emr-editor-group/msun-editor-common-base.git"}, "realVersion": "10.12.35"}