import Editor from "./Editor";
import EditorHelper from "./EditorHelper";

import handlerEvent from "./HandlerEvent";
import { Path } from "./Path";
import { allowEditing, getImageSrcWithWhiteBgc } from "./Utils";

export default class InputAdapter {
  target: HTMLTextAreaElement;
  timer: any = null;

  static onCompositionInputHelper(editor: Editor, ime_start: Path | null, text: string, isEnd: boolean) {
    // 设置选区然后删除选区，并不需要往历史堆栈里边记录，因为撤销的时候不需要展示拼音了
    const ime_end = [...editor.selection.end];
    if (ime_start) editor.selection.setSelectionByPath(ime_start, ime_end, "model_path"); // 设置选区是为了 insertText 的时候会把选区内容删掉，否则展示拼音的时候会重复展示字母
    if (!allowEditing(editor)) return false;
    const font = editor.contextState.getFontState();
    editor.contextState.setFontState(font);
    if (isEnd) {
      if (editor.config.useLetterPlaceholder) {
        return EditorHelper.insertText(editor, text, "ime_input");
      } else {
        editor.delSectionRecordStack(false);
        return editor.insertText(text, "ime_input");
      }
    } else {
      if (editor.config.useLetterPlaceholder) {
        return EditorHelper.insertText(editor, text, "init");
      } else {
        return editor.insertText(text, "init");
      }
    }
  }

  constructor(target: HTMLTextAreaElement, editor: Editor, size: any) {
    this.target = target;
    editor.inputDOM = target;
    this._editor = editor;
    this.size = size;
  }

  key_down(event: KeyboardEvent) {
    handlerEvent("onKeyDown", event, this._editor);
  }

  key_up(event: KeyboardEvent) {
    handlerEvent("onKeyUp", event, this._editor);

    this.target.focus();
    this.setTargetPosition();
  }

  dblclick(event: MouseEvent) {
    handlerEvent("dblClick", event, this._editor);
  }

  getImageFileListFromClipboard(event: ClipboardEvent): File[] {
    const items = event.clipboardData?.items;
    const list: File[] = [];
    if (items && items.length) {
      // 检索剪切板 items
      for (let i = 0; i < items.length; i++) {
        if (items[i].type.startsWith("image")) {
          const imageFile = items[i].getAsFile();
          imageFile && list.push(imageFile);
        }
      }
    }
    return list;
  }

  /**
   * 监听的 input 上的粘贴事件
   * @param event ClipboardEvent
   */
  paste(event: ClipboardEvent) {
    const imageList = this.getImageFileListFromClipboard(event);
    if (imageList.length > 0) {
      for (const imageFile of imageList) {
        const url = window.URL.createObjectURL(imageFile);
        const image = new Image();
        image.src = url;

        if (this._editor.config.localImageMaxSize !== undefined && imageFile.size / 1024 / 1024 > this._editor.config.localImageMaxSize) {
          this._editor.compressImage(url, this._editor.page_size.width, this._editor.page_size.heighit).then(res => {
            if (typeof res === "string") {
              image.src = res;
              image.onload = () => {
                const src = getImageSrcWithWhiteBgc(image);
                this._editor.insertImage(src);
              };
            }
          })
        } else {
          image.onload = () => {
            const src = getImageSrcWithWhiteBgc(image);
            this._editor.insertImage(src);
          };
        }
      }
    }
  }

  // 处理手写输入
  async handleHandwritingInput() {
    const text = await navigator.clipboard.readText();
    if (text) {
      this._editor.insertText(text);
    }
  }

  wheel(event: WheelEvent) {
    event.preventDefault();
    requestAnimationFrame(() => {
      // 添加防抖 优化 设置目标位置 ↓
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.timer = setTimeout(() => {
        this.setTargetPosition();
      }, 200);
      // 添加防抖 优化 设置目标位置 ↑
      this._editor.internal.scroll(event.deltaY);
      // 编辑器滚动
      this._editor.event.emit("scroll", event);
    });
  }

  pointer_down(event: PointerEvent) {
    
    handlerEvent("onPointerDown", event, this._editor);
   
    this.setTargetPosition();

    const target = event.target as HTMLCanvasElement;

    target.setPointerCapture(event.pointerId);
  }

  pointer_up(event: PointerEvent) {

    if (!this._editor.isMobileTerminal()) {
      this.target.focus();
      handlerEvent("onPointerUp", event, this._editor);
    } else {
      // this._editor.event.emit("pointerUp", event);
      this._editor.internal.pointer_up_ev = event
    }
    const target = event.target as HTMLCanvasElement;

    target.releasePointerCapture(event.pointerId);
  }

  pointer_move(event: PointerEvent) {
    // todo 防抖性能优化
    if (!this._editor.isMobileTerminal()) {
      handlerEvent("onPointerMove", event, this._editor);
    } else {
      // this._editor.event.emit("pointerMove", event);
      this._editor.internal.pointer_move_ev = event
    }
  }

  input(event: InputEvent) {
    handlerEvent("onInput", event, this._editor);
  }

  composition_start(event: CompositionEvent) {
    handlerEvent("onCompositionStart", event, this._editor);
  }

  composition_update(event: CompositionEvent) {
    handlerEvent("onCompositionUpdate", event, this._editor);
  }

  composition_end(event: CompositionEvent) {
    handlerEvent("onCompositionEnd", event, this._editor);

    this.target.value = "";
  }

  private setTargetPosition() {
    const offset = this._editor.internal.view_scale_offset;
    this.target.style.left = this._editor.internal.client_left + (this._editor.caret.x + offset) * this._editor.viewScale + 12 + "px";
    let top = (this._editor.internal.client_top +
      this._editor.caret.y -
      this._editor.scroll_top +
      this._editor.caret.height -
      12) * this._editor.viewScale;
    if (top > this.size.height * this._editor.config.devicePixelRatio - this._editor.caret.height) {
      this.target.style.top =
        this.size.height * this._editor.config.devicePixelRatio - this._editor.caret.height + "px";
    }
    const max_top = this._editor.init_canvas.clientHeight - 25;
    top = Math.abs(top);
    if (top > max_top) {
      this.target.style.top = max_top + "px";
    } else {
      this.target.style.top = top + "px";
    }
  }

  readonly _editor: Editor;
  private size: any;
}
