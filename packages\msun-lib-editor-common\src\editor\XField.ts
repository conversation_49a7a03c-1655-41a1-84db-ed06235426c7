/* eslint-disable no-use-before-define */
import Font, { FontStyle } from "./Font";
import Character from "./Character";
import Image from "./Image";
import Widget from "./Widget";
import Cell from "./Cell";
import Paragraph from "./Paragraph";
import PathUtils, { Path } from "./Path";
import {
  getCellIndex,
  handleCopyData,
  insertParagraphsHelper,
  isImage,
  isWidget,
  saveSelectionData,
  specialCharHandle,
  updateFieldTextHelper_HeaderFooter,
  updateFieldTextHelper_RootCell,
  stringDateTransformNormDate,
  isBoxField,
} from "./Helper";
import XSelection from "./Selection";
import {
  decodeBase64 ,
  encodeBase64 ,
  initCell ,
  isCharacter ,
  isField ,
  isParagraph ,
  isNotNullAndUndefined ,
  mergeObjNonNull ,
  removeRepeat ,
  replaceLineBreakWith ,
  serializeCopy ,
  useRawDataByConfig ,
  uuid ,
  keepDecimal ,
  isNumber , isTable , extractNumber ,
} from "./Utils";
import valids from "./Validate";
import Renderer from "./Renderer";
import BoxField from "./BoxField";
import Line from "./line";
import Box from "./Box";
import Editor from "./Editor";
import { setFocusImage } from "./ImageEditing";
import { Config , editor_prompt_msg , fieldDefaultFormatList } from "./Config";
import Row from "./Row";
import Button from "./Button";
import EditorHelper from "./EditorHelper";
import { ViewMode } from "./Constant";
import { ElementInParagraph, FieldType } from "./Definition";
import { FieldShowMode } from "./EditorConfig";
import { medicalFormula } from "./medicalFormula";
import findKeywordsInParagraphs from "./find";
import Fraction from "./Fraction";

export type display_type = "normal" | "input" | "line" | "printInput";
export type align_type = "left" | "center" | "right";
export default class XField {
  id: string;

  style: FontStyle; // 文本域内容样式

  symbol_style: FontStyle | null = null; // 文本域边界符样式

  type: string = "normal"; // select | label | normal | date | box | anchor

  name: string = ""; // 用作文本替换时的标识

  placeholder: string = "文本域";

  tip: string = ""; // 鼠标移入提示信息

  start_symbol: string;

  field_symbol_color: string;

  start_sym_char!: Character;

  end_symbol: string;

  end_sym_char!: Character;

  show_symbol: number = 1;

  deletable: number = 1; // 是否可删除

  position: number = 0; // 复选框的位置 默认是 0 是在前边 1 是在后边

  readonly: number = 0; // 是否只读

  display_type: display_type = "normal"; // 文本域边框展示类型

  parent: XField | BoxField | null = null;

  cell: Cell;

  align: align_type = "left"; // 文本域对齐方式

  children: (Widget | XField | Character | Image | Line | Box | Button | Fraction)[] = [];

  placeholder_characters: Character[];

  cascade_list: any = [];

  automation_list: any = [];

  show_field: boolean = true; // 我查找了一遍 发现这个玩意儿 一点儿用没有 没有用的地儿

  /** *********************日期类型特有属性 ****************/
  show_format: number = 0; // 0日期选择，1日期时间选择不含秒，2-日期时间选择 含秒

  /* 0: "YYYY-MM-DD",
    1: "YYYY-MM-DD HH:mm",
    2: "YYYY-MM-DD HH:mm:ss",
    3: "YYYY年MM月DD日",
    4: "YYYY年MM月DD日 HH时",
    5: "YYYY年MM月DD日 HH时mm分",
    6: "MM-DD",
    7: "MM月DD日",
    8: "HH:mm",
    9: "HH时mm分",
    10: "YYYY年MM月DD日 HH时mm分ss秒",
    11："MM-DD HH:mm" */
  replace_format: number = 0;
  /*
  0:无限制
  1：整数形式
  2：保留一位小数
  3：保留两位小数
  4：保留三位小数
  5：保留四位小数
  */
  number_format: number = 0; // 数字格式

  forbidden: any = {
    start: null, // 有值禁止此日期之前的时间
    end: null, // 有值禁止此日期之后的时间
  }; // 文本域时间弹框禁止选择的日期

  /** **********下拉选择框特有属性 *************/
  source_id: string = ""; // 数据源id 下拉选择特有属性

  source_list: any[] = []; // 数据源列表，记录下拉源数据,存在source_id则使用id查询的数据，如果没有则读取该数据

  meta: any = {}; // 扩展属性

  active_type: number = 0; // 激活方式 0 click 1-dblclick

  multi_select: number = 0; // 1 选择器可多选， 0 单选

  separator: number = 0; // 多选模式下，选项之间的分隔符号

  formula_value: number | string = 0; // 下拉选择框代表的值

  /** ***********************文本域校验*****************************/
  valid: number = 0; // 是否进行校验
  // 校验类型及规则：字符串的组大长度  数字的 最小值 | 最大值 date mobile_phone fixed_phone / regex 正则
  valid_content: any = {
    require: true,
    type: "string",
    phone_type: "",
    rule: {
      max_length: 20,
      min_length: 5,
    },
    regex: "", // 自定义正则表达式
  };

  valid_tip: string = "";

  new_text: string | null = null; // 用于批量替换文本域字符时的中间变量

  max_width: number = 0; // 文本域的最大宽度值，如果值非0，则该文本域内的内容宽度不允许超出该值，如果超出自动压缩宽度

  min_width: number = 0; // 文本域的最小宽度值，如果值非0，则该文本域默认的宽度为该值，内容超出则自动适应，内容小于该宽度值则按该宽度值

  maxHeight: number = 0; // 文本域的最大高度 初始值为 0 说明没设置

  inputMode: number = 0; // 文本域的输入方式

  formula: string = ""; // 数学计算公式

  insRows: Row[] = [];

  showPoint: Boolean = false;

  canBeCopied: number = 1;

  ext_cell: any = null; //扩展cell

  replaceRule: any = [];

  defaultValue: string | null = null;

  static updateFieldsTextByDefaultValue(param: {
    editor: Editor,
    fields: XField[]
  }) {
    const { fields, editor } = param;
    if (!fields?.length) return
    fields.forEach(field => {
      field.new_text = field.defaultValue;
    })
    return editor.updateFieldText({ fields });
  }

  static  autoFieldCharacterSize(editor:Editor,field:XField,options?:any){
    // 处理字体缩放 ↓
    const maxHeight = field.maxHeight;

    if (!maxHeight) return false;

    let count1 = 0;
    let count2 = 0;
    // uniformFontHeight 统一字体高度
    if(options && options.uniformFontHeight){
      editor.setCharacterSize(field, "base");
    }
    // focus_field.height 不能缓存 需要每次都重新计算
    // 字体放大
    while (
      field.height < maxHeight &&
        field.children[0]?.height < field.style.height &&
        count2 < 10
    ) {
      editor.setCharacterSize(field, "bigger");
      count2++;
    }

    // 字体缩小
    while (field.height > maxHeight && count1 < 12) {
      // 加个次数限制 避免字体已经缩小到最小了还是超高进入死循环
      editor.setCharacterSize(field, "smaller");
      count1++;
    }
    return true;
    // 处理字体缩放 ↑
  }

  static hasID(editor: Editor, id: string) {
    // 获取编辑器中所有文本域对象
    const allFields = editor.getAllFields();
    for (const field of allFields) {
      if (field.id === id) {
        return true;
      }
    }
    return false;
  }

  static getFieldById(editor: Editor, id: string, assign_cell?: Cell) {
    const allCells = editor.getAllCells(assign_cell);
    for (let i = 0; i < allCells.length; i++) {
      const cell = allCells[i];
      const res = cell.getFieldById(id);
      if (res) {
        return res;
      }
    }
  }

  static getFieldsByName(editor: Editor, name: string|string[], assign_cell?: Cell) {
    const fields: XField[] = [];
    const allCells = editor.getAllCells(assign_cell);
    for (let i = 0; i < allCells.length; i++) {
      const cell = allCells[i];
      const res = cell.getFieldsByName(name);
      fields.push(...res);
    }
    return fields;
  }

  static getFieldsByType(editor: Editor, type: string, assign_cell?: Cell) {
    const fields: XField[] = [];
    const allCells = editor.getAllCells(assign_cell);
    for (let i = 0; i < allCells.length; i++) {
      const cell = allCells[i];
      const res = cell.getFieldsByType(type);
      fields.push(...res);
    }
    return fields;
  }

  static getAllFields(
    editor: Editor,
    assign_cell?: Cell
  ): (XField | BoxField)[] {
    const fields: XField[] = [];
    const allCells = editor.getAllCells(assign_cell);
    for (let i = 0; i < allCells.length; i++) {
      fields.push(...allCells[i].fields);
    }
    return fields;
  }

  static getNamesOfShowFields(cascade_list: any) {
    let name_list: any = [];
    for (let i = 0; i < cascade_list.length; i++) {
      const show_list = cascade_list[i].show_field_names;

      name_list = name_list.concat(show_list);
    }
    name_list = [...new Set(name_list)];

    return name_list;
  }

  /**
   * 设置文本域是否只读
   * @param isReadonly 是否只读
   * @param cascade 是否级联
   * @param field 干嘛滴
   * @returns 设置成功了没
   */
  static setReadonly(
    editor: Editor,
    isReadonly: boolean,
    cascade: boolean = false,
    field: XField
  ) {
    // 级联
    if (field && cascade) {
      const all_field = field.getFieldInChildren();
      for (let i = 0; i < all_field.length; i++) {
        const element = all_field[i] as XField;
        element.readonly = isReadonly ? 1 : 0;
      }
      return true;
    } else if (field) {
      field.readonly = isReadonly ? 1 : 0;
      return true;
    } else {
      return false;
    }
  }

  static insert(editor: Editor, props?: any) {
    // 分组如果锁定 则不能编辑
    if (!editor.operableOrNot(["cell", "group"])) return false;
    if (!editor.selection.isCollapsed) {
      editor.delete_backward();
    }
    const para_path = editor.selection.para_focus;
    const focus_row = editor.selection.getFocusRow();
    const focus_field = editor.selection.getFocusField();
    if (focus_field && focus_field.isReadonly) {
      return false;
    }

    if (focus_field && focus_field.children.length === 0) {
      // 判断children中是否有内容 如果没有 显示的内容就是 placeholder 需要删除
      focus_row.paragraph.characters.splice(
        // TODO 文本域不存在跨段落的情况
        para_path[para_path.length - 1],
        focus_field.placeholder_characters.length
      );
    }
    // 判断插入的文本域id是否重复
    if (props && props.id) {
      if (editor.judgeFieldIdExist(props.id)) {
        delete props.id;
      }
    }
    const new_field = focus_row.paragraph.insertField(
      para_path[para_path.length - 1],
      focus_field,
      editor.contextState.getFontState(),
      props
    );
    //文本域anchor模式记录原来的类型
    new_field.meta.before_type = props?.type;
    // 插入文本域后将光标置于文本域后，否则连续插入label会删除原来的label
    const end_para_path = new_field.end_para_path;
    PathUtils.movePathCharNum(end_para_path, 1); // 将 end_para_path 最后一位 移动 1 的距离
    editor.selection.setCursorPosition(
      editor.paraPath2ModelPath(end_para_path)
    );
    // 新插入的文本域存在公式的话更新公式
    if (new_field.formula) {
      editor.updateFieldsFormulaList(new_field);
    }
    if (props?.text) {
      new_field.new_text = props.text;
      editor.updateFieldText({ fields: [new_field] });
      return new_field;
    }
    editor.update(...editor.getUpdateParamsByContainer(focus_row));
    editor.scroll_by_focus();
    editor.render();
    return new_field;
  }

  static reviseAttr(editor: Editor, parameter: any) {
    // 百度了一下 revise 用于修改已有对象 modify 用于大的结构修改 (实际无所谓，但为了跟暴露接口统一) 文本域实例上还有一个 reviseAttr
    // 分组如果锁定 则不能编辑
    if (editor.selection.getFocusGroup()?.lock) {
      editor.event.emit("message", editor_prompt_msg.forbid_delete);
      return false;
    }

    if (editor.selection.getFocusCell()?.lock) {
      editor.event.emit("message", {
        type: "warning",
        msg: "单元格锁定，不能编辑",
      });
      return false;
    }
    if (!parameter.field && !parameter.fields) {
      parameter.field = editor.selection.getFocusField();
    }
    // 以下逻辑替换成 updateAttr 方法 ↓
    if (!parameter.field && !parameter.fields) return false;
    if (parameter.fields) {
      for (const field of parameter.fields) {
        field.updateAttr(parameter);
      }
    } else {
      parameter.field.updateAttr(parameter);
    }
    // 以下逻辑替换成 updateAttr 方法 ↑
    editor.refreshDocument();
    return true;
  }

  static setAnchorExtCell(editor: Editor, field: XField, rawData?: any) {
    const cell = editor.createElement("cell") as Cell;
    cell.id = uuid("ext_cell")
    cell.meta = {
      isExtCell: true
    }
    if (!rawData) {
      field.type = "normal";
      rawData = field.getRawData();
      field.type = "anchor";
      this.clear(editor, [field]);
    }
    //上边给rawData赋值
    if (rawData) {
      // editor.internal.imageSrcObj = rawData.imageSrcObj || {};
      Object.assign(editor.internal.imageSrcObj, rawData.imageSrcObj);
      (rawData as any).content.forEach((raw: any, i: number, array: any[]) => {
        cell.insert_raw(raw, array[i + 1]);
      });
      field.ext_cell = cell;
    }
  }

  static changeFieldDisplay(
    editor: Editor,
    type: display_type,
    fields?: (XField | BoxField)[]
  ) {
    // 分组如果锁定 则不能编辑
    if (editor.selection.getFocusGroup()?.lock) {
      editor.event.emit("message", editor_prompt_msg.forbid_delete);
      return false;
    }
    if (editor.selection.getFocusCell()?.lock) {
      editor.event.emit("message", {
        type: "warning",
        msg: "单元格锁定，不能编辑",
      });
      return false;
    }
    let fieldsList = editor.getAllFields();

    if (fields && fields.length > 0) {
      fieldsList = fields;
    }
    for (let i = 0; i < fieldsList.length; i++) {
      const field = fieldsList[i];
      if (field) {
        field.display_type = type;
      }
    }
    editor.current_cell.update();
    editor.refreshDocument();
  }

  static remove(editor: Editor, fields: (XField | BoxField)[]) {
    let res_path: Path | undefined | boolean;
    const isEditHf = editor.is_edit_hf_mode;
    const viewMode = editor.view_mode;
    editor.view_mode = ViewMode.NORMAL;
    editor.selection.setCursorByRootCell();
    editor.selection.clearSelectedInfo();
    fields = [...fields];
    for (let i = 0; i < fields.length; i++) {
      const field = fields[i];
      const belong = field.cell.getLocation();
      if (belong === "header") {
        editor.enterEditHeaderAndFooterMode("header");
      } else if (belong === "footer") {
        editor.enterEditHeaderAndFooterMode("footer");
      } else if (editor.is_edit_hf_mode) {
        editor.quitEditHeaderAndFooterMode();
      }
      res_path = field.remove(editor.selection);
      if (Array.isArray(res_path)) {
        const model_path = editor.paraPath2ModelPath(res_path);
        // 将para_path再转为model_path进行光标位置设置
        editor.selection.setCursorPosition(model_path);
        const row = editor.selection.getRowByPath(model_path);
        editor.update(...editor.getUpdateParamsByContainer(row));
      }
    }
    if (editor.is_edit_hf_mode && !isEditHf) {
      editor.quitEditHeaderAndFooterMode();
    }
    editor.view_mode = viewMode;
    editor.render();
  }

  /**
   * 验证文本域
   * @param editor Editor
   * @param fields 要验证的文本域
   * @returns
   */
  static valid(editor: Editor, fields?: XField[]) {
    const fieldArr = fields ?? editor.getAllFields(editor.root_cell);
    const errField = [];
    for (const field of fieldArr) {
      const res = field.validValue();
      if (res === false) {
        errField.push(field);
      }
    }
    editor.updateCaret();
    editor.render();
    return errField;
  }

  static updateFieldAdvancedFunctions(
    field: XField | BoxField,
    editor: Editor,
    options? : { clear : boolean }
  ) {
    if (!editor.selection.isCollapsed) return false;
    const formulaFieldList: any = [];
    const fieldAutomationList: any = [];
    const fieldCascadeList: any = [];
    const fields = editor.getAllFields();

    for (let i = 0; i < fields.length; i++) {
      const fieldInfo = fields[i];
      if (fieldInfo.formula && field) {
        //如果
        formulaFieldList.push(fieldInfo);
      }
      if (fieldInfo.automation_list && fieldInfo.automation_list.length) {
        fieldAutomationList.push(fieldInfo);
      }
      if (fieldInfo.cascade_list && fieldInfo.cascade_list.length) {
        fieldCascadeList.push(fieldInfo);
      }
    }
    const realFormulaList: any = [];
    const realFieldAutoList: any = [];
    const whileAllFormulaField = (formulaField: any) => {
      for (let i = 0, len = formulaFieldList.length; i < len; i++) {
        const cField = formulaFieldList[i];
        if (
          cField &&
          cField.formula.indexOf("[" + formulaField.name + "]") > -1
        ) {
          realFormulaList.push(cField);
          formulaFieldList.splice(i, 1, null);
          whileAllFormulaField(cField);
        }
      }
    };
    if (isBoxField(field)) {
      const boxField = field.parent_box;
      if (boxField) {
        whileAllFormulaField(boxField);
      }
    }
    whileAllFormulaField(field);
    //根据focusField查询fieldAutomationList中是否存在与focusField有联动，如果有联动则将focusField.meta.changed置为true
    const getConnectLinkage = (linkField: any) => {
      for (let i = 0; i < fieldAutomationList.length; i++) {
        const autoField = fieldAutomationList[i];
        for (let j = 0; j < autoField.automation_list.length; j++) {
          const autoInfo = autoField.automation_list[j];
          if (autoInfo.action !== "联动") continue;
          for (let k = 0; k < autoInfo.changeFields.length; k++) {
            const changeField = autoInfo.changeFields[k];
            if (changeField.name) {
              const nameList = changeField.name.split(/,|，/).filter(Boolean);
              nameList.forEach((e: any) => {
                if (e === linkField.name) {
                  linkField.meta.changed = true;
                }
              });
            }
          }
        }
      }
    };
    if (field && fieldAutomationList.length) {
      getConnectLinkage(field);
    }
    editor.updateFieldsFormulaList(realFormulaList);
    if (field.meta.is_edit) {
      delete field.meta.is_edit;
    }

    const includeAutoFormulaList = (formulaField: any) => {
      if (
        !formulaField.automation_list ||
        !(formulaField.automation_list && formulaField.automation_list.length)
      )
        return;
      realFieldAutoList.push(formulaField);
    };
    for (let i = 0; i < realFormulaList.length; i++) {
      const aField = realFormulaList[i];
      includeAutoFormulaList(aField);
    }

    if (realFieldAutoList.length) {
      realFieldAutoList.push(field)
      for (let i = 0; i < realFieldAutoList.length; i++) {
        const realFieldAuto = realFieldAutoList[i];
        if (isBoxField(realFieldAuto)) {
          const boxField = realFieldAuto.parent_box;
          if (boxField) {
            editor.updateFieldAutomation(boxField, field, options);
          }
        } else {
          editor.updateFieldAutomation(realFieldAuto, field, options);
        }
      }
    } else {
      if (isBoxField(field)) {
        const boxField = field.parent_box;
        if (boxField) {
          editor.updateFieldAutomation(boxField, field, options);
        }
      } else {
        editor.updateFieldAutomation(field, field, options);
      }
    }

    // TODO 存在严重性能问题
    // fieldCascadeList.forEach(((e: any) => {
    //   XField.toggle(editor, field)
    // }))

    return true;
  }
  static updateFieldsFormulaList(editor: Editor, fields: any) {
    for (let i = 0; i < fields.length; i++) {
      const fieldInfo = fields[i];
      if (fieldInfo.formula) {
        XField.updateFieldsFormula(editor, fieldInfo);
      }
    }
  }

  static updateFieldsFormula(editor: Editor, field: XField) {
    const formula = XField.exchangeFormulaLanguage(field.formula).replace(
      /\s/g,
      ""
    );
    const formulaTextList = formula.split(";");
    // 判断分割后的formulaTextList是否为公式
    for (let i = 0; i < formulaTextList.length; i++) {
      const text = formulaTextList[i];
      try {
        const result = XField.updateFormulaFieldText(text, editor, field);
        if (result) {
          break;
        }
      } catch (error) {
        return editor.event.emit("message", "计算公式格式不正确");
      }
    }
  }
  //更新文本域自动化
  /**
   *
   * @param fields
   * @param editor
   * @param focusField
   * @param clear 清空focusField递归时关联的文本域内容
   * @param chainList 记录循环的文本域名字，防止死循环
   * @returns
   */
  static updateFieldAutomation(
    field: any,
    focusField: any,
    editor: Editor,
    options?: { clear : boolean },
    chainList: any = []
  ) {
    const restoreList: any = []; //记录需要还原的文本域
    const useList: any = []; //记录更改属性的文本域

    if (
      !field.automation_list ||
      !(field.automation_list && field.automation_list.length)
    ) {
      return;
    }
    //判断死循环
    chainList.push(field.name);
    let fieldTextList: any = [];
    let autoField = field;
    if (field instanceof BoxField) {
      fieldTextList.push(...field.box_checked_items.map((e) => e.text));
      //点击文本域与含有条件boxField不是同一个文本域时，找到第一个含有automation_list的文本域
      if (!(field.automation_list && field.automation_list.length)) {
        const fieldBox = field.group_boxes.filter(
          (e) => e.automation_list && e.automation_list.length
        );
        autoField = fieldBox[0];
      }
    } else {
      fieldTextList = field.text.split(/,|，/).filter(Boolean);
    }
    //把显示隐藏排序到最前方
    const targetChars = ["显示", "隐藏"];
    autoField.automation_list.sort((a: any, b: any) => {
      const containsTargetChar = (text: any) => targetChars.some((char: any) => text.includes(char));
      const aContains = containsTargetChar(a.action) ? 1 : 0;
      const bContains = containsTargetChar(b.action) ? 1 : 0;
      return bContains - aContains;
    });

    for (let i = 0; i < autoField.automation_list.length; i++) {
      const fieldAutomation = autoField.automation_list[i];
      if (fieldAutomation.action === "联动") {
        for (let j = 0; j < fieldAutomation.changeFields.length; j++) {
          const changeField = fieldAutomation.changeFields[j];
          const names = changeField.name.split(/,|，/).filter(Boolean);
          for (let k = 0; k < names.length; k++) {
            const name = names[k];
            const infoFields = editor.getFieldsByName(name);
            if (infoFields.length) {
              for (let m = 0; m < infoFields.length; m++) {
                const infoField = infoFields[m];
                if (infoField instanceof BoxField) {
                  editor.event.emit("message", "联动文本域不能包含单选多选框");
                  return;
                } else {
                  if (infoField.meta.changed) {
                    continue;
                  }
                  infoField.new_text = field.text;
                }
              }
              XField.updateText(editor, { fields: infoFields });
              for (let n = 0; n < infoFields.length; n++) {
                const infoField1 = infoFields[n];
                this.updateFieldAutomation(
                  infoField1,
                  focusField,
                  editor,
                  options,
                  chainList,
                );
              }
            }
          }
        }
        continue;
      }
      let result;
      if (fieldAutomation.condition === "=") {
        let content = fieldAutomation.content.text;
        result = XField.areArraysEqual(fieldTextList, content);

      } else {
        if (fieldTextList.length) {
          result = XField.determiningSize(
            fieldTextList,
            fieldAutomation.content,
            fieldAutomation.condition
          );
        } else {
          let content = fieldAutomation.content.text;
          if (fieldAutomation.condition === "!=" && content) {
            result = true
          }
        }
      }
      if (result) {
        if (fieldAutomation.action === "弹窗") {
          editor.event.emit("alert", fieldAutomation.changeFields[0]);
        } else if (fieldAutomation.action === "提示") {
          editor.event.emit("message", fieldAutomation.changeFields[0]);
        } else {
          this.updateChangeFields(
            fieldAutomation,
            useList,
            chainList,
            editor,
            focusField,
            options,
          );
        }
      } else {
        //当是点击的文本域时才触发清空的方法
        // if (field === focusField) {
        restoreList.push(field);
        // }
      }
    }
    // }
    if(!options || options && options.clear !== false){
      restoreList.forEach((e: any) => {
        const chain: any = [];
        this.deleteAutoField(e, useList, editor, chain);
      });
    }
    return;
  }
  static updateChangeFields(
    fieldAutomation: any,
    useList: any,
    chainList: any,
    editor: Editor,
    focus_field: any,
    options: any
  ) {
    for (let j = 0; j < fieldAutomation.changeFields.length; j++) {
      const automationInfo = fieldAutomation.changeFields[j];
      let fieldName = automationInfo.name.split(/,|，/).filter(Boolean);
      for (let k = 0; k < fieldName.length; k++) {
        const name = fieldName[k];
        const connectFields = editor.getFieldsByName(name);
        //当前文本域名称不能与关联规则中文本域名称一致
        const exist = connectFields.findIndex((item: any) => item.id === focus_field.id)
        if (exist !== -1) {
          editor.event.emit(
            "message",
            `当前文本域名称【${focus_field.name}】与自动化规则存在冲突`
          );
          continue;
        }
        if (connectFields.length) {
          if (fieldAutomation.action === "隐藏") {
            for (let i = 0; i < connectFields.length; i++) {
              const connectField = connectFields[i];
              if (connectField.type !== "anchor") {
                connectField.type = "anchor";
                editor.reviseFieldAttr({ type: "anchor", field: connectField });
              }
            }
            continue;
          } else if (fieldAutomation.action === "显示") {
            for (let i = 0; i < connectFields.length; i++) {
              const connectField = connectFields[i];
              if (
                connectField.meta?.before_type &&
                connectField.meta?.before_type !== "anchor"
              ) {
                connectField.type = connectField.meta?.before_type;
              } else {
                connectField.type = "normal";
              }
              editor.reviseFieldAttr({
                type: connectField.type,
                field: connectField,
              });
            }
            continue;
          }

          //把所有关联的文本域收集起来
          useList.push(...connectFields);
          const connectField = connectFields[0];
          if (connectField instanceof BoxField) {
            const allFields = connectField.group_all_items;
            const checkFields = connectField.box_checked_items;
            const coordinates = [];
            for (let m = 0; m < allFields.length; m++) {
              const itemField = allFields[m];
              for (let n = 0; n < checkFields.length; n++) {
                const checkField = checkFields[n];
                if (itemField.id === checkField.id) {
                  coordinates.push(m);
                }
              }
            }
          }
        }

        for (let m = 0; m < connectFields.length; m++) {
          const fieldInfo = connectFields[m];
          if (fieldInfo.type === "select") {
            let subscript = String(automationInfo.changeText)
              .split(/,|，/)
              .filter(Boolean);
            const list = subscript.map(
              (n: any) => fieldInfo.source_list[n].text
            );
            const symbol = Config.separatorGroups.filter(
              (e: any) => e.index === fieldInfo.separator
            );
            const changeText = list.join(symbol[0].separator);
            fieldInfo.new_text = changeText;
            break;
          } else if (fieldInfo.type === "box") {
            const boxField = fieldInfo as BoxField;

            const selectList = automationInfo.changeText
              .split(/,|，/)
              .filter(Boolean);
            const items = boxField.group_all_items;
            for (let n = 0; n < items.length; n++) {
              const item = items[n];
              if (selectList.indexOf(String(n)) !== -1) {
                item.updateBoxChecked(true);
                if (!boxField.box_multi) break;
              } else {
                item.updateBoxChecked(false);
              }
            }
          } else {
            fieldInfo.new_text = String(automationInfo.changeText);
          }
        }
        XField.updateText(editor, { fields: connectFields });

        for (let i = 0; i < connectFields.length; i++) {
          const fieldInfo = connectFields[i];
          if (fieldInfo.automation_list && fieldInfo.automation_list.length) {
            this.updateFieldAutomation(
              fieldInfo,
              focus_field,
              editor,
              options,
              chainList
            );
            if (chainList.length) {
              if (new Set(chainList).size !== chainList.length) {
                editor.event.emit(
                  "message",
                  `文本域【${fieldInfo.name}】自动化规则存在冲突`
                );
                continue;
              }
            }
          }
        }
      }
    }
  }
  static deleteAutoField(
    field: XField,
    useList: any,
    editor: Editor,
    chainList: any
  ) {
    if (
      !field.automation_list ||
      !(field.automation_list && field.automation_list.length)
    ) {
      return;
    }
    chainList.push(field.name);
    let autoField = field;
    if (field instanceof BoxField) {
      //点击文本域与含有条件boxField不是同一个文本域时，找到第一个含有automation_list的文本域
      if (!(field.automation_list && field.automation_list.length)) {
        const fieldBox = field.group_boxes.filter(
          (e) => e.automation_list && e.automation_list.length
        );
        autoField = fieldBox[0];
      }
    }

    for (let i = 0; i < autoField.automation_list.length; i++) {
      const element = autoField.automation_list[i];
      A: for (let j = 0; j < element.changeFields.length; j++) {
        const changeField = element.changeFields[j];
        //如果是提示和弹窗打断循环
        if (element.action !== "关联") {
          break A;
        }

        if (chainList.length) {
          if (new Set(chainList).size !== chainList.length) {
            editor.event.emit(
              "message",
              `文本域【${changeField.name}】自动化规则存在冲突`
            );
            return false;
          }
        }
        const nameList = changeField.name.split(",").filter(Boolean);
        for (let k = 0; k < nameList.length; k++) {
          const name = nameList[k];
          const fields = editor.getFieldsByName(name);
          //需要还原的文本域内关联的文本域与不需要还原的文本域内关联的文本域不能时同一个文本域，会将修改的值还原掉

          const finalList = fields.filter(
            (item: any) => !useList?.includes(item)
          );

          for (let m = 0; m < finalList.length; m++) {
            const clearField = finalList[m];
            if (clearField instanceof BoxField) {
              clearField.clearBoxChecked();
            } else {
              editor.clearFields([clearField]);
            }
            //不传null就会触发  fields.filter((item: any) => !useList?.includes(item))，导致更新不了递归的文本域
            this.deleteAutoField(clearField, null, editor, chainList);
          }
        }
      }
    }

    return true;
  }
  /**
   * 切换显示隐藏文本域
   * @param editor Editor
   * @param field
   * @param txt
   * @returns
   */
  static toggle(editor: Editor, field: XField | BoxField, txt?: Array<string>) {
    if (!(field.cascade_list && field.cascade_list.length > 0)) return;

    editor.setAdminMode(true);

    const field_text = txt === undefined ? [field.text] : txt;
    const ancestorsFields = field.getAllParents();
    let cascadeList = field.cascade_list;
    // 此处如果是分离成组的单选框，则获取所有的级联规则进行合并
    if (isBoxField(field)) {
      const groupBoxes = field.group_boxes;
      if (groupBoxes.length) {
        cascadeList = [];
        groupBoxes.forEach((item: BoxField) => {
          cascadeList.push(...item.cascade_list);
        });
      }
    }
    const show_field_names = this.getNamesOfShowFields(cascadeList);
    // 隐藏文本域逻辑
    show_field_names.forEach((hide_name: any) => {
      if(!hide_name) return ;
      const fields = editor.getFieldsByName(hide_name);
      if (!fields.length) return;
      for (let i = 0; i < fields.length; i++) {
        if (
          ancestorsFields.some((currentField) => currentField === fields[i])
        ) {
          editor.event.emit("message", {
            type: "warning",
            msg: `name值为"${fields[i].name}"的文本域隐藏时,不能影响到当前文本域`,
          });
          return;
        }
        const startParaPath = fields[i].start_para_path;
        const endParaPath = fields[i].end_para_path_outer;
        if(fields[i].isRemove) continue;

        const currentGroup = editor.getGroupByParaPth(startParaPath);
        if (currentGroup && currentGroup.lock) continue;
        
        editor.selection.setSelectionByPath(
          startParaPath,
          endParaPath
        );

        const selectionData = saveSelectionData(editor);

        if (!selectionData || !selectionData.length) return;

        // const rawData = rawDataTrans.selectionData2RawData(editor, selectionData);
        const rawData = editor.event.emit(
          "selectionData2RawData",
          selectionData
        );

        let str = JSON.stringify(rawData);
        // ↓ 处理样式
        const res: any = {};
        const fontMap = editor.fontMap.get();
        for (const [k, v] of fontMap.entries()) {
          if(str.indexOf(k)>-1){
            const obj = { ...v };
            res[k] = obj;
          }
        }

        const fontStr =
            Config.font_map_flag + JSON.stringify(res) + Config.font_map_flag; // 首尾都要有 方便粘贴的时候好取
        // ↑ 处理样式


        str += fontStr;

        const resStr = encodeBase64(encodeURIComponent(str)); // 转base64

        EditorHelper.deleteBackward(editor);

        EditorHelper.insertBox(editor, resStr, hide_name);
      }
    });

    const show_names: any = [];

    cascadeList.forEach((e: any) => {
      if (
        (field_text as any).includes(e.text) ||
        (e.text === "*" && field_text[0])
      ) {
        show_names.push(...e.show_field_names);
      }
    });
    // 展示文本域逻辑

    show_names.forEach((show_name: any) => {
      if (!show_name) return;
      const boxes = editor.getBoxesByName(show_name);
      if (boxes.length > 0) {
        for (let i = boxes.length - 1; i >= 0; i--) {
          const box_info = boxes[i];

          const currentGroup = editor.getGroupByParaPth(box_info.para_path);
          if (currentGroup && currentGroup.lock) continue;
          const content = decodeURIComponent(
            decodeBase64(box_info.box.content)
          );
          const arr = content.split(Config.font_map_flag);
          const fontStr = arr.splice(1, 1).join("");

          const field_name = JSON.parse(arr.join(""))[0].children[0].name;
          if (field_name === show_name) {
            const fontObj = JSON.parse(fontStr);
            for (const id in fontObj) {
              const style = fontObj[id];
              editor.fontMap.add(style, id);
            }

            const cell = editor.getCellByRawNodeList(JSON.parse(arr.join("")));
            if (!cell) {
              return;
            }
            const sel = editor.selection; // 选区
            // 如果是选中了一些内容 然后输入 则需要先删除
            const end_path = [...box_info.para_path];
            end_path[end_path.length - 1] = end_path[end_path.length - 1] + 1;
            sel.setSelectionByPath(box_info.para_path, end_path);
            if (!sel.isCollapsed) {
              const del_result = editor.delete_backward();
              if (!del_result) {
                return;
              }
            }
            const para_path = box_info.para_path;

            const insert_field = sel.getFieldByPath(para_path);

            const copy_data = handleCopyData(
              cell,
              insert_field,
              para_path,
              editor
            ); // 处理后复制出来的数据
            if (!editor.delSectionRecordStack()) return;

            insertParagraphsHelper(
              sel,
              para_path,
              insert_field,
              copy_data,
              false
            );
            editor.refreshDocument();
          }
        }
      }
    });
    editor.setAdminMode(false);
    return true;
  }

  /**
   * 控制边框的显示隐藏
   * @param editor Editor
   * @param isShow 是否展示边框
   */
  static toggleSymbol(
    editor: Editor,
    isShow: boolean = !editor.config.show_field_symbol
  ) {
    // 该方法非常消耗性能的主要原因就是 editor.root_cell.update 里边调用了 paragraph.updateChildren 在里边调用了 createRow 这个方法是最消耗性能的 createRow 里边有个 getFieldById 那个玩意儿消耗性能 以通过传 cell 纠正
    editor.config.show_field_symbol = isShow;
    // 无边框模式时不用切换
    if (
      editor.config.fieldShowMode !== FieldShowMode.normal &&
      editor.config.keepSymbolWidthWhenPrint
    ) {
      // 必须得是 keepSymbolWidthWhenPrint 模式的时候，否则就会打印出来没内容的文本域 placeholder 显示空白 在预览的时候显示 placeholder 这几个字
      return editor.refreshDocument();
    }
    // 先使用版本判断，防止续打问题
    // if (editor.document_meta.useNewToggleSymbol) {
    //   // 首先记录段落坐标
    //   const start_para_path = [...editor.selection.para_focus];
    //   editor.header_cell.update();
    //   editor.root_cell.update(false);
    //   editor.footer_cell.update();
    //   // 还原坐标，调用后修改内容导致报错
    //   editor.selection.setCursorPosition(editor.paraPath2ModelPath(start_para_path));
    //   // 因为隐藏边框后，viewData中的row已经不对应，所以需要重新更新
    //   editor.pages = [];
    //   editor.refreshDocument();
    //   return;
    // }
    // TODO 临时解决卡顿问题，有空继续优化以下注释代码
    editor.refreshDocument(true); // 直接调用这个 会导致 打印的时候影响到校验文本域的颜色
  }

  /**
   * 替换元素
   * @param editor Editor
   * @param fields
   * @param elements
   */
  static replaceElements(
    editor: Editor,
    fields: XField[],
    elements: ElementInParagraph[]
  ) {
    // 清空逻辑使用到了选区删除逻辑，所以此处应该先取消选区
    editor.selection.setCursorByRootCell();
    editor.selection.clearSelectedInfo();
    for (let i = 0; i < fields.length; i++) {
      const new_elements: ElementInParagraph[] = [];
      for (let j = 0; j < elements.length; j++) {
        new_elements.push(elements[j].copy());
      }
      const field = fields[i];

      field.clear();

      new_elements.forEach((ele) => {
        ele.field_id = field.id;
        if (isImage(ele)) {
          editor.imageMap.addOnload(ele);
          setFocusImage(null);
        }
        field.children.push(ele);
      });
      field.updateChildren();
      // 将光标定位到文本域末尾位置
      editor.locatePathInField(field, "end");
    }
    editor.refreshDocument();
  }

  static updateElements(
    editor: Editor,
    field: XField,
    elements: ElementInParagraph[],
    params?: any
  ) {
    // 首先设置光标，如果是追加则应将光标设置到文本域结束字符前
    if (params?.append) {
      editor.locatePathInField(field, "end");
    } else {
      // 如果是替换则将文本域先进行清空
      field.clear();
      field.reShowPlaceholder();
      editor.locatePathInField(field, "start");
    }
    const ori_field_type = field.type;
    field.type = "normal";
    for (let i = 0; i < elements.length; i++) {
      const ele = elements[i];
      if (isField(ele)) {
        if (ele.type === "box") {
          editor.insertWidget(ele);
        } else {
          editor.insertField(ele);
        }
      } else if (isImage(ele)) {
        const copy_image = ele.copy(); // 复制一下否则多文本替换时bug
        const para_path = [...editor.selection.para_focus];
        const focus_para = editor.selection.getParagraphByPath(para_path);
        // paragraph中按para_path位置插入图片
        focus_para.insertImage(
          copy_image,
          para_path[para_path.length - 2],
          para_path[para_path.length - 1],
          field
        );
        editor.imageMap.addOnload(copy_image);
        setFocusImage(null);
        editor.locatePathInField(field, "end");
        editor.refreshDocument();
      } else if (typeof ele === "string") {
        editor.insertText(ele);
      } else if (isWidget(ele)) {
        editor.insertSimpleWidget(ele.widgetType, ele.border);
      }
    }
    field.type = ori_field_type;
    return true;
  }

  static operation(num1: number, num2: any, symbol: string) {
    let numList = [];
    switch (symbol) {
      case "!=":
        return num1 !== Number(num2) ? true : false;
      case "≠":
        return num1 !== Number(num2) ? true : false;
      case ">":
        return num1 > Number(num2) ? true : false;
      case "<":
        return num1 < Number(num2) ? true : false;
      case ">=":
        return num1 >= Number(num2) ? true : false;
      case "<=":
        return num1 <= Number(num2) ? true : false;
      case ">=<=":
        numList = num2.split(",");
        return numList[0] <= num1 && num1 <= numList[1] ? true : false;
      case "><=":
        numList = num2.split(",");
        return numList[0] < num1 && num1 <= numList[1] ? true : false;
      case ">=<":
        numList = num2.split(",");
        return numList[0] <= num1 && num1 < numList[1] ? true : false;
      case "><":
        numList = num2.split(",");
        return numList[0] < num1 && num1 < numList[1] ? true : false;
    }
  }
  static determiningSize(arr: Array<string>, content: any, symbol: string) {
    for (let i = 0; i < arr.length; i++) {
      const num = Number(arr[i]);
      const result = this.operation(num, content.text, symbol);
      if (result) return true;
    }
  }
  static areArraysEqual(arr1: Array<string>, content: any) {
    if (!content) {
      if (!arr1.length) {
        return true;
      } else {
        return false
      }
    }
    if (content === "*" && arr1.length) {
      return true;
    }
    const list = content.split(/,|，/);

    if (arr1.length !== list.length) {
      return false;
    }
    arr1.sort();
    list.sort();

    for (let i = 0; i < arr1.length; i++) {
      if (arr1[i] !== list[i]) {
        return false;
      }
    }

    return true;
  }
  static exchangeFormulaLanguage(formula: string) {
    let newFormula = formula
      .replace(/[；]/g, ";")
      .replace(/[：]/g, ":")
      .replace(/[×]/g, "*")
      .replace(/[÷]/g, "/")
      .replace(/[≥]/g, ">=")
      .replace(/[≤]/g, "<=");
    return newFormula;
  }

  static updateFormulaFieldText(
    formulaText: string,
    editor: Editor,
    field: XField
  ) {
    const reg = /\[(.*?)\]/gi;
    const textList = formulaText.match(reg);
    let saveText = formulaText;
    let timeMode = false;
    let formulaValue = "";
    const timeType = [];
    // 如果没匹配到文本域name
    if (textList) {
      const dateField = [];
      const allFields = [];
      // 匹配到了文本域的name
      for (let i = 0; i < textList.length; i++) {
        const text = textList[i].replace(reg, "$1");
        const fields = editor.getFieldsByName(text);
        allFields.push({ name: text, fields: fields });
        // 如果找不到对应的文本域，则把原字符串中的文本域名字换成0
        if (!fields.length) {
          formulaText = XField.textReplace(text, formulaText);
          continue;
        }
        let fieldText = "";
        let fieldNum = 0;
        let dateNum = 0; // 文本域中有date类型的文本域则+1
        for (let i = 0; i < fields.length; i++) {
          const formulaField = fields[i];
          if (formulaField.type === "date") {
            dateNum += 1;
            dateField.push(formulaField);
            break;
          }
          //切换文本域类型时清空文本域的formula_value
          if (formulaField.type === "normal") {
            formulaField.formula_value = "";
          }
          // 如果文本域存在公式就递归解析文本域公式
          // if (formulaField.formula) {
          //   XField.updateFormulaFieldText(formulaField.formula, editor, formulaField);
          // }
          // 如果文本域没有公式就把相同name文本域的值求和
          if (formulaField.formula_value) {
            if (
              !(
                formulaField instanceof BoxField && !formulaField.box_checked
              ) &&
              isNumber(formulaField.formula_value)
            ) {
              fieldNum += Number(formulaField.formula_value);
            }
          } else if (!isBoxField(formulaField)) {
            let text = formulaField.text;
            //将带数字的text转换为数字
            let numText: any = extractNumber(text)
            if (formulaText.includes("==")) {
              numText = text
            }
            if (isNumber(numText)) {
              // 若文本域没有formula_value属性则用text
              fieldNum += Number(numText);
            } else {
              fieldText += text;
            }

          } else if (
            formulaField instanceof BoxField &&
            formulaField.box_children.length
          ) {
            // 如果是单选复选框而且内部有选择的文本域则把内部打钩的formula_value求和
            for (let i = 0; i < formulaField.box_children.length; i++) {
              const boxField = formulaField.box_children[i];
              if (
                boxField.formula_value &&
                boxField.box_checked &&
                isNumber(boxField.formula_value)
              ) {
                fieldNum += Number(boxField.formula_value);
              }
            }
          }
        }
        if (dateNum === 0) {
          dateField.push(fields[0]);
        }

        if (!fieldNum) {
          // 如果是字符串判断，此时直接使用文本
          if (formulaText.split(":").length > 1) {
            // 当比较运算时发现text不是数字，则强制判定为不成立
            if (!isNumber(text) && /[><]/.test(formulaText)) {
              formulaText = "1>2";
            } else {
              formulaText = XField.textReplace(
                text,
                formulaText,
                "'" + fieldText + "'"
              );
            }
          } else {
            formulaText = XField.textReplace(text, formulaText);
          }
          continue;
        }
        formulaText = formulaText.replace(
          "[" + fields[0].name + "]",
          String(fieldNum)
        );
      }
      // 判断文本域是否全是date类型的，才能正常加减
      timeMode = dateField.every((e) => e.type === "date");
      if (timeMode) {
        for (let i = 0; i < allFields.length; i++) {
          const nameFields = allFields[i];
          for (let j = 0; j < dateField.length; j++) {
            const field = dateField[j];
            if (field.name === nameFields.name) {
              const time = field.text;
              // 把各种类型的时间格式转换为毫秒数
              const { date, type } = stringDateTransformNormDate(time);
              // 所有的精确类型收集起来
              timeType.push(type);
              saveText = saveText.replace("[" + field.name + "]", String(date));
            }
          }
        }
        formulaText = saveText;
      }
    }

    if (formulaText === "") return false;
    let formula2 = "";
    const inequality = formulaText.split(":");
    const dealFormula = XField.exchangeFormulaLanguage(inequality[0]);
    formulaValue = XField.math("(" + dealFormula + ")");
    if (inequality.length > 1) {
      formula2 = inequality[1];
      if (
        formula2 === "''" ||
        formula2 === "“”" ||
        formula2 === '""' ||
        formula2 === "‘’"
      ) {
        formula2 = "";
      }
    }
    // 如果是判断大小则给formulaValue赋值
    if (typeof formulaValue === "boolean") {
      if (formulaValue) {
        formulaValue = formula2;
      } else {
        formulaValue = "";
      }
    }
    // 如果是时间计算类型，则根据时间类型返回相应的时长
    if (timeMode) {
      if (timeType.indexOf("秒") !== -1) {
        formulaValue =
          keepDecimal(XField.math("(" + Number(formulaValue) / 1000 + ")"), 2) +
          "";
      } else {
        if (timeType.indexOf("分") !== -1) {
          formulaValue =
            keepDecimal(
              XField.math("(" + Number(formulaValue) / 1000 / 60 + ")"),
              2
            ) + "";
        } else {
          if (timeType.indexOf("时") !== -1) {
            formulaValue =
              keepDecimal(
                XField.math("(" + Number(formulaValue) / 1000 / 3600 + ")"),
                2
              ) + "";
          } else {
            formulaValue =
              keepDecimal(
                XField.math(
                  "(" + Number(formulaValue) / 1000 / 3600 / 24 + ")"
                ),
                2
              ) + "";
          }
        }
      }
    }
    if (
      field.formula_value !== Number(formulaValue) ||
      !field.children.length
    ) {
      field.setNewText(formulaValue);
      field.formula_value = Number(formulaValue);
      XField.updateText(editor, { fields: [field] });
    }
    return formulaValue;
  }

  /**
   *  formulaText公式中文本域name替换为别的数字的逻辑
   * @param text
   * @param formulaText
   * @returns
   */
  static textReplace(
    text: string,
    formulaText: string,
    number: number | string = 0
  ) {
    formulaText = formulaText.replace("[" + text + "]", String(number));
    if (formulaText.indexOf("/0")) {
      formulaText = formulaText.replace("/0", "");
    }
    return formulaText;
  }

  // 通过传入字符串计算结果
  static math(text: string) {
    const Formula = Function;
    return new Formula("return" + text)();
  }

  static updateRaw(editor: Editor, field: XField, rawData: any, params?: any) {
    rawData = useRawDataByConfig(rawData);
    // 如果是已经删除的文本域不继续执行
    if (field.isRemove) {
      return false;
    }
    // 首先设置光标，如果是追加则应将光标设置到文本域结束字符前
    if (params?.append) {
      editor.locatePathInField(field, "end");
    } else {
      // 如果是替换则将文本域先进行清空
      field.clear();
      field.reShowPlaceholder();
      editor.locatePathInField(field, "start");
    }
    // 如果是标签类型文本域，则先设置成普通类型，执行完成后再还原，光标定位会存在问题
    if (field.type === "label") {
      field.type = "normal";
      editor.insertTemplateData(rawData);
      if (params && params.linebreak === false) {
        editor.delete_backward();
      }
      field.type = "label";
    } else {
      editor.insertTemplateData(rawData);
      if (params && params.linebreak === false) {
        editor.delete_backward();
      }
    }
    return true;
  }

  // 不能直接调用这个 否则的话 就会在设置光标的地方报错 需要调用 editor.updateFieldText 里边在调用这个
  static updateText(
    editor: Editor,
    afferent_parameter: any,
    isDocumentAlign?: boolean
  ) {
    // 找到所有需要处理的文本域
    let need_handle_fields: XField[] = [];
    if (afferent_parameter.fields) {
      need_handle_fields = removeRepeat(afferent_parameter.fields);
    } else {
      if (afferent_parameter.name) {
        need_handle_fields.push(
          ...editor.getFieldsByName(afferent_parameter.name)
        );
      }
      if (afferent_parameter.id) {
        const res_field = editor.getFieldById(afferent_parameter.id);
        if (res_field) {
          need_handle_fields.push(res_field);
        }
      }
    }
    for (let i = 0; i < need_handle_fields.length; i++) {

      const field = need_handle_fields[i];
      const replaceRule = field.replaceRule;
      if (replaceRule && replaceRule.length) {
        editor.handleFieldNewTextByReplaceRule(field, replaceRule);
      }
    }

    // 区分页眉页脚文本域 和 root_cell 中的文本域 后边单独进行处理
    const hf_cell_need_handle_fields = [];
    const root_cell_need_handle_fields = [];
    for (const field of need_handle_fields) {
      // field上肯定是有cell的 只不过 cell 有可能是表格里边的  所以要进行判断 因为表格里边的单元格 不会等于 editor.root_cell 该Cell上的 hf_part 属性值可能也不对
      const current_cell = field.cell.parent
        ? field.cell.parent.parent
        : field.cell;
      if (current_cell.hf_part) {
        hf_cell_need_handle_fields.push(field);
      } else {
        root_cell_need_handle_fields.push(field);
      }
    }
    // 记录原编辑模式
    const origin_is_edit_hf_mode = editor.is_edit_hf_mode;

    // 处理页眉页脚中的文本域
    editor.is_edit_hf_mode = true;
    updateFieldTextHelper_HeaderFooter(
      hf_cell_need_handle_fields,
      afferent_parameter
    );

    const origin_current_cell = editor.current_cell; // 先保存下来 为了后边恢复
    const origin_focus = editor.selection.focus;
    const origin_anchor = editor.selection.anchor;
    if (origin_is_edit_hf_mode) {
      // 因为 updateFieldTextHelper 这个方法是替换的正文的文本域内容 所以必须保证 editor.current_cell 是 editor.root_cell
      editor.current_cell = editor.root_cell;
    }
    editor.is_edit_hf_mode = false;
    updateFieldTextHelper_RootCell(
      editor,
      root_cell_need_handle_fields,
      afferent_parameter,
      isDocumentAlign
    );
    editor.current_cell = origin_current_cell;

    // 换过current_cell之后 光标位置也不对了 所以也要恢复
    if (origin_is_edit_hf_mode) {
      // TODO 后边都重置 光标了 这个还有没有必要
      editor.selection.focus = origin_focus;
      editor.selection.anchor = origin_anchor;
    }

    // 重置光标
    editor.update();
    editor.is_edit_hf_mode = origin_is_edit_hf_mode;
    // 因文本域公式更新多个文本域乱跳而注掉
    // editor.scroll_by_focus();
    editor.render();
    return true;
  }

  static replaceImage(
    editor: Editor,
    fields: XField[],
    image_info: {
      src: string;
      url?: string;
      width?: number | string;
      height?: number | string;
      meta?: any;
    }
  ) {
    const width = image_info.width ? Number(image_info.width) : 100;
    const height = image_info.height ? Number(image_info.height) : 50;

    const font = editor.contextState.getFontState();
    const image = new Image(
      image_info.src,
      width,
      height,
      font,
      image_info.meta ? image_info.meta : {}
    );
    image.url = image_info.url ?? "";
    editor.replaceFieldsElements(fields, [image]);
  }

  /**
   * 导航/定位到某个文本域
   * @param editor Editor
   * @param field 导航/定位到的文本域
   * @param position 是开头还是结束
   */
  static navigateTo(
    editor: Editor,
    field: XField,
    position: "start" | "end" | "preStart" | "afterEnd" = "end"
  ) {
    let para_path: Path;
    const cellLocation = field.cell.getLocation();
    if (cellLocation !== "root" && !editor.is_edit_hf_mode) return; // 如果不在正文 就必须得是编辑页眉页脚模式 否则就 return 掉
    if (cellLocation === "root" && editor.is_edit_hf_mode) return; // 如果在正文 但是是编辑页眉页脚模式也得 return 否则就会报错
    if (
      position === "start" ||
      (position === "end" && !field.children.length)
    ) {
      para_path = field.start_para_path_inner;
    } else if (position === "end") {
      para_path = field.end_para_path;
    } else if (position === "preStart") {
      para_path = field.start_para_path;
    } else {
      para_path = field.end_para_path_outer;
    }
    editor.selection.setCursorPosition(editor.paraPath2ModelPath(para_path));
  }

  /**
   * 通过标识符定位到文本域
   * @param eidtor Editor
   * @param id id
   * @param name name
   * @param field field
   * @returns 定位到的文本域
   */
  static navigateByIdentifier(
    eidtor: Editor,
    id?: string,
    name?: string,
    field?: XField
  ) {
    let targetField: XField | null = field ?? null;
    if (!targetField) {
      if (name) {
        targetField = eidtor.getFieldsByName(name).length
          ? eidtor.getFieldsByName(name)[0]
          : null;
      } else if (id) {
        targetField = eidtor.getFieldById(id) || null;
      }
    }
    if (!targetField) {
      return "未获取到目标文本域";
    }
    const fieldStratPath = targetField.start_para_path_inner;
    eidtor.selection.setCursorPosition(
      eidtor.paraPath2ModelPath(fieldStratPath)
    );
    eidtor.update();
    eidtor.scroll_by_focus();
    eidtor.render();
    return targetField;
  }

  /**
   * 清空文本域
   * @param editor Editor
   * @param fields 要清空的文本域
   * @returns 执行是否成功
   */
  static clear(editor: Editor, fields: XField[]) {
    const last_cleared_field = editor.clearFieldsOnly(fields);
    if (last_cleared_field) {
      if (
        editor.current_cell.getLocation() ===
        last_cleared_field.cell.getLocation()
      ) {
        // 重置光标
        editor.locatePathInField(last_cleared_field, "start");
        editor.update();
        editor.scroll_by_focus();
        editor.render();
        return true;
      } else {
        // 因为有可能最后一个文本域在页眉或者页脚
        editor.selection.setCursorByRootCell("start");
        editor.update();
        editor.scroll_by_focus();
        editor.render();
        return true;
      }
    }
    return false;
  }

  /**
   * 在过渡期间清空 并不改变光标位置
   * 返回最后清空的文本域
   */
  static clearOnly(fields: XField[]) {
    let last_cleared_field: XField | null = null;
    for (let i = 0; i < fields.length; i++) {
      const field = fields[i];
      if (!field || !isField(field)) {
        throw new TypeError(`数组中第${i}个文本域类型不对,值为${field}`);
      }
      if (field.isRemove) {
        continue;
      }
      field.clear();
      field.reShowPlaceholder();
      last_cleared_field = field;
    }
    return last_cleared_field;
  }

  // 匿名化处理文本域
  static updateFieldsAsterisk(editor: Editor) {
    // 根据name拿到需要匿名的文本域,给这些文本域增加匿名属性
    const fields: XField[] = [];
    const names = editor.config.fieldAsteriskNames;
    if (!names.length) {
      return;
    }
    fields.push(...editor.getAllFields(editor.root_cell));
    editor.pages.forEach((page) => {
      fields.push(...editor.getAllFields(page.header.header_cell));
      fields.push(...editor.getAllFields(page.footer.footer_cell));
    });
    fields.forEach((field) => {
      field.setFieldAsterisk(false);
    });
    fields.forEach((field) => {
      if (names && (names as any).includes(field.name)) {
        field.setFieldAsterisk(true);
      }
    });
  }

  static attrJudgeUndefinedAssign(newModel: XField, raw: any) {
    if (raw.name !== undefined) newModel.name = raw.name;
    if (raw.start_symbol !== undefined)
      newModel.start_symbol = raw.start_symbol;
    if (raw.end_symbol !== undefined) newModel.end_symbol = raw.end_symbol;
    if (raw.display_type !== undefined)
      newModel.display_type = raw.display_type;
    if (raw.readonly !== undefined) newModel.readonly = raw.readonly;
    if (raw.deletable !== undefined) newModel.deletable = raw.deletable;
    if (raw.canBeCopied !== undefined) newModel.canBeCopied = raw.canBeCopied;
    if (raw.replaceRule !== undefined) newModel.replaceRule = raw.replaceRule;
    if (raw.placeholder !== undefined) newModel.placeholder = raw.placeholder;
    if (raw.show_format !== undefined) newModel.show_format = raw.show_format;
    if (raw.show_field !== undefined) newModel.show_field = raw.show_field;
    if (raw.cascade_list !== undefined)
      newModel.cascade_list = raw.cascade_list;
    if (raw.replace_format !== undefined)
      newModel.replace_format = raw.replace_format;
    if (raw.number_format !== undefined)
      newModel.number_format = raw.number_format;
    if (raw.source_id !== undefined) newModel.source_id = raw.source_id;
    if (raw.source_list !== undefined) newModel.source_list = raw.source_list;
    if (raw.meta !== undefined) newModel.meta = raw.meta;
    if (raw.active_type !== undefined) newModel.active_type = raw.active_type;
    if (raw.multi_select !== undefined)
      newModel.multi_select = raw.multi_select;
    if (raw.separator !== undefined) newModel.separator = raw.separator;
    if (raw.max_width !== undefined) newModel.max_width = raw.max_width;
    if (raw.min_width !== undefined) newModel.min_width = raw.min_width;
    if (raw.valid !== undefined) newModel.valid = raw.valid;
    if (raw.valid_content !== undefined)
      newModel.valid_content = raw.valid_content;
    if (raw.forbidden !== undefined) newModel.forbidden = raw.forbidden;
    if (raw.tip !== undefined) newModel.tip = raw.tip;
    if (raw.maxHeight !== undefined) newModel.maxHeight = raw.maxHeight;
    if (raw.inputMode !== undefined) newModel.inputMode = raw.inputMode;
    if (raw.formula !== undefined) newModel.formula = raw.formula;
    if (raw.formula_value !== undefined)
      newModel.formula_value = raw.formula_value;
    if (raw.align !== undefined) newModel.align = raw.align;
    if (raw.automation_list !== undefined)
      newModel.automation_list = raw.automation_list;
    if (raw.defaultValue !== undefined)
      newModel.defaultValue = raw.defaultValue;
    if (raw.position !== undefined)
      newModel.position = raw.position;
  }

  /**
   *
   * */
  constructor(
    id: string,
    style: FontStyle,
    cell: Cell,
    mainProps: boolean = true
  ) {
    this.id = id;
    this.style = mergeObjNonNull(
      {},
      cell.editor.config.default_font_style,
      style
    );
    cell.editor.fontMap.add(this.style);
    this.cell = cell;
    this.end_symbol = cell.editor.config.field_end_symbol;
    this.start_symbol = cell.editor.config.field_start_symbol;
    this.field_symbol_color = cell.editor.config.field_symbol_color;
    this.placeholder_characters = [];
    if (mainProps) {
      this.generateMainProps();
    }
  }

  /**
   * 文本域包含开始字符与结束字符的总长度
   */
  get total_length(): number {
    let total_length: number;
    if (this.children.length) {
      total_length = this.children.length;
    } else {
      total_length = this.placeholder_characters.length;
    }
    if ((this.children as any).includes(this.start_sym_char)) {
      total_length += 2;
    }
    return total_length;
  }

  /**
   * 开始字符所在段落中下标
   */
  get start_char_index(): number {
    return this.start_para.characters.indexOf(this.start_sym_char);
  }

  /**
   * 判断文本域以及父级文本域是否只读
   * @returns 是否只读
   */
  get isReadonly(): boolean {
    let isReadonly: boolean = false;
    // 当时label类型时同样为只读
    if (this.type === "label") {
      return true;
    }
    if (!this.readonly) {
      if (this.parent) {
        isReadonly = this.parent.isReadonly;
        return isReadonly;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }

  /**
   * 用于判断是否已经删除，当调用clear时，如果先清空父文本域会导致子文本域已经在cell中删除
   */
  get isRemove() {
    return !(this.cell.fields as any).includes(this);
  }

  /**
   * 文本域开始字符所在段落
   */
  get start_para(): Paragraph {
    const paragraph = this.cell.paragraph.find(
      (para) =>
        isParagraph(para) &&
        para.characters.find(
          (char) => char.field_position === "start" && char.field_id === this.id
        )
    ) as Paragraph;
    return paragraph!;
  }

  /**
   * 文本域结束字符所在段落
   */
  get end_para(): Paragraph {
    const paragraph = this.cell.paragraph.find(
      (para) =>
        isParagraph(para) &&
        para.characters.find(
          (char) => char.field_position === "end" && char.field_id === this.id
        )
    ) as Paragraph;
    return paragraph!;
  }

  /**
   * 文本域起始段落路径 跨段的情况始末位置不一段 (文本域开始字符前的位置)
   */
  get start_para_path(): Path {
    const path: Path = [];
    if (this.cell.parent) {
      path.push(this.cell.parent.para_index, getCellIndex(this.cell));
    }
    const start_para = this.start_para;
    path.push(
      start_para.para_index,
      start_para.characters.indexOf(this.start_sym_char)
    );
    return path;
  }

  /**
   * 文本域开始字符后一个位置
   */
  get start_para_path_inner(): Path {
    const outer_path = this.start_para_path;
    // 后移一位
    PathUtils.movePathCharNum(outer_path, 1);
    return outer_path;
  }

  /**
   * 文本域结束段落路径 跨段的情况始末位置不一段 （文本域结束字符前的位置）
   */
  get end_para_path(): Path {
    const path: Path = [];
    if (this.cell.parent) {
      path.push(this.cell.parent.para_index, getCellIndex(this.cell));
    }
    const end_para = this.end_para;
    path.push(
      end_para.para_index,
      end_para.characters.indexOf(this.end_sym_char)
    );
    return path;
  }

  /**
   * 文本域结束字符后面的位置
   */
  get end_para_path_outer(): Path {
    const outer_path = this.end_para_path;
    // 后移一位
    PathUtils.movePathCharNum(outer_path, 1);
    return outer_path;
  }

  /**
   * 文本域内容纯文本
   */
  get text(): string {
    let allValues: string = "";
    const allCharacters = this.getAllElements();
    for (let i = 1; i < allCharacters.length - 1; i++) {
      const char = allCharacters[i];
      if (!isCharacter(char) || char.field_position !== "normal") {
        continue;
      }
      allValues += char.value;
    }
    return allValues;
  }

  get rows(): Row[] {
    const returnRows: Row[] = [];
    const children = this.cell.children as Row[]; // 文本域内不可能有表格 只有可能是 row 数组
    let startRow;
    let startRowIndex = 0;
    for (let i = 0; i < children.length; i++) {
      const row = children[i];
      if (
        !startRow &&
        row.children.find(
          (character) =>
            character.field_position === "start" &&
            character.field_id === this.id
        )
      ) {
        // 找到了起始行就记录下来初始行和初始行所在的页码
        startRow = row;
        startRowIndex = i;
        returnRows.push(row);
      }

      if (startRow && i > startRowIndex) {
        returnRows.push(row);
      }

      // 要将末尾行的 return 放在最后 否则最后一行在下一页的第一行时会返回 0
      if (
        row.children.find(
          (character) =>
            character.field_position === "end" && character.field_id === this.id
        )
      ) {
        // 一旦找到了末尾行就可以 return 出去了应该是末尾行的 bottom 减去该页第一行的 top 值(如果首尾都在同一页这个算式也是对的) 再加上 上边若干页文本域的高度值
        return returnRows;
      }
    }

    return returnRows;
  }

  get height(): number {
    const rows = this.cell.children as Row[]; // 文本域内不可能有表格 只有可能是 row 数组
    let startRow;
    let rowPageNumber;
    let initTop: number = 0; // 初始的 top 值应该为 startRow.top 如果跨页的话就应该是内容距离页上边距的距离了(也就是该页第一行的 top 值)
    let preHeight = 0;
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];
      if (
        !startRow &&
        row.children.find(
          (character) =>
            character.field_position === "start" &&
            character.field_id === this.id
        )
      ) {
        // 找到了起始行就记录下来初始行和初始行所在的页码
        startRow = row;
        rowPageNumber = row.page_number;
        initTop = startRow.top;
      }

      if (startRow && rowPageNumber !== row.page_number) {
        // 页码一旦出现不一样就说明跨页了 就要重置页码和重置 top 值并且计算分页处的文本域高度
        rowPageNumber = row.page_number;
        // 如果已经找到了起始行了 并且首尾也没有在同一页上
        preHeight += rows[i - 1].bottom - initTop;
        initTop = row.top; // 要在计算 preHeight 之后修改
      }
      // 要将末尾行的 return 放在最后 否则最后一行在下一页的第一行时会返回 0
      if (
        row.children.find(
          (character) =>
            character.field_position === "end" && character.field_id === this.id
        )
      ) {
        // 一旦找到了末尾行就可以 return 出去了应该是末尾行的 bottom 减去该页第一行的 top 值(如果首尾都在同一页这个算式也是对的) 再加上 上边若干页文本域的高度值
        return row.bottom - initTop + preHeight;
      }
    }
    return 0;
  }

  get paragraphs() {
    const startParaPath = this.start_para_path;
    const endParaPath = this.end_para_path;
    const startParaIndex = startParaPath[startParaPath.length - 2];
    const endParaIndex = endParaPath[endParaPath.length - 2];
    const allParas = this.cell.paragraph.slice(
      startParaIndex,
      endParaIndex + 1
    );
    return allParas;
  }

  // 判断当前文本域是否可编辑
  get editable() {
    if (
      this.cell.editor.readonly ||
      this.type === FieldType.label ||
      this.cell.lock ||
      this.isReadonly
    ) {
      return false;
    }
    const para = this.start_para;
    let groupId = para.group_id;
    if (para.cell.parent) {
      groupId = para.cell.parent.group_id;
    }
    if (
      groupId &&
      this.cell.editor.selection.getGroupByGroupId(groupId)!.lock
    ) {
      return false;
    }
    return true;
  }

  get pageNumber() {
    const editor = this.cell.editor;
    const path = editor.modelPath2viewPath(editor.paraPath2ModelPath(this.start_para_path))
    return path[0];
  }

  getLeftTopXY() {
    let x = this.start_sym_char.left + this.rows[0].left;
    let y = this.rows[0].top;
    if (this.cell.parent) {
      x += this.cell.parent.left + this.cell.left;
      y += this.cell.parent.top + this.cell.top;
    }
    return {
      x,
      y
    }
  }

  deleteSentenceWithKeywords(keywords: any[]) {
    if (!keywords || keywords.length === 0) return;
    let elements = this.getAllElements();
    let paras: any = [[]];
    for (let i = 0; i < elements.length; i++) {
      const last = paras[paras.length - 1];
      if (elements[i].value !== "\n") {
        last.push(elements[i]);
      } else {
        paras.push([]);
      }
    }
    const editor = this.cell.editor;
    const originViewMode = editor.view_mode;
    editor.setAdminMode(true);
    let arr = findKeywordsInParagraphs(keywords, paras);
    arr.sort((a, b) => {
      if (a.paragraphIndex !== b.paragraphIndex) {
        return a.paragraphIndex - b.paragraphIndex;
      }
      return a.start - b.start;
    })

    arr = arr.filter((item, index, arr) => {
      return !arr.some((otherItem, otherIndex) => {
        return otherIndex !== index &&
          item.paragraphIndex === otherItem.paragraphIndex &&
          item.start >= otherItem.start &&
          item.end <= otherItem.end;
      });
    });

    const start = [...this.start_para_path_inner];
    let initParaIndex = start[start.length - 2];
    const symbolMap = new Map([
      [",", true],
      ["。", true],
      ["，", true],
      ["！", true],
      ["!", true],
    ])

    // TODO 这样的循环次数有点多 应该在循环段落的时候去 arr 里边找当前段落里边有没有关键字 先这样吧 后边再说
    for (let i = arr.length - 1; i >= 0; i--) {
      const currentKeywordPosition = arr[i];
      const startCharacterIndex = start[start.length - 1];
      F: for (let m = paras.length - 1; m >= 0; m--) {
        let currentParaElements = paras[m];
        const hasSymbol = currentKeywordPosition.keyword.includes(",") || currentKeywordPosition.keyword.includes("。") || currentKeywordPosition.keyword.includes("，");
        let symbolPosition = 0; // 逗号或者句号等符号的位置
        const currentParaIndex = initParaIndex + m;
        if (m !== currentKeywordPosition.paragraphIndex) continue;
        for (let j = 0; j < currentParaElements.length; j++) {
          if (symbolMap.has(currentParaElements[j].value)) {
            if (currentKeywordPosition.start >= symbolPosition && currentKeywordPosition.end <= j) {
              const startParaPath = [...start];
              const endParaPath = [...start];
              startParaPath[startParaPath.length - 2] = currentParaIndex;
              endParaPath[endParaPath.length - 2] = currentParaIndex;
              if (m === 0) {
                if (symbolPosition === 0) {
                  startParaPath[startParaPath.length - 1] = startCharacterIndex;
                  endParaPath[endParaPath.length - 1] = startCharacterIndex + j;
                } else {
                  startParaPath[startParaPath.length - 1] = startCharacterIndex + symbolPosition - 1;
                  endParaPath[endParaPath.length - 1] = startCharacterIndex + j - 1;
                }
              } else {
                startParaPath[startParaPath.length - 1] = symbolPosition;
                endParaPath[endParaPath.length - 1] = symbolPosition === 0 ? j + 1 : j;
              }

              editor.selection.setSelectionByPath(startParaPath, endParaPath)
              editor.delete_backward();

              let newElements = this.getAllElements();
              let newParas: any = [[]];
              for (let i = 0; i < newElements.length; i++) {
                const last = newParas[newParas.length - 1];
                if (newElements[i].value !== "\n") {
                  last.push(newElements[i]);
                } else {
                  newParas.push([]);
                }
              }

              paras = newParas;

              for (let n = i - 1; n >= 0; n--) {
                const c = arr[n];
                if (c && (c.start >= symbolPosition && c.end <= j) && c.paragraphIndex === m) {
                  i--
                }
              }
              break F;
            }
            if (hasSymbol) {
              if (j < currentKeywordPosition.start || j > currentKeywordPosition.end) {
                symbolPosition = j;
              }
            } else {
              symbolPosition = j;
            }
          } else if (j === currentParaElements.length - 1 && (currentKeywordPosition.start >= symbolPosition && currentKeywordPosition.end <= j)) {
            const startParaPath = [...start];
            const endParaPath = [...start];
            startParaPath[startParaPath.length - 2] = currentParaIndex;
            endParaPath[endParaPath.length - 2] = currentParaIndex;
            if (m === 0) {
              if (symbolPosition === 0) {
                startParaPath[startParaPath.length - 1] = startCharacterIndex;
              } else {
                startParaPath[startParaPath.length - 1] = startCharacterIndex + symbolPosition - 1;
              }
              endParaPath[endParaPath.length - 1] = startCharacterIndex + j;
            } else {
              startParaPath[startParaPath.length - 1] = symbolPosition;
              endParaPath[endParaPath.length - 1] = symbolPosition === 0 ? j + 1 : j - 1;
            }

            editor.selection.setSelectionByPath(startParaPath, endParaPath)
            editor.delete_backward();



            let newElements = this.getAllElements();
            let newParas: any = [[]];
            for (let i = 0; i < newElements.length; i++) {
              const last = newParas[newParas.length - 1];
              if (newElements[i].value !== "\n") {
                last.push(newElements[i]);
              } else {
                newParas.push([]);
              }
            }

            paras = newParas;

            for (let n = i - 1; n >= 0; n--) {
              const c = arr[n];
              if (c && (c.start >= symbolPosition && c.end <= j) && c.paragraphIndex === m) {
                i--
              }
            }
            break F;
          }
        }
      }
    }
    editor.setAdminMode(false);
    editor.view_mode = originViewMode;
  }


  /**
   * 设置用于替换的字符内容
   * @param text 需要替换的文本字符串
   */
  setNewText(text: string) {
    this.new_text = text;
  }

  /**
   * 复制文本域对象
   */
  copy(cell: Cell) {
    const field = this.simpleCopy(cell);
    field.generateMainProps();
    return field;
  }

  /**
   * 轻量复制，不生成关键属性
   * @param cell
   */
  simpleCopy(cell: Cell) {
    const field = new XField(this.id, this.style, cell, false);
    const propKeyArr: (keyof XField)[] = [
      "name",
      "type",
      "source_list",
      "placeholder",
      "tip",
      "start_symbol",
      "end_symbol",
      "display_type",
      "show_symbol",
      "deletable",
      "canBeCopied",
      "readonly",
      "show_field",
      "cascade_list",
      "automation_list",
      "show_format",
      "replace_format",
      "number_format",
      "source_id",
      "active_type",
      "multi_select",
      "formula",
      "formula_value",
      "separator",
      "inputMode",
      "ext_cell",
      "valid",
      "valid_content",
      "max_width",
      "min_width",
      "meta",
      "symbol_style",
    ];
    for (let i = 0; i < propKeyArr.length; i++) {
      // @ts-ignore
      field[propKeyArr[i]] = this[propKeyArr[i]];
    }
    return field;
  }

  /**
   * 生成必要的字符属性
   */
  generateMainProps() {
    this.symbol_style = serializeCopy(this.style);
    this.generatePlaceholderCharacters();
    this.generateStartSymbolCharacter();
    this.generateEndSymbolCharacter();
  }

  /**
   * 获取所有的父级对象数据
   * @param parents
   */
  getAllParents(parents: XField[] = []): XField[] {
    parents.push(this);
    if (this.parent) {
      return this.parent.getAllParents(parents);
    } else {
      return parents;
    }
  }

  judgeFieldNeedRecovery(parents: XField[] = []) {
    if (this.meta.before_type === "anchor") {
      parents.forEach((field) => {
        field.meta.before_type = "anchor";
      });
    }
    if (this.meta.before_type !== "anchor") {
      this.parent?.judgeFieldNeedRecovery([...parents, this]);
    }
  }
  /**
   * 生成背景文本
   */
  generatePlaceholderCharacters() {
    if (this.type === "anchor") {
      this.placeholder_characters = [];
      return;
    }
    const placeholder_characters: any[] = [];
    const editor = this.cell.editor;
    this.symbol_style!.color = editor.config.placeholderColor ?? "gray";
    const placeholder_font = editor.fontMap.add(this.symbol_style!);
    // 文本域展示无边框模式的时候必须要有背景文本，否则内容删除干净后很难将光标定位到文本域中
    if (editor.config.fieldShowMode !== FieldShowMode.normal) {
      const hfPart = this.cell.parent?.parent.hf_part || this.cell.hf_part;
      // 页眉页脚不进行处理
      if (
        !this.placeholder &&
        hfPart !== "header" &&
        hfPart !== "footer" &&
        this.type !== "box" &&
        this.type !== "label"
      ) {
        this.placeholder = "空";
      }
    }
    for (let i = 0; i < this.placeholder.length; i++) {
      const char = this.placeholder[i];
      const placeholder_char = new Character(placeholder_font, char);
      placeholder_char.field_id = this.id;
      placeholder_char.field_position = "placeholder";
      placeholder_characters.push(placeholder_char);
    }
    this.placeholder_characters = placeholder_characters;
  }

  /**
   * 生成开始字符
   */
  generateStartSymbolCharacter() {
    this.generateSymbolCharacter("start");
  }

  /**
   * 生成结束字符
   */
  generateEndSymbolCharacter() {
    this.generateSymbolCharacter("end");
  }

  /**
   * 生成开始结束字符
   * @param type
   */
  generateSymbolCharacter(type: "start" | "end") {
    const symbol_name = `${type}_symbol` as const;
    let char_text: string = this[symbol_name];
    if (this.type === "anchor") {
      char_text = "";
    }
    this.symbol_style!.color = this.field_symbol_color;
    // this.symbol_style!.underline = false;
    const sym_char_font = this.cell.editor.fontMap.add(this.symbol_style!);
    let sym_char: Character;
    if (!this.cell.editor.config.show_field_symbol || !char_text) {
      if (
        !this.cell.editor.document_meta.fieldSymbolWidth &&
        this.cell.editor.print_mode
      ) {
        if (!char_text) {
          char_text = "";
        }
        sym_char = new Character(sym_char_font, char_text, 0.001);
      } else {
        sym_char = new Character(sym_char_font, "", 1);
      }
    } else {
      sym_char = new Character(sym_char_font, char_text);
    }
    sym_char.field_id = this.id;
    sym_char.field_position = type;
    const sym_char_name = `${type}_sym_char` as const;
    this[sym_char_name] = sym_char;
  }

  /**
   * 清除段落中的placeholder字符
   */
  removeParaPlaceholder(start_para: Paragraph = this.start_para): any {
    for (let i = 0; i < this.placeholder_characters.length; i++) {
      const placeholder_char = this.placeholder_characters[i];
      start_para.removeElement(placeholder_char);
    }
    start_para.updateChildren();
    // 以下代码不可删除，否则会纯在复制携带换行符内容到空文本域时无法清空其背景文本的问题
    if (start_para !== this.end_para) {
      const next_para = start_para.nextParagraph;
      if (isParagraph(next_para)) {
        return this.removeParaPlaceholder(next_para);
      }
    }
  }

  /**
   * 如果文本域内有嵌套的情况，可以获取该文本域内所有的文本域对象
   * @returns 扁平化的文本域结构
   */
  getFieldInChildren(allField: XField[] = []): (XField | BoxField)[] {
    allField.push(this);
    for (let i = 0; i < this.children.length; i++) {
      const element = this.children[i];
      if (isField(element)) {
        element.getFieldInChildren(allField);
      }
    }
    return allField;
  }

  /**
   * 获取当前输入域中包含的所有字符，包含边框字符及背景文本字符
   */
  getAllElements(): (Character | Image | Widget | Line | Box | Button | Fraction)[] {
    //
    const return_characters: (
      | Character
      | Image
      | Widget
      | Line
      | Box
      | Button
      | Fraction
    )[] = [];
    return_characters.push(this.start_sym_char);
    for (let i = 0; i < this.children.length; i++) {
      const element = this.children[i];
      if (isField(element)) {
        const child_field_char = element.getAllElements();
        return_characters.push(...child_field_char);
      } else {
        return_characters.push(element);
      }
    }
    if (this.children.length === 0) {
      return_characters.push(...this.placeholder_characters);
    }

    return_characters.push(this.end_sym_char);
    return return_characters;
  }

  // 因为文本域边框可能也是有样式的，所以还是要获取整个文本域的 rawData ，不能只获取文本域里边的内容，只提花掉内容不行
  // 有必要进行数据转换，否则还要写文本域的克隆，获取 rawData 可能更通用
  getRawData() {
    const editor = this.cell.editor;
    const startParaPath = this.start_para_path;
    const endParaPath = this.end_para_path_outer;

    const indexOfStartPara = startParaPath[startParaPath.length - 2];
    const indexOfEndPara = endParaPath[endParaPath.length - 2];
    const indexOfStartParaCharacter = startParaPath[startParaPath.length - 1];
    const indexOfEndParaCharacter = endParaPath[endParaPath.length - 1];

    // TODO 跟 Helper.ts 文件中的 handleSaveData 方法有重复代码，逻辑相似 可以考虑优化(不要加各种判断，不好理解，也不好维护) 能不能拆分成更小的函数等其他优化手段
    const saveData = [];
    const paragraphs = this.cell.paragraph;

    for (let i = indexOfStartPara, end = indexOfEndPara; i <= end; i++) {
      const paragraph = paragraphs[i].copy(this.cell) as Paragraph; // 循环 当前段落或者表格
      if (i === indexOfStartPara) {
        const characters = paragraph.characters;
        if (
          indexOfStartParaCharacter === 0 &&
          (indexOfEndPara > indexOfStartPara ||
            indexOfEndParaCharacter >= characters.length - 1)
        ) {
          // 因为有个"\n" 所以减一
          // 开头是完整的段落
          saveData.push(paragraph);
        } else {
          // 否则开头不是完整的段落 要区分 开始和结尾路径是否都在该段落 因为截取参数不一样
          const para = new Paragraph(uuid("para"), this.cell, null);
          if (indexOfEndPara === indexOfStartPara) {
            para.characters = characters.slice(
              indexOfStartParaCharacter,
              indexOfEndParaCharacter
            );
          } else {
            para.characters = characters.slice(indexOfStartParaCharacter);
          }
          saveData.push(para);
        }
      } else if (i === indexOfEndPara) {
        const characters = paragraph.characters;
        if (indexOfEndParaCharacter !== 0) {
          // 如果indexOfEndParaCharacter === 0说明结尾段落啥也没选中，就什么也不用操作，所以只操作不等于0的情况
          if (indexOfEndParaCharacter === characters.length - 1) {
            saveData.push(paragraph);
          } else {
            const para = new Paragraph(uuid("para"), this.cell, null);
            para.characters = characters.slice(0, indexOfEndParaCharacter);
            saveData.push(para);
          }
        } else {
          // 说明末尾段落只有一个换行符
          const para = new Paragraph(uuid("para"), this.cell, null);
          const character = new Character(
            editor.fontMap.add(editor.config.default_font_style),
            "\n"
          );
          para.characters = [character];
          saveData.push(para);
        }
      } else {
        saveData.push(paragraph);
      }
    }

    const newRootCell = initCell(editor, "trans");
    const newHeaderCell = initCell(editor, "header_trans");
    const newFooterCell = initCell(editor, "footer_trans");
    newRootCell.paragraph = saveData;
    // return rawDataTrans.modelDataToRawData(
    //   newHeaderCell,
    //   newRootCell,
    //   newFooterCell
    // );
    return editor.event.emit(
      "modelData2RawData",
      newHeaderCell,
      newRootCell,
      newFooterCell
    );
  }

  // 获取描述
  getDescription() {
    return {
      id: this.id,
      name: this.name,
      type: this.type,
      children: this.getContentDescription()
    }
  }

  // 获取内容描述
  // 策略是 写完一个类型 测一个类型 先从 character 开始 目前只管值 不管样式
  getContentDescription() {
    const description: any[] = [];
    for (let i = 0; i < this.children.length; i++) {
      const element = this.children[i];
      let item: {
        type?: string;
        value?: string;
        width?: number;
        height?: number;
        selected?: boolean;
        border?: string;
        meta?: any;
        start_symbol?: string;
        end_symbol?: string;
        children?: any;
      } = {};
      if (isCharacter(element)) {
        const last = description[description.length - 1];
        if (!last || last.type !== "character") {
          item.type = "character";
          item.value = element.value;
          description.push(item);
        } else {
          last.value += element.value;
        }
      } else if (isImage(element)) {
        if (element.meta?.formula_type !== undefined) {
          item.type = "medicalFormula"; // 医学表达式： 月经史公式那些
          item.width = element.width;
          item.height = element.height;
          item.meta = JSON.parse(JSON.stringify(element.meta));
        } else {
          item.type = "image";
          item.value = element.src;
          item.width = element.width;
          item.height = element.height;
        }
        description.push(item);
      } else if (isWidget(element)) {
        item.type = element.widgetType;
        item.selected = element.selected;
        item.border = element.border;
        description.push(item);
      } else if (isField(element)) {
        item.type = "field";
        item.end_symbol = element.end_symbol;
        item.start_symbol = element.start_symbol;
        item.children = element.getContentDescription();
        description.push(item);
      }
    }

    return description;
  }

  // 根据描述恢复文本域
  recoveryByDescription(description: any[]) {
    // clear 和 reShowPlaceholder 得同时用，如果只调用了 clear 然后就插入文本域 文本域就会缺少后边的边框
    this.clear();
    this.reShowPlaceholder();
    const editor = this.cell.editor;
    editor.locatePathInField(this);
    const font = editor.fontMap.add(editor.config.default_font_style);
    for (let i = 0; i < description.length; i++) {
      const ele = description[i];
      if (ele.type === "character") {
        if (ele.value === "\n") continue; // TODO 暂时先不要换行符 因为 replaceFieldsElements 处理不了 还会报错
        // TODO 暂时全部调用 insertText 这个方法 因为 updateFieldText 里边也是这么用的 而且也要确保换行符只能通过 insertText 这一个方法修改
        // 但是这样太啰嗦 走的逻辑多 有些没必要 后期再说吧
        editor.contextState.setFontState(font);
        EditorHelper.insertText(editor, ele.value);
      } else if (ele.type === "image") {
        const editorImage = new Image(
          ele.value,
          ele.width,
          ele.height,
          font,
          ele.meta
        );
        // @ts-ignore
        editor.replaceFieldsElements([this], [...this.children, editorImage]);
      } else if (ele.type === "medicalFormula") {
        const image = medicalFormula.getMedicalFormulaImage(ele.meta);
        const editorImage = new Image(
          image.src,
          ele.width,
          ele.height,
          font,
          ele.meta
        );
        // @ts-ignore
        editor.replaceFieldsElements([this], [...this.children, editorImage]);
      } else if (ele.type === "radio" || ele.type === "checkbox") {
        const widget = Widget.insertSingle(editor, ele.type, ele.border);
        widget && (widget.selected = ele.selected);
      } else if (ele.type === "field") {
        const field = XField.insert(editor, {
          start_symbol: ele.start_symbol,
          end_symbol: ele.end_symbol,
        }) as XField;
        field.recoveryByDescription(ele.children);
        editor.locatePathOutField(field, "end");
      }
    }
  }

  replaceWith(...rawDataArr: any[]) {
    const editor = this.cell.editor;
    const originViewMode = editor.view_mode;
    const oriAdminMode = editor.adminMode;
    // 当调用到该方法时，加入文本域设置了不可删除，就要先设置管理员模式，然后下方删除接口才能正常将文本域删掉。
    editor.setAdminMode(true);
    // 不用 setViewMode() 因为里边会调用 refreshDocument(true) 改变了 页眉页脚的编辑状态
    // 更重要的是避免消耗性能因为里边就只根据了该属性值做判断 不需要刷新页面
    // 写在开头是因为 end_para_path_outer 的取值跟是否表单模式有关系，如果别的团队在替换之前设置成了表单模式，取值就取不到文本域外边去了
    editor.view_mode = ViewMode.NORMAL;

    const hf_part = this.cell.getLocation();
    if (hf_part !== "root") {
      editor.enterEditHeaderAndFooterMode(hf_part);
    }
    const startParaPath = this.start_para_path;
    const endParaPath = this.end_para_path_outer;
    editor.selection.setSelectionByPath(
      startParaPath,
      endParaPath,
      "para_path"
    );
    EditorHelper.deleteBackward(editor);
    for (const rawData of rawDataArr) {
      EditorHelper.insertTemplateData(editor, rawData, false, false, true);
    }
    if (hf_part !== "root") {
      editor.quitEditHeaderAndFooterMode();
    }
    editor.setAdminMode(oriAdminMode);
    // 这儿不能只改属性了 quitEditHeaderAndFooterMode 方法里边有重新刷新渲染的逻辑 只改属性赋值 页面显示文本域边框等可能会有问题
    editor.setViewMode(originViewMode);
  }

  /**
   * 获取最外层文本域
   */
  getOutermostParent(): XField {
    const parent = this.parent;
    if (!parent) {
      return this;
    } else {
      return parent.getOutermostParent();
    }
  }

  /**
   * 上层封装后右键菜单属性内容
   * @param parameter 传入的需要修改的属性 该方法只是属性赋值
   */
  reviseAttr(parameter: Partial<XField>) {
    const modifiableAttributes = [
      "id",
      "name",
      "tip",
      "placeholder",
      "type",
      "start_symbol",
      "end_symbol",
      "display_type",
      "active_type",
      "multi_select",
      "formula",
      "formula_value",
      "separator",
      "inputMode",
      "show_symbol",
      "readonly",
      "deletable",
      "canBeCopied",
      "show_format",
      "show_field",
      "cascade_list",
      "automation_list",
      "replace_format",
      "number_format",
      "source_id",
      "meta",
      "valid",
      "valid_content",
      "source_list",
      "max_width",
      "min_width",
      "align",
      "maxHeight",
      "defaultValue",
    ];
    const toBeModifiedAttributes = Object.keys(parameter);
    for (let i = 0; i < toBeModifiedAttributes.length; i++) {
      const attribute = toBeModifiedAttributes[i];
      if (!(modifiableAttributes as any).includes(attribute)) {
        continue;
      }
      if (attribute === "placeholder") {
        this.placeholder =
          parameter.placeholder || parameter.placeholder === ""
            ? parameter.placeholder.toString()
            : this.placeholder;
      } else {
        // 文本域id不可替换为空
        if (attribute === "id" && parameter[attribute]) {
          // @ts-ignore
          this[attribute] = parameter[attribute];
          this.updateCharactersFieldId();
        } else if (attribute !== "id") {
          // 加判断避免 id 值为假的时候也赋值
          // @ts-ignore
          this[attribute] = parameter[attribute] ?? this[attribute];
        }
      }
    }
  }

  // 该方法跟 reviseAttr 的区别是 不仅仅只是属性赋值了
  updateAttr(parameter: Partial<XField> & {label_text?: string}) {
    if (this.isRemove) return false
    parameter = { ...this, ...parameter, fields: undefined };
    if (this.readonly && (parameter.readonly || parameter.readonly === undefined)) return false

    let currentField = this;
    const editor = this.cell.editor;
    if (parameter.replaceRule) {
      currentField.replaceRule = parameter.replaceRule;
    }
    // 修改文本域的属性
    // 判断文本域是不是锚点，如果是更新ext_cell
    if (parameter.type === "anchor") {
      XField.setAnchorExtCell(editor, currentField);
    } else {
      //还原锚点内容
      if (currentField.meta.before_type === "anchor") {
        const field = currentField.ext_cell?.fields[0];
        const rawData = field?.getRawData();
        if (rawData) {
          editor.internal.imageSrcObj = rawData.imageSrcObj || {}
          currentField.replaceWith(rawData);
          // @ts-ignore
          currentField = editor.getFieldById(field.id);
          currentField.ext_cell = null
        }
      }
    }
    // 验证当前传入参数是否需要修改文本域id,如果需要修改则判断是否重复，重复的话则不进行修改, 传入cell说明传入的为一个文本域对象，不操作其id
    if (parameter.id && !parameter.cell) {
      if (editor.judgeFieldIdExist(parameter.id)) {
        delete parameter.id;
      }
    }
    const style = currentField.style;
    if (parameter.type === "anchor") {
      // 创建新的样式对象
      const newStyle = { ...style, height: 0 };
      // 生成新的ID
      newStyle.id = uuid("font");
      // 将新样式及其ID添加到editor的fontMap中
      editor.fontMap.add(newStyle, newStyle.id);
      // 更新parameter中的style字段
      currentField.style = newStyle;
    }
    // 修改文本域提示文本
    if (parameter.placeholder || parameter.placeholder === "") {
      if (!currentField.children.length) {
        // 该文本域中没有内容，修改placeholder的时候需要将其在段落中的character修改
        currentField.removeParaPlaceholder();
        // 修改对应文本域中的内容
        currentField.reviseAttr(parameter);
        currentField.generatePlaceholderCharacters();
        // 该文本域中没有内容，修改placeholder的时候需要将其在段落中的character修改
        currentField.reShowPlaceholder();
      } else {
        // 修改对应文本域中的内容
        currentField.reviseAttr(parameter);
        currentField.generatePlaceholderCharacters();
      }
    } else {
      currentField.reviseAttr(parameter);
    }
    // 如果当前类型为label类型
    if (
      ((parameter.type && parameter.type === "label") ||
        (!parameter.type && currentField.type === "label")) &&
      parameter.label_text
    ) {
      currentField.replaceText(parameter.label_text);
    }
    if (
      parameter.start_symbol !== undefined ||
      parameter.end_symbol !== undefined
    ) {
      // 除了空文本域时修改提示信息的情况外 其余属性设置
      currentField.updateSymbol();
    }
    currentField.meta.before_type = currentField.type;
  }

  /**
   * 更新所有相关字符的id
   */
  updateCharactersFieldId() {
    this.children.forEach((char) => {
      if (!isField(char)) {
        char.field_id = this.id;
      }
    });
    this.start_sym_char.field_id = this.id;
    this.end_sym_char.field_id = this.id;
    this.placeholder_characters.forEach((char) => {
      char.field_id = this.id;
    });
  }

  /**
   * 内容清空后重新显示placeholder
   */
  reShowPlaceholder() {
    if (!this.placeholder.length) {
      return;
    }
    const start_para = this.start_para;
    // 以下清空背景文本代码必须有，因为选区部分文本域背景文本时进行删除时调用了基础删除逻辑，删除后补全会出问题。
    this.removeParaPlaceholder(start_para);
    const start_index = start_para.characters.indexOf(this.start_sym_char);
    start_para.characters.splice(
      start_index + 1,
      0,
      ...this.placeholder_characters!
    );
    start_para.updateChildren();
  }

  /**
   * 更新开始字符与结束字符
   */
  updateSymbol() {
    const start_para = this.start_para;
    const end_para = this.end_para;
    const start_index = start_para.characters.indexOf(this.start_sym_char);
    const end_index = end_para.characters.indexOf(this.end_sym_char);
    this.generateStartSymbolCharacter();
    this.generateEndSymbolCharacter();
    start_para.characters.splice(start_index, 1, this.start_sym_char);
    start_para.updateChildren();
    end_para.characters.splice(end_index, 1, this.end_sym_char);
    end_para.updateChildren();
  }

  /**
   * 清空对应段落的字符及文本域
   */
  clear() {
    // 获取到其中的字文本域在cell中删除
    const allFields = this.getFieldInChildren();
    allFields.shift();
    for (let i = 0; i < allFields.length; i++) {
      const field = allFields[i];
      if (field.isRemove) {
        continue;
      }
      const index = this.cell.fields.indexOf(field);
      if (index > -1) {
        this.cell.fields.splice(index, 1);
      }
    }
    const start_para_path = [...this.start_para_path];
    const end_para_path = [...this.end_para_path];
    const start_para = this.start_para;
    PathUtils.movePathCharNum(start_para_path, 1);
    this.children = []; // 必须进行清空，否则会造成与段落字符不一致
    start_para.cell.handleDeleteParagraphs(start_para_path, end_para_path);
  }

  /**
   * 替换文本，包含段落中的字符
   * @param replace_text
   */
  replaceText(replace_text: string) {
    if (this.judgeParentCellIsLock()) return;
    replace_text = replaceLineBreakWith(replace_text);
    this.clear();
    this.children = this.textToFieldCharacter(replace_text);
    this.updateChildren();
  }
  /**
   * 拼接到当前文本域后面
   * @param value 拼接的字符
   */
  appendText(value: string) {
    if (this.judgeParentCellIsLock()) return;
    if (this.replaceRule && this.replaceRule.length) {
      this.replaceRule.forEach((regOb: any) => {
        value = value.replace(regOb.rule, regOb.replace);
      });
    }
    value = replaceLineBreakWith(value);
    const ori_children = [...this.children];
    ori_children.push(...this.textToFieldCharacter(value));
    this.children = []; // 这么做的就是不让cell的field删除掉，因为这是追加内容，不应该删除
    this.clear();
    this.children = ori_children;
    this.updateChildren();
  }

  judgeParentCellIsLock() {
    const cell = this.cell;
    if (cell) {
      return cell.lock;
    }
  }

  setCharacterSize(
    type: "bigger" | "smaller" | "base",
    baseFontHeight?: number,
    maxFontHeight?: number,
    isIncludesBorder: boolean = false
  ) {
    // 如果是个空文本域
    // 因为 placeholder 用的样式跟 field.style 用的是同一个
    // 所以空文本域调用该方法，包不包含边框都会设置成整个文本域的样式
    if (this.children.length === 0 && !isIncludesBorder) {
      type = "base";
      baseFontHeight = this.style.height;
    }

    // 使用 this.getAllElements() 改变 character 尺寸有问题 所以改成调用段落的 setCharacterSize
    const cell = this.cell;
    const editor = cell.editor;

    const originParaAnchor = [...editor.selection.para_anchor];
    const originParaFocus = [...editor.selection.para_focus];

    const startParaPath = isIncludesBorder
      ? this.start_para_path
      : this.start_para_path_inner;
    const endParaPath = this.end_para_path; // TODO 结束位置不用区分是否包含边框

    const startParaIndex = startParaPath[startParaPath.length - 2];
    const endParaIndex = endParaPath[endParaPath.length - 2];

    for (let i = startParaIndex; i <= endParaIndex; i++) {
      // TODO 计算 start 和 end 是否有必要抽离出一个公共方法 因为其他地方也有类似逻辑
      const paragraph = cell.paragraph[i] as Paragraph; // 文本域里边没有表格 只能是段落
      // 1. 当前段落就包含首尾位置
      let start = startParaPath[startParaPath.length - 1];
      let end = endParaPath[endParaPath.length - 1];

      if (i === startParaIndex && i !== endParaIndex) {
        // 2. 当前段落只包含起始位置
        end = paragraph.characters.length;
      } else if (i !== startParaIndex && i === endParaIndex) {
        // 3. 当前段落只包含结束位置
        start = 0;
      } else if (i !== startParaIndex && i !== endParaIndex) {
        // 4. 当前段落不包含首尾两个位置
        start = 0;
        end = paragraph.characters.length;
      }
      maxFontHeight = maxFontHeight || this.style.height;
      type === "base" && (baseFontHeight = baseFontHeight || this.style.height);
      paragraph.setCharacterSize(
        start,
        end,
        type,
        baseFontHeight,
        maxFontHeight
      );
    }
    editor.internal.tempFields.clear();

    // 分别记录，分别赋值，避免选区粘贴的时候不删除，不使用 resetSelectionFocus 这个装饰器，因为分页的时候会报错
    editor.selection.anchor = [...editor.paraPath2ModelPath(originParaAnchor)];
    editor.selection.focus = [...editor.paraPath2ModelPath(originParaFocus)];
    return true;
  }

  /**
   * text字符串转换成字符对象
   * @param value 文本字符串
   * @returns 生成的当前文本域字符对象
   */
  textToFieldCharacter(
    value: string
  ): (Character | Image | Widget | Line | Button)[] {
    const new_children: (Character | Image | Widget | Line)[] = [];
    value = String(value).replace(/\\n/g, "\n");

    const chars = specialCharHandle.splitString(value);
    const font = this.cell.editor.fontMap.add(this.style);
    for (let i = 0; i < chars.length; i++) {
      const char = chars[i];
      const new_character = new Character(font, char);
      new_character.field_id = this.id;
      new_children.push(new_character);
    }
    return new_children;
  }

  /**
   * 查找字符在文本域中的下标
   * @param character 需要查找的目标字符
   * @returns 查找结果在文本域中的下标
   */
  getCharIndexInField(character: Character): number {
    if (
      character.field_position === "start" &&
      this.id === character.field_id
    ) {
      return 0;
    } else if (
      character.field_position === "end" &&
      this.id === character.field_id
    ) {
      return this.children.length || this.children.length + 1;
    } else {
      for (let i = 0; i < this.children.length; i++) {
        const element = this.children[i];
        if (isField(element) && character.field_id === element.id) {
          return i + 1;
        }
      }
      return this.children.indexOf(character) + 1;
    }
  }

  /**
   * 更新文本域children
   * @param element 插入的元素
   * @param pre_element 插入位置的前一个元素 用来确定位置
   */
  updateChildren() {
    const start_path = this.start_para_path;
    const start_para = this.start_para;
    start_para.refreshFieldCharacters(
      this,
      start_path[start_path.length - 1] + 1
    );
  }

  /**
   * 删除指定元素
   * @param element
   */
  removeElement(element: ElementInParagraph): boolean {
    const index = this.children.indexOf(element);
    if (index > -1) {
      this.children.splice(index, 1);
      return true;
    } else {
      return false;
    }
  }

  /**
   * 将自身从cell fields中移除  非对外接口
   * @param selection
   */
  remove(selection?: XSelection): Path | undefined | boolean {
    if (this.isRemove) {
      return true;
    }
    // 因为如果删除的是一个父文本域，则其所有的子文本域也应该从cell上进行移除
    const allChildFields = this.getFieldInChildren();
    for (let i = 0; i < allChildFields.length; i++) {
      const field = allChildFields[i];
      this.cell.fields.splice(this.cell.fields.indexOf(field), 1);
    }
    if (this.parent) {
      this.parent.children.splice(this.parent.children.indexOf(this), 1);
      if (!this.parent.children.length && selection) {
        // 如果父级无子元素则展示背景文本
        this.parent.reShowPlaceholder();
      }
    }
    if (selection) {
      const end_para_path = [...this.end_para_path];
      const start_para_path = [...this.start_para_path];
      PathUtils.movePathCharNum(end_para_path, 1);
      return selection.editor.deleteContentByPath(
        start_para_path,
        end_para_path
      );
    }
  }

  /**
   * 根据传入的连续字符判断当前文本域是否是个完整的文本域
   */
  isCompleteField(
    chars: (Character | Image | Widget | Line | Button)[]
  ): boolean {
    return this.hasEndSymInChars(chars) && this.hasStartSymInChars(chars);
  }

  /**
   * 根据传入的连续字符判断字符是否为当前文本域的children字符或者其子文本域中的字符
   * @param chars
   */
  isChildrenChars(chars: (Character | Image | Widget | Line | Button)[]) {
    if (!chars.length) {
      return false;
    }
    const all_chars = this.getAllElements();
    all_chars.pop();
    all_chars.shift();
    for (let i = 0; i < chars.length; i++) {
      if (!(all_chars as any).includes(chars[i])) {
        // 判断是否为其子文本域字符
        return false;
      }
    }
    return true;
  }

  /**
   * 传入的字符中包含当前文本域的结束字符
   * @param chars
   */
  hasEndSymInChars(
    chars: (Character | Image | Widget | Line | Button)[]
  ): boolean {
    return (chars as any).includes(this.end_sym_char);
  }

  /**
   * 传入的字符中包含当前文本域的开始字符
   * @param chars
   */
  hasStartSymInChars(
    chars: (Character | Image | Widget | Line | Button)[]
  ): boolean {
    return (chars as any).includes(this.start_sym_char);
  }

  /**
   * 设置文本域校验状态开关
   * @param isValid
   */
  setValid(isValid: boolean) {
    this.valid = isValid ? 1 : 0;
    if (isValid) {
      this.validValue();
    } else {
      this.clearValidStyle();
    }
  }

  /**
   * 清除文本域校验样式
   */
  clearValidStyle() {
    const all_character = this.getAllElements();
    // 恢复原来的样式
    for (let i = 0; i < all_character.length; i++) {
      const element = all_character[i];
      if (
        element &&
        isCharacter(element) &&
        element.field_position !== "start" &&
        element.field_position !== "end"
      ) {
        if (this.children.length) {
          const oriFont = this.cell.editor.fontMap.add(
            Object.assign({}, element.font, { temp_valid_color: "" })
          );
          element.font = oriFont;
        }
      }
    }
  }

  /**
   * 校验方法
   */
  validValue() {

    if (isBoxField(this) && this.required && !this.parent) {
      const characters = [];
      let hasChecked = false;
      for (const b of this.children) {
        if (isBoxField(b)) {
          if (b.box_checked) {
            hasChecked = true;
          }
          for (const c of b.children) {
            if (isCharacter(c) || isWidget(c)) {
              characters.push(c);
            }
          }
        }
      }
      for (const c of characters) {
        const warn_font = this.cell.editor.fontMap.add(
          Object.assign({}, c.font, { temp_valid_color: hasChecked ? undefined : "red" })
        );
        c.font = warn_font;
      }
      return false;
    }
    
    if (!this.valid) return true;
    const valid_result = this.valitdation();
    if (!valid_result) {
      const all_character = this.getAllElements();
      // 第一个元素不是字符 则返回结果就行
      if (!isCharacter(all_character[0])) return;
      for (let i = 0; i < all_character.length; i++) {
        const element = all_character[i];
        if (
          element &&
          isCharacter(element) &&
          element.field_position !== "start" &&
          element.field_position !== "end"
        ) {
          // 不通过校验
          const warn_font = this.cell.editor.fontMap.add(
            Object.assign({}, element.font, { temp_valid_color: "red" })
          );
          element.font = warn_font;
        }
      }
      // 验证失败
      return false;
    } else {
      this.clearValidStyle();
      // 验证通过
      return true;
    }
  }

  /**
   * 更改文本域、背景文本、边框字符样式
   */
  changeFontStyle(style: Partial<FontStyle>) {
    const sym_char_color = this.start_sym_char.font.color;
    const symbol_font = new Font(
      Object.assign({}, this.start_sym_char.font, style)
    );
    const start_res = Renderer.measure(
      symbol_font,
      this.start_symbol,
      this.cell.editor
    );
    this.start_sym_char.width = start_res.width;
    this.start_sym_char.ori_width = start_res.width;
    this.start_sym_char.font = symbol_font;
    this.symbol_style = symbol_font;
    this.start_sym_char.font.color = sym_char_color;
    const end_res = Renderer.measure(
      symbol_font,
      this.end_symbol,
      this.cell.editor
    );
    //end_res.width为0的时候设置最小值1
    this.end_sym_char.width = end_res.width ? end_res.width : 1;
    this.end_sym_char.ori_width = end_res.width ? end_res.width : 1;
    this.end_sym_char.font = symbol_font;
    this.end_sym_char.font.color = sym_char_color;
    const field_style = new Font(Object.assign({}, this.style, style));
    this.style = field_style;
    this.placeholder_characters.forEach((item: Character) => {
      const placeholder_font = new Font(Object.assign({}, item.font, style));
      const { width } = Renderer.measure(
        placeholder_font,
        item.value,
        this.cell.editor
      );
      item.width = width;
      item.ori_width = width;
      item.font = placeholder_font;
      item.font.color = this.cell.editor.config.placeholderColor ?? "gray";
    });
  }

  /**
   * 文本域校验
   */
  valitdation() {
    const value = this.text;

    const editor = this.cell.editor;
    const arrowField = editor.getFieldById(this.id + 'arrow');

    const handleArrowAbout = (arrowDirection: string) => {
      editor.adminMode = true;
      const originAnchor = [...this.cell.editor.selection.anchor];
      if (!arrowField) {
        editor.insertFieldAfterField({ positionField: this, insertField: { id: this.id + "arrow", value: arrowDirection, readonly: 1, canBeCopied: 0, style: { color: "red" }, start_symbol: "", end_symbol: ""  } });
      } else {
        XField.updateText(editor, { fields: [arrowField], value: arrowDirection })
      }
      editor.selection.setCursorPosition(originAnchor);
      editor.refreshDocument();
      editor.adminMode = false;
    }

    const validParameter: any = {};
    validParameter.value = value;
    let result = false;
    this.valid_tip = ""; // 清空返回信息
    switch (this.valid_content.type) {
      case "date":
        result = valids.date_valid(validParameter);
        if (!result) {
          this.valid_tip = "日期格式不正确！";
        }
        break;
      case "number":
        validParameter.min_num =
          this.valid_content.rule.min_num !== null &&
            this.valid_content.rule.min_num !== undefined
            ? this.valid_content.rule.min_num * 1
            : undefined;
        validParameter.max_num =
          this.valid_content.rule.max_num !== null &&
            this.valid_content.rule.max_num !== undefined
            ? this.valid_content.rule.max_num * 1
            : undefined;
        result = valids.number_valid(validParameter);
        if (!this.valid_content.rule.outRangeShowSymbol && arrowField) {
          editor.adminRemoveFieldsKeepCursor([arrowField]);
        }
        if (!result) {
          if (
            (validParameter.min_num === null ||
              validParameter.min_num === undefined) &&
            validParameter.max_num !== null &&
            validParameter.max_num !== undefined
          ) {
            // 最小值没有 并且有最大值的时候
            this.valid_tip = `请输入小于${validParameter.max_num}的数！`;
            if (isNumber(value) && this.valid_content.rule.outRangeShowSymbol) {

              let arrowDirection = "↑"
              handleArrowAbout(arrowDirection);
            }
          } else if (
            (validParameter.max_num === null ||
              validParameter.max_num === undefined) &&
            validParameter.min_num !== null &&
            validParameter.min_num !== undefined
          ) {
            // 最大值没有 并且有最小值的时候
            this.valid_tip = `请输入大于${validParameter.min_num}的数！`;
            if (isNumber(value) && this.valid_content.rule.outRangeShowSymbol) {
              let arrowDirection = "↓"
              handleArrowAbout(arrowDirection);
            }
          } else if (
            validParameter.min_num !== null &&
            validParameter.min_num !== undefined &&
            validParameter.max_num !== null &&
            validParameter.max_num !== undefined
          ) {
            // 既有最小值 又有最大值的时候
            this.valid_tip = `请输入${validParameter.min_num}到${validParameter.max_num}之间的数！`;
            if (isNumber(value) && this.valid_content.rule.outRangeShowSymbol) {
              let arrowDirection = "↓"
              if (value > validParameter.max_num) {
                arrowDirection = "↑"
              }
              handleArrowAbout(arrowDirection);
            }
          }
        } else {
          if (arrowField) {
            editor.adminRemoveFieldsKeepCursor([arrowField]);
          }
        }
        break;
      case "string":
        validParameter.max_length = this.valid_content.rule.max_length;
        validParameter.min_length = this.valid_content.rule.min_length;
        result = valids.string_valid(validParameter);
        if (!result) {
          if (!validParameter.min_length && validParameter.max_length) {
            this.valid_tip = `最多输入${validParameter.max_length}个字符！`;
          } else if (validParameter.min_length && !validParameter.max_length) {
            this.valid_tip = `最少输入${validParameter.min_length}个字符！`;
          } else if (validParameter.min_length && validParameter.max_length) {
            this.valid_tip = `请输入${validParameter.min_length}到${validParameter.max_length}个字符！`;
          }
        }
        break;
      case "idCard":
        result = valids.idcard_valid(validParameter);
        if (!result) {
          this.valid_tip = `请输入有效身份证！`;
        }
        break;
      case "phone":
        validParameter.type = this.valid_content.phone_type; // 这里可以是手机或者固话
        result = valids.phone_valid(validParameter);
        if (!result) {
          this.valid_tip = `输入电话号码格式不正确！`;
        }
        break;
      case "regex":
        validParameter.regex = this.valid_content.regex;
        result = valids.regex_valid(validParameter);
        if (!result) {
          this.valid_tip = `校验失败！`;
        }
        break;
      default:
        result = true;
        break;
    }
    // 校验非空
    if (this.valid_content.require) {
      const isRequired = valids.required(validParameter);
      if (!isRequired) {
        this.valid_tip = `不可为空！`;
      }
      result = isRequired && result;
    }
    return result;
  }

  // 根据文本域属性设置字符固定宽度
  setFieldMinOrMaxWidth(
    character: Character | Image | Widget | Line | Box | Button,
    row: Row
  ) {
    const allEles = this.getAllElements();
    let maxTotalWidth = 0;
    let minTotalWidth = 0;
    if (character.field_position === "start") {
      this.insRows = [];
    }
    if (this.insRows.indexOf(row) === -1) {
      this.insRows.push(row);
    }
    for (let i = 1; i < allEles.length - 1; i++) {
      const char = allEles[i];
      if (isCharacter(char) || isImage(char)) {
        maxTotalWidth += char.ori_width;
      } else {
        maxTotalWidth += char.width;
      }
      minTotalWidth += char.width;
    }
    this.setCharWidthWhenMax(maxTotalWidth, allEles);
    // 此处需要考虑跨行时的情况
    this.setCharStartLeft(character, row, minTotalWidth, allEles);
  }

  setCharWidthWhenMax(totalWidth: number, allEles: any) {
    if (this.max_width === 0) {
      return;
    }
    if (totalWidth > this.max_width) {
      const scale = this.max_width / totalWidth;
      if (scale) {
        allEles.forEach((c: any) => {
          if (isCharacter(c)) {
            // 使用原始的字符宽度进行重新计算
            c.width = c.ori_width * scale;
            c.draw_width = c.ori_width * scale;
          } else if (isImage(c)) {
            c.width = c.ori_width * scale;
            c.height = c.ori_height * scale;
          }
        });
      }
    }
  }

  setCharStartLeft(
    character: Character | Image | Widget | Line | Box | Button,
    row: Row,
    total: number,
    elements: any
  ) {
    if (this.min_width === 0) {
      return 0;
    }
    if (this.align === "left" || this.align === undefined) {
      // 目前只能处理两行的，一旦文本域内容跨三行将会出现问题
      if (character === this.end_sym_char) {
        // 最小宽度设置
        let neeSetTotal;
        if (row.children.indexOf(this.start_sym_char) > -1) {
          neeSetTotal = this.min_width - total;
        } else {
          let preRowTotal = 0;
          if (this.insRows.length > 1) {
            preRowTotal = (this.insRows.length - 2) * row.width;
          }
          let endRowWidth = 0;
          if (
            this.insRows.length &&
            this.insRows[this.insRows.length - 1].children.length
          ) {
            const lastRowWidthArr = this.insRows[
              this.insRows.length - 1
            ].children.map((c) => c.width);
            endRowWidth = lastRowWidthArr.reduce((a, b) => a + b);
          }
          let firstRowWidth = row.width - this.start_sym_char.left;
          neeSetTotal =
            this.min_width - firstRowWidth - preRowTotal - endRowWidth;
        }
        if (neeSetTotal < 0 || neeSetTotal >= this.min_width) {
          return;
        }
        row.cursor_position += neeSetTotal;
      }
    } else if (this.align === "center") {
      if (total > this.min_width) {
        return 0;
      }
      if (elements.length > 2) {
        // 拿到文本域边框后第一个字符，设置第一个字符的left
        const secondChar = elements[1];
        if (character === secondChar) {
          row.cursor_position += (this.min_width - total) / 2;
        }
      }
      // 拿到后文本域边框设置距离左侧位置
      if (character === this.end_sym_char) {
        if (elements.length > 2) {
          row.cursor_position += (this.min_width - total) / 2;
        } else {
          row.cursor_position += this.min_width - total;
        }
      }
    } else if (this.align === "right") {
      if (total > this.min_width) {
        return 0;
      }
      if (elements.length > 2) {
        // 拿到文本域边框后第一个字符，设置第一个字符的left
        const secondChar = elements[1];
        if (character === secondChar) {
          row.cursor_position += this.min_width - total;
        }
      } else {
        if (character === this.end_sym_char) {
          row.cursor_position += this.min_width - total;
        }
      }
    }
  }

  /**
   * 将文本域内字符设置星号展示属性
   */
  setFieldAsterisk(command: boolean = true) {
    const children = this.getAllElements();
    if (children.length) {
      for (let j = 1; j < children.length - 1; j++) {
        const child = children[j];
        if (isCharacter(child) && child.field_position === "normal") {
          child.needAsterisk = command;
        }
      }
    }
  }

  static sortFields(field1: XField, field2: XField) {
    const pathA = field1.start_para_path;
    const pathB = field2.start_para_path;

    // 比较第一位
    if (pathA[0] < pathB[0]) {
      return -1;
    } else if (pathA[0] > pathB[0]) {
      return 1;
    } else {
      // 第一位相同，比较第二位
      if (pathA[1] < pathB[1]) {
        return -1;
      } else if (pathA[1] > pathB[1]) {
        return 1;
      } else {
        // 第二位相同，比较第三位（如果存在）
        if (pathA.length > 2 && pathB.length > 2) {
          if (pathA[2] < pathB[2]) {
            return -1;
          } else if (pathA[2] > pathB[2]) {
            return 1;
          } else {
            // 第三位相同，比较第四位（如果存在）
            if (pathA.length > 3 && pathB.length > 3) {
              if (pathA[3] < pathB[3]) {
                return -1;
              } else if (pathA[3] > pathB[3]) {
                return 1;
              }
            }
          }
        }
        return 0; // 所有位都相同
      }
    }
  }

  static transFields2Content(editor:Editor,pFields?:XField[]){
    let fields:any[] = []
    if(Array.isArray(pFields)){
      fields = pFields;
    }else{
      if(editor.selection.isCollapsed){
        const focusField = editor.selection.getFocusField();
        if(focusField){
          fields.push(focusField);
        }
      }else{
        const fieldK = editor.selection.selected_fields_chars.fieldIdVsChars
        for (const fId in fieldK) {
          fields.push({ id:fId })
        }
      }
    }
    let isHandleFields = ""
    if(fields){
      for (let i = 0 ; i <  fields.length; i++) {
        const id = fields[i].id
        const cells = editor.getAllCells()
        cells.forEach((cell)=>{
          const findIndex = cell.fields.findIndex((f)=>f.id === id)
          if(findIndex>-1){
            cell.fields.splice(findIndex,1)
            isHandleFields += id+","
            cell.paragraph.forEach(p=>{
              if(isParagraph(p)){
                p.characters.forEach((ele)=>{
                  if(ele.field_id === id){
                    if(ele.field_position !== "normal"){
                      ele.field_position = "normal"
                    }
                    ele.field_id = null;
                  }
                })
              }
            })
          }
        })
      }
    }
    editor.refreshDocument()
    return isHandleFields;
  }
  static handleDateFieldInput(editor:Editor,inputChar:any) {
    const field = editor.selection.getFocusField();
    if (!field || field.type !== "date") {
      return;
    }
    const formatIndex = field.replace_format;
    const formats = fieldDefaultFormatList;
    const format = formats[formatIndex]; // 根据传入的格式索引选取格式
    // 自动解析格式，提取pattern和separator
    function parseDateFormat(format:any) {
      let pattern = [];
      let separator = [];
      let currentNumLength = 0;

      // 遍历格式字符串的每个字符
      for (let i = 0; i < format.length; i++) {
        let char = format[i];

        // 如果是数字占位符 (Y, M, D, H, m, s)，计算连续的占位符长度
        if (/[YMDHms]/.test(char)) {
          currentNumLength++;
        } else {
          // 遇到非数字字符，保存之前的数字长度到pattern，并记录分隔符
          if (currentNumLength > 0) {
            pattern.push(currentNumLength);
            currentNumLength = 0;
          }
          separator.push(char); // 保存分隔符
          if(char === "日"){ //因为汉字时年份完成后会多分一个间隔符
            pattern.push(0)
          }
        }
      }

      // 处理最后一段数字占位符
      if (currentNumLength > 0) {
        pattern.push(currentNumLength);
      }

      return { pattern, separator };
    }

    // 处理输入并自动格式化
    function handleInput(field:any, selectedFormat:any) {
      let value = field.text;
      if (value.indexOf("\n") > -1) {
        return;
      }
      let selectionStart = editor.selection.para_start; // 当前光标位置
      let oriCharIndex = selectionStart[selectionStart.length - 1];
      const allChars = editor.selection.getFocusParagraph().characters;
      let dateFieldStartIndex = allChars.indexOf(field.start_sym_char);
      let cursorIndexInField = oriCharIndex - dateFieldStartIndex - 1;
      let afterCursorChar = allChars[oriCharIndex];
      // 自动解析当前格式
      const formatObj = parseDateFormat(selectedFormat);
      let formattedValue = "";
      // 提取数字
      let numericValue = value.replace(/\D/g, "");
      // 获取当前输入的字符
      let newCursorPos = 0;
      let separatorCount = 0;
      if (
        field.children.length &&
          !PathUtils.equals(selectionStart, field.end_para_path)
      ) {
        // 当前已有内容不符合对应格式时，不进行自动格式化处理
        // 输入的不是数字，也不自动化处理
        const splitVal = value.split("");
        splitVal.splice(cursorIndexInField - 1, 1);
        if (
          !/\d/.test(inputChar) ||
            !validateFormat(splitVal.join(""), formatObj)
        ) {
          return;
        }
        // 计算光标在数字部分的位置（跳过分隔符）
        let posInDigits = 0;
        for (let i = 0; i < cursorIndexInField; i++) {
          if (/\d/.test(value[i])) {
            posInDigits++;
          } else {
            separatorCount++;
          }
        }
        newCursorPos = posInDigits; // 光标在数字中的位置

        // 覆盖逻辑：替换相应的数字
        if (inputChar) {
          let newNumericValue = numericValue.split(""); // 将数字部分转换为字符数组

          // 逐个替换输入的字符
          for (let i = 0; i < inputChar.length; i++) {
            if (newCursorPos < newNumericValue.length) {
              // newNumericValue[newCursorPos - 1] = "";
              newNumericValue.splice(newCursorPos - 1, 1);
              // 覆盖当前光标位置上的字符
              newNumericValue[newCursorPos - 1] = inputChar[i];
            }
            newCursorPos++; // 移动光标到下一个数字
          }

          numericValue = newNumericValue.join(""); // 拼接替换后的数字部分
        }
        if (!/\d/.test(afterCursorChar.value)) {
          if(/^[\u4e00-\u9fa5]+$/.test(afterCursorChar.value)){
            if(afterCursorChar.value !== formatObj.separator[formatObj.separator.length - 1]){
              newCursorPos++;
            }
          }else{
            newCursorPos++;
          }
        }
      }
      // 根据格式进行分割并插入间隔符号

      let currentPos = 0;
      for (let i = 0 ; i <  formatObj.pattern.length; i++) {
        const len = formatObj.pattern[i];
        if (numericValue.length > currentPos) {
          let part = numericValue.slice(currentPos, currentPos + len);
          formattedValue += part;
          if (i < formatObj.separator.length && part.length === len) {
            formattedValue += formatObj.separator[i];
            if(formatObj.separator[i] === "日" && formatObj.separator[i+1]){
              formattedValue += formatObj.separator[i+1]
              i++
            }
          }
          currentPos += len;
        }
      }

      if (!formattedValue) {
        return;
      }
      // 更新输入框内容
      const newValue = formattedValue;
      field.replaceText(newValue);
      // 设置光标位置
      if (oriCharIndex) {
        if (newCursorPos) {
          selectionStart[selectionStart.length - 1] =
              dateFieldStartIndex + newCursorPos + separatorCount;
          editor.selection.setCursorPosition(
            editor.paraPath2ModelPath(selectionStart)
          );
        } else {
          let newCharIndex = calculateNewCursorPos(
            oriCharIndex,
            value,
            formattedValue
          );
          selectionStart[selectionStart.length - 1] = newCharIndex;
          editor.selection.setCursorPosition(
            editor.paraPath2ModelPath(selectionStart)
          );
        }

        editor.refreshDocument();
        return true;
      }
    }

    // 计算新的光标位置（与之前的逻辑相同）
    function calculateNewCursorPos(oldPos:any, oldValue:any, newValue:any) {
      let diff = newValue.length - oldValue.length;
      return oldPos + diff;
    }
    function validateFormat(str:string, formatObj:any) {
      const { pattern, separator } = formatObj;

      // 根据 pattern 创建一个正则表达式
      let regexPattern = pattern
        .map((len:number, index:number) => {
          let part = `\\d{${len}}`; // 每段应该是对应长度的数字
          if (index < separator.length) {
            part += separator[index].replace(/[.*+?^${}()|[\]\\]/g, "\\$&"); // 添加分隔符，注意转义特殊字符
          }
          return part;
        })
        .join("");

      // 创建正则表达式对象
      const regex = new RegExp(`^${regexPattern}$`);

      // 检查字符串是否匹配正则表达式
      return regex.test(str);
    }
    return handleInput(field, format); // 你可以动态传入用户选择的格式
  }
}
