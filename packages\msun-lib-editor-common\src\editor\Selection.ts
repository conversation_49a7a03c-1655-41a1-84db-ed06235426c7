import PathUtils, { Path } from "./Path";
import Cell from "./Cell";
import Row from "./Row";
import Table from "./Table";
import {
  allowEditing,
  caesarCipher,
  encodeBase64,
  initCell,
  isParagraph,
  isRow,
  isTable,
  writeClipboard
} from "./Utils";

import {
  getCellIndex,
  getNextCell,
  isCell,
  isLastCell,
  nearOffsetCell,
  saveSelectionData
} from "./Helper";
import Caret from "./Caret";
import Editor from "./Editor";
import Paragraph from "./Paragraph";
import XField from "./XField";
import Group from "./Groups";
import Font, { FontStyle } from "./Font";
import { Config } from "./Config";
import EditorHelper from "./EditorHelper";
import EditorLocalTest from "../../localtest";

export interface RangeBase {
  anchor: Path;
  focus: Path;
}

export default class XSelection {
  selected_cells_path: Path[] = []; // cell的para_path，如果使用model_path在段落字体字号改变后有可能会造成model_path变化
  selected_areas: {
    start_para_path: Path;
    end_para_path: Path;
    last_linebreak?: boolean; // 区域的最后一行是否包含换行符
  }[] = []; // 用于记录选中的普通文本区域

  // FIXME 多选区思路
  // 原来的选区逻辑保持不变 原有的所有逻辑都保持不变 包括绘制等所有逻辑
  // 新的多选区就用新的属性 全部都加判断走新的逻辑 将原来的选区内容存储在新的属性里
  // 绘制的时候还是走原来的绘制 只不过将保存的多选区的也绘制上就可以了 因为绘制的时候多选区肯定还没有记录
  // 现在还有一个没搞清楚 就是新的选区覆盖了原来的部分选区 怎么将原来的选区去掉
  // TODO 现在的问题
  // 1. 选不按 ctrl 选区 然后再按 Ctrl 选区 原来的选区应该保留 不应该清空
  // 2. 多选区，鼠标按下不抬起，选区就不进行绘制 我按着 Ctrl 点一下 不移动鼠标(不选区) 原来的选区也不进行绘制了 就看不出来是多选区了
  // 3. 绘制次数有点多，我鼠标抬起的时候会多绘制一遍 颜色不一样了

    
  // selected_areas 这个属性在 选区跨表格的时候会有多个选项 否则里边就一个内容
  multipleSelected: {
    selectedAreas: {
      start_para_path: Path;
      end_para_path: Path;
      last_linebreak?: boolean; // 区域的最后一行是否包含换行符
    }[],
    selectedCellPath: Path[],
    start: Path,
    end: Path,
  }[] = [];


  editor: Editor; // 编辑器对象

  static hasCaret(editor: Editor, x: number, y: number) {
    if (editor.selection.isCollapsed) {
      return false;
    } else {
      // 取反此时说明是选区
      const res = editor.getElementByPoint(x, y);

      if (editor.selection.selected_cells.find(({ cell }) => cell === res.cell)) {
        return true;
      }
      const rows = editor.selection.selected_para_info.rows;
      const current_element = editor.getContainerInfoByPointHelper(x, y + editor.scroll_top);
      const current_row = current_element?.row as Row;
      if ((current_row && rows.findIndex(row => row.id === current_row.id) > -1)) {
        if (current_element) {
          const { model_path } = current_element;
          const selected_start_path = editor.selection.start;
          const selected_end_path = editor.selection.end;
          if (rows.length > 1) {
            if (current_row.id === rows[0].id) {
              if (model_path[model_path.length - 1] < selected_start_path[selected_start_path.length - 1]) {
                return false;
              }
            }
            if (current_row.id === rows[rows.length - 1].id) {
              if (model_path[model_path.length - 1] > selected_end_path[selected_end_path.length - 1]) {
                return false;
              }
            }
          } else {
            if (model_path[model_path.length - 1] < selected_start_path[selected_start_path.length - 1] ||
              model_path[model_path.length - 1] > selected_end_path[selected_end_path.length - 1]) {
              return false;
            }
          }
        }
        return true;
      } else { return false; }
    }
  }

  static changeFontStyle(editor: Editor, style: Partial<FontStyle> | "bigger" | "smaller") {
    if (!editor.operableOrNot(["cell", "group"])) return false;
    if (typeof style === "string" && editor.selection.isCollapsed) {
      editor.contextState.setCharacterSize(style)
      return true;
    }
    const selectedParaInfo = editor.selection.selected_para_info;
    const offsetInStartParagraph = selectedParaInfo.start_end[0]; // 选区第一段偏移量，表格内为-1
    const offsetInEndParagraph = selectedParaInfo.start_end[1]; // 选区最后一段偏移量，表格内为-1
    const paragraphs = selectedParaInfo.paragraphs; // 选区段落
    if (paragraphs.length > 1) {
      for (let i = 0; i < paragraphs.length; i++) {
        let start: number = 0;
        let end: number = 0;
        const paragraph = paragraphs[i];
        const chars_length = paragraph.characters.length;
        if (i === 0) {
          // 第一段改变样式的character
          start = offsetInStartParagraph === -1 ? 0 : offsetInStartParagraph;
          end = chars_length;
        } else if (i === paragraphs.length - 1) {
          // 最后一段改变样式的character
          start = 0;
          end = offsetInEndParagraph === -1 ? chars_length : offsetInEndParagraph;
        } else {
          // 中间段落改变样式的character
          start = 0;
          end = chars_length;
        }
        if (typeof style !== "string") {
          paragraph.changeStyle(start, end, style);
        } else {
          paragraph.setCharacterSize(start, end, style);
        }
      }
    } else {
      if (typeof style !== "string") {
        paragraphs[0].changeStyle(offsetInStartParagraph, offsetInEndParagraph, style);
      } else {
        paragraphs[0].setCharacterSize(offsetInStartParagraph, offsetInEndParagraph, style);
      }
    }
  }

  static changeTableBgStyle(color: string, selection: XSelection, editor: Editor) {
    if (color.indexOf("#") === -1) {
      editor.event.emit("message", "请传入16进制#开头的颜色，目前不支持其他颜色类型");
      return false;
    }
    if (selection.selected_cells.length) {
      for (let i = 0; i < selection.selected_cells.length; i++) {
        const cell = selection.selected_cells[i].cell;
        cell.style.bgColor = color;
      }
    }
    editor.render();
    return true;
  }

  static setCharactersZoom(editor: Editor, type: "bigger" | "smaller") {
    this.changeFontStyle(editor, type);
    editor.internal.tempFields.clear();
    return true;
  }

  static setParagraphStyleByFormatBrush(editor: Editor) {
    // 判断是段落操作还是文字操作
    if (editor.selection.isCollapsed) {
      editor.change_paragraph_style();
    } else {
      const font_style = editor.contextState.getFontState();
      // 刷格式前判断格式刷是否为选区格式刷，若为选区，只刷文字样式，不刷段落样式
      if (editor.internal.brush_selection) {
        editor.change_font_style(font_style);
      }
      editor.change_paragraph_style();
    }
    editor.format_brush = false;
    return true;
  }

  static setSelectionArea(editor: Editor, x: number, y: number) {
    const container_info = editor.getContainerInfoByPointHelper(x, y);
    if (container_info) {
      const { model_path, x } = container_info;

      editor.selection.focus = model_path;
      // 当锚点焦点重合时不设置选区
      if (editor.selection.isCollapsed) {
        return;
      }
      editor.selection.setSelectionByPath(editor.selection.anchor, editor.selection.focus, "model_path", x);
      editor.caret.cache = null;
    }
  }

  static getRawData(editor: Editor, containHf: boolean = false) {
    const partDataArr = saveSelectionData(editor);
    if (!partDataArr) return null;

    const newRootCell = initCell(editor, "trans");
    const newHeaderCell = containHf ? initCell(editor, "header_trans") : editor.header_cell;
    const newFooterCell = containHf ? initCell(editor, "footer_trans") : editor.footer_cell;
    newRootCell.paragraph = partDataArr;

    // return rawDataTrans.modelDataToRawData(
    //   newHeaderCell,
    //   newRootCell,
    //   newFooterCell
    // );
    return editor.event.emit("modelData2RawData",
      newHeaderCell,
      newRootCell,
      newFooterCell
    );
  }

  static draw(editor: Editor) {
    if (editor.selection.isCollapsed) {
      // 只读模式不需要展示光标
      if (editor.readonly || editor.formReadonly || (!editor.config.cursor_blinks && !editor.is_focus)) {
        return;
      }
      // 绘制光标
      const cell = editor.selection.getFocusCell();
      // 单元格滚动时光标位置在单元格内才绘制，超出单元格不绘制
      if (cell && cell.parent && cell.set_cell_height.type === "scroll") {
        const real_top = cell.realTop;
        if (!(editor.caret.y >= cell.height + real_top || editor.caret.y <= real_top - editor.caret.height)) {
          editor.caret.show && editor.caret.draw(cell);
        }
      } else {
        editor.caret.show && editor.caret.draw();
      }
      if (editor.internal.isMultipleSelection && editor.selection.multipleSelected.length) {
        editor.drawSelectionCells();
        editor.drawSelectionAreas();
      }
    } else {
      // 绘制路径选区
      // 绘制选中的单元格
      editor.drawSelectionCells();
      // 绘制选中的文本区域
      editor.drawSelectionAreas();
      //绘制移动端小水滴
      editor.drawSelectionMobileEdit();
    }
  }

  static deleteAndControlRecord(editor: Editor, isRecord: boolean = true) {
    // 分组如果锁定 则不能编辑
    if (!allowEditing(editor)) return false;
    // 如果是选中了一些内容 然后输入 则需要先删除
    if (!editor.selection.isCollapsed) {
      let del_result;
      if (editor.config.useLetterPlaceholder) {
        // 先记录一下字体 删除之后再恢复 避免先改变字体为红色然后调用接口 insertText 之后 再改变字体为黑色 再输入英文的时候没有问题 输入汉字就会有个删除就又改回红色了 所以记录一下再恢复
        const font = editor.contextState.getFontState();
        del_result = EditorHelper.deleteBackward(editor); // 不能用 editor 调用，因为会往历史堆栈里记录
        editor.contextState.setFontState(font);
      } else {
        del_result = editor.delete_backward();
      }
      if (!del_result) {
        return false;
      }
    }
    editor.selection.clearSelectedInfo();
    // 返回false,不记录到堆栈信息中
    return isRecord;
  }

  constructor(selection: RangeBase | number[], editor: Editor) {
    if ((<RangeBase>selection).anchor) {
      this._anchor = (<RangeBase>selection).anchor;

      this._focus = (<RangeBase>selection).focus;
    } else {
      this._anchor = this._focus = selection as Path;
    }
    this.editor = editor;
  }

  get selected_cells(): {
    para_path: Path; // para_path
    cell: Cell;
  }[] {
    const paths = this.selected_cells_path;
    return this.getCellsInfoByPath(paths);
  }

  getCellsInfoByPath(paths: Path[]): {cell: Cell, para_path: Path}[] {
    const cells: {cell: Cell, para_path: Path}[] = [];
    for (let i = 0; i < paths.length; i++) {
      const paraTableArr = this.editor.current_cell.paragraph;
      const path = paths[i];
      const table = paraTableArr[path[0]];
      const cell = table.children[path[1]];
      if (isCell(cell)) {
        cells.push({ para_path: path, cell: cell });
      }
    }
    return cells;
  }

  /**
   * 获取所有选中单元格中的文本域
   */
  get selected_cells_fields() {
    const fields = [];
    const cells = this.selected_cells;
    for (let i = 0; i < cells.length; i++) {
      const cell = cells[i].cell;
      fields.push(...cell.fields);
    }
    return fields;
  }

  /**
   * 选区结尾是否包含换行符
   */
  get hasLastLinebreak(): boolean {
    if (this.isOnlyCell || this.isOneTable) {
      return false;
    }
    return !!this.selected_areas[this.selected_areas.length - 1].last_linebreak;
  }

  /**
   * 用于判断是否是某个文本域中的文本（不含文本域边框字符）
   */
  get isOnlyFieldText(): boolean {
    const start_field = this.getFieldByPath(this.para_start);
    const end_field = this.getFieldByPath(this.para_end);
    let field: XField | undefined;
    if (start_field && end_field) {
      const all_chars = this.selected_fields_chars.all_chars;
      if (
        all_chars[0].field_position === "start" ||
        all_chars[all_chars.length - 1].field_position === "end") {
        return false;
      }
      if (start_field === end_field) {
        return start_field.isChildrenChars(all_chars);
      }
      if ((start_field.getFieldInChildren() as any).includes(end_field)) {
        field = start_field;
      } else if ((end_field.getFieldInChildren() as any).includes(start_field)) {
        field = end_field;
      } else {
        return false;
      }
      return field.isChildrenChars(all_chars);
    }
    return false;
  }

  /**
   * 获取选区内开始与结束都是文本域时最外层的文本域
   */
  get selected_outermost_field(): XField | undefined {
    const start_field = this.getFieldByPath(this.para_start);
    const end_field = this.getFieldByPath(this.para_end);
    let field: XField | undefined;
    if (start_field && end_field) {
      if (start_field === end_field) {
        field = start_field;
      }
      if ((start_field.getFieldInChildren() as any).includes(end_field)) {
        field = start_field;
      } else if ((end_field.getFieldInChildren() as any).includes(start_field)) {
        field = end_field;
      }
    }
    return field;
  }

  /**
   * 是否只选中了单元格
   */
  get isOnlyCell(): boolean {
    return !!(this.selected_cells_path.length && !this.selected_areas.length);
  }

  /**
   * 获取选中的所有文本域字符
   */
  get selected_fields_chars() {
    const { paragraphs, start_end, end_linebreak } = this.selected_para_info;
    if (!paragraphs.length) return {};
    const all_chars: any = []; // 段落中包含的所有字符
    if (PathUtils.isSameParagraph(this.para_start, this.para_end)) {
      let slice_chars = paragraphs[0].characters.slice(start_end[0], start_end[1]);
      if (end_linebreak) {
        slice_chars = paragraphs[0].characters.slice(start_end[0]);
      }
      all_chars.push(...slice_chars);
    } else {
      let start_index = 0;
      let end_index = paragraphs.length;
      if (start_end[0] !== -1) {
        all_chars.push(...paragraphs[0].characters.slice(start_end[0]));
        start_index += 1;
      }
      if (start_end[1] !== -1) {
        end_index -= 1;
      }
      for (let i = start_index; i < end_index; i++) {
        const paragraph = paragraphs[i];
        if (isParagraph(paragraph)) {
          all_chars.push(...paragraph.characters);
        }
      }
      if (start_end[1] !== -1) {
        // TODO 文本域中选区末尾如果是换行符，选区字符中没有该换行符导致删除不掉的问题（共3处，第1处）
        // if (end_linebreak) {
        //   all_chars.push(...paragraphs[end_index].characters.slice(0, start_end[1] + 1));
        // } else {
        all_chars.push(...paragraphs[end_index].characters.slice(0, start_end[1]));
        // }
      }
    }
    const fieldIdVsChars: any = {};
    const field_chars: any = [];
    for (let i = 0; i < all_chars.length; i++) {
      const char = all_chars[i];
      if (!char.field_id) continue;
      field_chars.push(char);
      if (!fieldIdVsChars[char.field_id]) {
        fieldIdVsChars[char.field_id] = [];
      }
      fieldIdVsChars[char.field_id].push(char);
    }
    return { fieldIdVsChars, field_chars, all_chars, end_linebreak, paragraphs };
  }

  /**
   * 是一个完整的表格
   */
  get isOneTable(): boolean {
    return (
      (PathUtils.isSameTable(this.start, this.end) ||
        (this.start.length === 4 &&
          this.end[0] === this.start[0] + 1 &&
          this.end[1] === 0)) &&
      this.editor.current_cell.children[this.start[0]].children.length ===
      this.selected_cells_path.length
    );
  }

  get anchor() {
    return this._anchor;
  }

  set anchor(path: Path) {
    this._anchor = path;
  }

  get focus() {
    return this._focus;
  }

  set focus(path: Path) {
    this._focus = path;
  }

  get para_focus(): Path {
    return this.editor.modelPath2ParaPath(this.focus);
  }

  get para_anchor(): Path {
    return this.editor.modelPath2ParaPath(this.anchor);
  }

  get para_start(): Path {
    return this.editor.modelPath2ParaPath(this.start);
  }

  get para_end(): Path {
    return this.editor.modelPath2ParaPath(this.end);
  }

  /**
   * 获取所有选中信息 段落信息 其中的 paragraph 和 row 会将表格中的平铺出来跟外层一个级别返回
   * @returns start_end:选区开始和结束字符在所在段落中的下标 如果开始和结束字符在表格中 值为 -1; 选择内容所在段落对象 、 开始点与结束点字符所在段落下标对象 、 结尾是否包含换行符、所有的行信息
   *
   */
  getParaInfoByPath(
    startParaPath: Path, 
    endParaPath: Path, 
    startModelPath?: Path,
    endModelPath?: Path,
    selected_areas?: {
      start_para_path: Path;
      end_para_path: Path;
      last_linebreak?: boolean; // 区域的最后一行是否包含换行符
    }[], 
    selected_cells?: {
      para_path: Path; // para_path
      cell: Cell;
    }[],
  ):{
    paragraphs: Paragraph[];
    start_end: number[];
    end_linebreak: boolean | undefined;
    rows: Row[]
    }  
  {
    const paragraphs: Paragraph[] = [];
    const start_end: number[] = [-1, -1]; // -1代表为表格
    let end_linebreak: boolean | undefined;
    const end_para_index = endParaPath[endParaPath.length - 2];
    let current_cell = this.editor.current_cell;
    // 如果是单元格内文本选区  或者选区开始不是单元格
    if (
      !PathUtils.isTablePath(startParaPath) ||
        PathUtils.isSameCell(this.start, this.end)
    ) {
      start_end[0] = startParaPath[startParaPath.length - 1];
    }
    if (
      !PathUtils.isTablePath(endParaPath) ||
        PathUtils.isSameCell(this.start, this.end)
    ) {
      start_end[1] = endParaPath[endParaPath.length - 1];
    }
    if (PathUtils.isSameCell(this.start, this.end)) {
      current_cell = current_cell.children[this.start[0]].children[
        this.start[1]
      ] as Cell;
    }
    const areas = selected_areas || this.selected_areas;
    for (let i = 0; i < areas.length; i++) {
      const start_para_path = areas[i].start_para_path;
      const end_para_path = areas[i].end_para_path;
      const start_area_para_index = start_para_path[start_para_path.length - 2];
      const end_area_para_index = end_para_path[end_para_path.length - 2];
      // 如果只有一个段落
      if (start_area_para_index === end_area_para_index) {
        paragraphs.push(current_cell.paragraph[start_area_para_index] as Paragraph);
        if (start_area_para_index === end_para_index) {
          end_linebreak = areas[i].last_linebreak;
        }
      } else {
        for (
          let j = start_para_path[start_para_path.length - 2];
          j <= end_para_path[end_para_path.length - 2];
          j++
        ) {
          paragraphs.push(current_cell.paragraph[j] as Paragraph);
          if (j === end_para_index) {
            end_linebreak = areas[i].last_linebreak;
          }
        }
      }
    }
    const selectedCells = selected_cells || this.selected_cells;
    for (let i = 0; i < selectedCells.length; i++) {
      const cell = selectedCells[i].cell;
      paragraphs.push(...(cell.paragraph as Paragraph[]));
    }
    paragraphs.sort((a: Paragraph, b: Paragraph) => {
      let a_para_index = a.cell.parent
        ? a.cell.parent.para_index
        : a.para_index;
      let b_para_index = b.cell.parent
        ? b.cell.parent.para_index
        : b.para_index;
      if (a.cell.parent?.para_index === b.cell.parent?.para_index) {
        a_para_index = a.para_index;
        b_para_index = b.para_index;
      }
      return a_para_index - b_para_index;
    });
    const rows: Row[] = [];
    for (let i = 0; i < paragraphs.length; i++) {
      const para = paragraphs[i];
      if (paragraphs.length === 1) {
        const start_row = this.getRowByPath(startModelPath || this.start);
        const end_row = this.getRowByPath(endModelPath || this.end);
        rows.push(...para.children.slice(para.children.indexOf(start_row), para.children.indexOf(end_row) + 1));
      } else {
        if (i === 0) {
          const row = this.getRowByPath(startModelPath || this.start);
          rows.push(...para.children.slice(para.children.indexOf(row)));
        } else if (i === paragraphs.length - 1) {
          const row = this.getRowByPath(endModelPath || this.end);
          rows.push(...para.children.slice(0, para.children.indexOf(row) + 1));
        } else {
          rows.push(...para.children);
        }
      }
    }
    return { paragraphs, start_end, end_linebreak, rows };
  }

  /**
   * 获取所有选中信息 段落信息 其中的 paragraph 和 row 会将表格中的平铺出来跟外层一个级别返回
   * @returns start_end:选区开始和结束字符在所在段落中的下标 如果开始和结束字符在表格中 值为 -1; 选择内容所在段落对象 、 开始点与结束点字符所在段落下标对象 、 结尾是否包含换行符、所有的行信息
   *
   */
  get selected_para_info(): {
    paragraphs: Paragraph[];
    start_end: number[];
    end_linebreak: boolean | undefined;
    rows: Row[]
    } {
    const para_start = this.para_start;
    const para_end = this.para_end;
    return this.getParaInfoByPath(para_start, para_end);
    // 不能用下边的这种方式 因为比如删除等操作是循环设置的单元格选区进行操作的 如果这里也返回了全部 肯定就不对了
    // const res: {
    //   paragraphs: Paragraph[],
    //   start_end: number[],
    //   rows: Row[],
    //   end_linebreak: boolean | undefined;
    // } = {
    //   paragraphs: [],
    //   start_end: [],
    //   rows: [],
    //   end_linebreak: undefined
    // };
    // if (this.editor.selection.multipleSelected.length) {
    //   for (const current of this.editor.selection.multipleSelected) {
    //     const {
    //       paragraphs,
    //       start_end,
    //       end_linebreak,
    //       rows
    //     } = this.getParaInfoByPath(current.start, current.end, this.editor.paraPath2ModelPath(current.start), this.editor.paraPath2ModelPath(current.end), current.selectedAreas, this.getCellsInfoByPath(current.selectedCellPath));
    //     res.paragraphs.push(...paragraphs);
    //     res.rows.push(...rows);
    //     res.start_end = start_end;
    //     res.end_linebreak = end_linebreak;
    //   }
    // } else {
    //   const {
    //     paragraphs,
    //     start_end,
    //     end_linebreak,
    //     rows
    //   } = this.getParaInfoByPath(para_start, para_end);
    //   res.paragraphs.push(...paragraphs);
    //   res.rows.push(...rows);
    //   res.start_end = start_end;
    //   res.end_linebreak = end_linebreak;
    // }
    // return res;
  }


  get start(): Path {
    if (this.isReverse) {
      return this._focus;
    }
    return this._anchor;
  }

  get end(): Path {
    if (this.isReverse) {
      return this._anchor;
    }
    return this._focus;
  }

  get isCollapsed(): boolean {
    return PathUtils.equals(this._anchor, this._focus);
  }

  /**
   * _anchor 位置是否在 focus 后
   */

  get isReverse(): boolean {
    return PathUtils.isAfter(this._anchor, this._focus);
  }

  getCellRange () {
    if (this.isCollapsed) return 
    const selectedCells = this.selected_cells;
    let minRow = Infinity;
    let maxRow = -Infinity;
    let minCol = Infinity;
    let maxCol = -Infinity;
    let table;
    for (const { cell } of selectedCells) {
      !table && (table = cell.parent);
      if (cell.start_row_index < minRow) {
        minRow = cell.start_row_index;
      }
      if (cell.end_row_index > maxRow) {
        maxRow = cell.end_row_index;
      }

      if (cell.start_col_index < minCol) {
        minCol = cell.start_col_index;
      }
      if (cell.end_col_index > maxCol) {
        maxCol = cell.end_col_index;
      }
    }
    return {
      table, 
      minRow, 
      maxRow,
      minCol,
      maxCol
    }
  }
  
  /**
 * 将光标置到分组开头
 */
  setCursorByGroup(group: Group) {
    const para = group.paragraph[0];
    if (isTable(para)) {
      this.editor.selection.setCursorPosition([para.cell_index, 0, 0, 0]);
    } else {
      this.editor.selection.setCursorPosition([(para as Paragraph).children[0].cell_index, 0]);
    }
  }

  /**
   *   根据rootCell设置光标位置到文档头或文档尾
   */
  setCursorByRootCell(posit: "start" | "end" = "start") {
    const root_cell: any = this.editor.current_cell;
    if (posit === "start") {
      // 判断开始位置是否为表格
      this.setCursorPosition(PathUtils.getDocumentStart(root_cell));
    } else {
      this.setCursorPosition(PathUtils.getDocumentEnd(root_cell));
    }
  }

  /**
   * 根据容器类型与位置设置光标路径
   * @param container_type  容器 row：行  para:段落 doc：文档
   * @param posit 位置
   * @param isRender 是否更新渲染光标
   * @param isSelection 是否选区
   */
  setCursorByContainerType(container_type: "row" | "para" | "doc" = "row", posit: "start" | "end" = "start", isRender: boolean = true, isSelection: boolean = false) {
    let res_cursor_path: Path;
    const anchor = [...this.anchor];
    if (container_type === "row") {
      if (posit === "start") {
        res_cursor_path = PathUtils.getRowStartPathByPath(this.anchor);
      } else {
        res_cursor_path = PathUtils.getEndPathByRow(this.getFocusRow());
      }
      this.setCursorPosition(res_cursor_path);
    } else if (container_type === "para") {
      if (posit === "start") {
        const para_path = PathUtils.getParagraphStartParaPathByPath(this.para_anchor);
        res_cursor_path = this.editor.paraPath2ModelPath(para_path);
      } else {
        const para_path = PathUtils.getEndParaPathByPara(this.getFocusParagraph());
        res_cursor_path = this.editor.paraPath2ModelPath(para_path);
      }
      this.setCursorPosition(res_cursor_path);
    } else {
      this.setCursorByRootCell(posit);
    }
    if (isRender) {
      this.editor.updateCaret();
      this.editor.render();
    }
    if (isSelection) {
      this.setSelectionByPath(anchor, this.focus, "model_path");
      this.editor.refreshDocument();
    }
  }

  /**
   * 设置锚点与焦点为同一路径，定位光标位置
   */
  setCursorPosition(path: Path) {
    this.anchor = this.focus = path;
  }

  /**
   * 清空选区信息
   */
  clearSelectedInfo(clearMultipleSelection: boolean = true) {
    this.selected_areas = [];
    this.selected_cells_path = [];
    if (!this.editor.internal.isMultipleSelection && clearMultipleSelection) {
      // 如果不是多选区 那么保存的多选区内容也要清空
      this.multipleSelected = [];
    }

  }

  includes(location: Path): boolean {
    return (
      (PathUtils.equals(this.start, location) ||
        PathUtils.isBefore(this.start, location)) &&
      PathUtils.isAfter(this.end, location)
    );
  }

  /**
   * 光标向前移动x步
   * @param x
   * @param container
   */

  stepForward(x: number, container: Row) {
    this.setCursorPosition(this.stepForwardByPath(x, container, this.focus));
  }

  /**
   * 光标向后移动x步
   * @param x
   * @param container
   */

  stepBackward(x: number, container: Row) {
    this.setCursorPosition(this.stepBackwardByPath(x, container, this.focus));
  }

  /**
   * 光标向上或向下移动1步
   * @param moveType 移动方式
   */

  stepVertical(moveType: "up" | "down", caret: Caret) {
    const path = this.stepVerticalByPath(moveType, this.focus, caret)
    this.setCursorPosition(path);
  }

  /**
   * 选区后按方向键移动光标
   * @param moveType 左右上下
   */
  stepWhenSelected(moveType: "left" | "right" | "up" | "down", caret: Caret) {
    let reset_start_path: Path = this.start;
    let reset_end_path: number[] = this.end;
    let isEndTableAndNextRow: boolean = false; // 是否结束为表格并且下一行为普通行
    // 表格与普通行同时选中时
    if (this.selected_areas.length && this.selected_cells_path.length) {
      // 如果开始为表格，结束不为表格
      if (PathUtils.isTablePath(this.start)) {
        // 将开始坐标置为表格第一个单元格内的段首位置
        reset_start_path = PathUtils.getTableStartPathByPath(this.start);
      }
      // 如果开始不为表格，结束为表格
      if (PathUtils.isTablePath(this.end)) {
        // 将结束位置置为表格最后一个单元格段末位置
        const table = this.editor.current_cell.children[this.end[0]] as Table;
        reset_end_path = PathUtils.getEndPathByTable(table);
        // 如果下一行为普通row
        if (isRow(this.editor.current_cell.children[this.end[0] + 1])) {
          isEndTableAndNextRow = true;
        }
      }
    }
    // 单个表格中单元格选中
    if (this.isOnlyCell) {
      // 将开始坐标置为表格第一个单元格内的段首位置
      reset_start_path = PathUtils.getTableStartPathByPath(this.start);
      // 记录原光标选区结束坐标
      const ori_end = [...this.end];
      const end_cell = this.getDeepCellByPath(this.end) as Cell;
      const row_index = end_cell.children.length - 1;
      const end_row = end_cell.children[row_index];
      const char_index = end_row.children.length;
      reset_end_path = [ori_end[0], ori_end[1], row_index, char_index];
    }
    if (moveType === "left") {
      this.setCursorPosition(reset_start_path);
    } else if (moveType === "right") {
      this.setCursorPosition(reset_end_path);
    } else if (moveType === "up") {
      this.setCursorPosition(reset_start_path);
      this.stepVertical(moveType, caret);
    } else {
      this.setCursorPosition(reset_end_path);
      this.stepVertical(moveType, caret);
      // 开始是表格，结束为表格，下一行为普通行，并且是向下移动时需要特殊处理
      if (isEndTableAndNextRow) {
        // 将光标置于行首
        this.setCursorPosition([this.focus[0], 0]);
      }
    }
  }

  /**
   * 表单模式下的光标移动
   * @param type
   * @param ori_focus
   */
  caretMoveAboutFormView(type: "left" | "right" | "up" | "down", model_path: Path): any {
    const field = this.getFieldByPath(this.para_focus);
    if (field) {
      return false;
    }
    if (type === "up") {
      const row = this.getRowByPath(model_path);
      const pri_chars = row.children.slice(0, model_path[model_path.length - 1]);
      if (pri_chars.findIndex((char) => char.field_position === "end") > -1) {
        type = "left";
      } else if (row.children.findIndex(char => char.field_position === "start") > -1 || model_path[0] === 0) {
        type = "right";
      }
    }
    if (type === "down") {
      const row = this.getRowByPath(model_path);
      const pri_chars = row.children.slice(model_path[model_path.length - 1]);
      if (pri_chars.findIndex((char) => char.field_position === "start") > -1) {
        type = "right";
      } else if (row.children.findIndex(char => char.field_position === "end") > -1 ||
        model_path[0] === this.editor.current_cell.children.length - 1) {
        type = "left";
      }
    }
    if (type === "left" && PathUtils.isStartInDocument(model_path)) {
      type = "right";
    }
    if (type === "right" && PathUtils.isEndInDocument(model_path, this.editor.current_cell)) {
      return false;
    }
    return type;
  }

  /**
   * 当光标移动遇到文本域后的处理
   * @param type
   */
  caretMoveAboutField(type: "left" | "right" | "up" | "down") {
    const para_focus = this.para_focus;
    const field = this.getFieldByPath(para_focus);
    if (!field) {
      return;
    }
    let para_path: any = null;
    if (!field.children.length) {
      para_path = [...field.start_para_path];
      if (type === "right") {
        PathUtils.movePathCharNum(para_path, 1);
        if (PathUtils.equals(para_path, para_focus)) {
          return;
        }
        para_path = [...field.end_para_path];
      }
      PathUtils.movePathCharNum(para_path, 1);
    } else if (field.type === "label") {
      // 当是一个label时，光标不允许出现在开始与结束字符之间，只能是在两端
      if (type === "left") {
        para_path = [...field.start_para_path];
      } else {
        para_path = [...field.end_para_path];
        PathUtils.movePathCharNum(para_path, 1);
      }
    }
    if (para_path) {
      this.setCursorPosition(this.editor.paraPath2ModelPath(para_path));
    }
  }

  /**
   * 根据路径计算向前移动x步的新路径
   * @param x
   * @param container
   * @param path
   */

  stepBackwardByPath(x: number, container: Row, path: Path): Path {
    const current_path = [...path];
    // 如果当前已经是文档开头则不再移动
    if (PathUtils.isStartInDocument(current_path)) {
      return current_path;
    }
    // 计算向前移动x步后的字符下标
    let current_index = current_path[current_path.length - 1] - x;
    // 得到当前行下标
    let row_index = current_path[current_path.length - 2];
    // 得到当前容器的父容器
    const parent_container = container.parent;

    // 没有移出本cell
    while (row_index >= 0) {
      let current_container = parent_container.children[row_index];

      if (isRow(current_container)) {
        // 未移动出本行
        if (current_index >= 0) {
          current_path.splice(-2, 2, row_index, current_index);

          return current_path;
          // 移出本行
        } else {
          row_index -= 1;

          if (row_index < 0) break;

          current_container = parent_container.children[row_index];

          if (isRow(current_container)) {
            const character_length = current_container.linebreak
              ? current_container.children.length + 1
              : current_container.children.length;

            current_index = character_length + current_index;
          }
        }
      } else {
        const cell_index = current_container.children.length - 1;

        const current_cell = current_container.children[cell_index];

        const current_row_index = current_cell.children.length - 1;

        container = current_cell.children[current_row_index] as Row; // 不考虑嵌套，只能为row

        const character_index = container.linebreak
          ? container.children.length + 1
          : container.children.length;

        current_path.splice(
          -4,
          4,
          row_index,
          cell_index,
          current_row_index,
          character_index
        );

        return this.stepBackwardByPath(-current_index, container, current_path);
      }
    }

    // 移出本cell
    // parent_container 是某个table的cell
    const previous_cell_index = getCellIndex(parent_container);

    if (previous_cell_index === 0) {
      // 跳出table
      const root_index = current_path[current_path.length - 4] - 1; // table上一个container必为row，table不能紧跟table

      const root_container = parent_container.parent!.parent.children[
        root_index
      ] as Row;

      const character_index = root_container.linebreak
        ? root_container.children.length + 1
        : root_container.children.length;

      current_path.splice(-4, 4, root_index, character_index);

      return this.stepBackwardByPath(
        -current_index,
        root_container,
        current_path
      );
    } else { // 移到上个cell的末尾
      const current_cell_index = previous_cell_index - 1;

      const current_cell = parent_container.parent!.children[
        current_cell_index
      ];

      const current_row_index = current_cell.children.length - 1;

      const current_row = current_cell.children[current_row_index] as Row;

      const character_index = current_row.linebreak
        ? current_row.children.length + 1
        : current_row.children.length;

      current_path.splice(
        -3,
        3,
        current_cell_index,
        current_row_index,
        character_index
      );

      return this.stepBackwardByPath(
        -current_index,
        current_row,
        current_path
      );
    }
  }

  /**
   * 根据路径计算向后移动x步的新路径，注意path要精确到row,在遇到表格后，在单元格中以从左到右，从上到下的方向前进
   * @param x
   * @param container
   * @param path
   */

  stepForwardByPath(x: number, container: Row, path: Path): Path {
    const current_path = [...path];
    // 如果已经移动到文档末尾则终止
    if (PathUtils.isEndInDocument(current_path, this.editor.current_cell)) {
      return current_path;
    }

    let current_index = current_path[current_path.length - 1] + x;

    let row_index = current_path[current_path.length - 2];

    const parent_container = container.parent;

    while (row_index < parent_container.children.length) {
      const current_container = parent_container.children[row_index];

      if (isRow(current_container)) {
        const character_length = current_container.linebreak
          ? current_container.children.length + 1
          : current_container.children.length;

        // 未移动出本行
        if (character_length - current_index > 0) {
          // 暂时先这样调整着 > >=
          current_path.splice(-2, 2, row_index, current_index);

          return current_path;
        } else { // 超出本行
          current_index = current_index - character_length;

          row_index += 1;
        }
      } else { // current_container 为 table
        current_path.splice(-2, 2, row_index, 0, 0, 0);

        container = current_container.children[0].children[0] as Row; // 不考虑嵌套表格，此处认为单元内部第一个元素只能为Row

        return this.stepForwardByPath(current_index, container, current_path);
      }
    }

    // 如果父容器为最后一个单元格
    if (isLastCell(parent_container)) {
      // 需要跳出table
      const table_index = parent_container.parent!.cell_index;

      container = parent_container.parent!.parent.children[
        table_index + 1
      ] as Row; // table后面必须跟一个row

      current_path.splice(-4, 4, table_index + 1, current_index);

      return this.stepForwardByPath(current_index, container, current_path);
    } else {
      const current_cell = getNextCell(parent_container)!;

      const current_cell_index = getCellIndex(current_cell);

      container = current_cell.children[0] as Row;
      current_path.splice(-3, 3, current_cell_index, 0, 0);
      return this.stepForwardByPath(current_index, container, current_path);
    }
  }

  /**
   * 方向键上下移动
   * @param moveType
   * @param container
   * @param path
   * @param caret 插入点 用于记录上一次光标所在位置
   */
  stepVerticalByPath(moveType: "up" | "down", path: Path, caret: Caret): Path {
    let current_path = [...path];
    // 获得当前行所在下标
    const outer_row_index = current_path[0];
    // 获取编辑器最外层容器
    const root_cell = this.editor.current_cell;
    // 当前所在的根级行容器
    const current_root_row = root_cell.children[outer_row_index];

    const offset_number = moveType === "up" ? -1 : 1;

    const next_container = root_cell.children[outer_row_index + offset_number];

    if (current_path.length === 2 && isTable(next_container) && this.editor.view_mode === "form") {
      if (moveType === "down") {
        for (let i = 0; i < next_container.children.length; i++) {
          const cell = next_container.children[i]
          const fields = cell.fields
          if (fields.length) {
            current_path = fields[0].start_para_path
            return current_path
          }
        }
      }else{
        for (let i = next_container.children.length-1; i >= 0; i--) {
          const cell = next_container.children[i]
          const fields = cell.fields
          if (fields.length) {
            current_path = fields[0].start_para_path
            return current_path
          }
        }
      }
    }

    // 判断当前光标所在行为普通行时
    if (isRow(current_root_row)) {
      if (caret.cache === null) {
        caret.cache = (current_root_row as Row).x_by(current_path[1]);
      }
      current_path = this.moveVerticalNext(
        current_path,
        next_container as Row | Table | Cell,
        caret.cache,
        moveType + "_in"
      );
    } else if (isTable(current_root_row)) {
      // current_root_row 为 table
      const current_cell = current_root_row.children[current_path[1]];
      // 获取到当前行下标
      let cell_row_index = current_path[2];

      const cell_row = current_cell.children[cell_row_index];

      let current_index = current_path[3];

      if (caret.cache === null) {
        caret.cache = current_cell.left + (cell_row as Row).x_by(current_index);
      }
      if (
        cell_row_index + offset_number >= 0 &&
        cell_row_index + offset_number < current_cell.children.length
      ) {
        // 单元格内移动
        cell_row_index = cell_row_index + offset_number;

        const next_cell_row = current_cell.children[cell_row_index];

        current_index = (next_cell_row as Row).arraw_offset_near(caret.cache);

        current_path.splice(-2, 2, cell_row_index, current_index);
      } else {
        // 跳出当前单元格
        const next_need_cell = nearOffsetCell(
          current_root_row.children,
          caret.cache,
          moveType,
          current_cell
        );

        if (next_need_cell) {
          // 进入下一个单元格

          const next_cell_row_index =
            moveType === "up" ? next_need_cell.children.length - 1 : 0;

          const cellIndex = getCellIndex(next_need_cell);

          current_index = (next_need_cell.children[
            next_cell_row_index
          ] as Row).arraw_offset_near(caret.cache);

          current_path = [
            outer_row_index,
            cellIndex,
            next_cell_row_index,
            current_index
          ];
        } else {
          // 进入页面下一行 （有可能仍然是表格  或者  是普通row）
          // if(this.editor.view_mode==="form") return current_path
          current_path = this.moveVerticalNext(
            current_path,
            next_container as Row | Table | Cell,
            caret.cache,
            moveType + "_in"
          );
        }
      }
    }

    return current_path;
  }

  /**
   * 光标上下移动根据下一个容器类型进行判断
   * @param current_path
   * @param next_container
   * @param caret_cache
   * @param moveType
   */
  moveVerticalNext(
    current_path: Path,
    next_container: Row | Table | Cell,
    caret_cache: number,
    moveType: any
  ): Path {
    const offset_number = moveType === "up_in" ? -1 : 1;

    if (isRow(next_container)) {
      const current_index = next_container.arraw_offset_near(caret_cache);

      current_path = [current_path[0] + offset_number, current_index];
    } else if (isTable(next_container)) {
      // 如果下一行为table

      const cell = nearOffsetCell(next_container.children, caret_cache, moveType);

      if (cell) {
        const next_cell_index =
          moveType === "up_in" ? cell.children.length - 1 : 0;
        // 不考虑嵌套表格
        const inner_container = cell.children[next_cell_index];

        const current_index = (inner_container as Row).arraw_offset_near(
          caret_cache
        );

        const cellIndex = getCellIndex(cell);

        current_path = [
          current_path[0] + offset_number,
          cellIndex,
          0,
          current_index
        ];
      }
    }
    return current_path;
  }

  /**
   * 获取当前focus位置下的row
   */

  getFocusRow(): Row {
    return this.getRowByPath(this.focus);
  }
  /**
   * 获取当前focus位置下的paragraph
   * @param root
   */

  getFocusParagraph(): Paragraph {
    return this.getParagraphByPath(this.para_focus);
  }

  getParagraphById(id: string) {
    const tblAndPara = this.editor.current_cell.paragraph;
    const para = tblAndPara.find(para => para.id === id);
    if (!para) {
      // 如果root_cell内没有找到段落  就去表格里边找
      for (const tblOrPara of tblAndPara) {
        if (isTable(tblOrPara)) {
          const cells = tblOrPara.children;
          for (const cell of cells) {
            const paras = cell.paragraph;
            const resPara = paras.find(p => p.id === id);
            if (resPara && isParagraph(resPara)) {
              return resPara;
            }
          }
        }
      }
    }
    if (isParagraph(para)) {
      return para;
    }
  }

  /**
   * 根据光标位置 获取 文本域
   * @param root 根cell
   * @returns 对应的文本域 和 null
   */
  getFocusField() {
    return this.getFieldByPath(this.para_focus);
  }

  /**
   * 根据坐标获取文本域
   */
  getFieldByPath(para_path: Path) {
    const focus_para = this.getParagraphByPath(para_path);
    if (!this.isCollapsed && !this.editor.internal.drag_in_path.length) {
      // 因为在拖拽的时候 会实时的走这个方法 拖拽的内容会一直是选区 然后 drag_in_path 里也有值 所以要 在 drag_in_path 没有值的时候进这个判断 有值的时候 根据路径，走下边的逻辑 应该就是对的
      // 拖拽的时候 para_path 一直是变化的 获取的就是虚拟光标 所在位置处的 field
      // label放在文本域里边 选中该label的时候 selection.focus不会在该label内 导致返回的文本域不对 特此修改
      const all_chars = this.selected_fields_chars.all_chars;
      const field = focus_para.cell.getFieldById(all_chars?.[0]?.field_id);
      if (field?.isCompleteField(all_chars)) {
        return field;
      }
    }
    const target_index = para_path[para_path.length - 1];
    const current_char = focus_para.characters[target_index];
    const previous_char = focus_para.characters[target_index - 1];
    let result_field: XField | null = null;
    // 当前段存在光标后一个字符
    if (current_char && current_char.field_id &&
      current_char.field_position !== "start") {
      result_field = focus_para.cell.getFieldById(current_char.field_id);
    } else if (previous_char && previous_char.field_id &&
      previous_char.field_position !== "end") {
      // 相邻两个字符的文本域id相同
      result_field = focus_para.cell.getFieldById(previous_char.field_id);
    } else if (current_char.field_id && previous_char &&
      current_char.field_position === "start" &&
      previous_char.field_position === "end") {
      result_field = focus_para.cell.getFieldById(current_char.field_id)!.parent;
    } else if (current_char?.field_position === "start") {
      // 如果前边根据路径都没有找到文本域 那么看看他的父级是不是个文本域 有可能光标在父级的文本域里边
      const parentField = focus_para.cell.getFieldById(current_char.field_id)?.parent;
      if (parentField) {
        result_field = parentField;
      }
    }
    return result_field;
  }

  /**
   * 获取光标位置处的表格
   */
  getFocusTable(): Table | undefined {
    const table = this.getFocusRow()?.parent?.parent;
    if (isTable(table)) {
      return table;
    }
  }

  getFocusCell(): Cell | undefined {
    const cell = this.getFocusRow()?.parent;
    if (isCell(cell) && isTable(cell?.parent)) {
      return cell;
    }
  }

  getFocusGroup() {
    let focus_group: Group | null = null;
    const paragraph: Paragraph = this.getFocusParagraph();
    let para_table: any = paragraph;
    if (paragraph.cell.parent) {
      para_table = paragraph.cell.parent;
    }
    if (para_table.group_id && this.editor.root_cell.groups.length) {
      for (let i = 0; i < this.editor.root_cell.groups.length; i++) {
        const group = this.editor.root_cell.groups[i];
        if (group.id === para_table.group_id) {
          focus_group = group;
          break;
        }
      }
    }
    return focus_group;
  }

  getGroupByGroupId(id: string) {
    for (let i = 0; i < this.editor.root_cell.groups.length; i++) {
      const group = this.editor.root_cell.groups[i];
      if (group.id === id) {
        return group;
      }
    }
  }

  getGroupsByName(name: string) {
    const result = []
    for (let i = 0; i < this.editor.root_cell.groups.length; i++) {
      const group = this.editor.root_cell.groups[i];
      if (group.name === name) {
        result.push(group);
      }
    }
    return result;
  }

  /**
   * 获取 path 下的row
   * @param path
   * @param container
   */

  getRowByPath(path: Path): Row {
    const current_path = [...path];
    let index = current_path.shift()!;
    let container: Cell | Table | Row = this.editor.current_cell;
    let result!: Row;

    while (current_path.length) {
      container = container.children[index] as Table | Cell | Row;

      if (isRow(container)) {
        result = container;
        break;
      }

      index = current_path.shift()!;
    }
    return result;
  }

  /**
   * 根据modelPath获取最深层cell，此处path精确到char
   * @param path
   * @param container
   */
  getDeepCellByPath(
    path: Path,
    container: Cell | Table = this.editor.current_cell
  ): Cell | undefined {
    while (path.length > 2) {
      const current_index = path.shift()!;

      const current_container = container.children[current_index];

      if (path.length === 2 && isCell(current_container)) {
        return current_container;
      }
      if (isTable(current_container) || isCell(current_container)) {
        return this.getDeepCellByPath(path, current_container);
      }
    }
  }

  /**
   * 获取 para_path 下的Paragraph
   * @param path
   * @param container
   */
  getParagraphByPath(para_path: Path): Paragraph {
    const container = this.editor.current_cell;
    let cell: Cell;
    if (PathUtils.isTablePath(para_path)) {
      const table = container.paragraph[para_path[0]];
      cell = table.children[para_path[1]] as Cell;
    } else {
      cell = container;
    }
    return cell.paragraph[para_path[para_path.length - 2]] as Paragraph;
  }

  /**
 * 根据para_path获取元素方法
 */
  getElementByParaPath(para_path: Path) { // TODO 没什么卵用 base vue demo 层都没有搜到谁用它了
    let element;
    if (PathUtils.isTablePath(para_path)) {
      element = (this.editor.current_cell.paragraph[para_path[0]] as Table).children[para_path[1]].children[para_path[2]].children[para_path[3]];
    } else {
      element = (this.editor.current_cell.paragraph[para_path[0]] as Paragraph).characters[para_path[1]];
    }
    if (element) {
      return element;
    }
  }

  /**
   * 获取选区最上下左右的cell
   * @returns 四个方向的cell集合
   */
  getMaxMinCell() {
    const cells = this.selected_cells.length ? this.selected_cells : null;
    if (!cells) return null;
    // 最底部的单元格
    const max_row_cells: Cell[] = [];
    // 最顶部的单元格
    const min_row_cells: Cell[] = [];
    // 最左边的单元格
    const min_col_cells: Cell[] = [];
    // 最右边的单元格
    const max_col_cells: Cell[] = [];
    // 循环比较行坐标
    for (let i = 0; i < cells.length; i++) {
      const cell = cells[i].cell;
      const table = cell.getOrigin().parent?.getOrigin();
      if (!table) continue;
      // 数组空的 或者 数组中第一个 行坐标 等于当前行的横坐标，那么就是最顶的
      if (!min_row_cells[0] || cell.position[0] === min_row_cells[0].position[0]) {
        // 替换掉数组中的内容
        min_row_cells.push(cell);
        // 小于了 那就说明数组中的cell并不是选区最上部的cell
      } else if (cell.position[0] < min_row_cells[0].position[0]) {
        min_row_cells.length = 0;
        min_row_cells.push(cell);
      }
      // 数组空的 或者 数组中第一个 行 等于当前行的横坐标，那么就是最底的
      if (!max_row_cells[0] || (cell.position[0] + cell.rowspan) === (max_row_cells[0].position[0] + max_row_cells[0].rowspan)) {
        // 放到数组中
        max_row_cells.push(cell);
        // 大于了 那就说明数组中的cell并不是选区最下部的cell
      } else if (cell.position[0] + cell.rowspan > max_row_cells[0].position[0] + max_row_cells[0].rowspan) {
        max_row_cells.length = 0;
        max_row_cells.push(cell);
      }
      // 数组空的 或者 数组中第一个 列坐标 等于当前行的列坐标，那么就是最左边的
      if (!min_col_cells[0] || cell.position[1] === min_col_cells[0].position[1]) {
        // 放到数组中
        min_col_cells.push(cell);
        // 大于了 那就说明数组中的cell并不是选区最左边的cell 需要替换掉
      } else if (cell.position[1] < min_col_cells[0].position[1]) {
        min_col_cells.length = 0;
        min_col_cells.push(cell);
      }
      if ((cell.position[1] + cell.colspan) === table.col_size.length) {
        max_col_cells.push(cell);
      }
    }
    return { max_col_cells, min_col_cells, min_row_cells, max_row_cells };
  }

  /**
   * 获取选区所有的线
   * @returns 横线竖线的集合
   */
  getSelectedAllLine() {
    const cells = this.selected_cells.length ? this.selected_cells : null;
    const row_lines: number[][] = [];
    const col_lines: number[][] = [];
    cells!.forEach((e) => {
      row_lines.push(...e.cell.getNumbersOfFourLine().bottom);
      row_lines.push(...e.cell.getNumbersOfFourLine().top);
      col_lines.push(...e.cell.getNumbersOfFourLine().left);
      col_lines.push(...e.cell.getNumbersOfFourLine().right);
    });
    return { row_lines, col_lines };
  }

  /**
   * 选区的最外层框线
   * @returns 最外层横线竖线的集合
   */
  getSelectedOutLine() {
    const selected_cells = this.getMaxMinCell()!;
    const row_lines: number[][] = [];
    const col_lines: number[][] = [];
    // 获取边线
    selected_cells.max_col_cells.forEach((cell: Cell) => {
      // 右边线
      col_lines.push(...cell.getNumbersOfFourLine().right);
    });
    selected_cells.min_col_cells.forEach((cell: Cell) => {
      // 左边线
      col_lines.push(...cell.getNumbersOfFourLine().left);
    });
    selected_cells.min_row_cells.forEach((cell: Cell) => {
      // 上边线
      row_lines.push(...cell.getNumbersOfFourLine().top);
    });
    selected_cells.max_row_cells.forEach((cell: Cell) => {
      // 下边线
      row_lines.push(...cell.getNumbersOfFourLine().bottom);
    });
    return { row_lines, col_lines };
  }

  /**
   * 获取选区结构化数据
   */
  getSelection() {
    return {
      anchor: [...this.anchor],
      focus: [...this.focus],
      selected_areas: [...this.selected_areas],
      cell_path: [...this.selected_cells_path]
    };
  }

  // 将font转为style字符串
  convertFont2StyleStr(font: Font): String {
    return `background-color: ${font.bgColor};${font.bold ? "font-weight: bold;" : ""}color: ${font.color};${font.dblUnderLine ? "border-bottom:3px double black;" : ""}font-family: ${font.family};font-size: ${font.height * (3 / 4)}pt;${font.italic ? "font-style: italic;" : ""}${font.strikethrough ? "text-decoration:line-through;" : ""}${font.underline ? "text-decoration: underline;" : ""}`;
  }

  // 复制
  async copyData(is_drag: boolean) {
    let selectionData = [];
    if (this.editor.internal.isMultipleSelection) {
      for (let i = this.multipleSelected.length - 1; i >= 0; i--) {
        const current = this.multipleSelected[i];
        this.clearSelectedInfo();
        this.setSelectionByPath(current.start, current.end, "para_path", 0, false);
        const data = saveSelectionData(this.editor, current);
        if (!data || !data.length) continue;
        if(this.selected_fields_chars.fieldIdVsChars){
          const fieldId = Object.keys(this.selected_fields_chars.fieldIdVsChars)
          fieldId.forEach((id)=>{
            const field = this.editor.getFieldById(id)
            if(field&&!field.canBeCopied){
              this. editor.event.emit("message", "选区中存在不可复制内容！");
            }
          })
        }
        Array.isArray(data) && selectionData.push(...data);
      }
    } else {
      const data = saveSelectionData(this.editor);
      if (!data || !data.length) return;
      if(this.selected_fields_chars.fieldIdVsChars){
        const fieldId = Object.keys(this.selected_fields_chars.fieldIdVsChars)
        fieldId.forEach((id)=>{
          const field = this.editor.getFieldById(id)
          if(field&&!field.canBeCopied){
            this. editor.event.emit("message", "选区中存在不可复制内容！");
          }
        })
      }
      Array.isArray(data) && selectionData.push(...data);
    }
    const rawData = this.editor.event.emit("selectionData2RawData", selectionData);
    // ↓ 处理样式
    const res: any = {};
    const fontMap = this.editor.fontMap.get();
    for (const [k, v] of fontMap.entries()) {
      const obj = { ...v };
      res[k] = obj;

      // 这里删除 复制出来的数据 就不带临时样式了 估计是因为 再次 fontMap.add 的时候 判断了 样式是否一致 重新生成了 fontId
      delete res[k].temp_valid_color; // 可以直接 Delete obj.temp_valid_color
      delete res[k].temp_word_bgColor;
      delete res[k].temp_word_color;
    }
    const fontStr = Config.font_map_flag + JSON.stringify(res) + Config.font_map_flag; // 首尾都要有 方便粘贴的时候好取
    // ↑ 处理样式
    const hasLastLinebreak = this.editor.selection.hasLastLinebreak;
    let str = JSON.stringify(rawData);
    str += fontStr;
    if (hasLastLinebreak) {
      str += Config.program_flag_end;
    }
    const resStr = str;

    const base64Data = encodeBase64(encodeURIComponent(resStr)); // 转base64
    if (is_drag) { // 如果是拖动导致的复制 就把数据存在editor里边
      this.editor.internal.drag_data = Config.program_flag_start + caesarCipher(base64Data, Config.caesar_shift);
    } else {
      let html = `<pre editor-msun-copy-data="editor-msun-copy-slice${Config.program_flag_start + caesarCipher(base64Data, Config.caesar_shift)}editor-msun-copy-slice" data-self-end="end">`; // 特殊标记 说明是复制的文档内的内容
      let plainStr = ""; // 纯文本
      // if (EditorLocalTest.useLocal) {
      html += this.editor.event.emit("modelData2Html", selectionData);
      for (let i = 0; i < selectionData.length; i++) {
        const tblOrPara = selectionData[i];
        plainStr += tblOrPara.getStr(true);
      }
      // } else {
      //   for (let i = 0; i < selectionData.length; i++) {
      //     const tblOrPara = selectionData[i];
      //     plainStr += tblOrPara.getStr(true);
      //   }
      //   html += `<span>` + plainStr + `</span>`;
      // }
      html += `</pre>`;
      await writeClipboard(plainStr, html);
    }
    if (this.editor.internal.isMultipleSelection) {
      this.clearSelectedInfo();
    }
    this.editor.focus();
  }

  copy() {
    if (this.editor.selection.isCollapsed) {
      // 不是选区
      return;
    }
    this.copyData(false);
    this.editor.event.emit("exeCommand", { command: "copy" }, event);
  }

  /**
 * 获取选区纯文本的方法
 */
  get text(): string {
    if (this.isCollapsed) {
      return "";
    } else {
      const { all_chars } = this.selected_fields_chars;
      const texts = all_chars.map((char: any) => char.value ?? "");
      return texts.join("");
    }
  }

  // 粘贴
  cutData() {
    this.editor.delete_backward();
  }

  /**
   * 根据路径设置选区（只设置文本选区）
   * @param start_path :开始路径坐标
   * @param end_path : 结束路径坐标
   * @param path_type : 坐标类型
   * @param x : 光标偏移
   */
  setSelectionByPath(start_path: Path, end_path: Path, path_type: "para_path" | "model_path" = "para_path", x: number = 0, isRemoveDuplicate: boolean = true) {
    // 清空选区信息
    this.clearSelectedInfo(isRemoveDuplicate);
    // 此时的 start_path 是鼠标初始按下的位置 end_path 是一直变化的位置

    let initStartParaPath = path_type === "model_path" ? this.editor.modelPath2ParaPath([...start_path]) : [...start_path];
    let initEndParaPath = path_type === "model_path" ? this.editor.modelPath2ParaPath([...end_path]) : [...end_path];
    let startParaPath = initStartParaPath;
    let endParaPath = initEndParaPath;
    if (startParaPath[0] > endParaPath[0]) {
      startParaPath = initEndParaPath;
      endParaPath = initStartParaPath;
    } else if (startParaPath[0] === endParaPath[0]) {
      if (startParaPath[1] > endParaPath[1]) {
        startParaPath = initEndParaPath;
        endParaPath = initStartParaPath;
      }
      if (startParaPath[1] === endParaPath[1] && startParaPath.length > 2) {
        // 只判断开始 是表格里边就可以了 结束也必然在表格里边 因为这里边的 都是外边同一个段落的 也就是近这里边的 必然是在同一个单元格里
        if (startParaPath[2] > endParaPath[2]) {
          startParaPath = initEndParaPath;
          endParaPath = initStartParaPath;
        }
        if (startParaPath[2] === endParaPath[2]) {
          // 在同一个单元格里边的 同一个段落里边了
          if (startParaPath[3] > endParaPath[3]) {
            startParaPath = initEndParaPath;
            endParaPath = initStartParaPath;
          }
        }
      }
    }

    
    if (path_type === "model_path") {
      isRemoveDuplicate && this.handleOverlapping(startParaPath, endParaPath);
      this.anchor = start_path;
      this.focus = end_path;
    } else {
      isRemoveDuplicate && this.handleOverlapping([...startParaPath], [...endParaPath]);
      this.anchor = this.editor.paraPath2ModelPath(start_path);
      this.focus = this.editor.paraPath2ModelPath(end_path);
    }

    let cur_cell: Cell;
    // 单元格内行选区
    if (PathUtils.isSameCell(this.start, this.end)) {
      // 此处进行行内选择，处理单个单元格内文本选中
      start_path = this.start.slice(2, 4);
      end_path = this.end.slice(2, 4);
      cur_cell = this.getDeepCellByPath([...this.start])!;
    } else {
      cur_cell = this.editor.current_cell;
      start_path = this.start;
      end_path = this.end;
    }
    // 如果传入的开始坐标为2位，但是在cell中对应的是一个表格，则补全坐标
    if (!PathUtils.isTablePath(start_path) && isTable(cur_cell.children[start_path[0]])) {
      this.anchor = start_path = start_path.concat([0, 0]);
    }
    this.saveSelectionArea(start_path, end_path, cur_cell, x);
    // 放到 saveSelectionArea 后边是要用到里边保存的数据判断新选区在哪儿
    if (path_type === "model_path") {
      this.handleOverlapping(startParaPath, endParaPath);
    } else {
      isRemoveDuplicate && this.handleOverlapping([...startParaPath], [...endParaPath]);
    }
  }

  // 处理重叠
  handleOverlapping(start_path: Path, end_path: Path) {
    if (!this.editor.internal.isMultipleSelection) return
    let [ ns1, ns2, ns3, ns4 ] = start_path;
    let [ ne1, ne2, ne3, ne4 ] = end_path;
    for (let i = 0; i < this.multipleSelected.length; i++) {
      const currentSelecion = this.multipleSelected[i];
      // 1. 既有表格又有段落的
      if (currentSelecion.selectedAreas.length && currentSelecion.selectedCellPath.length) {
        const paraFirst = currentSelecion.selectedAreas[0].start_para_path;
        const paraLast = currentSelecion.selectedAreas[currentSelecion.selectedAreas.length - 1].end_para_path;
        const cellFirst = currentSelecion.selectedCellPath[0];
        const cellLast = currentSelecion.selectedCellPath[currentSelecion.selectedCellPath.length - 1];
        const first = paraFirst[0] < cellFirst[0] ? paraFirst : cellFirst;
        const last = paraLast[0] > cellLast[0] ? paraLast : cellLast;
        
        
        const [ os1, os2, os3, os4 ] = first;
        const [ oe1, oe2, oe3, oe4 ] = last;
        // 此时是既有表格 又有段落 只能是表格级别的 不可能是单元格里边的
        
        if ((ns1 > os1 && ns1 < oe1) || (ne1 > os1 && ne1 < oe1)) {
          // 在两个段落中间了 肯定是覆盖了 其实这个情况是遇不到的 因为在选区碰到的时候 一定会先经过开始段落或者结束段落 下边的判断罗就就会先走 不会进来这里的 但是留着吧
          this.multipleSelected.splice(i, 1);
          this.editor.render();
          return
        }

        if (ns1 === oe1) {
          // 如果新选区的开始跟旧选区的结束在同一个段落 就可能覆盖 
          // 会有几种情况
          // 1. 跟旧选区的开始段落也是同一个
          if (ns1 === os1) {
            // 此时就应该判断字了
            if (ns2 > os2 && ns2 < oe2) {
              this.multipleSelected.splice(i, 1);
              this.editor.render();
              return
            }
          }
          // 2. 新选区的开始段落大于旧选区的开始段落
          if (ns1 > os1) {
            if (ns2 < oe2) {
              this.multipleSelected.splice(i, 1);
              this.editor.render();
              return
            }
          }
          // 3. 如果旧的结束是个表格
          if (last.length > 2) {
            this.multipleSelected.splice(i, 1);
            this.editor.render();
            return
          }
        }
        if (ne1 === os1) {
          // 1. 跟旧选区的结束段落也是同一个 这个情况不会出现 因为有表格
          if (ne1 === oe1) {
            // 此时就应该判断字了
            if (ne2 > os2 && ne2 < oe2) {
              this.multipleSelected.splice(i, 1);
              this.editor.render();
              return
            }
          }
          // 2. 新选区的结束 比旧选区的结束小
          if (ne1 < oe1) {
            if (ne2 > os2) {
              this.multipleSelected.splice(i, 1);
              this.editor.render();
              return
            }
          }
          // 3. 如果旧的开始是个表格
          if (first.length > 2) {
            this.multipleSelected.splice(i, 1);
            this.editor.render();
            return
          }
        }
      }

      // 2. 只有段落的
      // 那就又有两种情况 1. 在表格里边 2. 在表格外边
      if (currentSelecion.selectedAreas.length && !currentSelecion.selectedCellPath.length) {
        const first = currentSelecion.selectedAreas[0].start_para_path;
        const last = currentSelecion.selectedAreas[currentSelecion.selectedAreas.length - 1].end_para_path;
        let [ os1, os2, os3, os4 ] = first;
        let [ oe1, oe2, oe3, oe4 ] = last;

        // 旧选区在表格里边
        if (first.length > 2) {
          // 1. 新选区也是只有段落 且跟当前段落在同一个单元格里边 这种情况就走下边当做在表格外边处理
          
          // 2. 新选区只有单元格会包含旧选区所在的单元格
          if (this.selected_cells_path.length && !this.selected_areas.length) {
            // 我就判断是否单元格重了就可以了
            // 先判断表格 其他表格的就不用循环了
            const currentTableIndex = this.selected_cells_path[0][0];
            if (os1 === currentTableIndex) {
              for (let m = 0; m < this.selected_cells_path.length; m++) {
                const cellIndex = this.selected_cells_path[m][1];
                if (cellIndex === os2) {
                  this.multipleSelected.splice(i, 1);
                  this.editor.render();
                  i--;
                  break;
                }
              }
            }
          }
          // 3. 新选区有表格外的段落又选中了表格 包含了若干次选区的内容 所以这个情况不能 return 掉
          if (this.selected_cells_path.length && this.selected_areas.length) {
            // 新选区既有单元格又有段落的时候
            // 我要找到这次选区里边有几个表格 这个表格里边的所有旧选区都要删除掉
            const tableIndex = [];
            for (let m = start_path[0]; m <= end_path[0]; m++) {
              if (isTable(this.editor.current_cell.paragraph[m])) {
                tableIndex.push(m);
              }
            }
            for (let m = 0; m < tableIndex.length; m++) {
              if (tableIndex[m] === first[0]) {
                this.multipleSelected.splice(i, 1);
                this.editor.render();
                i--;
              }
            }
          }
          if (first.length > 2 && start_path.length > 2 && start_path[0] === first[0] && start_path[1] === first[1]) {
            // 这种情况下 跟外边逻辑一致
            ns1 = ns3;
            ns2 = ns4;
            ne1 = ne3;
            ne2 = ne4;
  
            os1 = os3;
            os2 = os4;
            oe1 = oe3;
            oe2 = oe4;
          }
        } else if (start_path.length > 2 && end_path.length > 2) {
          // 旧选区不在表格里边 但是新选区都在表格里边 所以就没必要走了 因为不可能重叠

          continue;
        }

        // 在表格外边
        // 如果新选区的开始跟旧选区的结束在同一个段落 就可能覆盖 
        // 段落都被包含了
        if (ns1 < oe1 && ne1 > oe1) {
          this.multipleSelected.splice(i, 1);
          this.editor.render();
          return
        }

        if (ns1 < os1 && ne1 > os1) {
          this.multipleSelected.splice(i, 1);
          this.editor.render();
          return
        }
        
        if (ns1 === oe1) {
          // 会有几种情况
          // 1. 跟旧选区的开始段落也是同一个
          if (ns1 === os1) {
            // 此时就应该判断字了
            if ((ns2 > os2 && ns2 < oe2) || (ns2 <= os2 &&ne2 > os2)) {
              this.multipleSelected.splice(i, 1);
              this.editor.render();
              return
            }
          }
          // 2. 新选区的开始段落大于旧选区的开始段落
          if (ns1 > os1) {
            if (ns2 < oe2) {
              this.multipleSelected.splice(i, 1);
              this.editor.render();
              return
            }
          }
        }
        // 如果新选区的结束跟旧选区的开始在同一个段落 就可能覆盖 
        if (ne1 === os1) {
          // 会有几种情况
          // 1. 跟旧选区的结束段落也是同一个
          if (ne1 === oe1) {
            // 此时就应该判断字了
            if (ne2 > os2 && ne2 < oe2) {
              this.multipleSelected.splice(i, 1);
              this.editor.render();
              return
            }
          }
          // 2. 新选区的结束段落小于旧选区的结束段落
          if (ne1 < oe1) {
            if (ne2 > os2) {
              this.multipleSelected.splice(i, 1);
              this.editor.render();
              return
            }
          }
        }
        if (ns1 < oe1 && ne1 === oe1 && ne2 > os2) {
          this.multipleSelected.splice(i, 1);
          this.editor.render();
          return
        }
        if (ne1 > os1 && ns1 === os1 && ns2 < os2) {
          this.multipleSelected.splice(i, 1);
          this.editor.render();
          return
        }
      }

      // 3. 只有单元格的情况
      if (!currentSelecion.selectedAreas.length && currentSelecion.selectedCellPath.length) {
        console.log("这是只有单元格的选区, 肯定在表格里边");
        // 不管是开始还是结束 只要跟任何一个单元格重复了就该干掉
        for (let j = 0; j < currentSelecion.selectedCellPath.length; j++) {
          const cellPath = currentSelecion.selectedCellPath[j];
          if (ns1 === cellPath[0] && ns2 === cellPath[1] && start_path.length > 2) {
            this.multipleSelected.splice(i, 1);
            this.editor.render();
            return
          }
          if (ne1 === cellPath[0] && ne2 === cellPath[1] && end_path.length > 2) {
            this.multipleSelected.splice(i, 1);
            this.editor.render();
            return
          }
          if (start_path.length !== end_path.length) {
            // 说明新选区既有表格 又有外边的段落
            if (cellPath[0] >= start_path[0] || cellPath[0] <= end_path[0]) {
              this.multipleSelected.splice(i, 1);
              this.editor.render();
              return
            }
          }
        }
      }
      
      // for (let j = 0; j < currentSelecion.selectedAreas.length; j++) {
      //   const { start_para_path, end_para_path } = currentSelecion[j];
      //   const [ os1, os2, os3, os4 ] = start_para_path;
      //   const [ oe1, oe2, oe3, oe4 ] = end_para_path;
      //   const [ ns1, ns2, ns3, ns4 ] = newSelectionStartParaPath;
      //   const [ ne1, ne2, ne3, ne4 ] = newSelectionEndParaPath;
      //   if ((ns1 > os1 && ns1 < oe1) || (ne1 > os1 && ne1 < oe1)) {
      //     // 在两个段落中间了 肯定是覆盖了 其实这个情况是遇不到的 因为在选区碰到的时候 一定会先经过开始段落或者结束段落 下边的判断罗就就会先走 不会进来这里的 但是留着吧
      //     this.multipleSelected.splice(i, 1);
      //     this.editor.render();
      //     return
      //   }
      //   // 有可能老选区是多个段 也有可能是就在一个段里边的小选区 要考虑这两种甚至更多的情况
      //   if (ns1 === oe1) {
      //     // 如果新选区的开始跟旧选区的结束在同一个段落 就可能覆盖 
      //     // 会有几种情况
      //     // 1. 跟旧选区的开始段落也是同一个
      //     if (ns1 === os1) {
      //       // 此时就应该判断字了
      //       if (ns2 > os2 && ns2 < oe2) {
      //         this.multipleSelectedAreas.splice(i, 1);
      //         this.editor.render();
      //         return
      //       }
      //     }
      //     // 2. 新选区的开始段落大于旧选区的开始段落
      //     if (ns1 > os1) {
      //       if (ns2 < oe2) {
      //         if (this.multipleSelectedAreas[i].length > 1) {
      //           const first = this.multipleSelectedAreas[i][0];
      //           const last = this.multipleSelectedAreas[i][this.multipleSelectedAreas[i].length - 1];
      //           // 说明当前的这个选区 包含了表格 那么删除的时候就要把表格也删除掉
      //           for (let m = 0; m < this.multipleSelectedCellsPath.length; m++) {
      //             const firstCellPath = this.multipleSelectedCellsPath[m][0];
      //             // TODO 如果开始或者结束就是表格怎么办 暂时先不考虑
      //             if (firstCellPath[0] > first.end_para_path[0] && firstCellPath[0] < last.start_para_path[0]) {
      //               this.multipleSelectedCellsPath.splice(m , 1);
      //               m--;
      //             }
      //           }
      //         }
              
      //         this.multipleSelectedAreas.splice(i, 1);
      //         this.editor.render();
      //         return
      //       }
      //     }
      //   }
      //   if (ne1 === os1) {
      //     // 如果新选区的结束跟旧选区的开始在同一个段落 就可能覆盖 
      //     // 会有几种情况
      //     // 1. 跟旧选区的结束段落也是同一个
      //     if (ne1 === oe1) {
      //       // 此时就应该判断字了
      //       if (ne2 > os2 && ne2 < oe2) {
      //         this.multipleSelectedAreas.splice(i, 1);
      //         this.editor.render();
      //         return
      //       }
      //     }
      //     if (ne1 < oe1) {
      //       if (ne2 > os2) {
      //         if (this.multipleSelectedAreas[i].length > 1) {
      //           const first = this.multipleSelectedAreas[i][0];
      //           const last = this.multipleSelectedAreas[i][this.multipleSelectedAreas[i].length - 1];
      //           // 说明当前的这个选区 包含了表格 那么删除的时候就要把表格也删除掉
      //           for (let m = 0; m < this.multipleSelectedCellsPath.length; m++) {
      //             const firstCellPath = this.multipleSelectedCellsPath[m][0];
      //             // TODO 如果开始或者结束就是表格怎么办 暂时先不考虑
      //             if (firstCellPath[0] > first.end_para_path[0] && firstCellPath[0] < last.start_para_path[0]) {
      //               this.multipleSelectedCellsPath.splice(m , 1);
      //               m--;
      //             }
      //           }
      //         }
      //         this.multipleSelectedAreas.splice(i, 1);
      //         this.editor.render();
      //         return
      //       }
      //     }
      //   }
      // }
    }
  }

  /**
   * 记录普通文本行选区
   * @param start_path
   * @param end_path
   * @param container 除了在单个单元格内选区以外 container 都是 editor.current_cell
   * @param 横向轴光标坐标
   */
  saveSelectionArea(
    start_path: Path,
    end_path: Path,
    container: Cell,
    x: number
  ) {
    let last_path = start_path;
    let prefix_path: Path = [];

    let newSelectionStartParaPath: any = [];
    let newSelectionEndParaPath: any = [];
    for (let i = start_path[0]; i <= end_path[0]; i++) { // 循环选中的所有段落
      const row = container.children[i];
      if (isTable(row)) {
        // 遇到表格 然后判断结束坐标是否在表格内，
        // 如果不再则将该表格所有单元格获取存储，如果结束坐标在则判断结束坐标在第几行，将该行以上单元格存储
        this.saveTableCells(row, i, start_path, end_path);
      }
      const next_row = container.children[i + 1];
      const previous_row = container.children[i - 1];
      let last_linebreak;
      // 记录该区域最后一行是否包含换行符（只适合自上而下选中）
      if (this.focus[0] >= this.anchor[0]) {
        if (isRow(row) && row.linebreak && (x >= row.linebreak.right)) {
          last_linebreak = true;
        }
      }
      if (
        previous_row &&
        isTable(previous_row) &&
        i - 1 >= start_path[0]
      ) {
        last_path = [i, 0];
        if (end_path[1] === row.children.length) {
          last_linebreak = true;
        }
      }
      if (next_row && isTable(next_row) && i + 1 <= end_path[0]) {
        last_linebreak = true;
        if (isRow(row)) {
          newSelectionStartParaPath = this.editor.modelPath2ParaPath(last_path);
          newSelectionEndParaPath = this.editor.modelPath2ParaPath([i, row.children.length]);
          this.selected_areas.push({
            start_para_path: newSelectionStartParaPath,
            end_para_path: newSelectionEndParaPath,
            last_linebreak: last_linebreak
          });
          last_path = [i + 2, 0];
        } else {
          // 如果两个表格间没有空行，则不能选中，否则其他复制、粘贴等会报错
          return;
        }
      }
      // 如果是单元格内
      if (isTable(container.parent)) {
        prefix_path = [this.focus[0], this.focus[1]];
      }
      if (i === end_path[0] && end_path.length === 2) {
        newSelectionStartParaPath = this.editor.modelPath2ParaPath(
          prefix_path.concat(last_path)
        );
        newSelectionEndParaPath = this.editor.modelPath2ParaPath(prefix_path.concat(end_path));
        this.selected_areas.push({
          start_para_path: newSelectionStartParaPath,
          end_para_path: newSelectionEndParaPath,
          last_linebreak: last_linebreak
        });
      }
    }
  }

  /**
   * 存储表格单元格 路径
   * @param table
   * @param current_index
   * @param start_path
   * @param end_path
   */
  saveTableCells(
    table: Table,
    current_index: number,
    start_path: number[],
    end_path: number[]
  ) {
    // 同一表格
    if (PathUtils.isSameTable(start_path, end_path)) {
      this.clearSelectedInfo();
      /*
       * 根据开始路径与结束路径得到开始时单元格与结束时单元格，且获得到开始行与结束行单元格所在的行与列。
       */
      const start_cell = table.children[start_path[1]];
      const end_cell = table.children[end_path[1]];
      const start_row_index = start_cell.position[0];
      const end_row_index = end_cell.position[0];
      const start_col_index = start_cell.position[1];
      const end_col_index = end_cell.position[1];

      for (let i = 0; i < table.children.length; i++) {
        const cell = table.children[i];
        /*
         * 按照wps行为逻辑
         * 1、行列在范围内
         * 2、左边距与右边距在区间内
         * 3、右边距大于结束单元格左边距并且左边距小于开始单元格的左边距（因为开始结束坐标是按照单元格下标顺序，合并单元格会出现开始单元格在结束单元格的右侧）
         * 4、右边距小于开始单元格左边距并且左边距大于结束单元格右边距
         */
        if (
          cell.position[0] >= start_row_index &&
          cell.position[0] <= end_row_index &&
          ((cell.position[1] >= start_col_index &&
            cell.position[1] <= end_col_index) ||
            (cell.position[1] >= end_col_index &&
              cell.position[1] <= start_col_index) ||
            (cell.left >= start_cell.left && cell.right <= end_cell.right) ||
            (cell.right > end_cell.left && cell.left < start_cell.left) ||
            (cell.right < start_cell.left && cell.left > end_cell.right))
        ) {
          this.selected_cells_path.push(this.editor.modelPath2ParaPath([current_index, i, 0, 0]));
        }
      }
    } else {
      // 保存该表格中所有单元格
      const cells = table.children;
      for (let i = 0; i < cells.length; i++) {
        this.selected_cells_path.push(this.editor.modelPath2ParaPath([current_index, i, 0, 0]));
      }
    }
  }

  private _anchor: number[];

  private _focus: number[];
}
