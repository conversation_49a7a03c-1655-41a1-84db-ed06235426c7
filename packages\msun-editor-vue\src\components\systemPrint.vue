<template>
  <a-modal
    class="systemModal"
    v-model="visible"
    :width="previewWidth"
    :mask="masks"
    :footer="null"
    :maskClosable="false"
    @cancel="cancel"
    destroyOnClose
  >
    <div slot="title" class="title_system_print">
      <a-button class="editor-maxsize-btn" @click="changeWindowSize">
        <icon-common :icon="maxSizeBtn" style="cursor: pointer"></icon-common>
      </a-button>
      <div style="font-size: 14px">{{ title }}</div>
    </div>
    <div class="modal_system_print" :style="maxSizeStyle">
      <div
        class="modal-left msun-editor-forms-scrollbar"
        ref="printLeft"
        v-if="!loading"
      >
        <div
          v-for="(item, index) in pageSrc"
          :key="index"
          ref="printImg"
          class="page_system_print"
        >
          <img :src="item" alt="" class="img" />
        </div>
        <div class="editor-page-mention">{{ pageMention }}</div>
      </div>
      <div class="modal-left-loading" v-if="loading">
        <div class="loading-div" v-if="loadingError">
          <div>
            浏览器版本过低，部分功能不可用，可直接点击【打印】按钮全部打印
          </div>
        </div>
        <div class="loading-div" v-else>
          <div>加载中</div>
          <div class="box1">.</div>
          <div class="box2">.</div>
          <div class="box3">.</div>
        </div>
      </div>
      <div class="modal-right msun-editor-forms-scrollbar">
        <div class="print-title-top">
          <span>选择打印机</span>
          <a-button
            style="margin-top: 3px; margin-left: 50px"
            type="primary"
            size="small"
            @click="updatePrinterList"
            >刷新列表</a-button
          >
        </div>
        <div class="select-print">
          <a-select
            style="width: 100%"
            dropdownClassName="xeditor-input-up"
            @change="changePrinter"
            :value="selectPrinter"
          >
            <a-select-option v-for="(item, index) in printerList" :key="index"
              >{{ item.printerName }}
            </a-select-option>
          </a-select>
        </div>
        <a-alert
          v-if="printerList.length === 0"
          message="获取打印机列表超时,请检查托盘是否正常启动。"
          type="error"
          show-icon
        />
        <div class="print-list msun-editor-forms-scrollbar" ref="printList">
          <div class="print-order">
            <span class="print-title">打印份数</span>
            <a-input-number
              class="inputNumber"
              v-model="printMultiple"
              :min="1"
              :max="100"
              @change="changePrintMultiple"
            />
          </div>
          <div class="print-order">
            <span class="print-title">打印顺序</span>
            <a-radio-group
              style="width: 100%"
              v-model="sortValue"
              :default-value="1"
            >
              <a-radio :value="1">正序</a-radio>
              <a-radio :value="2">逆序</a-radio>
            </a-radio-group>
          </div>

          <div>
            <span class="print-title">双面打印</span>
            <a-radio-group
              style="width: 100%"
              v-model="doublePrint"
              :default-value="0"
              class="radio-group"
            >
              <div class="editor-radio-row">
                <a-tooltip :overlayStyle="{ 'z-index': 99999 }">
                  <template slot="title">
                    <span>选择默认后将使用打印机首选项的配置</span>
                  </template>
                  <a-radio :value="2">默认</a-radio>
                </a-tooltip>
                <a-radio :value="0">单面</a-radio>
              </div>
              <div class="editor-radio-row" style="margin-top: 10px">
                <a-radio :value="1" @click="dblPrint">双面</a-radio>
                <a-tooltip :overlayStyle="{ 'z-index': 99999 }">
                  <template slot="title">
                    <span>兼容不支持双面打印功能的打印机</span>
                  </template>
                  <a-radio :value="3">双面（兼容）</a-radio>
                </a-tooltip>
              </div>
            </a-radio-group>
          </div>

          <div v-if="isA5Print" class="print-order">
            <span class="print-title">纸张摆放</span>
            <a-radio-group
              style="width: 100%"
              v-model="paperOrientation"
              :default-value="0"
            >
              <a-radio :value="0">纵向</a-radio>
              <a-radio :value="1">横向</a-radio>
            </a-radio-group>
          </div>
          <div v-if="doublePrint === 1" class="double-print">
            <span class="print-title">翻转方式</span>
            <a-radio-group
              style="width: 100%"
              v-model="duplexFlip"
              :default-value="1"
            >
              <a-radio :value="1">长边</a-radio>
              <a-radio :value="2">短边</a-radio>
            </a-radio-group>
          </div>
          <div class="b-print-content">
            <span class="print-title">打印范围:</span>
            <a-radio-group v-model="value" style="width: 100%">
              <a-radio :style="radioStyle" :value="1"> 全部 </a-radio>
              <a-radio :style="radioStyle" :value="2"> 当前页 </a-radio>
              <a-radio
                :style="radioStyle"
                :value="3"
                :disabled="disabled"
                v-if="printSrcList.length >= 2"
              >
                奇数页
              </a-radio>
              <a-radio
                :style="radioStyle"
                :value="4"
                v-if="printSrcList.length >= 2"
                :disabled="disabled"
              >
                偶数页
              </a-radio>
              <a-radio
                :style="radioStyle"
                :value="5"
                v-if="printSrcList.length >= 2"
                :disabled="disabled"
              >
                页码
                <a-input
                  type="text"
                  ref="inputdata"
                  @click="changeRadio"
                  v-model="pageNum"
                />
              </a-radio>
            </a-radio-group>
            <div
              class="warning_system_print"
              v-if="value === 5 && warning && printSrcList.length >= 2"
            >
              请输入正确页码范围
            </div>
            <div class="b-print-text" v-if="printSrcList.length >= 2">
              输入页码或页码范围，如：1,2,5-10
            </div>
          </div>
          <div class="print-order">
            <span class="print-title">彩色打印</span>
            <a-radio-group
              style="width: 100%"
              v-model="color"
              :default-value="1"
            >
              <a-radio :value="1">彩色</a-radio>
              <a-radio :value="0">黑白</a-radio>
            </a-radio-group>
          </div>
          <div>
            <span class="print-title">保存本次配置到本地</span>
            <a-tooltip placement="top" :overlayStyle="{ 'z-index': 99999 }">
              <template slot="title">
                <span
                  >打印之后生效，保存此次的“默认打印机”，“正逆序”，“单双面”，“翻转方式”，“彩色打印”，“A5纸张的放纸方式”</span
                >
              </template>
              <a-switch
                size="small"
                checked-children="开"
                un-checked-children="关"
                class="save-config-switch-button"
                v-model="saveConfig"
              ></a-switch>
            </a-tooltip>
          </div>

          <!-- <div class="restoreDefaultConfig">
            <a-button
              size="small"
              class="restore-default-config"
              @click="restoreConfig"
              >恢复默认配置</a-button
            >
          </div> -->
          <div class="footer_system_print">
            <a-button type="primary" @click="submit" :disabled="warning"
              >打印</a-button
            >
            <a-button type="defalut" @click="cancel" style="margin-left: 10px"
              >取消</a-button
            >
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script>
// import BUS from "@/assets/js/eventBus";
import iconCommon from "./common/iconCommon.vue";
const PRINT_PREVIEW_WINDOW_TYPE = {
  NORMAL: 0,
  MAX: 1,
};
const WINDOW_SIZE_TYPE = {
  NORMAL: "icon-chuangkouzuidahua",
  MAX: "icon-3zuidahua-3",
};
export default {
  name: "SystemPrint",
  components: {
    iconCommon,
  },
  data() {
    return {
      isA5Print: false,
      paperOrientation: 0,
      saveConfig: false,
      savedConfig: {},
      selectPrinter: "",
      disabled: false,
      loading: true,
      pageSrc: [],
      loadingError: false,
      parameter_arr: [],
      value: 1,
      printMultiple: 1,
      sortValue: 1,
      doublePrint: 2,
      duplexFlip: 3, //双面打印翻转方式
      color: 1,
      warning: false,
      pageNum: "",
      printSrcList: [],
      previewWidth: "60%",
      title: "系统打印",
      showImage: false,
      visible: false,
      masks: false,
      pageMention: "",
      firstTimeShow: true,
      currenPageNumber: 0,
      fixCurPageNum: 0,
      maxSizeBtn: WINDOW_SIZE_TYPE.NORMAL,
      maxSizeStyle: {},
      maxWidth: "0",
      radioStyle: {
        display: "block",
        height: "30px",
        lineHeight: "30px",
      },
    };
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    localConfigOther: {
      type: Object,
      default: () => {},
    },
    printConfig: {
      type: Object,
      default: () => {},
    },
    printerList: {
      type: Array,
      default: () => [],
    },
    editorId: {
      type: String,
      default: "",
    },
  },
  watch: {
    show(e) {
      this.initializePageMention();
      this.setMaxWidth();
      if (this.firstTimeShow) {
        this.setInitialPrintConfig();
        this.firstTimeShow = false;
      }
      if (this.printConfig.printName) {
        this.printerList.forEach((printer) => {
          printer.isDefault =
            printer.printerName === this.printConfig.printName;
        });
      }
      this.resetState(e);
      this.loadPageImages();
      if (
        this.localConfigOther?.printPreviewWindowType ===
        PRINT_PREVIEW_WINDOW_TYPE.MAX
      ) {
        this.maxSizeBtn = WINDOW_SIZE_TYPE.MAX;
      } else {
        this.maxSizeBtn = WINDOW_SIZE_TYPE.NORMAL;
      }
      this.$nextTick(() => {
        this.changeWindowSizeByMaxSizeBtn();
      });
    },
    printerList(val) {
      if (val.length > 0) {
        for (let i = 0; i < val.length; i++) {
          const printer = val[i];
          if (printer.isDefault) {
            this.selectPrinter = val[i].printerName;
          }
        }
        this.warning = false;
        // if (this.$refs.printList) {
        //   this.$refs.printList.style.height = "400px";
        // }
      } else {
        this.selectPrinter = "";
        this.warning = true;
        // if (this.$refs.printList) {
        //   this.$refs.printList.style.height = "400px";
        // }
      }
    },
    value(e, oldValue) {
      this.warning = false;
      this.parameter_arr = [];
      this.pageSrc = [];
      this.pageNum = "";
      if (e === 1) {
        this.parameter_arr = new Array(this.printSrcList.length)
          .fill(0)
          .map((val, index) => index);
        this.pageSrc = this.printSrcList;
        this.handleScroll();
      } else if (e === 2) {
        const parentDom = this.$refs.printLeft;
        const pageHight = this.$refs.printImg[0].offsetHeight;
        const scrollTop = parentDom.scrollTop;
        let pageNum = Math.round(scrollTop / (pageHight + 10));
        if (oldValue === 3) {
          pageNum = pageNum * 2;
        } else if (oldValue === 4) {
          pageNum = pageNum * 2 + 1;
        }
        this.parameter_arr.push(pageNum);
        this.pageSrc.push(this.printSrcList[pageNum]);
        parentDom.scrollTop = 0;
        this.fixCurPageNum = pageNum;
        this.handleScroll();
        // this.pageMention = pageNum + this.editor.editor.pages.length;
      } else if (e === 3) {
        let totalList = new Array(this.printSrcList.length)
          .fill(0)
          .map((val, index) => index);
        totalList.forEach((e) => {
          if (e % 2 === 0) {
            this.parameter_arr.push(e);
            this.pageSrc.push(this.printSrcList[e]);
          }
        });
        this.handleScroll();
      } else if (e === 4) {
        let totalList = new Array(this.printSrcList.length)
          .fill(0)
          .map((val, index) => index);
        totalList.forEach((e) => {
          if (e % 2 === 1) {
            this.parameter_arr.push(e);
            this.pageSrc.push(this.printSrcList[e]);
          }
        });
        this.handleScroll();
      } else if (e === 5) {
        this.warning = true;
        this.pageSrc = this.printSrcList;
        this.$nextTick(() => {
          this.$refs.inputdata.focus();
        });
        this.handleScroll();
      }
    },
    loading(val) {
      if (!val) {
        this.$nextTick(() => {
          this.$refs.printLeft.addEventListener("scroll", this.handleScroll);
        });
      }
    },
    pageNum(val) {
      if (this.value === 5) {
        this.pageSrc = [];
        this.parameter_arr = [];
        this.verificationRules(val);

        if (this.parameter_arr.length > 0) {
          for (let i = 0; i < this.parameter_arr.length; i++) {
            this.pageSrc.push(this.printSrcList[this.parameter_arr[i]]);
          }
        } else {
          this.pageSrc = this.printSrcList;
        }
      }
    },
    maxWidth(newWidth) {
      document.documentElement.style.setProperty("--img-max-width", newWidth);
    },

    immediate: true,
    deep: true,
  },
  inject: ["parentInstance"],
  mounted() {},
  beforeDestroy() {
    if (this.$refs.printLeft) {
      this.$refs.printLeft.removeEventListener("scroll", this.handleScroll);
    }
  },
  methods: {
    handleScroll() {
      const parentDom = this.$refs.printLeft;
      const pageHight = this.$refs.printImg[0].offsetHeight;
      const scrollTop = parentDom.scrollTop;
      let currenPageNumber = 1;
      currenPageNumber = Math.round(scrollTop / (pageHight + 10)) + 1;
      if (this.value !== 2) {
        this.pageMention = currenPageNumber + "/" + this.pageSrc.length;
      } else {
        this.pageMention =
          this.fixCurPageNum + 1 + "/" + this.printSrcList.length;
      }
    },
    cancel() {
      this.$emit("cancel", this.afterPrint);
      this.printMultiple = 1;
      this.sortValue = 1;
    },
    changeRadio() {
      this.value = 5;
    },
    //改变打印机
    changePrinter(val) {
      this.selectPrinter = this.printerList[val].printerName;
      for (let i = 0; i < this.printerList.length; i++) {
        const printer = this.printerList[i];
        if (printer.isDefault) {
          printer.isDefault = false;
        }
        if (i === val) {
          printer.isDefault = true;
        }
      }
    },
    dblPrint() {
      if (this.duplexFlip === 3) {
        this.duplexFlip = 1;
      }
    },

    updatePrinterList() {
      this.$emit("updatePrinterList");
    },
    submit() {
      if (this.doublePrint === 0) {
        this.duplexFlip = 1;
      } else if (this.doublePrint === 2) {
        this.duplexFlip = 3;
      }
      this.$emit(
        "submit",
        this.printMultiple, // 打印份数
        this.parameter_arr, // 具体页数
        this.selectPrinter, // 打印机名称
        {
          sort: this.sortValue,
          doublePrint: this.doublePrint,
          duplexFlip: this.duplexFlip,
          color: Boolean(this.color),
          paperOrientation: this.paperOrientation, // A5纸的横纵向放置
        },
        this.afterPrint, // 打印回调
        this.printType
      );

      if (this.saveConfig) {
        this.savePrintConfig();
      }

      this.value = 1;
      this.parameter_arr = [];
      this.printMultiple = 1;
      this.saveConfig = false;
    },

    savePrintConfig() {
      this.savedConfig.sortValue = this.sortValue;
      this.savedConfig.doublePrint = this.doublePrint;
      this.savedConfig.duplexFlip = this.duplexFlip;
      this.savedConfig.printName = this.selectPrinter;
      this.savedConfig.color = this.color;
      this.savedConfig.paperOrientation = this.paperOrientation;

      this.$emit(
        "savePrintConfig",
        this.savedConfig.doublePrint,
        this.savedConfig.duplexFlip,
        this.savedConfig.sortValue,
        this.savedConfig.printName,
        this.savedConfig.color,
        this.paperOrientation
      );
    },
    //改变打印份数
    changePrintMultiple(val) {
      this.printMultiple = val;
    },
    //验证规则
    verificationRules(page_num) {
      const pageNumRule = /^\d+(-\d+)?((,|，)\d+(-\d+)?)*$/;
      const rule = /^\d+(-\d+)$/;
      const num_arr = [];
      if (pageNumRule.test(page_num)) {
        let commaNumList = page_num.split(/[,，]/);
        for (let i = 0; i < commaNumList.length; i++) {
          const commaSplit = commaNumList[i];
          if (rule.test(commaSplit)) {
            let spliteNum = commaSplit.split("-");
            spliteNum = spliteNum.map((num) => num * 1);
            if (spliteNum[0] && spliteNum[1]) {
              if (spliteNum[1] < spliteNum[0]) {
                this.warning = true;
                return;
              }
              if (
                spliteNum[0] > this.printSrcList.length ||
                spliteNum[1] > this.printSrcList.length ||
                spliteNum[0] === "0" ||
                spliteNum[1] === "0"
              ) {
                this.warning = true;
                return;
              }
              for (let j = parseInt(spliteNum[0]); j <= spliteNum[1]; j++) {
                num_arr.push(j - 1);
              }
            }
          } else {
            if (commaSplit > this.printSrcList.length || commaSplit === "0") {
              this.warning = true;
              return;
            } else {
              num_arr.push(parseInt(commaSplit) - 1);
            }
          }
        }
      } else {
        this.warning = true;
        return;
      }

      this.parameter_arr = this.unique(num_arr);
      for (var i = 0; i < this.parameter_arr.length; i++) {
        for (var j = i + 1; j < this.parameter_arr.length; j++) {
          var temp = "";
          if (this.parameter_arr[i] > this.parameter_arr[j]) {
            temp = this.parameter_arr[i];
            this.parameter_arr[i] = this.parameter_arr[j];
            this.parameter_arr[j] = temp;
          }
        }
      }
      this.warning = false;
    },
    unique(arr) {
      if (!Array.isArray(arr)) {
        console.log("type error!");
        return;
      }
      var array = [];
      for (var i = 0; i < arr.length; i++) {
        if (array.indexOf(arr[i]) === -1) {
          array.push(arr[i]);
        }
      }
      return array;
    },
    generateImageShow(editor) {
      try {
        this.printSrcList = editor.print({
          printRatio: 1,
          printView: true,
        });
        this.title = "系统打印：" + this.editor.editor.config.page_size_type;

        this.parameter_arr = new Array(this.printSrcList.length)
          .fill(0)
          .map((val, index) => index);
        this.pageSrc = this.printSrcList;
        this.loading = false;
      } catch (e) {
        this.loadingError = true;
      }
    },
    changeWindowSizeByMaxSizeBtn() {
      const viewportHeight = window.innerHeight;
      const modalContent = document.querySelector(
        ".systemModal .ant-modal-content"
      );
      const modal = document.querySelector(".systemModal .ant-modal");
      if (this.maxSizeBtn === WINDOW_SIZE_TYPE.NORMAL) {
        modalContent.style.top = "-90px";
        modal.style.top = "100px";
        this.maxSizeStyle = {};
        this.previewWidth = "60%";
      } else {
        modalContent.style.top = "0px";
        modal.style.top = "0px";
        this.maxSizeStyle = {
          width: "100%",
          height: viewportHeight - 60 + "px",
        };
        this.previewWidth = "100%";
      }
    },
    changeWindowSize() {
      this.maxSizeBtn =
        this.maxSizeBtn === WINDOW_SIZE_TYPE.MAX
          ? WINDOW_SIZE_TYPE.NORMAL
          : WINDOW_SIZE_TYPE.MAX;
      this.changeWindowSizeByMaxSizeBtn();
      let type = PRINT_PREVIEW_WINDOW_TYPE.NORMAL;
      if (this.maxSizeBtn === WINDOW_SIZE_TYPE.MAX) {
        type = PRINT_PREVIEW_WINDOW_TYPE.MAX;
      }
      this.$emit("savePrintPreviewWindowSizeType", type);
    },
    initializePageMention() {
      this.pageMention = "1/" + this.editor.editor.pages.length;
    },

    setMaxWidth() {
      const verticalPageSizeConfig =
        this.editor.builtInVariable.vertical_page_size_config;
      const config = this.editor.editor.config;
      const pageType = config.page_size_type;
      const pageDirection = config.page_direction;
      if (pageType === "custom") {
        this.maxWidth =
          pageDirection === "vertical"
            ? config.customPageSize.width + "px"
            : config.customPageSize.height + "px";
      } else {
        this.maxWidth =
          pageDirection === "vertical"
            ? verticalPageSizeConfig[pageType].width + "px"
            : verticalPageSizeConfig[pageType].height + "px";
      }
    },

    setInitialPrintConfig() {
      this.savedConfig.sortValue = this.printConfig.sortValue || 1;
      this.savedConfig.doublePrint = this.printConfig.isDuplex ? 1 : 2;
      this.savedConfig.color = this.printConfig.color ? 1 : 0;
      this.savedConfig.paperOrientation =
        this.printConfig.paperOrientation ?? 0;
      this.savedConfig.duplexFlip = this.printConfig.duplexFlip || 3;
    },

    resetState(e) {
      this.visible = e;
      this.pageSrc = [];
      this.printSrcList = [];
      this.value = 1;
      this.loading = true;
      this.page_num = "";
      this.loadingError = false;
      this.printMultiple = 1;
      this.setPrintConfig();
    },

    setPrintConfig() {
      this.doublePrint = this.printConfig.isDuplex ? 1 : 0;
      // this.color = this.printConfig.color ? 1 : 0;
      if (this.printConfig.color === false) {
        this.color = 0;
      }
      if (this.printConfig.color === true) {
        this.color = 1;
      }
      this.duplexFlip = this.printConfig.duplexFlip || 3;
      this.sortValue = this.printConfig.sortValue || 1;
      this.paperOrientation = this.printConfig.paperOrientation ?? 0;

      if (this.duplexFlip === 3) {
        this.doublePrint = 2;
      }
      if (this.printConfig.doublePrint === 3) {
        this.doublePrint = 3;
      }

      this.isA5Print = this.editor.editor.config.page_size_type === "A5";
    },

    loadPageImages() {
      if (this.visible) {
        if (this.parentInstance.newPrintEditor) {
          this.loadImagesWithInterval(this.parentInstance.newPrintEditor);
        } else {
          setTimeout(() => {
            this.generateImageShow(this.editor.editor);
          });
        }
      }
    },

    loadImagesWithInterval(editorInstance) {
      let loadingImageInterval = setInterval(() => {
        if (this.parentInstance.editor.judgeImageAllLoad()) {
          clearInterval(loadingImageInterval);
          this.generateImageShow(editorInstance);
        }
      }, 200);
    },
  },
};
</script>

<style scoped lang="less">
.systemModal /deep/ .ant-modal-header {
  padding: 8px 10px;
}

.systemModal /deep/ .ant-modal-close-x {
  height: 40px;
  line-height: 40px;
}

.systemModal /deep/ .ant-modal-body {
  text-align: center;
  padding: 12px;
}

.systemModal /deep/ .ant-radio-group {
  margin: 5px 0;
}

.systemModal /deep/.ant-modal-content {
  top: -90px;
  box-shadow: 0 0px 12px rgb(0 0 0 / 40%);
  overflow: hidden;
}

.systemModal /deep/.ant-modal {
  top: 100px;
}

.systemModal /deep/.ant-input {
  height: 24px;
  margin-left: 20px;
  width: 105px;
}

.systemModal /deep/ .ant-modal-footer {
  border-top: none;
  padding: 0 16px 10px 16px;
}

.systemModal /deep/ .ant-modal-wrap {
  height: 100%;
  width: 100%;
  z-index: 10000;
  overflow: hidden;
}

.systemModal /deep/.ant-select-selection {
  height: 26px;
  line-height: 26px;
}

.systemModal /deep/.ant-select-selection__rendered {
  line-height: 26px;
}

.systemModal /deep/.ant-input-number {
  height: 24px;
}

.systemModal /deep/.ant-input-number-input-wrap {
  height: 24px;
}

.systemModal /deep/.ant-input-number-input {
  height: 24px;
  line-height: 24px;
}

.title_system_print {
  height: 23px;
  display: flex;
}

.modal_system_print {
  width: 100%;
  height: 85vh;
  background-color: rgb(255, 255, 255);
  box-shadow: 0 0 4px rgb(0 0 0 / 40%);
  color: #000;
  display: flex;
}

.modal-left {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: rgb(240, 240, 240);
  overflow: auto;
}

.inputNumber {
  width: 90%;
  margin-right: 20px;
}

.modal-right {
  // width: 320px;
  padding: 0 10px;
  height: 100%;
  text-align: left;
  position: relative;
  overflow-x: auto;
  overflow-y: auto;
}

.modal-left-loading {
  display: flex;
  text-align: center;
  font-size: 16px;
  width: 900px;
  height: 100%;
  background-color: rgb(240, 240, 240);
}

.page_system_print {
  margin: auto;
  margin-top: 10px;
  max-width: var(--img-max-width);
  margin-bottom: 10px;
  width: 80%;
  box-shadow: 0 0 12px rgb(0 0 0 / 40%);
}

img {
  height: 100%;
  width: 100%;
  max-width: var(--img-max-width);
  image-rendering: -moz-crisp-edges;
  image-rendering: -o-crisp-edges;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  -ms-interpolation-mode: nearest-neighbor;
  -webkit-font-smooting: antialiased;
}

.select-print {
  margin-top: 10px;
  margin-bottom: 10px;
  width: 100%;
}

.xeditor-input-up {
  z-index: 99999 !important;
}

.b-print-content {
  margin-left: 0px;
  margin-top: 5px;
}

.b-print-text {
  margin-top: 5px;
  margin-bottom: 10px;
}

.print-title {
  font-size: 16px;
  width: 100%;
}

.print-title-top {
  font-size: 16px;
  margin-top: 5px;
  width: 100%;
}

.print-order {
  margin-top: 5px;
}

.print-list {
  border: 1px solid rgb(50, 144, 252);
  width: 100%;
  padding-left: 10px;
  margin-top: 10px;
  height: calc(100% - 130px);
  border-radius: 10px;
  overflow-x: hidden;
  overflow-y: auto;
}

.footer_system_print {
  position: absolute;
  bottom: 0;
  right: 0;
  text-align: right;
  margin-right: 10px;
  margin-bottom: 10px;
}

.loading-div {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 350px;
}

.box1 {
  margin: 0 2px 0 4px;
  animation: rotate 1s linear infinite;
}

.box2 {
  margin: 0 2px 0 4px;
  animation: rotate 1s linear 0.2s infinite;
}

.box3 {
  margin: 0 2px 0 2px;
  animation: rotate 1s linear 0.4s infinite;
}

@keyframes rotate {
  0% {
    transform: translateY(0);
  }

  35% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }

  65% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(0);
  }
}

.warning_system_print {
  color: red;
  margin-left: 100px;
}

.save-config-title {
  font-size: 16px;
  margin-top: 10px;
  position: relative;
  left: 10px;
}

.saveCurrentConfig {
  display: flex;
}

.save-config-switch-button {
  margin-left: 15px;
  margin-bottom: 5px;
}

.restoreDefaultConfig {
  display: flex;
}

.restore-default-config {
  margin-left: 10px;
  margin-top: 10px;
}

.editor-page-mention {
  position: sticky;
  width: 60px;
  height: 30px;
  bottom: 0px;
  left: 50%;
  padding: 10px;
  text-align: center;
  line-height: 10px;
  background-color: rgba(128, 128, 128, 0.3);
  transform: translateX(-30px);
}

.editor-maxsize-btn {
  border: 1px solid rgb(255, 255, 255);
  position: absolute;
  top: 3px;
  right: 40px;
  transition: none;
}

.systemModal /deep/ .ant-modal-close-x {
  height: 40px;
  width: 30px;
  margin-right: 10px;
  line-height: 40px;
}

.editor-radio-group {
  display: flex;
  flex-direction: column;
}

.editor-radio-row {
  display: flex;
}

.msun-editor-forms-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: white;

  &::-webkit-scrollbar {
    width: 10px;
    height: 10px;
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #bfbfbf;
    border-radius: 6px;
  }

  &::-webkit-scrollbar-track {
    background-color: white;
    -webkit-box-shadow: inset 0 0 4px rgba(100, 100, 100, 0.01);
  }
}
</style>
