<template>
  <modal
    :show="show"
    :width="modal_width"
    :title="title"
    @cancel="cancel"
    class="fieldPropertyModal"
  >
    <a-button
      class="stay-top-btn"
      @click="handleStayTopClick"
      :style="{ backgroundColor: btnColor }"
    >
      <icon-common icon="icon-dingzi1" style="cursor: pointer"></icon-common>
    </a-button>
    <a-tabs defaultActiveKey="1" class="tags">
      <a-tab-pane key="1" tab="基础">
        <div class="prop">
          <div class="prop-type prop-type-first">
            <strong>文本域基础</strong>
            <hr class="hr-style" />
          </div>
          <div class="prop-div">
            <label>编号（ID）：</label>
            <a-tooltip
              :visible="disabled"
              placement="topLeft"
              title="文本域ID不能为空"
              :overlayStyle="{ 'z-index': 99999 }"
            >
              <a-input
                class="prop-input"
                id="field_id"
                type="text"
                placeholder="文本域ID"
                v-model="field.id"
                @blur="field.id = $event.target.value"
                @change="IsExistId"
              />
            </a-tooltip>
          </div>
          <div class="prop-div">
            <label> 名称(Name)：</label>
            <a-input
              class="prop-input"
              id="field_name"
              type="text"
              placeholder="文本域名称"
              v-model="field.name"
              @blur="field.name = $event.target.value"
            />
          </div>
          <div class="prop-div">
            <label>起始边框：</label>
            <a-input
              class="prop-input"
              id="start_symbol"
              type="text"
              placeholder="起始边框字符"
              v-model="field.start_symbol"
              @blur="field.start_symbol = $event.target.value"
            />
          </div>
          <div class="prop-div">
            <label>结束边框：</label>
            <a-input
              class="prop-input"
              id="end_symbol"
              type="text"
              placeholder="结束边框字符"
              v-model="field.end_symbol"
              @blur="field.end_symbol = $event.target.value"
            />
          </div>
          <div class="prop-div display">
            <label style="margin-top: 5px">最大宽度：</label>
            <div
              style="
                position: absolute;
                width: 20px;
                margin-left: 80px;
                margin-top: 4px;
              "
              class="edit-btn"
              title="设置文本域最大宽度，可用快捷键Ctrl+Alt快速设置，点击需要更改的文本域按住Ctrl+Alt键出现两个点，点击上方的点进行拖动设置文本域最大宽度"
            >
              <icon-common
                icon="icon-jieshi"
                style="cursor: pointer"
              ></icon-common>
            </div>
            <a-input-number
              :min="0"
              class="prop-input"
              id="max_width"
              type="text"
              placeholder="文本域最大宽度"
              v-model="field.max_width"
            />
          </div>
          <div class="prop-div display">
            <label style="margin-top: 5px">最小宽度：</label>
            <div
              style="
                position: absolute;
                width: 20px;
                margin-left: 80px;
                margin-top: 4px;
              "
              class="edit-btn"
              title="设置文本域最大宽度，可用快捷键Ctrl+Alt快速设置，点击需要更改的文本域按住Ctrl+Alt键出现两个点，点击下方的点进行拖动设置文本域最小宽度"
            >
              <icon-common
                icon="icon-jieshi"
                style="cursor: pointer"
              ></icon-common>
            </div>
            <a-input-number
              :min="0"
              class="prop-input"
              id="min_width"
              type="text"
              placeholder="文本域最小宽度"
              v-model="field.min_width"
            />
          </div>
          <div class="prop-div">
            <label>最大高度：</label>
            <a-input-number
              :min="0"
              class="prop-input"
              id="maxHeight"
              type="text"
              placeholder="文本域最大高度"
              v-model="field.maxHeight"
            />
          </div>
          <div class="prop-div">
            <label>背景文本 ：</label>
            <a-input
              class="prop-input"
              id="placeholder"
              type="text"
              placeholder="文本域背景文本"
              v-model="field.placeholder"
              @blur="field.placeholder = $event.target.value"
            />
          </div>
          <div class="prop-div">
            <label>提示文本 ：</label>
            <a-textarea
              class="prop-input"
              id="field_tip"
              type="text"
              :rows="1"
              placeholder="提示文本"
              v-model="field.tip"
              @blur="field.tip = $event.target.value"
            />
          </div>
          <div class="prop-div">
            <label>边框类型：</label>
            <a-select
              class="prop-select"
              v-model="field.display_type"
              dropdownClassName="xeditor-input-up"
            >
              <a-select-option value="normal">默认类型</a-select-option>
              <a-select-option value="input">边框(不打印)</a-select-option>
              <a-select-option value="printInput">边框(打印)</a-select-option>
              <a-select-option value="line">底线(打印)</a-select-option>
            </a-select>
          </div>
        </div>
        <div class="prop">
          <div class="prop-type">
            <strong>文本域编辑</strong>
            <hr class="hr-style" />
          </div>
          <div class="prop-div">
            <label>只读状态：</label>
            <a-select
              class="prop-select"
              v-model="field.readonly"
              dropdownClassName="xeditor-input-up"
            >
              <a-select-option :value="0">非只读</a-select-option>
              <a-select-option :value="1">只读</a-select-option>
            </a-select>
          </div>
          <div class="prop-div">
            <label>可否删除：</label>
            <a-select
              class="prop-select"
              v-model="field.deletable"
              dropdownClassName="xeditor-input-up"
            >
              <a-select-option :value="0">不可删除</a-select-option>
              <a-select-option :value="1">可删除</a-select-option>
            </a-select>
          </div>
          <div class="prop-div">
            <label>对齐方式：</label>
            <a-select
              class="prop-select"
              v-model="field.align"
              dropdownClassName="xeditor-input-up"
            >
              <a-select-option value="left">左对齐</a-select-option>
              <a-select-option value="center">居中对齐</a-select-option>
              <a-select-option value="right">右对齐</a-select-option>
            </a-select>
          </div>
          <div class="prop-div">
            <label>可否复制：</label>
            <a-select
              class="prop-select"
              v-model="field.canBeCopied"
              dropdownClassName="xeditor-input-up"
            >
              <a-select-option :value="0">不可复制</a-select-option>
              <a-select-option :value="1">可复制</a-select-option>
            </a-select>
          </div>
        </div>
        <div class="prop">
          <div class="prop-type">
            <strong>文本域类型</strong>
            <hr class="hr-style" />
          </div>
          <div class="prop-div">
            <label>类型：</label>
            <a-select
              class="prop-select"
              v-model="field.type"
              @change="fieldTypeSelected"
              dropdownClassName="xeditor-input-up"
            >
              <a-select-option :value="'normal'">常规</a-select-option>
              <a-select-option :value="'label'">标签</a-select-option>
              <a-select-option :value="'select'">下拉列表</a-select-option>
              <a-select-option :value="'date'">日期选择器</a-select-option>
              <a-select-option :value="'number'">数字</a-select-option>
              <a-select-option :value="'anchor'">锚点</a-select-option>
            </a-select>
          </div>
          <div class="prop-div" v-if="field.type === 'label'">
            <label>标签内容：</label>
            <a-input
              class="prop-input"
              type="text"
              placeholder="标签内容"
              v-model="field.label_text"
              @blur="field.label_text = $event.target.value"
            />
          </div>
          <div class="prop-div" v-if="field.type === 'select'">
            <label>数据源ID：</label>
            <a-input
              class="prop-input"
              type="text"
              placeholder="数据源ID"
              v-model="field.source_id"
              @blur="field.source_id = $event.target.value"
            />
          </div>
          <div class="prop-div" v-if="field.type === 'number'">
            <label>输出格式：</label>
            <a-select
              class="prop-select"
              v-model="field.number_format"
              dropdownClassName="xeditor-input-up"
            >
              <a-select-option
                v-for="(item, index) in numberFormatList"
                :value="index"
                :key="item"
                :title="item"
                >{{ item }}
              </a-select-option>
            </a-select>
          </div>
          <div
            class="prop-div"
            v-if="
              field.type === 'date' ||
              field.type === 'select' ||
              field.type === 'number'
            "
          >
            <label>激活模式：</label>
            <a-select
              class="prop-select"
              v-model="field.active_type"
              dropdownClassName="xeditor-input-up"
            >
              <a-select-option :value="0">单击</a-select-option>
              <a-select-option :value="1">双击</a-select-option>
            </a-select>
          </div>
          <div class="prop-div" v-if="field.type === 'select'">
            <label>选择模式：</label>
            <a-select
              class="prop-select"
              v-model="field.multi_select"
              dropdownClassName="xeditor-input-up"
            >
              <a-select-option :value="0">单选</a-select-option>
              <a-select-option :value="1">多选</a-select-option>
            </a-select>
          </div>
          <div class="prop-div" v-if="field.type === 'select'">
            <label>输入模式：</label>
            <a-select
              class="prop-select"
              v-model="field.inputMode"
              dropdownClassName="xeditor-input-up"
            >
              <a-select-option :value="0">可输入</a-select-option>
              <a-select-option :value="1">仅选择</a-select-option>
            </a-select>
          </div>
          <div class="prop-div" v-if="field.multi_select === 1">
            <label>选择分隔符号：</label>
            <a-select
              class="prop-select"
              v-model="field.separator"
              dropdownClassName="xeditor-input-up"
            >
              <a-select-option
                v-for="(item, index) in separatorGroups"
                :value="item.index"
                :key="index"
                >{{ item.separator }}</a-select-option
              >
            </a-select>
          </div>
          <div class="prop-div" v-if="field.type === 'date'">
            <label>输出格式：</label>
            <a-select
              class="prop-select"
              v-model="field.replace_format"
              dropdownClassName="xeditor-input-up"
            >
              <a-select-option
                v-for="(item, index) in replaceFormatList"
                :value="index"
                :key="item"
                :title="item"
                >{{ item }}
              </a-select-option>
            </a-select>
          </div>

          <div class="prop-div" v-if="field.type === 'date'">
            <label>禁用前方日期：</label>
            <a-date-picker
              class="prop-select"
              dropdownClassName="xeditor-input-up set-date"
              :valueFormat="'YYYY-MM-DD'"
              :value="field.forbidden.start"
              @change="beforeDate"
            />
          </div>
          <div class="prop-div" v-if="field.type === 'date'">
            <label>禁用后方日期：</label>
            <a-date-picker
              class="prop-select"
              dropdownClassName="xeditor-input-up"
              :valueFormat="'YYYY-MM-DD'"
              :value="field.forbidden.end"
              @change="afterDate"
            />
          </div>
          <div
            class="prop-div"
            style="display: flex; line-height: 32px"
            v-if="field.type === 'select'"
          >
            <label>编辑下拉项：</label>
            <div @click="openSelectOptions">
              <icon-common
                icon="icon-bianji"
                style="cursor: pointer"
              ></icon-common>
            </div>
            <modal
              class="table-modal"
              :show="showSelectOption"
              :width="modal_width"
              :sessionMove="true"
              @cancel="tableCancel"
            >
              <a-table
                ref="selectTable"
                :columns="columns"
                :data-source="source_list"
                bordered
                :pagination="false"
                :scroll="{ y: 224, scrollToFirstRowOnChange: true }"
              >
                <template
                  v-for="col in ['text', 'value', 'code']"
                  :slot="col"
                  slot-scope="text, record"
                >
                  <template>
                    <div :key="col">
                      <a-input
                        v-if="col === 'value'"
                        size="small"
                        style="margin: -5px -10px"
                        :value="text"
                        @change="
                          (e) => handleChange(e.target.value, record.key, col)
                        "
                      />
                      <a-input
                        size="small"
                        v-else
                        style="margin: -5px 0"
                        :value="text"
                        @change="
                          (e) => handleChange(e.target.value, record.key, col)
                        "
                      />
                    </div>
                  </template>
                </template>
                <template slot="operation" slot-scope="text, record">
                  <div class="editable-row-operations">
                    <a @click="() => deleteRow(record.key)">删除</a>
                  </div>
                </template>
              </a-table>
              <div class="attention">
                注意：根据值设置选中项，如果不存在与值匹配的选项将设置值为“*”的选项为选中项
              </div>
              <div slot="editor-modal-footer" class="footer">
                <div>
                  <a-button type="default" @click="handleAdd">添加</a-button>
                </div>
                <div>
                  <a-button type="default" @click="tableCancel">取消</a-button>
                  <a-button type="primary" @click="tableSubmit">确定</a-button>
                </div>
              </div>
            </modal>
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane key="4" tab="其他">
        <div class="prop">
          <div class="prop-type prop-type-first">
            <strong>文本域校验</strong>
            <hr class="hr-style" />
          </div>
          <div class="prop-div">
            <label>是否校验：</label>
            <a-select
              class="prop-select"
              v-model="field.valid"
              dropdownClassName="xeditor-input-up"
            >
              <a-select-option :value="0">不校验</a-select-option>
              <a-select-option :value="1">校验</a-select-option>
            </a-select>
          </div>
          <div class="prop-div" v-if="field.valid === 1">
            <label>是否必填：</label>
            <a-select
              class="prop-select"
              placeholder="请选择是否必填"
              dropdownClassName="xeditor-input-up"
              v-model="field.valid_content.require"
              @change="setFieldRequired"
            >
              <a-select-option :value="0">不必填</a-select-option>
              <a-select-option :value="1">必填</a-select-option>
            </a-select>
          </div>
          <div class="prop-div" v-if="field.valid === 1">
            <label>校验类型：</label>
            <a-select
              class="prop-select"
              placeholder="请选择校验规则"
              dropdownClassName="xeditor-input-up"
              @change="setValidTypes"
              v-model="field.valid_content.type"
            >
              <a-select-option
                v-for="(item, index) in validation_types"
                :key="index"
                :value="item.value"
              >
                {{ item.name }}
              </a-select-option>
            </a-select>
          </div>
          <div
            class="prop-div"
            v-if="field.valid_content.type === 'number' && field.valid === 1"
          >
            <label>数字最小值：</label>
            <a-input-number
              class="prop-input"
              @blur="setThreshold('number')"
              placeholder="最小值"
              v-model="min_num"
            />
          </div>
          <div
            class="prop-div"
            v-if="field.valid_content.type === 'number' && field.valid === 1"
          >
            <label>数字最大值：</label>
            <a-input-number
              class="prop-input"
              @blur="setThreshold('number')"
              placeholder="最大值"
              v-model="max_num"
            />
          </div>
          <div
            class="prop-div"
            v-if="field.valid_content.type === 'string' && field.valid === 1"
          >
            <label>字符最小长度：</label>
            <a-input-number
              class="prop-input"
              placeholder="请输入字符长度"
              @blur="setThreshold('string')"
              v-model="str_min_length"
            />
          </div>
          <div
            class="prop-div"
            v-if="field.valid_content.type === 'string' && field.valid === 1"
          >
            <label>字符最大长度：</label>
            <a-input-number
              class="prop-input"
              placeholder="请输入字符长度"
              @blur="setThreshold('string')"
              v-model="str_max_length"
            />
          </div>
          <div
            class="prop-div"
            v-if="field.valid_content.type === 'phone' && field.valid === 1"
          >
            <label>电话类型：</label>
            <a-select
              class="prop-select"
              :defaultValue="1"
              dropdownClassName="xeditor-input-up"
              @change="setFieldPhoneType"
            >
              <a-select-option :value="1">手机号码</a-select-option>
              <a-select-option :value="0">固定电话</a-select-option>
            </a-select>
          </div>
          <div
            class="prop-div"
            v-if="field.valid_content.type === 'regex' && field.valid === 1"
          >
            <label>正则表达式：</label>
            <a-input
              class="prop-input"
              placeholder="例：^[0-9]*$"
              v-model="field.valid_content.regex"
            />
          </div>
          <div class="prop-type">
            <strong>高级</strong>
            <hr class="hr-style" />
          </div>
          <div class="editor-flex-container">
            <div
              class="prop-div"
              style="display: flex; line-height: 32px; margin-right: 30%"
            >
              <label style="width: 50px">级联：</label>
              <div @click="openFieldAdvanced('cascade')">
                <icon-common
                  icon="icon-bianji"
                  style="cursor: pointer"
                ></icon-common>
              </div>
            </div>
            <div
              class="prop-div"
              style="display: flex; line-height: 32px; margin-right: 30%"
            >
              <label>文本域自动化：</label>
              <div @click="openFieldAdvanced('fieldAuto')">
                <icon-common
                  icon="icon-bianji"
                  style="cursor: pointer"
                ></icon-common>
              </div>
            </div>
            <div class="prop-div" style="display: flex; line-height: 32px">
              <label style="width: 70px">展示规则：</label>
              <div @click="openFieldAdvanced('replaceRule')">
                <icon-common
                  icon="icon-bianji"
                  style="cursor: pointer"
                ></icon-common>
              </div>
            </div>
          </div>
          <div class="prop-type">
            <strong>其他</strong>
            <hr class="hr-style" />
          </div>
          <div class="prop-div formula-div">
            <label style="width: 60px">公式：</label>
            <div
              class="formula-input"
              @click="openFieldAdvanced('fieldFormula')"
            >
              {{ field.formula }}
            </div>
            <a-popover :title="popTitle" :overlayStyle="{ zIndex: 99999 }">
              <template slot="content">
                <div style="width: 760px">
                  <div class="textTitle">需要代入计算的文本域：</div>
                  <div>
                    文本域同时存在“值”和“名称（name）”时，方可使用文本域公式功能。
                  </div>
                  <div>文本域“值”的在不同类型的文本域中的展示形式如下：</div>
                  <div>
                    &nbsp;&nbsp;&nbsp;&nbsp;(1)普通文本域：普通文本域内为纯数字时，会直接将该数字代入计算。<br />
                    &nbsp;&nbsp;&nbsp;&nbsp;(2)下拉列表：下拉列表类型的文本域的值在【文本域属性【编辑下拉项】】中填写，代入计算的值为当前选中项对应的值。<br />
                    &nbsp;&nbsp;&nbsp;&nbsp;(3)自定义选择框：在【复选框属性】中填写。
                  </div>
                  <div class="textTitle">需要展示结果的文本域：</div>
                  <div>
                    &nbsp;&nbsp;&nbsp;&nbsp;(1)找到【文本域属性】-【其他】-【公式】，点击公式编辑图标弹出公式编辑窗口，可在编辑区域进行公式编辑，此时编辑器页面开启文本域公式编辑模式，右上角出现蓝色方块的文本域可直接点击将文本域的名称插入(也可手动输入中括号包裹文本域名称)。<br />
                    &nbsp;&nbsp;&nbsp;&nbsp;(2)如果展示结果的文本域需要控制结果展示精度，则需要将该文本域类型改为数字类型，然后在输出格式中选择需要保留的小数位数。
                  </div>
                  <div class="textTitle">文本域公式规则：</div>
                  <div>
                    &nbsp;&nbsp;&nbsp;&nbsp;(1)满足基本的数学公式如＋、－、×、÷及（）运算。例如文本域录入的公式为[name1]+[name2]，则当前文本域显示的内容为name1的文本域的值与name2文本域值的和。<br />
                    &nbsp;&nbsp;&nbsp;&nbsp;(2)满足＞、＜、≥、≤、=，其中≥也可写为“＞=”，≤可写为"＜="
                    运算，条件组合需与“：”配合使用，多个条件表达式用“；”间隔，判断“=”时需要用“==”进行判断。<br />
                    &nbsp;&nbsp;&nbsp;&nbsp;(3)做条件运算的时候，当所有条件都不符合时将清空文本域内容，如果所有条件都不符合时需要显示其他内容则在公式末尾直接拼上即可，例如：[name]＜60:不合格；"合格"，表示名称为name的文本域值小于60的时候显示不合格，否则显示合格。
                  </div>
                  <div>
                    &nbsp;&nbsp;&nbsp;&nbsp;例1：[name1]>[name2]:大；[name1]&lt;
                    [name2]:小；[name1]=[name2]:中，意为名称为name1的文本域的值若大于name2文本域的值，则当前文本域内容显示为“大”；当name1文本域的值小于name2文本域的值则内容显示为“小”；两个文本域值相等则显示为“中”。<br />
                    &nbsp;&nbsp;&nbsp;&nbsp;例2：[name]＜60:不及格；[name]≥60&&[name]＜80:良好；[name]≥80:优秀&lt;
                    其中&&代表”并且“，如需表达“或者”使用||表示。
                  </div>
                  <div class="textTitle">限制：</div>
                  <div style="color: red">
                    &nbsp;&nbsp;&nbsp;&nbsp;(1)文本域公式中不允许包含本身。<br />
                    &nbsp;&nbsp;&nbsp;&nbsp;(2)含有公式的文本域内容不可修改。<br />
                    &nbsp;&nbsp;&nbsp;&nbsp;(3)不支持0＜[name]＜60写法，需转换为[name]＞0&&[name]＜60。
                  </div>
                </div>
              </template>
              <div
                style="margin-bottom: 5px; margin-left: 10px"
                class="edit-btn"
                title="点击左侧公式编辑后弹框后，弹出公式编辑弹框，可点击出现小方块的文本域将文本域插入输入框中，点击按钮插入数学符号，完成公式后点击确定将公式插入到文本域编辑的输入框中"
              >
                <icon-common
                  icon="icon-jieshi"
                  style="cursor: pointer"
                ></icon-common>
              </div>
            </a-popover>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
    <div slot="editor-modal-footer" class="parent-footer">
      <div>
        <a-button type="default" @click="cancel">取消</a-button>
        <a-button type="primary" :disabled="disabled" @click="submit"
          >确定</a-button
        >
      </div>
    </div>
  </modal>
</template>

<script>
const columns = [
  {
    title: "选项名",
    dataIndex: "text",
    width: "40%",
    scopedSlots: { customRender: "text" },
  },
  {
    title: "检索码",
    dataIndex: "code",
    width: "20%",
    scopedSlots: { customRender: "code" },
  },
  {
    title: "值",
    dataIndex: "value",
    width: "20%",
    scopedSlots: { customRender: "value" },
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: "20%",
    scopedSlots: { customRender: "operation" },
  },
];
import moment from "moment";
import iconCommon from "../common/iconCommon.vue";
import modal from "../common/modal.vue";
// import BUS from "@/assets/js/eventBus";
export default {
  name: "fieldProperty",
  components: {
    modal,
    iconCommon,
    // rulerEditor,
  },
  data() {
    return {
      caretPath: null, //缓存光标位置，定位打开时的文本域
      btnColor: "#ffffff",
      popTitle: "公式使用说明",
      inputDomId: null,
      clickSelect: null,
      moment,
      disabled: false,
      source_list: [],
      columns,
      showSelectOption: false,

      showCascadeOption: false,
      showFieldAutomation: false,
      modal_width: 580,
      title: "文本域属性",
      replaceFormatList: [
        "YYYY-MM-DD",
        "YYYY-MM-DD HH:mm",
        "YYYY-MM-DD HH:mm:ss",
        "YYYY年MM月DD日",
        "YYYY年MM月DD日 HH时",
        "YYYY年MM月DD日 HH时mm分",
        "MM-DD",
        "MM月DD日",
        "HH:mm",
        "HH时mm分",
        "YYYY年MM月DD日 HH时mm分ss秒",
        "MM-DD HH:mm",
      ],
      numberFormatList: [
        "无限制",
        "整数",
        "保留一位小数",
        "保留两位小数",
        "保留三位小数",
        "保留四位小数",
      ],
      // showFormatList: ["年月日", "年月日时分", "年月日时分秒"],
      field: {
        id: "",
        name: "",
        tip: "",
        placeholder: "",
        type: "normal",
        start_symbol: "[",
        end_symbol: "]",
        readonly: 0,
        deletable: 1,
        canBeCopied: 1,
        replaceRule: [],
        max_width: 0,
        forbidden: {
          start: null,
          end: null,
        },
        min_width: 0,
        maxHeight: 0,
        display_type: "normal",
        cascade_list: [],
        automation_list: [],
        show_format: 0,
        align: "left",
        replace_format: 0,
        number_format: 0,
        valid: 0,
        label_text: "",
        source_id: "",
        multi_select: 0,
        meta: {},
        inputMode: 0,
        separator: 0,
        formula: "",
        active_type: 0,
        show_field: true,
        source_list: [],
        valid_content: {
          require: 0,
          type: "",
          phone_type: "",
          rule: {
            max_length: 20,
            min_length: 0,
          },
          regex: "",
        },
      },

      validation_types: [
        { name: "任意", value: "" },
        { name: "日期", value: "date" },
        { name: "数字", value: "number" },
        { name: "字符长度", value: "string" },
        { name: "身份证", value: "idCard" },
        { name: "电话号码", value: "phone" },
        { name: "正则表达式", value: "regex" },
      ],
      min_num: undefined,
      max_num: undefined,
      // 字符最大的长度
      str_max_length: 20,
      // 字符最小长度
      str_min_length: 0,
    };
  },
  mounted() {
    document.addEventListener("mouseup", this.fieldPropertyMouseUpEvent);
  },
  beforeDestroy() {
    document.removeEventListener("mouseup", this.fieldPropertyMouseUpEvent);
  },
  watch: {
    show: {
      handler(val) {
        if (val) {
          this.$emit("changeStayTop", this.stayTop);
          const fieldInfo = this.editor.editor._curFieldInfo;
          if (fieldInfo.source_id) {
            this.searchMode =
              fieldInfo.source_id.split("_")[0] === "searchBox" || false;
          }
          for (const attrName in this.field) {
            if (
              fieldInfo[attrName] === null ||
              fieldInfo[attrName] === undefined
            ) {
              continue;
            }
            if (
              attrName === "valid" ||
              attrName === "deletable" ||
              attrName === "canBeCopied"
            ) {
              if (!fieldInfo[attrName]) {
                this.field[attrName] = 0;
              } else {
                this.field[attrName] = 1;
              }
              continue;
            }
            if (attrName === "valid_content") {
              if (fieldInfo[attrName]) {
                this.setValidAttr(fieldInfo);
              }
              continue;
            }
            this.field[attrName] = fieldInfo[attrName];
          }
          if (fieldInfo) {
            this.field.label_text = fieldInfo.text;
          }
          let list = [];
          if (!this.searchMode) {
            this.field.source_list.forEach((e) => {
              list.push({
                key: this.editor.utils.getUUID(),
                text: e.text,
                value: e.value,
                code: e.code,
              });
            });
            if (list.length === 0) {
              list = [
                {
                  key: this.editor.utils.getUUID(),
                  text: "",
                  code: "",
                  value: null,
                },
              ];
            }
          } else {
            this.field.source_list.forEach((e) => {
              list.push({
                key: this.editor.utils.getUUID(),
                time: e.time,
                content: e.content,
              });
            });
            if (list.length === 0) {
              list = [
                {
                  key: this.editor.utils.getUUID(),
                  time: "",
                  content: "",
                },
              ];
            }
          }
          this.source_list = list;
        } else {
          this.searchMode = false;
        }
      },
    },
    curFieldID(val) {
      if (val && !this.isInsert) {
        const curField = this.editor.editor.getFieldById(val);
        this.field.field = curField;
        for (let key in this.field) {
          if (key !== "field") {
            this.field[key] = curField[key];
          }
        }
      }
    },
    stayTop(val) {
      let modalMask;
      if (val) {
        this.btnColor = "rgb(230,230,230)";
      } else {
        this.btnColor = "#ffffff";
      }
      this.$nextTick(() => {
        const el = this.$el;
        modalMask = el.childNodes[0];
        if (modalMask && val) {
          modalMask.style.pointerEvents = "none";
        } else {
          modalMask.style.pointerEvents = "auto";
        }
      });
    },
    isInsert(val) {
      if (val) {
        this.title = "插入文本域";
      } else {
        this.title = "修改文本域属性";
      }
    },
    arrayLength(val) {
      if (val) {
        this.handleSourceListChange();
      }
    },
    // reg(val) {
    //   if (val) {
    //     this.field.replaceRule = val;
    //   }
    // },

    deep: true,
    immediate: true,
  },
  computed: {
    arrayLength() {
      return this.source_list.length;
    },
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    editorId: {
      type: String,
      default: "",
    },
    separatorGroups: {
      type: Array,
      default: () => [],
    },
    stayTop: {
      type: Boolean,
      default: false,
    },
    curFieldID: {
      type: String,
      default: "",
    },
    isInsert: {
      type: Boolean,
      default: false,
    },
    reg: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    handleSourceListChange() {
      this.$nextTick(() => {
        // 获取最后一个输入框的元素
        if (this.$refs.selectTable) {
          const inputElements =
            this.$refs.selectTable.$el.querySelectorAll("input");
          const lastInputElement = inputElements[inputElements.length - 1];

          // 滚动到最后一个输入框
          if (lastInputElement) {
            lastInputElement.scrollIntoView({ behavior: "smooth" });
          }
        }
      });
    },
    fieldPropertyMouseUpEvent(e) {
      // const currentDom = e.target;
      // const cascadeSelect = document.getElementById(this.inputDomId);
      // const cascadeDiv = document.getElementById("cascadeList");
      // const fieldAutoDiv = document.getElementById("fieldAutoList");
      // if (
      //   (cascadeSelect && !cascadeSelect.contains(currentDom)) ||
      //   (cascadeDiv && !cascadeDiv.contains(currentDom)) ||
      //   (fieldAutoDiv && !fieldAutoDiv.contains(currentDom))
      // ) {
      //   this.$refs.cascade && (this.$refs.cascade.showCascadeSelect = false);
      //   this.$refs.fieldAuto &&
      //     (this.$refs.fieldAuto.showFieldAutomationSelect = false);
      // }
    },

    beforeDate(val, date) {
      if (!date) {
        this.field.forbidden.start = null;
      } else {
        this.field.forbidden.start = date;
      }
      if (!this.field.forbidden.end) {
        this.field.forbidden.end = null;
      }
    },
    afterDate(val, date) {
      if (!date) {
        this.field.forbidden.end = null;
      } else {
        this.field.forbidden.end = date;
      }
      if (!this.field.forbidden.start) {
        this.field.forbidden.start = null;
      }
    },
    submit() {
      // 如果传递进去的值为 null 再次显示的时候就为 0 了，如果是 undefined 那么再次显示就只有 placeholder
      if (this.field.valid_content.rule.max_num === null) {
        this.field.valid_content.rule.max_num = undefined;
      }
      if (this.field.valid_content.rule.min_num === null) {
        this.field.valid_content.rule.min_num = undefined;
      }
      this.field.replaceRule = this.reg;
      this.$emit("submit", this.field);
      this.clear();
    },
    cancel() {
      this.$emit("cancel");
      this.btnColor = "#ffffff";
      const modalMask = document.querySelector(".ant-modal-wrap");
      modalMask.style.pointerEvents = "auto";
      this.clear();
    },
    clear() {
      if (!this.stayTop) {
        this.field = {
          id: "",
          name: "",
          searchMode: false,
          tip: "",
          placeholder: "",
          source_id: "",
          label_text: "",
          type: "normal",
          start_symbol: "[",
          end_symbol: "]",
          readonly: 0,
          deletable: 1,
          canBeCopied: 1,
          replaceRule: [],
          max_width: 0,
          min_width: 0,
          maxHeight: 0,
          align: "left",
          display_type: "normal",
          show_format: 0,
          source_list: [],
          show_field: true,
          cascade_list: [],
          automation_list: [],
          replace_format: 0,
          number_format: 0,
          valid: 0,
          forbidden: {
            start: null,
            end: null,
          },
          multi_select: 0,
          inputMode: 0,
          meta: {},
          separator: 0,
          formula: "",
          active_type: 0,
          valid_content: {
            require: 0,
            type: "",
            phone_type: "",
            rule: {
              max_length: 20,
              min_length: 0,
            },
            regex: "",
          },
        };
      }
      this.validation_types = [
        { name: "任意", value: "" },
        { name: "日期", value: "date" },
        { name: "数字", value: "number" },
        { name: "字符长度", value: "string" },
        { name: "身份证", value: "idCard" },
        { name: "电话号码", value: "phone" },
        { name: "正则表达式", value: "regex" },
      ];
    },
    IsExistId(val) {
      if (val.target.value === "") {
        this.disabled = true;
      } else {
        this.disabled = false;
      }
    },
    setFieldRequired(value) {
      this.field.valid_content.require = value;
    },
    setValidTypes(value) {
      this.field.valid_content.type = value;
    },
    fieldTypeSelected(value) {
      if (value === "date") {
        this.validation_types = this.validation_types.filter(
          (type) => type.value === value || type.value === ""
        );
      }
    },
    openFieldAdvanced(type) {
      if (this.stayTop) {
        this.$emit("changeStayTop", false);
        this.$editor.warning("固定功能已取消");
      }
      this.$emit("openFieldAdvanced", type, "field", this.field);
      this.$el.style.display = "none";
    },
    //打开编辑下拉列表模态框
    openSelectOptions() {
      if (!this.searchMode) {
        this.showSelectOption = true;
      }
    },
    /**
     * 设置数字校验 或者 字符长度阈值
     */
    setThreshold(type) {
      if (type === "string") {
        this.field.valid_content.rule.max_length = this.str_max_length;
        this.field.valid_content.rule.min_length = this.str_min_length;
      } else if (type === "number") {
        this.field.valid_content.rule.max_num = this.max_num;
        this.field.valid_content.rule.min_num = this.min_num;
      }
    },
    setFieldPhoneType(value) {
      this.field.valid_content.phone_type = value
        ? "mobile_phone"
        : "fixed_phone";
    },

    handleChange(value, key, column) {
      const newData = [...this.source_list];
      const target = newData.filter((item) => key === item.key)[0];
      if (target) {
        target[column] = value;
        this.source_list = newData;
      }
    },
    deleteRow(key) {
      if (this.source_list.length === 1) {
        return;
      }
      this.source_list = this.source_list.filter((item) => item.key !== key);
    },
    handleAdd() {
      const new_source_list = [
        { key: this.editor.utils.getUUID(), text: "", code: "", value: null },
      ];
      this.source_list = this.source_list.concat(new_source_list);
    },
    tableSubmit() {
      this.showSelectOption = false;
      const new_source_list = [];
      this.source_list.forEach((e) => {
        new_source_list.push({ text: e.text, value: e.value, code: e.code });
      });
      this.field.source_list = new_source_list;
    },
    tableCancel() {
      this.showSelectOption = false;
    },
    setValidAttr(field) {
      this.field["valid_content"] = JSON.parse(
        JSON.stringify(field["valid_content"])
      );
      this.field["valid_content"].require = field["valid_content"].require
        ? 1
        : 0;
      const str_max = Number(this.field.valid_content.rule.max_length);
      this.str_max_length = isNaN(str_max) ? 20 : str_max;
      const str_min = Number(this.field.valid_content.rule.min_length);
      this.str_min_length = isNaN(str_min) ? 0 : str_min;
      const max_num = Number(this.field.valid_content.rule.max_num);
      this.max_num = isNaN(max_num) ? null : max_num;
      const min_num = Number(this.field.valid_content.rule.min_num);
      this.min_num = isNaN(min_num) ? null : min_num;
    },
    handleStayTopClick() {
      if (this.btnColor === "#ffffff") {
        this.btnColor = "rgb(230,230,230)";
        this.$emit("changeStayTop", true);
      } else {
        this.btnColor = "#ffffff";
        this.$emit("changeStayTop", false);
      }
    },
  },
};
</script>
<style scoped>
.editable-row-operations a {
  margin-right: 8px;
}

.footer {
  display: flex;
  justify-content: space-between;
  padding-top: 35px;
}

.parent-footer {
  position: relative;
  right: 0;
}
.display {
  display: flex;
}
.set-date {
  width: 130px;
}

.prop-col {
  display: flex;
  margin-top: 10px;
  display: inline-block;
}

.prop-col label {
  margin-left: 20px;
  display: inline-block;
  width: 100px;
}

.table-modal /deep/ .ant-input-sm {
  height: 30px;
}

.textTitle {
  color: black;
  font-weight: 700;
}

.cascade-select-div:hover {
  background-color: rgb(231, 247, 255);
}

/* .table-modal /deep/.ant-select-selection {
  width: 188px;
} */

.tags {
  background-color: rgb(255, 255, 255);
}

.prop-type-first {
  margin-top: 0;
}

.formula-div {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.stay-top-btn {
  border: 1px solid rgb(255, 255, 255);
  position: absolute;
  top: 4px;
  left: 490px;
  transition: none;
}

.stay-top-btn:hover {
  border: 1px solid rgb(230, 230, 230);
}

.formula-input {
  border: 1px solid rgb(232, 232, 232);
  width: 350px;
  border-radius: 4px;
  line-height: 26px;
  min-height: 26px;
  padding-left: 5px;
  padding-right: 5px;
  cursor: pointer;
}

.attention {
  position: absolute;
  bottom: 45px;
  text-align: center;
  color: red;
}
.editor-modal-x /deep/ .ant-modal-close-x {
  height: 40px;
  width: 30px;
  margin-right: 10px;
  line-height: 40px;
}
.editor-flex-container {
  display: flex;
  align-items: center;
}
</style>
