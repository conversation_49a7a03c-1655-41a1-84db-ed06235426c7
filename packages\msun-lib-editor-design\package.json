{"name": "msun-lib-editor-design", "version": "10.23.0", "type": "module", "files": ["dist"], "module": "./dist/msunLibEditorDesign.esm.js", "exports": {".": "./src/App.vue"}, "publishConfig": {"exports": {".": "./dist/msunLibEditorDesign.esm.js"}}, "scripts": {"dev": "vite", "serve": "vite", "build": "vite build", "build1": "vite build --config vite-config-example.js", "example": "vite build --config vite-config-example.js", "lib": "vite build"}, "dependencies": {"ant-design-vue": "^1.7.8", "msun-editor-vue": "workspace:*", "vcolorpicker": "^1.1.0"}, "projectType": "library", "devDependencies": {"@originjs/vite-plugin-commonjs": "^1.0.3", "@vitejs/plugin-vue2": "^2.2.0", "@vue/eslint-config-prettier": "^7.1.0", "marked": "^0.3.5", "axios": "^1.7.2", "eslint": "^7.28.0", "eslint-plugin-vue": "^9.15.1", "less": "^4.1.3", "prettier": "^2.8.8", "vite": "^4.4.3", "vite-plugin-antdv-fix": "^1.0.3", "vite-plugin-css-injected-by-js": "^3.2.0", "vue": "2.7.14", "vuex": "^3.4.0", "vue-template-compiler": "2.7.14"}, "peerDependencies": {"vue": "2.7.14"}, "realVersion": "10.23.0"}