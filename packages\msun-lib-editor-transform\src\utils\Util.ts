import { SkipMode } from "../../../msun-lib-editor-common/src/editor/Constant";

declare let ActiveXObject: any;

export function getImageSrcWithWhiteBgc (image: HTMLImageElement, type = "image/png") {
  const canvas = document.createElement("canvas");
  canvas.width = image.width;
  canvas.height = image.height;
  const context = canvas.getContext("2d")!;
  context.fillStyle = "#fff";
  context.fillRect(0, 0, canvas.width, canvas.height);
  context.drawImage(image, 0, 0);
  const src = canvas.toDataURL(type, 0.96);
  return src;
}
/**
 * 将string转xml对象
 * @param xmlString
 */
export function loadXML (xmlString:string) {
  let xmlDoc = null;
  if (!window.DOMParser && ActiveXObject) {
    const xmlDomVersions = ["MSXML.2.DOMDocument.6.0", "MSXML.2.DOMDocument.3.0", "Microsoft.XMLDOM"];
    for (let i = 0; i < xmlDomVersions.length; i++) {
      try {
        // eslint-disable-next-line no-undef
        xmlDoc = new ActiveXObject(xmlDomVersions[i]);
        xmlDoc.async = false;
        xmlDoc.loadXML(xmlString);
        break;
      } catch (e) {
        console.error(e);
      }
    }
  } else if (window.DOMParser && document.implementation) {
    try {
      const domParser = new DOMParser();
      xmlDoc = domParser.parseFromString(xmlString, "text/xml");
    } catch (e) {
      console.error(e);
    }
  } else {
    return null;
  }

  return xmlDoc;
}

// xml对象转json
export function xmlToJson (xml:any) {
  // Create the return object
  let obj:any = {};

  if (xml.nodeType === 1) { // element
    // do attributes
    if (xml.attributes.length > 0) {
      obj["@attributes"] = {};
      for (let j = 0; j < xml.attributes.length; j++) {
        const attribute = xml.attributes.item(j);
        obj["@attributes"][attribute.nodeName] = attribute.nodeValue;
      }
    }
  } else if (xml.nodeType === 3) { // text
    obj = xml.nodeValue;
  }

  // do children
  if (xml.hasChildNodes()) {
    for (let i = 0; i < xml.childNodes.length; i++) {
      const item = xml.childNodes.item(i);
      const nodeName = item.nodeName;
      if (typeof (obj[nodeName]) === "undefined") {
        obj[nodeName] = xmlToJson(item);
      } else {
        if (typeof (obj[nodeName].push) === "undefined") {
          const old = obj[nodeName];
          obj[nodeName] = [];
          obj[nodeName].push(old);
        }
        obj[nodeName].push(xmlToJson(item));
      }
    }
  }
  return obj;
};
/**
 * 新建原始数据初始化模型对象  默认值
 * @param type
 */
export default function newRawInitModel (
  type: "p" | "table" | "cell" | "field" | "group" | "image" | "text" | "widget" | "style" | "line" | "box" | "button" | "fraction"
): any {
  // { // 最基础的文本域的样式
  //   height: 0,
  //   family: "宋体",
  //   underline: false,
  //   strikethrough: false,
  //   bgColor: ""
  // }
  switch (type) {
    case "p":
      return {
        id: "",
        type: type,
        align: "left",
        deepNum: 0, // list的级别
        islist: false, // 是否为list
        isOrder: false, // 是否为有序列表
        indentation: 0, // 首行缩进量
        dispersed_align: false, // 分散对齐
        before_paragraph_spacing: 0, // 段间距
        restart_list_index: false, // 是否序号重排
        row_ratio: 1.6, // 行间距
        page_break: false, // 是否换页
        children: [], //
        title_length: 0,
        content_padding_left: 0
      };
    case "table":
      // 如果是table
      return {
        id: "",
        name: null,
        type: type,
        col_size: [],
        meta: {}, // 允许他们自定义存储数据
        row_size: [],
        min_row_size: [],
        cells: [],
        notAllowDrawLine: { // 保存不让画的线
          row: [], // 这是不让画的线
          col: [],
          changeOpacityRow: [], // 这是修改透明度的线
          changeOpacityCol: [] // 这是修改透明度的线
        },
        newPage: false,
        skipMode: SkipMode.ROW,
        fixed_table_header_num: 0,
        editableInForMode: false,
        rowLineType: 0,
        tableFiexedStyle: false,
        fullPage: false
      };
    case "cell":
      return {
        id: "",
        pos: [],
        style: {
          bgColor: null
        },
        colspan: 1,
        rowspan: 1,
        noWrap: false,
        children: [],
        split_parts: [],
        vertical_align: "top",
        padding_left: 0,
        padding_top: 0,
        padding_right: 0,
        padding_bottom: 0,
        is_show_slash_up: false,
        is_show_slash_down: false,
        is_show_line_top: true,
        is_show_line_right: true,
        is_show_line_bottom: true,
        is_show_line_left: true,
        rowLineType: 0,
        aggregationMode: 0,
        lock: false,
        set_cell_height: {
          type: "normal"
        },
        meta: {}
      };
    case "field":
      return {
        type: type,
        id: "",
        field_type: "normal",
        start_symbol: "[",
        end_symbol: "]",
        display_type: "normal",
        readonly: 0,
        deletable: 1,
        canBeCopied: 1,
        replaceRule: [],
        placeholder: "",
        tip: "",
        source_id: "",
        source_list: [],
        show_format: "",
        replace_format: "",
        number_format: "",
        meta: {},
        active_type: 0,
        multi_select: 0,
        separator: 0,
        ext_cell: null,
        inputMode: 0,
        align: "left",
        max_width: 0,
        min_width: 0,
        style: {},
        cascade_list: [],
        automation_list: [],
        valid: 0,
        valid_content: {
          require: true,
          type: "string",
          phone_type: "",
          rule: {},
          regex: ""
        },
        box_checked: 0,
        required: 0,
        box_multi: 0,
        children: []
      };
    case "group":
      return {
        id: "",
        name: "",
        date: "",
        lock: false,
        content_para_id: [],
        is_form: false,
        header_info: "",
        replace: false,
        page_break: false,
        new_page: false,
        meta: {}
      };
    case "text":
      return {
        type: type, // TODO 此处注释掉style 还原就还原不回去了
        // style: {
        //   height: 0,
        //   family: "仿宋",
        //   underline: false,
        //   strikethrough: false,
        //   bgColor: ""
        // },
        value: ""
      };
    case "fraction":
      return {
        type,
        value: ""
      };
    case "line":
      return {
        type: type,
        color: "black",
        height: 16
      };
    case "box":
      return {
        type: type,
        height: 16,
        name: "",
        content: {},
        meta: {}
      };
    case "image":
      return {
        type: type,
        meta: {},
        src: "",
        field_id: "",
        field_position: "normal",
        width: 50,
        height: 50
      };
    case "widget":
      return {
        type: type,
        field_id: "",
        disabled: 0,
        selected: false,
        field_position: "normal",
        widgetType: "checkbox",
        height: 16,
        selectNum: undefined,
        params: {
          num: 10,
          spacing: 20
        }
      };
    case "button":
      return {
        type: type,
        color: "black",
        field_id: "",
        field_position: "normal",
        height: 16,
        width: 60,
        value: "请输入文字",
        meta: {}
      };
    case "style":
      return {
        id: "",
        height: 16,
        family: "宋体",
        bold: false,
        italic: false,
        underline: false,
        dblUnderLine: false,
        strikethrough: false,
        script: 3,
        color: "#000",
        bgColor: null,
        highLight: null,
        characterSpacing: 0
      };
  }
}
