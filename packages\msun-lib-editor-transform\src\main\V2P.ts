/* eslint-disable no-labels */
import Editor from "../../../msun-lib-editor-common/src/editor/Editor";

import { changeDpiDataUrl, getImageSrcWithWhiteBgc } from "../../../msun-lib-editor-common/src/editor/ImageDpiUtil";
import { isBoolean, numberToChinese, getIntersection, isRow, isTable, versionDiff } from "../../../msun-lib-editor-common/src/editor/Utils";
import { Config, font_size_config } from "../../../msun-lib-editor-common/src/editor/Config";
import { ScriptType } from "../../../msun-lib-editor-common/src/editor/Constant";
import { isFraction, isImage, isLine } from "../../../msun-lib-editor-common/src/editor/Helper";
import Row from "../../../msun-lib-editor-common/src/editor/Row";
import { RowLineType } from "../../../msun-lib-editor-common/src/editor/Definition";
import Cell from "../../../msun-lib-editor-common/src/editor/Cell";
import Group from "../../../msun-lib-editor-common/src/editor/Groups";
import Table from "../../../msun-lib-editor-common/src/editor/Table";
import Fraction from "../../../msun-lib-editor-common/src/editor/Fraction";
export const V2P: any = {
  fontMap: new Map(), // 所有的字体样式
  startPrintPageNumber: 0, // 开始打印页码
  config: {},
  editor: null,
  printType: "cpp", // 只有指定为pdf时才是pdf打印
  areaPrintInfo: {
    cover_height: 0,
    end_area_location: 0
  },
  imageMap: null,
  printStatus: {},
  specialCharHandle: null,
  initExtOptions (instance: any) {
    this.specialCharHandle = instance.utils.specialCharHandle;
    this.isTibetan = instance.utils.isTibetan;
  },
  // 设置精度
  setAccuracy (number: number, n: number = 3) {
    return Math.round(number * Math.pow(10, n)) / Math.pow(10, n);
  },
  init (editor: Editor, extraInfo: any) {
    this.editor = editor;
    this.fontMap.clear();
    this.imageMap = editor.imageMap.get();
    this.startPrintPageNumber = 0;
    this.printType = extraInfo.printType;
    this.areaPrintInfo = {
      cover_height: 0,
      end_area_location: 0
    };
    this.printStatus = {
      area_print: editor.area_print,
      area_location: editor.internal.area_location,
      page_left: editor.page_left,
      page_size: editor.page_size,
      print_continue: editor.print_continue,
      print_absolute_y: editor.internal.print_absolute_y
    };

    this.config = { // 这是打印时候获取到的配置项
      img_margin: Config.img_margin,
      page_padding_left: editor.config.page_padding_left,
      page_padding_right: editor.config.page_padding_right,
      source: editor.config.source,
      show_header_line: editor.config.show_header_line,
      show_footer_line: editor.config.show_footer_line,
      rowLineType: editor.config.rowLineType,
      startPageNumber: editor.config.startPageNumber,
      editor_padding_top: editor.config.editor_padding_top,
      page_margin_bottom: editor.config.page_margin_bottom,
      show_field_symbol: editor.config.show_field_symbol,
      keepSymbolWidthWhenPrint: !!editor.document_meta.keepSymbolWidthWhenPrint
    };
  },
  clear () {
    this.editor = null;
    this.imageMap = null;
    this.fontMap.clear();
  },
  getPosition (direction: string, char: any, item: any, cell: any, offset: number = 0) {
    let pos: number = 0;
    const baseLeft = item.left + (cell && cell.parent ? cell.left + cell.parent.left : 0);
    const baseTop = item.bottom + (cell && cell.parent ? cell.top + cell.parent.top : 0);
    switch (direction) {
      case "left":
        pos = char.left + baseLeft;
        break;
      case "right":
        pos = char.right + baseLeft;
        break;
      case "top":
        pos = baseTop;
        break;
      case "bottom":
        pos = baseTop;
        break;
    }
    return this.setAccuracy(pos + offset);
  },
  areaPrint (char: any, item: any, cell?: any) {
    const { area_print, area_location, page_left } = this.printStatus;
    if (area_print) {
      const print_left = area_location.start_absolute_x - page_left + 2;
      const print_right = area_location.end_absolute_x - page_left - 2;
      const abs_left = this.getPosition("left", char, item, cell);
      const abs_right = this.getPosition("right", char, item, cell);
      if ((abs_left <= print_left && abs_right <= print_left) || (abs_left >= print_right && abs_right >= print_right)) {
        return false;
      }
    }
    return true;
  },
  drawFieldLine (row: any, cell: any, curPage: any) {
    for (const key in row.field_chars_in_row) {
      const chars = row.field_chars_in_row[key].meta;
      const type = row.field_chars_in_row[key].type;
      const first_char = chars[0];
      const last_char = chars[chars.length - 1];
      const baseLeft = row.left + (cell && cell.parent ? cell.left + cell.parent.left : 0);
      const baseTop = row.bottom - row.padding_vertical + (cell && cell.parent ? cell.top + cell.parent.top : 0) + 2;
      const hasStart = first_char.field_position === "start";
      const hasEnd = last_char.field_position === "end";
      if (type === "line") {
        curPage.push({
          type: "line",
          left: hasStart ? first_char.left + 3 + baseLeft : this.getPosition("left", { left: 0 }, row, cell),
          top: baseTop,
          right: hasEnd ? last_char.right + 2 + baseLeft : this.getPosition("right", { right: row.width }, row, cell),
          bottom: baseTop
        });
      } else if (type === "printInput") {
        let start_x = first_char.left - 2;
        let end_x = last_char.right + 2;

        if (first_char.field_position === "start") {
          start_x = this.printType === "pdf" ? first_char.left + first_char.width / 2 : first_char.left + first_char.width / 2 + 2;
        }
        if (last_char.field_position === "end") {
          end_x = this.printType === "pdf" ? last_char.right - last_char.width / 2 : last_char.right - last_char.width / 2 + 3;
        }
        if (!hasStart) {
          start_x = 0;
        }
        if (!hasEnd) {
          end_x = row.width;
        }
        // 获取chars.height中具有最大高度的值
        const maxChar = chars.reduce((prev: any, current: any) => { return (prev.height > current.height) ? prev : current; }, 0);
        if (!maxChar) continue;
        let height = 1.4 * maxChar.height + row.padding_vertical / 4 - 3;
        let y = row.height - height - 4;
        if (isImage(maxChar)) {
          height = maxChar.height;
          y = row.height - height;
        }
        const offset = this.printType === "pdf" ? 4 : 3;
        curPage.push({
          type: "rect",
          left: this.getPosition("left", first_char, row, cell, start_x + 0.5 - first_char.left),
          top: this.getPosition("top", maxChar, row, cell, y - row.height + offset),
          width: this.setAccuracy(end_x - start_x),
          height: this.setAccuracy(height)
        });
      }
    }
    // 如果当前行为空行，并且上一行存在field_chars_in_row， 则直接绘制
    if (!row.children.length && !row.linebreak && isRow(row.pre_row)) {
      const preHasCharRow = row.getPreRowNotEmpty(row);
      const char = preHasCharRow.children[preHasCharRow.children.length - 1];
      if (char.colspan || !char || !char.field_id) {
        return;
      }
      const field = row.parent.getFieldById(char.field_id)!;
      const maxChar = (preHasCharRow as Row).children.reduce((prev: any, current: any) => { return (prev.height > current.height) ? prev : current; }, 0);
      // 获取chars.height中具有最大高度的值
      if (field?.display_type === "line") {
        const baseTop = row.bottom - row.padding_vertical + (cell && cell.parent ? cell.top + cell.parent.top : 0) + 2;
        const offsetX = this.printType === "pdf" ? 0 : 1.5;
        curPage.push({
          type: "line",
          left: this.getPosition("left", { left: offsetX }, row, cell),
          top: this.setAccuracy(baseTop),
          right: this.getPosition("right", { right: row.width }, row, cell),
          bottom: this.setAccuracy(baseTop)
        });
      }
      if (field?.display_type === "input" || field?.display_type === "printInput") {
        const { y, height } = row.getFieldShowTypeParam(maxChar, field.display_type);
        const offsetY = this.printType === "pdf" ? 4 : 3;
        const offsetX = this.printType === "pdf" ? 0 : 2;
        curPage.push({
          type: "rect",
          left: this.getPosition("left", { left: offsetX }, row, cell, 0),
          top: this.getPosition("top", maxChar, row, cell, y - row.height + offsetY),
          width: this.setAccuracy(row.width),
          height: this.setAccuracy(height)
        });
      }
    }
  },
  setClip (type:string, row: any, cell: any, curPage: any) {
    if (this.printType === "pdf") {
      if (type === "end") {
        curPage.push({
          type: "coverRect",
          width: this.setAccuracy(cell.width),
          height: this.setAccuracy(row.height - (cell.height - row.top)),
          left: this.setAccuracy(cell && cell.parent ? cell.left + cell.parent.left : 0),
          top: this.setAccuracy((cell && cell.parent ? cell.bottom + cell.parent.top : 0))
        });
      }
    } else {
      if (type === "start") {
        curPage.push({
          type: "startClip",
          width: this.setAccuracy(cell.width),
          height: this.setAccuracy(cell.height - row.top),
          left: this.setAccuracy(cell && cell.parent ? cell.left + cell.parent.left : 0),
          top: this.setAccuracy(row.top + (cell && cell.parent ? cell.top + cell.parent.top : 0))
        });
      } else {
        curPage.push({ type: "endClip" });
      }
    }
  },
  drawChar (chars: any, item: any, cell: any, curPage: any) {
    for (let j = 0; j < chars.length; j++) {
      const char = chars[j];
      if (this.printType === "pdf" && ((char.value.length > 1 || this.isTibetan(char.value)) || this.specialCharHandle.specialSymbol.includes(char.value))) {
        let src = "";
        if (this.editor.raw.imageSrcObj) {
          src = this.editor.raw.imageSrcObj[char.value];
        }
        if (!src && this.editor.config.source !== "server") {
          src = this.specialCharHandle.convertImg(char);
        }
        if (src) {
          this.drawImage({ src, width: char.width, height: char.height, left: char.left, top: char.top, font: char.font }, item, cell, curPage);
        }
        continue;
      }
      const area_print_result = this.areaPrint(char, item, cell);
      if (!area_print_result) continue;
      if (char.type === "box") {
        continue;
      }
      // 如果存在字体样式则添加
      if (char.font) {
        this.fontMap.set(char.font.id, char.font);
      }
      if (isFraction(char)) {
        const real_height = -item.height + char.height / 2 + item.padding_vertical;
        this.drawFraction(char, item, cell, real_height, curPage);
        continue;
      } if (char.widgetType) {
        this.drawWidget(char, item, cell, curPage);
        continue;
      } else if (isImage(char)) {
        this.drawImage(char, item, cell, curPage);
        continue;
      } else if (isLine(char)) {
        if (char.form === "solid") {
          curPage.push({
            type: "line",
            left: this.setAccuracy(this.getPosition("left", char, item, cell)),
            top: this.getPosition("top", char, item, cell, -item.height / 2),
            right: this.setAccuracy(this.getPosition("right", char, item, cell)),
            bottom: this.getPosition("bottom", char, item, cell, -item.height / 2),
            color: char.color,
            lineHeight: Number(char.line_height)

          });
        } else if (char.form === "dash") {
          const interval = 6;
          let left = this.getPosition("left", char, item, cell);
          while (left < this.getPosition("right", char, item, cell)) {
            curPage.push({
              type: "line",
              left: this.setAccuracy(left),
              top: this.getPosition("top", char, item, cell, -item.height / 2),
              right: this.setAccuracy(left + interval),
              bottom: this.getPosition("bottom", char, item, cell, -item.height / 2),
              color: char.color,
              lineHeight: Number(char.line_height)
            });
            left += 2 * interval;
          }
        }
        continue;
      } else if (char.type === "button") {
        continue;
      }
      if (char.field_position !== "normal") {
        continue;
      }
      if (char.font.script !== ScriptType.NORMAL) {
        if (!char.value) {
          continue;
        }
        let real_height = -item.height + char.height / 2 + item.padding_vertical;
        if (this.printType === "pdf") {
          real_height = char.font.script === 2 ? -item.padding_vertical : -item.height + char.height / 2 + item.padding_vertical;
        } else {
          real_height = char.font.script === 2 ? -char.height / 2 - item.padding_vertical : -item.height + item.padding_vertical;
        }

        const charHight = Math.round(char.height * item.paragraph.row_ratio);
        // 字体大小乘以行倍距和行高一样时不调整字符上中下对齐的高度
        if (charHight !== item.height) {
          if (char.font.align === "top") {
            if (this.printType === "pdf") {
              real_height = char.font.script === 2 ? -item.height + charHight + item.padding_vertical : -item.height + charHight - char.height / 2 + item.padding_vertical;
            } else {
              real_height = char.font.script === 2 ? -item.height + char.height / 2 + item.padding_vertical : -item.height + item.padding_vertical;
            }
          } else if (char.font.align === "middle") {
            if (this.printType === "pdf") {
              real_height = char.font.script === 2 ? -item.height / 2 + char.height / 2 : -item.height / 2 + char.height - item.padding_vertical;
            } else {
              real_height = char.font.script === 2 ? -item.height / 2 : -item.height / 2 - char.height / 2;
            }
          }
        }
        curPage.push({
          type: "text",
          fontId: char.font.id,
          value: char.value,
          width: this.setAccuracy(char.width),
          bgHeight: this.setAccuracy(item.height),
          bgWidth: this.setAccuracy(char.draw_width),
          bgTop: this.getPosition("top", char, item, cell, real_height - item.padding_vertical),
          left: this.getPosition("left", char, item, cell, 2),
          top: this.getPosition("top", char, item, cell, real_height)

        });
      } else {
        const leftX = this.printType === "pdf" ? 2.5 : 0;
        const drawValue = char.isShowAsterisk(this.editor) ? "*" : char.value;
        if (!char.value) {
          continue;
        }
        const charHight = Math.round(char.height * item.paragraph.row_ratio);
        let real_height = this.printType === "pdf" ? -item.padding_vertical : -char.height - item.padding_vertical;
        // 字体大小乘以行倍距和行高一样时不调整字符上中下对齐的高度
        if (charHight !== item.height) {
          const height = char.height * item.paragraph.row_ratio;
          if (char.font.align === "top") {
            real_height = this.printType === "pdf" ? -item.height + height + item.padding_vertical : -item.height + item.padding_vertical;
          } else if (char.font.align === "middle") {
            real_height = this.printType === "pdf" ? -item.height / 2 + height / 2 : -item.height / 2 - char.height / 2;
          }
        }

        const charInfo: any = {
          type: "text",
          fontId: char.font.id,
          value: drawValue,
          width: this.setAccuracy(char.width),
          bgHeight: this.setAccuracy(item.height),
          bgWidth: this.setAccuracy(char.draw_width),
          bgTop: this.getPosition("top", char, item, cell, real_height - item.padding_vertical),
          left: this.getPosition("left", char, item, cell, leftX),
          top: this.getPosition("top", char, item, cell, real_height)
        };
        if (char.width !== char.ori_width) {
          charInfo.width = charInfo.width * (this.printType === "pdf" ? 1 : 1.5);
          charInfo.limitWidth = true;
        }
        curPage.push(charInfo);
      }
      if (char.font.underline || char.font.dblUnderLine) {
        curPage.push({
          type: "line",
          left: this.getPosition("left", char, item, cell),
          top: this.getPosition("top", char, item, cell, -item.padding_vertical),
          right: this.getPosition("right", char, item, cell),
          bottom: this.getPosition("bottom", char, item, cell, -item.padding_vertical)
        });
        if (char.font.dblUnderLine) {
          curPage.push({
            type: "line",
            left: this.getPosition("left", char, item, cell),
            top: this.getPosition("top", char, item, cell, -item.padding_vertical + 2),
            right: this.getPosition("right", char, item, cell),
            bottom: this.getPosition("bottom", char, item, cell, -item.padding_vertical + 2)
          });
        }
      }
      if (char.font.strikethrough) {
        curPage.push({
          type: "line",
          left: this.getPosition("left", char, item, cell),
          top: this.getPosition("top", char, item, cell, -item.padding_vertical - char.height / 2),
          right: this.getPosition("right", char, item, cell),
          bottom: this.getPosition("bottom", char, item, cell, -item.padding_vertical - char.height / 2)
        });
      }
    }
  },
  drawFraction (fraction: Fraction, row: Row, cell: Cell, realHight: number, curPage: any) {
    const left = this.getPosition("left", fraction, row, cell, 0);
    const top = row.bottom + (cell && cell.parent ? cell.top + cell.parent.top : 0) - fraction.font.height - row.padding_vertical;
    if (fraction.int && fraction.int !== "undefined") {
      curPage.push({
        type: "text",
        fontId: fraction.font.id,
        value: fraction.int,
        width: this.setAccuracy(fraction.intWidth),
        bgHeight: this.setAccuracy(row.height),
        bgWidth: this.setAccuracy(fraction.intWidth),
        bgTop: this.getPosition("top", fraction, row, cell, realHight - row.padding_vertical),
        left,
        top
      });
    }

    const max = Math.max(fraction.numeratorWidth, fraction.denominatorWidth);
    const min = Math.min(fraction.numeratorWidth, fraction.denominatorWidth);

    const numeratorStartX = left + fraction.intWidth + (max === fraction.numeratorWidth ? 0 : (max - min) / 2);
    const numeratorStartY = top - fraction.font.height / 2 - 2; // 最后 -2 是要有一点距离 要不不好看

    curPage.push({
      type: "text",
      fontId: fraction.font.id,
      value: fraction.numerator,
      width: this.setAccuracy(fraction.numeratorWidth),
      bgHeight: this.setAccuracy(row.height),
      bgWidth: this.setAccuracy(fraction.numeratorWidth),
      bgTop: this.getPosition("top", fraction, row, cell, realHight - row.padding_vertical),
      left: numeratorStartX,
      top: numeratorStartY
    });

    curPage.push({
      type: "line",
      left: left + fraction.intWidth + 3,
      top: top + fraction.font.height / 2,
      right: left + fraction.intWidth + max + 3,
      bottom: top + fraction.font.height / 2
    });

    const denominatorStartX = left + fraction.intWidth + (max === fraction.denominatorWidth ? 0 : (max - min) / 2);
    const denominatorStartY = top + fraction.font.height / 2 + 2; // 跟上边的 -2 是一个道理 +2 也是要有点距离 好看一点
    curPage.push({
      type: "text",
      fontId: fraction.font.id,
      value: fraction.denominator,
      width: this.setAccuracy(fraction.denominatorWidth),
      bgHeight: this.setAccuracy(row.height),
      bgWidth: this.setAccuracy(fraction.numeratorWidth),
      bgTop: this.getPosition("top", fraction, row, cell, realHight - row.padding_vertical),
      left: denominatorStartX,
      top: denominatorStartY
    });
  },
  drawWidget (char: any, row: any, cell: any, curPage: any) {
    const typeOffset = this.printType === "pdf" ? 2.5 : 5;

    let real_height = row.height;
    const charHight = Math.round(char.height * row.paragraph.row_ratio);
    if (charHight !== real_height) {
      if (char.font.align === "top") {
        real_height = char.height + row.padding_vertical;
      } else if (char.font.align === "middle") {
        real_height = row.height / 2 + row.padding_vertical + char.height / 2;
      }
    }
    real_height = real_height - row.height - row.padding_vertical - char.height;

    if (char.widgetType === "checkbox") {
      if (char.border !== "dashed" && char.border !== "dotted") {
        curPage.push({
          type: "rect",
          left: this.getPosition("left", char, row, cell, typeOffset),
          top: this.getPosition("top", char, row, cell, real_height + typeOffset),
          width: this.setAccuracy(char.height - 5),
          height: this.setAccuracy(char.height - 5)
        });
      }
      if (char.selected) {
        curPage.push({
          type: "line",
          left: this.getPosition("left", char, row, cell, typeOffset),
          top: this.getPosition("top", char, row, cell, real_height + typeOffset + (char.height - 5) / 3),
          right: this.getPosition("left", char, row, cell, typeOffset + ((char.height - 5) * 7) / 20),
          bottom: this.getPosition("bottom", char, row, cell, real_height + typeOffset + ((char.height - 5) * 4) / 5)
        });

        curPage.push({
          type: "line",
          left: this.getPosition("left", char, row, cell, typeOffset + ((char.height - 5) * 7) / 20),
          top: this.getPosition("top", char, row, cell, real_height + typeOffset + ((char.height - 5) * 4) / 5),
          right: this.getPosition("left", char, row, cell, 2.5 + (char.height - 5)),
          bottom: this.getPosition("bottom", char, row, cell, real_height + typeOffset)
        });
      }
    } else if (char.widgetType === "radio") {
      const typeOffset = this.printType === "pdf" ? 0 : 2.5;
      const firstOffset = char.height / 2 + typeOffset;
      curPage.push({
        type: "circle",
        left: this.getPosition("left", char, row, cell, firstOffset),
        top: this.getPosition("top", char, row, cell, real_height + firstOffset),
        radius: this.setAccuracy((char.height - 5) / 2)
      });
      if (char.selected) {
        curPage.push({
          type: "circle",
          left: this.getPosition("left", char, row, cell, firstOffset),
          top: this.getPosition("top", char, row, cell, real_height + firstOffset),
          radius: this.setAccuracy((char.height - 5) / 4),
          selected: true
        });
      }
    } else if (char.widgetType === "caliper") {
      const top = (row.height - char.height) / 2;
      const r_top = 0.1 * char.height;
      const typeOffset = this.printType === "pdf" ? 2.5 : 5;
      const topOffset = char.height;
      const offsetTop = this.printType === "pdf" ? 0 : -char.font.height;
      if (char.params && char.params.num && char.params.spacing) {
        curPage.push({
          type: "line",
          left: this.getPosition("left", char, row, cell, typeOffset + 5),
          top: this.getPosition("top", char, row, cell, -topOffset),
          right: this.getPosition("left", char, row, cell, typeOffset + 5),
          bottom: this.getPosition("bottom", char, row, cell, row.height - 12 - topOffset)
        });
        curPage.push({
          type: "text",
          fontId: char.font.id,
          value: "0",
          bgHeight: this.setAccuracy(char.font.height),
          bgWidth: this.setAccuracy(char.font.height),
          bgTop: this.getPosition("top", char, row, cell),
          width: this.setAccuracy(char.font.height),
          left: this.getPosition("left", char, row, cell, 5),
          top: this.getPosition("top", char, row, cell, offsetTop)
        });

        for (let i = 1; i < char.params.num; i++) {
          curPage.push({
            type: "line",
            left: this.getPosition("left", char, row, cell, typeOffset + 5 + char.params.spacing * i),
            top: this.getPosition("top", char, row, cell, top + r_top - topOffset),
            right: this.getPosition("left", char, row, cell, typeOffset + 5 + char.params.spacing * i),
            bottom: this.getPosition("bottom", char, row, cell, top + 0.9 * char.height - 12 - topOffset)
          });
          let offset = 4;
          if (i > 9) {
            offset = 1;
          }
          curPage.push({
            type: "text",
            fontId: char.font.id,
            value: i + "",
            bgHeight: this.setAccuracy(char.font.height),
            bgWidth: this.setAccuracy(char.font.height),
            bgTop: this.getPosition("top", char, row, cell),
            width: this.setAccuracy(char.font.height),
            left: this.getPosition("left", char, row, cell, char.params.spacing * i + offset),
            top: this.getPosition("top", char, row, cell, offsetTop)
          });
        }

        curPage.push({
          type: "line",
          left: this.getPosition("left", char, row, cell, typeOffset + char.width - 5),
          top: this.getPosition("top", char, row, cell, -topOffset),
          right: this.getPosition("left", char, row, cell, typeOffset + char.width - 5),
          bottom: this.getPosition("bottom", char, row, cell, row.height - 12 - topOffset)
        });
        curPage.push({
          type: "text",
          fontId: char.font.id,
          value: char.params.num + "",
          bgHeight: this.setAccuracy(char.font.height),
          bgWidth: this.setAccuracy(char.font.height),
          bgTop: this.getPosition("top", char, row, cell),
          width: this.setAccuracy(char.font.height),
          left: this.getPosition("left", char, row, cell, char.width - 10),
          top: this.getPosition("top", char, row, cell, offsetTop)
        });
        curPage.push({
          type: "line",
          left: this.getPosition("left", char, row, cell, typeOffset + 5),
          top: this.getPosition("top", char, row, cell, top + char.height / 2 - 6 - topOffset),
          right: this.getPosition("left", char, row, cell, typeOffset + char.width - 5),
          bottom: this.getPosition("bottom", char, row, cell, top + char.height / 2 - 6 - topOffset)
        });
        if (char.selectNum) {
          const circleOffsetX = this.printType === "pdf" ? 7.5 : 11.5;
          const circleOffsetY = this.printType === "pdf" ? 0 : 1.5;
          curPage.push({
            type: "circle",
            left: this.getPosition("left", char, row, cell, circleOffsetX + char.params.spacing * char.selectNum),
            top: this.getPosition("top", char, row, cell, -char.height / 2 - 6 + circleOffsetY),
            radius: this.setAccuracy(char.params.spacing * 0.3)
          });
        }
      }
    }
  },
  drawTableLine (table: any, curPage: any) {
    const { end_area_location, cover_height } = this.areaPrintInfo;
    const { print_continue, area_print, page_left, area_location } = this.printStatus;
    // 本来就不画的 和 有透明度的线 都是不绘制的
    const notDrawRow = [
      ...table.notAllowDrawLine.row,
      ...table.notAllowDrawLine.changeOpacityRow
    ];
    const initY = table.top; // 不管横线还是竖线 初始起始点坐标 y
    const initX = table.left; // 不管横线还是竖线 初始起始点坐标 x
    const row_size = table.row_size; // 缓存row_size数组
    for (
      let row_index = 0, len = row_size.length;
      row_index <= len;
      row_index++
    ) {
      const currentY =
        row_size
          .slice(0, row_index)
          .reduce((prev: number, current: number) => prev + current, 0) + initY;
      // 续打位置判断条件 区域打印开始的位置
      const rowContinue = (print_continue || area_print) && currentY < cover_height;
      if (rowContinue) continue;
      // 区域打印结束
      const rowBreak = area_print && end_area_location && currentY >= end_area_location;
      if (rowBreak) break;
      const addWidth = 5 / table.col_size.length;
      table.col_size.forEach((col_size: any, col_index: any) => {
        // 第一步：找到不让画的线 return掉
        for (const arr of notDrawRow) {
          if (arr[0] === row_index && arr[1] === col_index) {
            return;
          }
        }
        const currentX =
          table.col_size
            .slice(0, col_index)
            .reduce((prev: number, current: number) => prev + current, 0) + initX;
        if (area_print) {
          const print_left = area_location.start_absolute_x - page_left + 2;
          const print_right = area_location.end_absolute_x - page_left - 2;
          const abs_left = this.setAccuracy(currentX);
          const abs_right = this.setAccuracy(currentX + col_size);
          if ((abs_left < print_left && abs_right < print_left) || (abs_left > print_right && abs_right > print_right)) {
            return;
          }
        }
        curPage.push({
          type: "line",
          left: this.setAccuracy(currentX + addWidth * col_index),
          top: this.setAccuracy(currentY),
          right: this.setAccuracy(currentX + addWidth * (col_index + 1) + col_size),
          bottom: this.setAccuracy(currentY)
        });
      });
    }

    // 画竖线 画竖线的条数应该是this.col_size的长度+1条 里边循环row_size 就是要画 该条竖线的几个线段
    const notDrawCol = [
      ...table.notAllowDrawLine.col,
      ...table.notAllowDrawLine.changeOpacityCol
    ];
    for (
      let col_index = 0;
      col_index <= table.col_size.length;
      col_index++
    ) {
      // 要画的这条竖线 距离该表格左边的距离
      const currentX =
        table.col_size
          .slice(0, col_index)
          .reduce((prev: number, current: number) => prev + current, 0) + initX;
      const addWidth = 5 / table.col_size.length;
      // eslint-disable-next-line no-labels
      row: for (let r = 0; r < table.row_size.length; r++) {
        const row_size = table.row_size[r];
        // 第一步：找到合并单元格记录的 不让画的线 直接return掉 在forEach里边的return 不会终止forEach的循环
        for (const arr of notDrawCol) {
          if (arr[0] === col_index && arr[1] === r) {
            // eslint-disable-next-line no-labels
            continue row;
          }
        }

        // top 是计算出 要画的这条竖线 距离该表格顶部的距离
        let currentY =
          table.row_size
            .slice(0, r)
            .reduce((prev: number, current: number) => prev + current, 0) + initY;
        if (area_print) {
          const print_left = area_location.start_absolute_x - page_left + 2;
          const print_right = area_location.end_absolute_x - page_left - 2;
          const abs = this.setAccuracy(currentX);
          if (abs <= print_left || abs >= print_right) {
            continue;
          }
        }
        // 续打位置判断条件
        const colContinue = (print_continue || area_print) && (currentY + row_size) <= cover_height;
        if (colContinue) continue;
        // 区域打印结束
        const rowBreak = area_print && end_area_location && currentY >= end_area_location;
        if (rowBreak) break;
        // 是否替换纵向坐标 条件： 续打、续打位置在竖线中间
        let end_location = row_size;
        const isreplaceCurrentY = (print_continue || area_print) && ((currentY + row_size) > cover_height && currentY <= cover_height);
        if (isreplaceCurrentY) {
          end_location = currentY + row_size - cover_height;
          currentY = cover_height;
        }
        const isAreaReplaceCurrentY = (area_print) && end_area_location && ((currentY + row_size) > end_area_location && currentY <= end_area_location);
        if (isAreaReplaceCurrentY) {
          end_location = end_area_location - currentY;
        }
        // 第三步：如果顺利走到这儿的话 就正常画线
        curPage.push({
          type: "line",
          left: this.setAccuracy(currentX + addWidth * col_index),
          top: this.setAccuracy(currentY - 0.5),
          right: this.setAccuracy(currentX + addWidth * col_index),
          bottom: this.setAccuracy(currentY + end_location + 0.5)
        });
      }
    }
  },
  drawList (row: any, cell: any, curPage: any) {
    if (row.paragraph.islist) {
      // 如果该行不是在段落的第一行
      const rowIndex = row.paragraph.children.findIndex((r: Row) => {
        return r.id === row.id;
      });
      if (rowIndex !== 0) {
        return;
      }
      // 数字序号的部分
      if (row.paragraph.isOrder) {
        const characters = row.paragraph.characters;
        const jsonSymbol = characters[0].value ? characters[0] : characters[characters.length - 1];
        const symbol = jsonSymbol.copy();
        symbol.field_position = "normal";
        let value: any = "";
        if (row.paragraph.level % 2 === 0) {
          let sign = row.paragraph.list_index % 26;
          const times = row.paragraph.list_index / 26; // 倍数 代表几个字符
          sign = times > 0 && sign === 0 ? 26 : sign;
          for (let j = 0; j < times; j++) {
            value = value + String.fromCharCode(sign + 96);
          }
        } else {
          value = row.paragraph.list_index;
        }
        let margin_left = 0;
        if (row.children.length) {
          margin_left = row.children[0].left - row.padding_left;
        } else {
          if (row.paragraph.align === "right") {
            margin_left = row.width - row.padding_left;
          } else if (row.paragraph.align === "center") {
            margin_left = row.width / 2 - row.padding_left;
          } else if (row.paragraph.align === "left") {
            margin_left = 0;
          }
        }
        if (row.paragraph.listNumStyle === "chinese") {
          // 这里将数字类型转换成汉字
          value = numberToChinese(value * 1);
          // 数字超过十之后会是多个字符，拆分成单个
          const val_arr = value.split("");
          // 记录字符的相对位置
          let char_localtion = 0;
          // 循环将多个字符展示
          for (let k = 0; k < val_arr.length; k++) {
            const element = val_arr[k];
            symbol.value = symbol.ori_value = k === val_arr.length - 1 ? element + "、" : element;
            // 计算第一个字符的位置 并记录
            if (k === 0) {
              char_localtion =
                (row.paragraph.level - 1) *
                row.paragraph.listLevelDislocation.chinese_num_width + margin_left;
            } else {
              // 第二个字符的位置是在前一个字符的基础上加上前一个字的宽度
              char_localtion += symbol.width;
            }
            // 最终将正确的位置赋值给绘制文字
            symbol.left = char_localtion;
            // 绘制
            this.drawChar([symbol], row, cell, curPage);
          }
        } else {
          // 列表是有序数字类型时执行以下代码
          symbol.value = symbol.ori_value = value + ".";

          symbol.left = margin_left -
            (row.paragraph.level - 1) *
            row.paragraph.listLevelDislocation.chinese_num_width;
          this.drawChar([symbol], row, cell, curPage);
        }
      } else {
        // 无序列表的部分
        let x = row.left + (row.paragraph.level - 1) *
          row.paragraph.listLevelDislocation.chinese_num_width;
        let y = 0;
        let highest_character = row.getHighestCharacter();
        if (!highest_character.value) {
          highest_character = row.paragraph.lastCharacter;
        }
        // 无序列表图标的半径
        const radius = (highest_character.font.height * 3) / 5;
        // 无序列表图标的高度
        y = row.top + row.height - row.padding_vertical - highest_character.font.height * (4 / 5);
        if (
          row.paragraph.align === "right" ||
          row.paragraph.align === "center"
        ) {
          // 实现有序列表序号位置准确，在居中展示以及居右展示
          x = row.children[0]
            ? row.left + row.children[0].left - row.padding_left
            : row.width / 2 - row.padding_left;
        }
        if (row.paragraph.title_length === 0) { x += row.paragraph.content_padding_left; }
        switch (row.paragraph.level % 2) {
          case 1:
            x += this.printType === "pdf" ? radius / 2 : radius;
            y += radius / 2;
            curPage.push({
              type: "circle",
              left: this.setAccuracy(x),
              top: this.setAccuracy(y),
              radius: this.setAccuracy(radius / 2),
              fill: true
            });
            break;
          case 0:
            curPage.push({
              type: "rect",
              left: this.setAccuracy(x),
              top: this.setAccuracy(y),
              width: this.setAccuracy(radius),
              height: this.setAccuracy(radius),
              fill: true
            });
            break;
        }
      }
    }
  },
  // 获取分组中所有的row或者table
  getGroupAllChildren (group: Group) {
    const children = [];
    const paras = group.paragraph;
    for (let i = 0; i < paras?.length; i++) {
      const para = paras[i];
      if (para instanceof Table) {
        children.push(para);
      } else {
        children.push(...para.children);
      }
      children.push(...para.children);
    }
    return children;
  },
  // 获取分组打印条件下将所有分组符合条件的row或者table
  getGroupPrintRowOrTableList (element: any) {
    const groupElement = [];
    for (let i = 0; i < element.groups.length; i++) {
      const group = element.groups[i];
      if (group?.meta?.print) {
        groupElement.push(...this.getGroupAllChildren(group));
      }
    }
    return groupElement;
  },
  /**
   * 绘制单元格
   * @param {cell} cell ,也可能是page
   * @param {number} header_outer_bottom 用到的页眉页脚高度信息
   */
  drawCell (cell: any, header_outer_bottom: any, curPage: any) { // TODO 绘制啥单元格 这绘制的是页眉页脚 header_cell footer_cell 和 正文啊 root_cell 可能也绘制单元格
    const { end_area_location, cover_height } = this.areaPrintInfo;
    const { print_continue, area_print } = this.printStatus;
    const { show_header_line, show_footer_line, page_padding_left, page_padding_right } = this.config;
    // 获取meta.print为true的所有group中的的row或者table并存到groupElement中
    const groupElement = this.getGroupPrintRowOrTableList(cell);
    for (let j = 0; j < cell.children.length; j++) {
      const item = cell.children[j]; // 获取 table 或 row
      const itemBottom = this.setAccuracy(item.bottom);
      // 续打判断、区域打印开始
      const isContinue = (print_continue || area_print) && itemBottom <= cover_height;
      if (isContinue) continue;
      // 如果分组中存在需要打印分组的内容并且cell.children中包含groupElement的对象，则只绘制groupElement中存的内容
      if (this.editor.group_print && !groupElement.includes(item)) continue;
      item.top = this.setAccuracy(item.top);
      // 区域打印结束
      const break_area_print = area_print && end_area_location && item.top >= end_area_location;
      if (break_area_print) break;
      if (item.row_size) {
        // 说明是表格
        const cellsEachRow = new Map();
        for (let k = 0; k < item.children.length; k++) {
          if (this.editor.config.rowLineType === RowLineType.SOLID && this.editor.config.rowLineTypeExplain.includesTable) {
            const cell = item.children[k];

            // 因为这是分页的 viewData 所以用 originCell

            // 不考虑有合并拆分的情况
            const originCell = cell.getOrigin();

            const versionList = cell.editor.document_meta?.versionList;
            const version = versionList && versionList[0] && versionList[0].version;

            if (originCell.rowspan === 1 && (versionDiff(version, "10.9.20") < 0 ? originCell.colspan === 1 : true)) {
              if (cellsEachRow.has(cell.position[0])) {
                cellsEachRow.get(cell.position[0]).push(cell);
              } else {
                cellsEachRow.set(cell.position[0], [cell]);
              }
            }
          }

          const table_cell = item.children[k];

          // 如果单元格上要绘制网格线就走这里
          const originCell = table_cell.getOrigin();

          if (originCell.rowLineType === RowLineType.SOLID) {
            if (cellsEachRow.has(table_cell.position[0])) {
              cellsEachRow.get(table_cell.position[0]).push(table_cell);
            } else {
              cellsEachRow.set(table_cell.position[0], [table_cell]);
            }
          }

          const originTable = item.getOrigin();
          if (originTable.rowLineType === RowLineType.SOLID) {
            if (originCell.rowspan === 1) {
              if (cellsEachRow.has(table_cell.position[0])) {
                cellsEachRow.get(table_cell.position[0]).push(table_cell);
              } else {
                cellsEachRow.set(table_cell.position[0], [table_cell]);
              }
            }
          }

          if (table_cell.style && table_cell.style.bgColor) {
            curPage.push({
              type: "bgColor",
              bgColorType: "rect",
              // 十六进制颜色，#七位
              color: table_cell.style.bgColor,
              left: this.setAccuracy(table_cell.left + table_cell.parent.left),
              top: this.setAccuracy(table_cell.top + table_cell.parent.top),
              width: this.setAccuracy(table_cell.width),
              height: this.setAccuracy(table_cell.height)
            });
          }
          // drawBgColor(table_cell)
          // 续打位置判断条件
          const table_cellContinue = (print_continue || area_print) && (table_cell.bottom + item.top) <= cover_height;
          if (table_cellContinue) continue;
          // 区域打印结束
          const tablebreak_area_print = area_print && end_area_location && (table_cell.top + item.top) >= end_area_location;
          if (tablebreak_area_print) break;
          const leftAddWidth = 5 / table_cell.col_size.length * table_cell.position[table_cell.position.length - 1];
          const rightAddWidth = 5 / table_cell.col_size.length * (table_cell.position[table_cell.position.length - 1] + 1);
          let isFull = false; // 单元格是否已经满了
          for (let cr = 0; cr < table_cell.children.length; cr++) {
            const row = table_cell.children[cr];
            const row_location = table_cell.top + item.top;
            // const bottom = this.setAccuracy(row.bottom);
            // row_location = this.setAccuracy(row_location)
            const start_location = cover_height + 1;
            // 续打位置判断条件
            const table_row_continue = (print_continue || area_print) && (row.bottom + row_location) <= start_location;
            if (table_row_continue) continue;
            // 区域打印结束
            const area_end_location = end_area_location - 1;
            const tablebreak_area_print = area_print && end_area_location && (row.top + row_location) >= area_end_location;
            if (tablebreak_area_print) break;
            this.drawList(row, table_cell, curPage);
            // 文字 此处应该允许些许误差
            if (row.bottom <= table_cell.height) {
              this.drawChar(row.children, row, table_cell, curPage);
            } else {
              if (!isFull) {
                this.setClip("start", row, table_cell, curPage);
                this.drawChar(row.children, row, table_cell, curPage);
                this.setClip("end", row, table_cell, curPage);
              }
              isFull = true;
            }
            this.drawFieldLine(row, table_cell, curPage);
          }
          if (isBoolean(table_cell.is_show_slash_up) || table_cell.is_show_slash_up === 1) {
            if (table_cell.is_show_slash_up && table_cell.parent && !table_cell.parent.parent.hf_part) {
              curPage.push({
                type: "line",
                left: this.setAccuracy(table_cell.left + table_cell.parent.left + leftAddWidth),
                top: this.setAccuracy(table_cell.top + table_cell.parent.top),
                right: this.setAccuracy(table_cell.width + table_cell.left + table_cell.parent.left + rightAddWidth),
                bottom: this.setAccuracy(table_cell.bottom + table_cell.parent.top)
              });
            }
          } else if (table_cell.is_show_slash_up === 2 && table_cell.parent && !table_cell.parent.parent.hf_part) {
            curPage.push({
              type: "line",
              left: this.setAccuracy(table_cell.left + table_cell.parent.left + leftAddWidth),
              top: this.setAccuracy(table_cell.top + table_cell.parent.top),
              right: this.setAccuracy(table_cell.width + table_cell.left + table_cell.parent.left + rightAddWidth),
              bottom: this.setAccuracy(table_cell.bottom + table_cell.parent.top - table_cell.height / 2)
            });
            curPage.push({
              type: "line",
              left: this.setAccuracy(table_cell.left + table_cell.parent.left + leftAddWidth),
              top: this.setAccuracy(table_cell.top + table_cell.parent.top),
              right: this.setAccuracy(table_cell.width + table_cell.left + table_cell.parent.left - table_cell.width / 2 + rightAddWidth),
              bottom: this.setAccuracy(table_cell.bottom + table_cell.parent.top)
            });
          }

          if (isBoolean(table_cell.is_show_slash_down) || table_cell.is_show_slash_down === 1) {
            if (table_cell.is_show_slash_down && table_cell.parent && !table_cell.parent.parent.hf_part) {
              curPage.push({
                type: "line",
                left: this.setAccuracy(table_cell.left + table_cell.parent.left + leftAddWidth),
                top: this.setAccuracy(table_cell.bottom + table_cell.parent.top),
                right: this.setAccuracy(table_cell.width + table_cell.left + table_cell.parent.left + rightAddWidth),
                bottom: this.setAccuracy(table_cell.top + table_cell.parent.top)
              });
            }
          } else if (table_cell.is_show_slash_down === 2 && table_cell.parent && !table_cell.parent.parent.hf_part) {
            curPage.push({
              type: "line",
              left: this.setAccuracy(table_cell.left + table_cell.parent.left + leftAddWidth),
              top: this.setAccuracy(table_cell.bottom + table_cell.parent.top),
              right: this.setAccuracy(table_cell.width + table_cell.left + table_cell.parent.left + rightAddWidth),
              bottom: this.setAccuracy(table_cell.top + table_cell.parent.top + table_cell.height / 2)
            });
            curPage.push({
              type: "line",
              left: this.setAccuracy(table_cell.left + table_cell.parent.left + leftAddWidth),
              top: this.setAccuracy(table_cell.bottom + table_cell.parent.top),
              right: this.setAccuracy(table_cell.width + table_cell.left + table_cell.parent.left - table_cell.width / 2 + rightAddWidth),
              bottom: this.setAccuracy(table_cell.top + table_cell.parent.top)
            });
          }
        }
        const originTable = item.getOrigin();

        // 处理单元格网格线 ↓
        const opacityRowLines = item.notAllowDrawLine.changeOpacityRow;
        for (const arr of cellsEachRow.entries()) {
          const cellsPerRow = arr[1];
          const index = cellsPerRow.findIndex((c: Cell) => c.children.length > 1);
          // const rowHeight = cellsPerRow[index > -1 ? index : 0].children[0]?.height || 0;
          const rowHeight = cellsPerRow[index > -1 ? index : 0].children[0]?.height || cellsPerRow[index > -1 ? index : 0].height || 0;
          for (const dCell of cellsPerRow) {
            let lineHeight = dCell.top + dCell.parent.top;
            if (!dCell.rowLineType || (dCell.rowLineType === RowLineType.VOID)) continue;
            // 在这里绘制每个单元格里边的线
            if (!opacityRowLines.find((lines: any[]) => lines[0] === dCell.position[0] + 1)) {
              while (true) {
                lineHeight += rowHeight;
                if (lineHeight + rowHeight > dCell.bottom + dCell.parent.top || rowHeight === 0) {
                  break;
                }
                // Renderer.draw_line([dCell.left, lineHeight], [dCell.right, lineHeight], "#000", 1, 0.5);
                curPage.push({
                  type: "line",
                  left: this.setAccuracy(dCell.left + dCell.parent.left + (5 / dCell.parent.col_size.length) * dCell.start_col_index),
                  top: this.setAccuracy(lineHeight),
                  right: this.setAccuracy(dCell.width + dCell.left + dCell.parent.left + (5 / dCell.parent.col_size.length) * (dCell.end_col_index || 1)),
                  bottom: this.setAccuracy(lineHeight)
                });
              }
            }
          }
        }
        // 处理单元格网格线 ↑

        const tableSolid = originTable.rowLineType === RowLineType.SOLID;
        if (this.editor.config.rowLineType === RowLineType.SOLID || tableSolid) {
          const opacityRowLines = item.notAllowDrawLine.changeOpacityRow;

          for (const arr of cellsEachRow.entries()) {
            const cellsPerRow = arr[1];
            if (cellsPerRow.length !== item.col_size.length && !tableSolid) {
              cellsEachRow.delete(arr[0]);
            }
          }

          for (const arr of cellsEachRow.entries()) {
            const cellsPerRow = arr[1];
            const index = cellsPerRow.findIndex((c: Cell) => c.children.length > 1);
            // const rowHeight = cellsPerRow[index > -1 ? index : 0].children[0]?.height || 0;
            const rowHeight = cellsPerRow[index > -1 ? index : 0].children[0]?.height || cellsPerRow[index > -1 ? index : 0].height || 0;
            for (const dCell of cellsPerRow) {
              let lineHeight = dCell.top + dCell.parent.top;
              // 在这里绘制每个单元格里边的线
              // 剩余空间如果不足以绘制一行字的时候就不再绘制网格线了  避免
              if (!opacityRowLines.find((lines: any[]) => lines[0] === dCell.position[0] + 1)) {
                while (true) {
                  lineHeight += rowHeight;
                  if (lineHeight + rowHeight > dCell.bottom + dCell.parent.top || rowHeight === 0) {
                    break;
                  }
                  // Renderer.draw_line([dCell.left, lineHeight], [dCell.right, lineHeight], "#000", 1, 0.5);
                  curPage.push({
                    type: "line",
                    left: this.setAccuracy(dCell.left + dCell.parent.left + (5 / dCell.parent.col_size.length) * dCell.start_col_index),
                    top: this.setAccuracy(lineHeight),
                    right: this.setAccuracy(dCell.width + dCell.left + dCell.parent.left + (5 / dCell.parent.col_size.length) * (dCell.end_col_index || 1)),
                    bottom: this.setAccuracy(lineHeight)
                  });
                }
              }
            }
          }
        }
        this.drawTableLine(item, curPage);
      } else {
        this.drawList(item, cell, curPage);
        this.drawChar(item.children, item, null, curPage);
        if (this.editor.config.rowLineType === RowLineType.SOLID) {
          if (item.parent.getLocation() === "root") {
            if (!item.parent.parent) {
              curPage.push({
                type: "line",
                left: this.setAccuracy(item.left),
                top: this.setAccuracy(item.bottom),
                right: this.setAccuracy(item.right),
                bottom: this.setAccuracy(item.bottom)
              });
              let bottom = item.bottom;
              const rowHeight = this.editor.internal.rowHeight;
              if (j === cell.children.length - 1 && rowHeight) {
                if (!this.editor.config.rowLineTypeExplain.excludesEmpty) { // 有的医院可能不想要下半页的行线
                  // 有半页的情况下没有 row 但是也有绘制完全
                  const lastPage = this.editor.pages[this.editor.pages.length - 1];
                  const lastPageFooterTop = lastPage.footer.footer_outer_top;
                  while (true) {
                    bottom += rowHeight;
                    if (Math.ceil(bottom) >= lastPageFooterTop) {
                      break;
                    }
                    curPage.push({
                      type: "line",
                      left: this.setAccuracy(item.left),
                      top: this.setAccuracy(bottom),
                      right: this.setAccuracy(item.right),
                      bottom: this.setAccuracy(bottom)
                    });
                  }
                }
              }
            }
          }
        }
        this.drawFieldLine(item, null, curPage);
      }
    }
    if (show_header_line && cell.hf_part === "header") {
      curPage.push({
        type: "line",
        left: this.setAccuracy(page_padding_left),
        top: this.setAccuracy(header_outer_bottom),
        right: this.setAccuracy(cell.width - page_padding_right + 5),
        bottom: this.setAccuracy(header_outer_bottom)
      });
    }
    if (show_footer_line && cell.hf_part === "footer") {
      curPage.push({
        type: "line",
        left: this.setAccuracy(page_padding_left),
        top: this.setAccuracy(header_outer_bottom),
        right: this.setAccuracy(cell.width - page_padding_right + 5),
        bottom: this.setAccuracy(header_outer_bottom)
      });
    }
  },
  drawImage (char: any, row: any, cell: any, curPage: any) {
    let src = char.src;
    // 占位图不处理
    if (char.meta && char.meta.placeholder) {
      return;
    }
    if (!src) {
      this.editor.event.emit("message", { type: "error", msg: "请检查图片是否正常" });
      return;
    }
    const imageMap = this.imageMap;
    const source = this.config.source;
    const img_margin = this.config.img_margin;
    // TODO 多测试
    if (source !== "server") {
      if (!src.startsWith("http")) {
        const imageData = (imageMap && imageMap.get(src)) ? imageMap.get(src).data : src;
        if (
          imageData instanceof Image
        ) {
          if (this.printType === "pdf") {
            src = getImageSrcWithWhiteBgc(imageData, "none");
          } else {
            if (localStorage.getItem("editorPrintChangeDpi")) {
              src = getImageSrcWithWhiteBgc(imageData);
              // 直接传 base64图片数据和需要修改的dpi
              src = changeDpiDataUrl(src, 96);
            }
          }
        }
      } else if (this.printType === "pdf") {
        src = (imageMap && imageMap.get(src)) ? imageMap.get(src).data : src;
      }
      // cpp打印在线图片无图情况处理
      // if (EditorLocalTest.transUse && (!this.printType || this.printType === "cpp") && src.startsWith("http")) {
      //   const imageData = (imageMap && imageMap.get(src)) ? imageMap.get(src).data : src;
      //   if (imageData instanceof Image) {
      //     src = getImageSrcWithWhiteBgc(imageData);
      //   }
      // }
    }
    const offsetLeft = this.printType === "pdf" ? 2.5 : 5;
    const offset = this.printType === "pdf" ? 0 : 5;

    let real_height = row.height;
    const charHight = char.height + img_margin * 2;
    if (charHight !== real_height) {
      if (char.font.align === "top") {
        real_height = char.height;
      } else if (char.font.align === "middle") {
        real_height = row.height / 2 + char.height / 2;
      }
    }
    real_height = real_height - row.height - char.height;
    let width = this.setAccuracy(char.width - offset - (img_margin * 2));
    width = width < 1.5 ? 1.5 : width; // 值为 1 都不绘制 为负值是绘制的 但是 -6 也有点宽
    const height = this.setAccuracy(char.height - offset - (img_margin * 2));
    if (width < 1 || height < 1) {
      return;
    }
    curPage.push({
      type: "image",
      src: src,
      left: this.getPosition("left", char, row, cell, offsetLeft + img_margin) - (width === 1.5 ? 3 : 0),
      top: this.getPosition("top", char, row, cell, real_height + offset + img_margin),
      width: width,
      height: height
    });
  },
  drawMarksText (y: number, mark: any, font_style: any, curPage: any) {
    const textList = mark.params.value.split("\n");
    for (let i = 0; i < textList.length; i++) {
      const text = textList[i].split("");
      for (let j = 0; j < text.length; j++) {
        const row_text = text[j];
        const offset = this.printType === "pdf" ? font_style.height * 0.9 : 0;
        curPage.push({
          type: "text",
          fontId: font_style.id,
          value: row_text,
          width: this.setAccuracy(font_style.height),
          bgHeight: this.setAccuracy(mark.height),
          bgWidth: this.setAccuracy(mark.width),
          bgTop: this.setAccuracy(y),
          left: this.setAccuracy(mark.start.x + font_style.height * j),
          top: this.setAccuracy(y + font_style.height * (1.2 * i) + offset)
        });
      }
    }
  },
  drawMarks (page: any, curPage: any, level?:number) {
    const editor = this.editor;
    const waterMarks = editor.waterMarks;
    for (let i = 0; i < waterMarks.length; i++) {
      const mark = waterMarks[i];
      if (mark.params.level) {
        if (mark.params.level !== level) {
          continue;
        }
      } else {
        if (level !== 1) {
          continue;
        }
      }
      if (mark.type === "imageMark") {
        const src = mark.params.src;
        let left = mark.start.x;
        let top = mark.start.y;
        if (mark.params.padding) {
          left = mark.start.x + editor.config.page_padding_left;
          top = mark.start.y + editor.config.page_padding_top;
        }
        if (mark.mode === "repeat") {
          curPage.push({
            type: "image",
            src: src,
            left: left,
            top: top,
            width: mark.width,
            height: mark.height
          });
        } else {
          if (
            page.top <= top &&
            top <= page.top + page.height
          ) {
            const y = editor.getSpacingPageTopByY(top);
            curPage.push({
              type: "image",
              src: src,
              left: left,
              top: y,
              width: mark.width,
              height: mark.height
            });
          }
        }
      } else if (mark.type === "textMark") {
        const font_style = mark.params.fontStyle;
        if (font_style) {
          this.fontMap.set(font_style.id, font_style);
        }
        if (mark.mode === "repeat") {
          this.drawMarksText(mark.start.y, mark, font_style, curPage);
        } else {
          if (
            page.top <= mark.start.y &&
            mark.start.y <= page.top + page.height
          ) {
            const y = editor.getSpacingPageTopByY(mark.start.y);
            this.drawMarksText(y, mark, font_style, curPage);
          }
        }
      }
    }
  },
  drawItalicMark (curPage: any) {
    const italicWatermark = this.editor.internal.italicWatermark;
    if (italicWatermark.text && italicWatermark.module.includes("printPaper")) {
      curPage.push({
        type: "waterMark",
        text: italicWatermark.text,
        direction: italicWatermark.direction,
        font: italicWatermark.font
      });
    }
  },
  drawShapeType (type: any, xy: any, page_margin_bottom: number, editor_padding_top: number, page_size: any, i: number, curPage: any) {
    if (type) {
      if (type === "circle") {
        curPage.push({
          type: "circle",
          left: this.setAccuracy(xy.x),
          top: this.setAccuracy(xy.y - editor_padding_top -
            (page_size.height + page_margin_bottom) * i),
          radius: 6
        });
      } else if (type === "cross") {
        curPage.push({
          type: "line",
          left: this.setAccuracy(xy.x - 6),
          top: this.setAccuracy(xy.y - 6 - editor_padding_top -
            (page_size.height + page_margin_bottom) * i),
          right: this.setAccuracy(xy.x + 6),
          bottom: this.setAccuracy(xy.y + 6 - editor_padding_top -
            (page_size.height + page_margin_bottom) * i)
        });
        curPage.push({
          type: "line",
          left: this.setAccuracy(xy.x - 6),
          top: this.setAccuracy(xy.y + 6 - editor_padding_top -
            (page_size.height + page_margin_bottom) * i),
          right: this.setAccuracy(xy.x + 6),
          bottom: this.setAccuracy(xy.y - 6 - editor_padding_top -
            (page_size.height + page_margin_bottom) * i)
        });
      }
    }
  },
  drawShapes (i: any, curPage: any) {
    const pageShapes = this.editor.internal.getPageShapes();
    const { page_size } = this.printStatus;
    const { editor_padding_top, page_margin_bottom } = this.config;
    const shapes = pageShapes[i];
    if (shapes && shapes.length) {
      shapes.forEach((shape: any) => {
        if (shape.type === "line") {
          if (shape.addition) {
            if (shape.addition.startXY) {
              this.drawShapeType(shape.addition.startXY, shape.startXY, page_margin_bottom, editor_padding_top, page_size, i, curPage);
            }
            if (shape.addition.endXY) {
              this.drawShapeType(shape.addition.endXY, shape.endXY, page_margin_bottom, editor_padding_top, page_size, i, curPage);
            }
          }

          const point = {
            left: shape.startXY.x,
            top: shape.startXY.y - editor_padding_top -
              (page_size.height + page_margin_bottom) * i,
            right: shape.endXY.x,
            bottom: shape.endXY.y - editor_padding_top -
              (page_size.height + page_margin_bottom) * i
          };
          if (shape.addition) {
            if (shape.addition.startXY === "circle") {
              const pointStart = getIntersection({ x: shape.startXY.x, y: shape.startXY.y }, 6, { x: shape.endXY.x, y: shape.endXY.y });
              point.left = pointStart.x;
              point.top = pointStart.y - editor_padding_top -
                (page_size.height + page_margin_bottom) * i;
            }
            if (shape.addition.endXY === "circle") {
              const pointEnd = getIntersection({ x: shape.endXY.x, y: shape.endXY.y }, 6, { x: shape.startXY.x, y: shape.startXY.y });
              point.right = pointEnd.x;
              point.bottom = pointEnd.y - editor_padding_top -
                (page_size.height + page_margin_bottom) * i;
            }
          }
          curPage.push({
            type: "line",
            left: this.setAccuracy(point.left),
            top: this.setAccuracy(point.top),
            right: this.setAccuracy(point.right),
            bottom: this.setAccuracy(point.bottom)
          });
        } else if (shape.type === "circle") {
          const width = Math.abs(shape.endXY.x - shape.startXY.x);
          const height = Math.abs(shape.endXY.y - shape.startXY.y);
          let offsetLeft = width;
          let offsetTop = height;
          if (this.printType === "pdf") {
            offsetLeft = 0;
            offsetTop = 0;
          }
          curPage.push({
            type: "ellipse",
            left: this.setAccuracy(shape.startXY.x - offsetLeft),
            top: this.setAccuracy(shape.startXY.y - offsetTop - editor_padding_top -
              (page_size.height + page_margin_bottom) * i),
            width: this.setAccuracy(width),
            height: this.setAccuracy(height)
          });
        } else if (shape.type === "cross") {
          const width = Math.abs(shape.endXY.x - shape.startXY.x);
          const height = Math.abs(shape.endXY.y - shape.startXY.y);
          curPage.push({
            type: "line",
            left: this.setAccuracy(shape.startXY.x - width),
            top: this.setAccuracy(shape.startXY.y - height - editor_padding_top -
              (page_size.height + page_margin_bottom) * i),
            right: this.setAccuracy(shape.startXY.x + width),
            bottom: this.setAccuracy(shape.startXY.y + height - editor_padding_top -
              (page_size.height + page_margin_bottom) * i)
          });
          curPage.push({
            type: "line",
            left: this.setAccuracy(shape.startXY.x - width),
            top: this.setAccuracy(shape.startXY.y + height - editor_padding_top -
              (page_size.height + page_margin_bottom) * i),
            right: this.setAccuracy(shape.startXY.x + width),
            bottom: this.setAccuracy(shape.startXY.y - height - editor_padding_top -
              (page_size.height + page_margin_bottom) * i)
          });
        } else if (shape.type === "rect") {
          const startY = shape.shapeStart.y;
          let startX = shape.shapeStart.x;
          if (shape.shapeEnd.x < shape.shapeStart.x) {
            startX = shape.shapeEnd.x;
          }
          const mistake = this.printType === "pdf" ? 0 : 3;
          curPage.push({
            type: "rect",
            left: this.setAccuracy(startX + 3),
            top: this.setAccuracy(startY - editor_padding_top + mistake -
              (page_size.height + page_margin_bottom) * i),
            width: this.setAccuracy(shape.width),
            height: this.setAccuracy(shape.height)
          });
        } else if (shape.type === "fold_line") {
          if (shape.foldLine && shape.foldLine.length) {
            for (let j = 0; j < shape.foldLine.length; j++) {
              const foldLine = shape.foldLine[j];

              const nextFoldLine = shape.foldLine[j + 1];
              if (foldLine.type === "circle") {
                this.drawShapeType("circle", foldLine.point, page_margin_bottom, editor_padding_top, page_size, i, curPage);
              } else if (foldLine.type === "cross") {
                this.drawShapeType("cross", foldLine.point, page_margin_bottom, editor_padding_top, page_size, i, curPage);
              }
              if (nextFoldLine) {
                let start = { x: foldLine.point.x, y: foldLine.point.y };
                let end = { x: nextFoldLine.point.x, y: nextFoldLine.point.y };
                if (foldLine.type === "circle") {
                  start = getIntersection({ x: foldLine.point.x, y: foldLine.point.y }, 6, { x: nextFoldLine.point.x, y: nextFoldLine.point.y });
                }
                if (nextFoldLine.type === "circle") {
                  end = getIntersection({ x: nextFoldLine.point.x, y: nextFoldLine.point.y }, 6, { x: foldLine.point.x, y: foldLine.point.y });
                }
                curPage.push({
                  type: "line",
                  left: this.setAccuracy(start.x + 1),
                  top: this.setAccuracy(start.y - editor_padding_top -
                    (page_size.height + page_margin_bottom) * i),
                  right: this.setAccuracy(end.x + 1),
                  bottom: this.setAccuracy(end.y - editor_padding_top -
                    (page_size.height + page_margin_bottom) * i)
                });
              };
            }
          }
        }
      });
    }
  },

  drawContentBorder (page: any, curPage: any) {
    if (this.editor.config.contentBorder) {
      const content_width = page.width - this.editor.config.page_padding_left - this.editor.config.page_padding_right;
      const content_height = page.footer.footer_outer_top - page.header.header_outer_bottom - this.editor.config.content_margin_footer - this.editor.config.content_margin_header;
      // curPage.push({
      //   type: "rect",
      //   left: this.setAccuracy(this.editor.config.page_padding_left + 1),
      //   top: this.setAccuracy(page.header.header_outer_bottom + this.editor.config.content_margin_header),
      //   width: this.setAccuracy(content_width + 5.25),
      //   height: this.setAccuracy(content_height)
      // });
      curPage.push({
        type: "line",
        left: this.setAccuracy(this.editor.config.page_padding_left),
        top: this.setAccuracy(page.header.header_outer_bottom + this.editor.config.content_margin_header),
        right: this.setAccuracy(this.editor.config.page_padding_left + content_width + 5),
        bottom: this.setAccuracy(page.header.header_outer_bottom + this.editor.config.content_margin_header)
      });
      curPage.push({
        type: "line",
        left: this.setAccuracy(this.editor.config.page_padding_left),
        top: this.setAccuracy(page.header.header_outer_bottom + this.editor.config.content_margin_header + content_height),
        right: this.setAccuracy(this.editor.config.page_padding_left + content_width + 5),
        bottom: this.setAccuracy(page.header.header_outer_bottom + this.editor.config.content_margin_header + content_height)
      });
      curPage.push({
        type: "line",
        left: this.setAccuracy(this.editor.config.page_padding_left),
        top: this.setAccuracy(page.header.header_outer_bottom + this.editor.config.content_margin_header),
        right: this.setAccuracy(this.editor.config.page_padding_left),
        bottom: this.setAccuracy(page.header.header_outer_bottom + this.editor.config.content_margin_header + content_height)
      });
      curPage.push({
        type: "line",
        left: this.setAccuracy(this.editor.config.page_padding_left + content_width + 5),
        top: this.setAccuracy(page.header.header_outer_bottom + this.editor.config.content_margin_header),
        right: this.setAccuracy(this.editor.config.page_padding_left + content_width + 5),
        bottom: this.setAccuracy(page.header.header_outer_bottom + this.editor.config.content_margin_header + content_height)
      });
    }
  },
  // 处理所需要的页
  handleNeedPages (selectedPages: any) {
    const editor = this.editor;
    const { editor_padding_top, page_margin_bottom } = this.config;
    const { print_continue, area_print, page_size, print_absolute_y, area_location } = this.printStatus;
    let pages = editor.pages;
    if (print_continue) {
      let start_print_page = Math.floor(
        (print_absolute_y - editor_padding_top) /
        (page_size.height + page_margin_bottom)
      );
      start_print_page = start_print_page < 0 ? 0 : start_print_page;
      pages = pages.slice(start_print_page, pages.length);
    } else if (area_print) {
      let start_print_page = Math.floor(
        (area_location.start_absolute_y -
          editor_padding_top) /
        (page_size.height + page_margin_bottom)
      );
      // 位置 如果没有点击，会计算出来一个-1，所以要置为0
      start_print_page = start_print_page < 0 ? 0 : start_print_page;
      let end_print_page =
        Math.floor(
          (area_location.end_absolute_y -
            editor_padding_top) /
          (page_size.height + page_margin_bottom)
        ) + 1;
      end_print_page =
        end_print_page <= 0 ? editor.pages.length : end_print_page;
      pages = pages.slice(start_print_page, end_print_page);
    } else {
      // 保留第一个带print的分组以后的页面
      A: for (let i = 0; i < pages.length; i++) {
        const groups = pages[i].groups;
        if (groups.length) {
          for (let j = 0; j < groups.length; j++) {
            const group = groups[j];
            if (group.meta.print) {
              pages = pages.slice(i);
              break A;
            }
          }
        }
      }
    }
    if (selectedPages) {
      const realPages = [];
      for (let i = 0; i < selectedPages.length; i++) {
        if (pages[selectedPages[i]]) {
          realPages.push(pages[selectedPages[i]]);
        }
      }
      pages = realPages;
    }
    return pages;
  },
  /**
   * viewData转换为存json数据
   * @param instance
  * @returns {[]}
  */
  viewData2Json (extraInfo: any, selectedPages: any) {
    const pages = this.handleNeedPages(selectedPages);

    this.startPrintPageNumber = pages[0] ? pages[0].number : 0;

    const { area_location, area_print, print_continue, print_absolute_y, page_size } = this.printStatus;

    // 区域打印模式
    if (area_print && area_location.end_absolute_y !== 0) {
      // 开始位置第一页记录正确位置，后续也应该是也的初始位置，可以设置为0
      this.areaPrintInfo.cover_height = this.setAccuracy(area_location.start_absolute_y - pages[0].top);
      // 结束位置设置成区域打印的结束位置，如果跨页，则第一次记录的位置是绝对位置。
      this.areaPrintInfo.end_area_location = this.setAccuracy(area_location.end_absolute_y);
    }

    const { editor_padding_top, page_margin_bottom } = this.config;

    const drawPage = [];
    for (let i = 0; i < pages.length; i++) {
      const curPage: any[] = []; // 当前页
      const page = pages[i];
      const header_bottom = page.header.header_bottom;
      const footer_top = page.footer.footer_top;
      // 先绘制水印，这样水印在文字下方

      this.drawMarks(page, curPage, 1);

      if (print_continue) {
        // 续打位置
        this.areaPrintInfo.cover_height = this.setAccuracy(print_absolute_y - page.top);
        const print_c_num = Math.floor(
          (print_absolute_y - editor_padding_top) /
          (page_size.height + page_margin_bottom)
        );

        const isInteger = Number.isInteger((print_absolute_y - editor_padding_top) /
          (page_size.height + page_margin_bottom));
        if (page.number - 1 !== print_c_num || (page.number - 1 === print_c_num && isInteger)) {
          // 绘制页眉
          this.drawCell(page.header.header_cell, header_bottom, curPage);
          // 绘制页脚`

          this.drawMarks(page, curPage, 2);

          this.drawCell(page.footer.footer_cell, footer_top, curPage);
          this.drawContentBorder(page, curPage);
        }
        // 绘制正文，添加参数续打高度
        this.drawCell(page, 0, curPage);
      } else {
        // 续打模式下 恢复第二页的绘制位置
        if (print_continue) {
          this.areaPrintInfo.cover_height = 0;
        }
        if (area_print && area_location.end_absolute_y !== 0) {
          if (i === pages.length - 1) {
            this.areaPrintInfo.end_area_location = this.setAccuracy(area_location.end_absolute_y - page.top);
          }
          if (i > 0) {
            this.areaPrintInfo.cover_height = 0;
          }
        }
        // 区域打印判断
        if (area_print) {
          const print_top = area_location.start_absolute_y;
          const print_end = area_location.end_absolute_y;
          const footer_outer_top = page.footer.footer_outer_top;
          const head_line = page.top + header_bottom;
          const footer_line = page.bottom + footer_outer_top;
          if (head_line > print_top && head_line < print_end) {
            this.drawCell(page.header.header_cell, header_bottom, curPage);
          } else if (footer_line > print_top && footer_line < print_end) {
            // 绘制页脚
            this.drawCell(page.footer.footer_cell, footer_top, curPage);
          }
        } else {
          const result = this.isIncludePrintGroups(page);
          if (result) {
            if (i !== 0) {
              this.drawCell(page.header.header_cell, header_bottom, curPage);
              // 绘制页脚

              this.drawMarks(page, curPage, 2);

              this.drawCell(page.footer.footer_cell, footer_top, curPage);
            }
          } else {
            this.drawCell(page.header.header_cell, header_bottom, curPage);
            // 绘制页脚

            this.drawMarks(page, curPage, 2);

            this.drawCell(page.footer.footer_cell, footer_top, curPage);
          }
        }
        // 绘制正文
        this.drawCell(page, 0, curPage);
        this.drawContentBorder(page, curPage);
      }

      this.drawMarks(page, curPage, 3);

      this.drawItalicMark(curPage);
      this.drawShapes(page.number - 1, curPage);

      drawPage.push(curPage);
    }
    if (extraInfo.sort === 2) {
      drawPage.reverse();
    }
    const length = drawPage.length;
    if (extraInfo.duplexFlip) {
      for (let i = 0; i < length - 1; i++) {
        const curPage = pages[i];
        if (curPage.number % 2 === 0) {
          const firstChild = curPage.children[0];
          const group = firstChild.group;
          if (firstChild && group) {
            if (group && group.new_page && group.paragraph.length > 0) {
              const firstParagraph = group.paragraph[0];
              const firstParagraphChild = firstParagraph.children[0];
              if ((isRow(firstParagraphChild) && isRow(firstChild) && firstChild.id === firstParagraphChild.id) ||
                (isTable(firstParagraph) && isTable(firstChild) &&
                  firstChild.children[0].children[0].id === firstParagraph.children[0].children[0].id)) {
                this.startPrintPageNumber = (i === 0) ? 1 : (drawPage.splice(i, 0, []), i++);
              }
            }
          }
        }
      }
    }

    return drawPage;
  },
  // 页面里是否包含需要打印的分组
  isIncludePrintGroups (page: any) {
    for (let i = 0; i < page.groups.length; i++) {
      const group = page.groups[i];
      if (group.meta?.print) {
        return true;
      }
    }
    return false;
  },
  /**
   * 处理页面配置
   */
  handlePageSettings (editor: Editor) {
    const editorConfig = editor.config;
    const pageDirection = editorConfig.getDirection();
    const pageKind = editorConfig.getPageType();
    const pageSize = editorConfig.getPageSize();
    let fixedPaper = false;
    const fixedPaperNoMargin = false;
    // 为提升打印时易用性，当纸张类型为A5时自动切换为固定纸张打印
    if (pageKind === "A5") {
      fixedPaper = true;
      // fixedPaperNoMargin = true;
    }
    const pageSetting = {
      pageKind,
      landscape: pageDirection === "horizontal",
      pageWidth: pageSize.width,
      pageHeight: pageSize.height,
      startPageNumber: this.startPrintPageNumber,
      pageCopies: 1, // 打印份数
      pageScale: true, // 是否自动缩放以匹配打印机设置纸张大小，默认true
      // 打印偏移
      marginLeft: 0,
      marginTop: 0,
      fixedPaper,
      fixedPaperNoMargin,
      // 打印机名称
      printerName: ""
    };
    return pageSetting;
  },
  /**
   * 处理字体样式
   */
  handleFontStyles (editor: Editor) {
    const fontStyles: any = {};
    const fontMap = this.fontMap;
    // 处理字体样式
    if (!fontMap) {
      return fontStyles;
    }
    // 字号列表 ，根据高度从该集合中查找字号，查找不到则按照高度的3/4
    const fontSizeList = font_size_config.filter((ele: any) => Number(ele.option));
    for (const [key, font] of fontMap) {
      if (!font.height) {
        font.height = editor.config.default_font_style.height;
      }
      if (!font.family) {
        font.family = editor.config.default_font_style.family;
      }
      const fontSizeTem = fontSizeList.find((ele: any) => Number(font.height) === Number(ele.value));
      let fontSize = font.height * 3 / 4;
      if (fontSizeTem) {
        fontSize = Number(fontSizeTem.option);
      }
      if (font.script !== ScriptType.NORMAL) {
        fontSize = fontSize / 2;
      }
      fontStyles[key] = {
        height: font.height,
        fontSize: fontSize,
        family: font.family,
        bold: font.bold,
        italic: font.italic,
        bgColor: font.bgColor,
        color: font.color
      };
    }
    return fontStyles;
  },
  /**
   * 组装包含纸张、字体、页面内容JSON数据，用于发送至插件
   * @param instance
   */
  assemblePageJson (editor: Editor, extraInfo: any = {}, selectedPages: any) {
    this.init(editor, extraInfo);

    const { show_field_symbol, keepSymbolWidthWhenPrint } = this.config;
    // 如果展示了需要隐藏边框
    if (show_field_symbol && !keepSymbolWidthWhenPrint) {
      editor.showFieldSymbol(false);
    }

    const pageData = this.viewData2Json(extraInfo, selectedPages);
    this.recordPrintPosition(editor);
    if (show_field_symbol) {
      // 关闭续打模式，及边框设置
      editor.printContinue(false);
    }
    const pageSetting = this.handlePageSettings(editor);
    pageSetting.color = !!extraInfo.color;
    const fontStyles = this.handleFontStyles(editor);
    this.clear();
    return {
      pageSetting,
      fontStyles,
      pageData
    };
  },
  // 记录打印位置，立即续打时使用
  recordPrintPosition (editor: Editor) {
    // 打印的最后一页
    const last_page = editor.pages[editor.pages.length - 1];
    // 打印最后一行的高度
    const last_page_children = last_page.children;
    // 判断原始数据meta中是否有最后一页最后一行高度的属性
    editor.document_meta.printInfo = {
      height:
        last_page.top +
        last_page_children[last_page_children.length - 1].top +
        last_page_children[last_page_children.length - 1].height
    };
  }

};
