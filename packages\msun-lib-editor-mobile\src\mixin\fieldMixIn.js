import fieldSelect from "../components/field/fieldSelect_.vue";

/**
 * 文本域相关逻辑混入
 * @type {{data(): {}, method: {}}}
 */
const fieldMixIn = {
  components: {
    fieldSelect,
  },
  data() {
    return {
      source_list: [],
      fieldPanelOffset: {
        left: 0,
        top: 0,
      },
      caretPath: null,
      pointerStayTimeout: -1, //鼠标停留定时器
      showFieldSelectPanel: false,
      showFieldPropModalWin: false, //元素属性面板是否展示
      showMedicalCalcFormula: false, // 展示医学计算公式
      showFieldDatePicker: false, //文本域日期选择组件展示
      // 移动端 - 日期选择器 ↓
      mobileDatePickerShow: false, // 移动端日期选择器
      currentDate: new Date(),
      // 移动端 - 日期选择器 ↓
      showFieldSearchBox: false,
      separator: 0,
      inputMode: 0, //输入方式
      formula: "", //数学公式
      stayTop: false,
      isInsert: false,
      quickSelectIndex: -1,
    };
  },
  methods: {
    //处理文本域点击事件
    handleFieldClick(isDblClick) {
      this.hideFieldNeedPanel();
      this.fieldInfo = this.curClickInfo.field;
      if (this.stayTop && this.isInsert && this.fieldInfo) {
        this.fieldInfo.isInsert = true;
      }
      if (this.stayTop && this.fieldInfo && !this.isInsert) {
        const curField = this.editor.selection.getFocusField();
        this.curFieldID = curField.id;
      }
      if (!this.fieldInfo) {
        // 点击位置不是文本域，关闭提示窗
        this.show_tip = false;
        this.showFieldSearchBox = false;
        this.isShowNumberSelect = false;
        return;
      }
      if (this.fieldInfo.type !== "number" || this.fieldInfo.formula) {
        this.isShowNumberSelect = false;
      }
      //如果是选区则不触发
      if (!this.editor.selection.isCollapsed && !isDblClick) {
        return;
      }
      if (
        (this.fieldInfo.active_type === 1 && !isDblClick) ||
        (this.fieldInfo.active_type === 0 && isDblClick)
      ) {
        return;
      }
      const group = this.curClickInfo.group;

      if (
        !this.editor.permitOperationValidation() ||
        this.fieldInfo.readonly ||
        (group && group.lock)
      ) {
        if (
          this.fieldInfo.type === "select" ||
          this.fieldInfo.type === "date"
        ) {
          this.$editor.info("当前文本域不可编辑");
        }
        return;
      }

      const { scroll_top, viewScale, caret } = this.editor;
      //使用该方法获取放大或缩小后的位置
      const focus_row = this.editor.selection.getFocusRow();
      const { x, y } = this.editor.getViewPositionByAbsolutePosition(
        caret.x,
        caret.y
      );
      this.fieldPanelOffset = {
        left: x + this.$refs.content.offsetLeft,
        top:
          y +
          focus_row.height * viewScale -
          scroll_top * viewScale +
          this.$refs.content.offsetTop,
      };
      this.editor._curFieldInfo = this.fieldInfo;
      let isCellLock = false;
      let focusCell = this.curClickInfo.cell;
      if (focusCell) {
        isCellLock = focusCell.lock;
      }
      if (this.editor.formula_mode) return;
      let isSearchBox = false;
      switch (this.fieldInfo.type) {
        case "date": //日期类型
          this.datePickerPanelSeatSet(this.fieldPanelOffset, focus_row);
          setTimeout(() => {
            if (!isCellLock) {
              this.showDatePicker();
            }
          });
          return;
        case "select": //选择框类型
          if (this.fieldInfo.source_id) {
            isSearchBox =
              this.fieldInfo.source_id.split("_")[0] === "searchBox" || false;
          }

          this.fieldPanelOffset.top -= 50; //- 50 是为了解决在底部弹出时闪烁问题
          setTimeout(() => {
            if (!this.stayTop && !isCellLock) {
              this.showFieldSelect(isSearchBox);
            }
          });
          return;
        case "number": //选择框类型
          if (this.fieldInfo.formula) return;
          setTimeout(() => {
            if (!isCellLock) {
              this.showNumberSelectPane();
            }
          });
          return;
      }
    },
    //日期选择框位置调整
    datePickerPanelSeatSet(fieldPanelOffset, focus_row) {
      const { viewScale } = this.editor;
      const view_height = document.body.clientHeight;
      const datePanelHeight = 345;
      //获取design外层的距顶部的距离
      const top =
        this.$refs.content.getBoundingClientRect().top -
        this.$refs.content.offsetTop;
      if (fieldPanelOffset.top + top + datePanelHeight > view_height) {
        fieldPanelOffset.top =
          fieldPanelOffset.top - datePanelHeight - focus_row.height * viewScale;
      }
    },
    //打开下拉选择菜单
    showFieldSelect(isSearchBox) {
      if (isSearchBox) {
        this.quickSelectIndex = -1;
        this.showFieldSearchBox = true;
      } else {
        this.showFieldSelectPanel = true;
      }
    },
    //打开日期选择器
    showDatePicker() {
      if (this.editor.isMobileTerminal()) {
        const text = this.editor._curFieldInfo.text;
        const date = text && new Date(text);
        date && (this.currentDate = date);
        this.mobileDatePickerShow = true;
      } else {
        this.showFieldDatePicker = true;
      }
    },
    //初始化文本域默认值
    initFieldDefault() {
      const id = this.instance.utils.getUUID("field");
      return {
        id: id,
        name: "",
        type: "normal",
        tip: "",
        placeholder: "文本域",
        start_symbol: "[",
        end_symbol: "]",
        show_format: 0,
        replace_format: 0,
        number_format: 0,
        readonly: 0,
        deletable: 1,
        canBeCopied: 1,
        replaceRule: [],
        source_list: [],
        // source_id: "",
        label_text: "",
        multi_select: 0,
        ext_cell: null,
        separator: 0,
        formula: "",
        inputMode: 0,
        active_type: 0,
      };
    },
    //弹出文本域插入窗口
    insertField() {
      this.fieldInfo = this.initFieldDefault();
      this.editor._curFieldInfo = this.fieldInfo;
      // BUS.$emit("editor_" + this.editorId, { fieldInfo: this.fieldInfo });
      this.fieldInfo.isInsert = true;
      this.isInsert = true;
      this.showFieldPropModalWin = true;
    },
    //设置光标位置
    setCursorAfterFieldReplace() {
      const para_path = [...this.fieldInfo.end_para_path];
      const model_path = this.editor.paraPath2ModelPath(para_path);
      //将光标定位到文本域末尾
      this.editor.selection.setCursorPosition(model_path);
      // 设置取消选区
      this.editor.selection.clearSelectedInfo();
    },
    async replaceNumberFieldText(val) {
      if (this.fieldInfo) {
        const newVal = await this.editor.event.emit(
          "beforeFieldSelect",
          val,
          "number"
        );
        if (newVal !== "origin") {
          val = newVal;
        }
        this.fieldInfo.new_text = val;
        this.editor.updateFieldText({ fields: [this.fieldInfo] });
      }
    },
    //替换文本域内容
    async replaceFieldText(val, selectType, isNotClose, value, code) {
      if (val !== "Quit" && this.fieldInfo) {
        const newVal = await this.editor.event.emit(
          "beforeFieldSelect",
          val,
          selectType
        );
        if (newVal !== "origin") {
          val = newVal;
        }
        this.fieldInfo.new_text = val;
        this.fieldInfo.formula_value = value ? value : null;
        if (this.fieldInfo.inputMode) {
          this.editor.setAdminMode();
          this.editor.updateFieldText({ fields: [this.fieldInfo] });
          this.editor.setAdminMode(false);
        } else {
          this.editor.updateFieldText({ fields: [this.fieldInfo] });
        }
        this.editor.event.emit("fieldSelect", val, selectType, code, value);
      }
      //是否关闭面板
      if (!isNotClose || !this.fieldInfo) {
        this.hideFieldNeedPanel();
        this.editor.focus();
      }
    },
    //隐藏文本域相关下拉菜单与日期选择器
    hideFieldNeedPanel() {
      this.showFieldDatePicker = false;
      this.showFieldSelectPanel = false;
      this.isShowNumberSelect = false;
      this.showFieldSearchBox = false;
    },
    //插入页码页数insType = 1页码
    insertPageNumCount(insType = 0) {
      const field = this.initFieldDefault();
      field.type = "label";
      if (insType === 1) {
        field.name = this.instance.sysVariables.page_number;
        field.placeholder = "页";
      } else {
        field.name = this.instance.sysVariables.page_count;
        field.placeholder = "页数";
      }
      this.curClickInfo.field = this.editor.insertField(field);
    },
    /**
     * 内容改变触发的验证
     */
    editorContentChangedValid(field) {
      if (field && field.valid) {
        field.validValue();
      }
      this.editor.render();
    },
    showNumberSelectPane() {
      this.isShowNumberSelect = true;
    },
  },
};
export default fieldMixIn;
