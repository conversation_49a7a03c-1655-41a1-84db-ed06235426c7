import FlowRectangle from "./FlowRectangle";
import Table from "./Table";
import Row from "./Row";
import Renderer from "./Renderer";

import Header from "./Header";
import Footer from "./Footer";
import Editor from "./Editor";
import Paragraph from "./Paragraph";
import WaterMark from "./WaterMark";
// import EditorLocalTest from "../../localtest";
import { hexToRgba, isRow, isTable } from "./Utils";
import Group from "./Groups";
import CommentBox from "./CommentBox";
import { COMMENT_LIST_ITEM_SPACE_HEIGHT, COMMENT_LIST_ITEM_TITLE_HEIGHT, COMMENT_LIST_MIN_WIDTH, COMMENT_LIST_TITLE_HEIGHT, COMMENT_WIDTH, ScriptType } from "./Constant";
import Comment from "./Comment";
// import Image from "./Image";
export default class Page extends FlowRectangle {
  cursor_position: number; // 游标位置

  cursor_index: number = 0; // 游标偏移索引

  header!: Header; // 页眉

  children: (Table | Row)[] = []; // 页里面的元素

  footer!: Footer; // 页脚

  padding_top: number;

  padding_left: number;

  padding_right: number;

  padding_bottom: number;

  number: number = 0; // 页码

  show_corner_line: boolean = true;

  comments: any = []; // 在 update 中记录这一页上的批注

  commentBox: CommentBox [] = []; // 记录实例

  scrollTop: number = 0; // 当前页 批注列表滚动的距离 是有最大值和最小值的

  clickScrollY: number = 0; // 点击在滚动条上的位置

  commentTotalHeight: number = 0; // 批注列表的总高度

  editor: Editor;

  constructor(left: number, top: number, width: number, height: number, editor: Editor) {
    super(left, top, width, height);
    this.padding_top = editor.config.page_padding_top;
    this.padding_right = editor.config.page_padding_right;
    this.padding_bottom = editor.config.page_padding_bottom;
    this.cursor_position = this.padding_top;
    this.editor = editor;
    this.padding_left = editor.config.page_padding_left;
    this.header = new Header(editor, this);
    this.footer = new Footer(editor, this);
  }

  // 拿到当前页所有的分组
  get groups() {
    let groups: any = [];
    let group_ids = this.children.map((container) => {
      if (isTable(container)) {
        return container.group_id;
      } else {
        return container.paragraph.group_id;
      }
    });
    // 去重
    group_ids = Array.from(new Set(group_ids));
    if (this.editor.root_cell.groups) {
      groups = this.editor.root_cell.groups.filter((group) => {
        return (group_ids as any).includes(group.id);
      });
    }
    return groups;
  }

  get pre_page() {
    return this.editor.pages[this.number - 2] ?? this.editor.pages[0];
  }

  // 假设该页表格占据了一整页
  get contentHeight() {
    return this.footer.footer_outer_top - this.editor.config.content_margin_footer - this.header.header_outer_bottom - this.editor.config.content_margin_header;
  }

  /**
 *
 * @param elements 要插入该页的元素集合(Table 和 Row 的集合)
 * @param page_index 从该页的page_index下标处 开始更新(包括 page_index 处的这一行)
 * @returns 剩余没有插完的元素集合 就是剩下的 elements
 */
  add(elements: (Table | Row)[], page_index: number): (Table | Row)[] {
    // page_index 的赋值在各个操作的时候，首次渲染的时候全部选择 即为0
    this.children.splice(page_index, this.children.length);

    // 光标位置 如果不在第一页 没有子元素 就在刚开始的位置 如果有子元素 就在元素末尾
    // 如果是第一行的话 就是该行顶部边界 距离 当前页顶部边界的距离
    // 如果不是第一行的话 就是上一行的bottom(还是距离当前页顶部边界的距离)
    if (page_index === 0) {
      // 如果从开头更新
      // 那么第一行的 top 值就应该是 页眉的底部边界距离该页顶部边界的距离 加上 配置的 该页的内容距离页眉底部边界的距离
      this.cursor_position = this.header.header_outer_bottom + this.editor.config.content_margin_header;
    } else {
      if (this.children.length) {
        // 如果不是从第 0 行开始 并且还得有内容 那么 更新处的 元素 top 值 就应该是 上一行的 bottom 的值
        this.cursor_position = this.children[page_index - 1].bottom;
      } else {
        this.cursor_position = 0;
      }
    }
    // elements 的各个子元素的属性赋值 elements数组的值来自 root_cell.children, root_node
    // element 是row | table
    for (let i = 0; i < elements.length; i++) {
      const element = elements[i];

      element.top = this.cursor_position; // 所以每个row或者table的 top值都是距离 所在页 顶部边界的距离

      // 段落前间距
      if (isRow(element) && element.row_index_in_para === 0) {
        element.top =
          element.top +
          element.paragraph.before_paragraph_spacing * element.height;
      }
      // TODO 图片拖至最大高度时页眉页脚添加row会挤压图片导致页面崩溃，此方法可以避免此种情况，但仍需验证
      // if (isRow(element)) {
      //   element.children.forEach(e => {
      //     if (isImage(e)) {
      //       const content_height = this.footer.footer_outer_top - this.header.header_outer_bottom - Config.content_margin_footer - Config.content_margin_header;
      //       if (e.height > content_height) {
      //         e.height = content_height;
      //         element.height = content_height;
      //       }
      //     };
      //   });
      // }

      // element.top 是在这儿赋的值 但是 elemtn.bottom 都是计算属性 用的 top + height
      // 元素底部高度是否超出有效页面的高度
      if (
        element.bottom >
        this.footer.footer_outer_top - this.editor.config.content_margin_footer
      ) {
        if (isRow(element)) {
          // 页面刚开始渲染,内容超出页面会调用这个方法，有返回值的时候会进入递归循环里 => editor => update
          return elements.slice(i, elements.length); // 超出了 当前页放不下 返回剩余元素并将剩余元素放在下一页
        }
        if (isTable(element)) {
          // if (EditorLocalTest.transUse && element.origin && element.origin.split_parts.length > 0) {
          //   const nextElement = elements[i + 1];

          //   element.page_number = this.number;
          //   element.page_index = page_index;
          //   page_index += 1;
          //   this.cursor_position = element.bottom;
          //   this.children.push(element);
          //   const result = elements.slice(i + 1, elements.length);
          //   if (isTable(nextElement) && nextElement.origin === element.origin) {
          //     return result;
          //   } else {
          //     continue;
          //   }
          // }
          // 记录表格优化的想法 ↓
          // 1. 改成一边计算单元格的 row_size 一边判断应该放到上一页还是拆分该单元格
          // 记录表格优化的想法 ↑

          // 分割线高度 应该是理论上的分割线位置处 距离表格该表格顶部边界的距离 跟下行注释比 这是真实的理论位置
          // 不能用 this.height - this.padding_bottom - element.top; 这样计算 因为页脚可能内容很多 这个算法没有考虑页脚内容和设置的 footer_top 等情况
          const split_line =
            this.footer.footer_outer_top -
            this.editor.config.content_margin_footer -
            element.top;
          const tables = element.split(split_line, this.editor.root_cell);
          // if (!EditorLocalTest.transUse) {
          if (element.page_break) {
            // 此element已经被分割过了
            element.getOrigin().split_parts.splice(-1, 1, tables[0], tables[1]);
          } else {
            element.getOrigin().split_parts = tables; // 没分割过 将操作后的tables数组赋值给对应的element对象
          }
          // }

          if (tables.length === 1) return elements.slice(i, elements.length); // 整个表格移到下一页

          element.page_number = this.number;

          element.page_index = page_index;

          // 分割后的两个列表
          const pre_table = tables[0];

          const nextTables = tables.slice(1);

          pre_table.page_number = this.number;

          this.children.push(pre_table);

          pre_table.page_index = this.children.indexOf(pre_table);

          const result = elements.slice(i + 1, elements.length);

          result.unshift(...nextTables);

          return result;
        }
      }
      if (isTable(element)) {
        // if (EditorLocalTest.transUse && element.origin && element.origin.split_parts.length > 0) {
        //   const nextElement = elements[i + 1];

        //   element.page_number = this.number;
        //   element.page_index = page_index;
        //   page_index += 1;

        //   this.cursor_position = element.bottom;

        //   this.children.push(element);
        //   const result = elements.slice(i + 1, elements.length);
        //   if (isTable(nextElement) && nextElement.origin === element.origin) {
        //     return result;
        //   } else {
        //     continue;
        //   }
        // } else {
        // 未被分割，重置split属性
        element.resetSplitAttr();
        // }
      }

      element.page_number = this.number;

      this.children.push(element);

      elements[i].page_index = page_index;

      page_index += 1;

      this.cursor_position = element.bottom;

      // TODO 普通文档的分页和下方分组分页的逻辑可能出现冲突，同时使用的时候请注意
      if (isRow(element) && element.page_break) {
        return elements.slice(i + 1, elements.length);
      }
      if ((isTable(element)) && element.group_id) {
        // 所在分组
        const group = this.editor.selection.getGroupByGroupId(element.group_id);
        // 分组换页 分组最后一个块级元素 当前元素为表格
        if ((group!.page_break || group!.next_group?.new_page) && element.id === group!.content_para_id[group!.content_para_id.length - 1]) {
          return elements.slice(i + 1, elements.length);
        }
      } else if ((isRow(element)) && element.paragraph.group_id) {
        // 所在分组
        const group = this.editor.selection.getGroupByGroupId(element.paragraph.group_id);
        // 分组最后一段
        const last_para = (group!.paragraph[group!.paragraph.length - 1] as Paragraph);
        // 分组换页 当前元素为分组最后一行
        if ((group!.page_break || group!.next_group?.new_page) && (element.id === last_para.id || element.id === last_para.lastRow?.id)) {
          return elements.slice(i + 1, elements.length);
        }
      }
    }

    return [];
  }


  draw() {
    Renderer.draw_rect_shadow(
      this.left,
      this.top,
      this.width,
      this.height,
      this.editor.config.page_color
    );
    // 绘制页眉
    this.drawWatermark(1);
    this.header.draw();
    this.drawWatermark(2);
    Renderer.save();
    Renderer.translate(this.left, this.top);
    // this.children.forEach((item) => item.draw(this.editor));

    const focus_group = this.editor.selection.getFocusGroup();
    // 锁定分组增加颜色标识
    let lockGroupStartInfo: any = {}
    let lockGroupEndInfo: any = {}
    const pushGroupLockInfo = (group: Group | undefined, ele: Table | Row, isStart: boolean) => {
      if (this.editor.config.group_lock_bg_color && group && group.lock) {
        if (isStart) {
          if (!lockGroupStartInfo[group.id]) {
            lockGroupStartInfo[group.id] = {
              x: ele.left,
              y: ele.top,
            }
          }
        } else {
          if (!lockGroupEndInfo[group.id]) {
            lockGroupEndInfo[group.id] = {
              x: ele.right,
              y: ele.bottom,
            }
          }
        }
      }
    }
    for (let i = 0; i < this.children.length; i++) {
      const element = this.children[i];
      if (!(isTable(element)) || (isTable(element) && i === this.children.length - 1)) {
        element.draw(this.editor);
      }

      const table = this.children[i - 1];
      if (isTable(table)) {
        table.draw(this.editor);
      }

      if (!this.editor.print_mode && this.editor.view_mode !== "view") {
        let color: string = this.editor.config.group_normal_color;
        if (isTable(element)) {
          // 当前是分组内的表格 前一个元素和当前元素的分组id不相同 或是 最开头了
          if (element.group_id) {
            if (focus_group && !focus_group.lock && focus_group.id === element.group_id) {
              color = this.editor.config.group_line_color;
            }
            const group = this.editor.selection.getGroupByGroupId(element.group_id);
            pushGroupLockInfo(group, element, true)
            const origin_table = element.getOrigin(); // 拆分前的表格或者没拆分的表格
            // if (!origin_table.previousPara || (origin_table.group_id !== origin_table.previousPara!.group_id && !(i === 0 && element.origin))) {
            // 表格拆分
            // 正常文档中的表格
            // 文档最开始为表格
            if ((!origin_table.previousPara && origin_table.split_parts[0] === element) || (origin_table.previousPara && origin_table.group_id !== origin_table.previousPara.group_id) || (!origin_table.previousPara && origin_table.split_parts.length === 0)) {
              Renderer.draw_line([element.left, element.top], [element.left, element.top + 14], color, 1, 1);
              Renderer.draw_line([element.left, element.top], [element.right, element.top], color, 1, 1);
              Renderer.draw_line([element.right, element.top], [element.right, element.top + 14], color, 1, 1);
              if (group && group.lock) {
                Renderer.draw_line([element.right + 5, element.top + 4], [element.right + 5, element.top + 14], color, 1);
                Renderer.draw_line([element.right + 5, element.top + 14], [element.right + 15, element.top + 14], color, 1);
                Renderer.draw_line([element.right + 15, element.top + 14], [element.right + 15, element.top + 4], color, 1);
                Renderer.draw_line([element.right + 15, element.top + 4], [element.right + 5, element.top + 4], color, 1);
                Renderer.draw_line([element.right + 8, element.top + 8], [element.right + 14, element.top + 8], color, 1);
                Renderer.drawArc(element.right + 10, element.top + 4, 4, Math.PI, 0, color, 1);
              }
            }
            // 拆分表格跨页
            // 正常文档
            // 文档末尾
            if ((!origin_table.nextPara && origin_table.split_parts[origin_table.split_parts.length - 1] === element) || (origin_table.nextPara && origin_table.group_id !== origin_table.nextPara.group_id) || (!origin_table.nextPara && origin_table.split_parts.length === 0)) {
              Renderer.draw_line([element.left, element.bottom], [element.left, element.bottom - 14], color, 1, 1);
              Renderer.draw_line([element.left, element.bottom], [element.right, element.bottom], color, 1, 1);
              Renderer.draw_line([element.right, element.bottom], [element.right, element.bottom - 14], color, 1, 1);
              pushGroupLockInfo(group, element, false)
            }
            if (i === this.children.length - 1) {
              pushGroupLockInfo(group, element, false)
            }
          }
        } else {
          if (element.paragraph.group_id) {
            if (focus_group && !focus_group.lock && focus_group.id === element.paragraph.group_id) {
              color = this.editor.config.group_line_color;
            }
            const group = this.editor.selection.getGroupByGroupId(element.paragraph.group_id);
            pushGroupLockInfo(group, element, true)
            // 分组开始
            if (element.row_index_in_para === 0 && (!element.paragraph.previousParagraph || element.paragraph.group_id !== element.paragraph.previousParagraph.group_id)) {
              Renderer.draw_line([element.left, element.top + 6], [element.left, element.top + 14], color, 1);
              Renderer.drawArc(element.left + 5, element.top + 6, 5, Math.PI, Math.PI * 3 / 2, color, 1);
              Renderer.draw_line([element.left + 5, element.top + 1], [element.right - 5, element.top + 1], color, 1);
              Renderer.drawArc(element.right - 5, element.top + 6, 5, Math.PI * 3 / 2, 0, color, 1);
              Renderer.draw_line([element.right, element.top + 6], [element.right, element.top + 14], color, 1);

              if (group && group.lock) {
                Renderer.draw_line([element.right + 10, element.top + 4], [element.right + 10, element.top + 14], color, 1);
                Renderer.draw_line([element.right + 10, element.top + 14], [element.right + 20, element.top + 14], color, 1);
                Renderer.draw_line([element.right + 20, element.top + 14], [element.right + 20, element.top + 4], color, 1);
                Renderer.draw_line([element.right + 20, element.top + 4], [element.right + 10, element.top + 4], color, 1);
                Renderer.draw_line([element.right + 13, element.top + 8], [element.right + 17, element.top + 8], color, 1);
                Renderer.drawArc(element.right + 15, element.top + 4, 4, Math.PI, 0, color, 1);
              }
            }
            // 分组结束
            if (element.row_index_in_para === element.paragraph.children.length - 1 && (!element.paragraph.nextParagraph || element.paragraph.group_id !== element.paragraph.nextParagraph.group_id)) {
              Renderer.draw_line([element.left, element.bottom - 6], [element.left, element.bottom - 14], color, 1);
              Renderer.drawArc(element.left + 5, element.bottom - 6, 5, Math.PI * 1 / 2, Math.PI, color, 1);
              Renderer.draw_line([element.left + 5, element.bottom - 1], [element.right - 5, element.bottom - 1], color, 1);
              Renderer.drawArc(element.right - 5, element.bottom - 6, 5, 0, Math.PI * 1 / 2, color, 1);
              Renderer.draw_line([element.right, element.bottom - 6], [element.right, element.bottom - 14], color, 1);
              pushGroupLockInfo(group, element, false)
            }
            if (i === this.children.length - 1) {
              pushGroupLockInfo(group, element, false)
            }
          }
        }
      }
    }
    if (this.editor.config.group_lock_bg_color) {
      const rgbaColor = hexToRgba(this.editor.config.group_lock_bg_color)
      for (const groupId in lockGroupStartInfo) {
        const { x: startX, y: startY } = lockGroupStartInfo[groupId]
        const { x: endX, y: endY } = lockGroupEndInfo[groupId]
        // 锁定的分组绘制浅灰遮罩
        Renderer.draw_rectangle(
          startX,
          startY,
          endX - startX,
          endY - startY,
          rgbaColor ?? "rgba(105,105,105,0.05)"
        );
      }
    }
    if (this.editor.config.contentBorder) {
      const content_width = this.width - this.editor.config.page_padding_left - this.editor.config.page_padding_right
      const content_height = this.footer.footer_outer_top - this.header.header_outer_bottom - this.editor.config.content_margin_footer - this.editor.config.content_margin_header

      Renderer.draw_stroke_rect(this.editor.config.page_padding_left - 0.5, this.header.header_outer_bottom + this.editor.config.content_margin_header - 0.5, content_width, content_height, "black")
    }
    Renderer.restore();
    // 绘制页脚
    this.footer.draw();
    // // 绘制批注
    if (this.editor.useNewVersionCommentList && this.editor.is_comment_mode) {
      this.drawComment();
    }
    this.drawWatermark(3);
    if (this.show_corner_line && this.editor.config.show_corner_line) {
      this.drawPageCornerLine();
    }
  }

  setCommentBox() {
    // 这些内容 我将挪到 update 里边去 ↑
    // 每个批注位置的策略是：
    // 1. 不能超过 批注列表 大标题 也就是说 top 值不能小于 此刻的 top + COMMENT_LIST_TITLE_HEIGHT
    // 2. 锚定第一个批注的位置 上方有空白就有空白
    // 3. 我要记录每个批注加起来的总高度 剩下的批注就往下排 但是要跟 position 
    let top = this.top - this.scrollTop;
    const left = this.left + this.width + 1;
    const editor = this.editor;
    const pageBottom = this.height + this.top;
    const newCommentBox = [];

    const fontStyle = { // 批注写死就用这种字体
      family: "宋体", 
      height: 16,
      bold: false,
      italic: false,
      underline: false,
      strikethrough: false,
      dblUnderLine: false,
      script: ScriptType.NORMAL,
      characterSpacing: 0,
      color: "#000",
      bgColor: null,
      highLight: null
    };
    const font = editor.fontMap.add(fontStyle);
    
    const COMMENT_LIST_WIDTH = Math.max(editor.config.comment.listWidth, COMMENT_LIST_MIN_WIDTH);
    top += COMMENT_LIST_TITLE_HEIGHT; // 先加上 批注 列表这四个字的高度
    const l = left + (COMMENT_LIST_WIDTH - COMMENT_WIDTH) / 2;
    const arr = []; // 我记录下来每个空的高度 如果超过一页了 我就减少这些空的高度 存对象吧 存第几个前边的  也就是说 这个数组里边是对象 每个对象记录能修改 top 值的批注 下标以及跟上方的空是多少(也就是 top 值还能减多少)
    let lastBottom = top; // 记录最后一个批注的下边位置 后边计算空白高度的时候就用
    for (let i = 0; i < this.comments.length; i++) {
      const comment = this.comments[i];
      const countRows = editor.newVersionOpenCommentMap.has(comment.id) ?  CommentBox.countRows(comment.value, 220, font, editor) : 0; // TODO 这个 220 估计也是试出来的
      const h = COMMENT_LIST_ITEM_TITLE_HEIGHT + countRows * 25; // TODO 猜测这个 25 是行间距 因为原来就是写的 25 等搞完了再研究这个 25
      const t = Math.max(top, comment.position); // 因为有可能页眉高度很小 批注在第一行 position 就很小 但是还有 标题 类似这种情况 所以要取最大值
      const blankHeight = t - lastBottom;
      if (blankHeight > 0) {
        // 说明有空白高度
        const obj = {
          i,
          blankHeight
        }
        arr.push(obj);
      }
      const commentBox = new CommentBox(l, t, h, editor, comment);
      newCommentBox.push(commentBox);
      top = commentBox.top + commentBox.height + COMMENT_LIST_ITEM_SPACE_HEIGHT;
      lastBottom = top;
      if (top > pageBottom) {
        let difference = top - pageBottom; // 差值
        let last = arr.pop();
        if (last) {
          if (last.blankHeight > difference) {
            // 这些都要减去 v 的值
            // newCommentBox[last.i].top -= difference; // 不仅仅是 last.i 位置的 批注要减 后边的所有批注都要同步往上位移 也就说 last.i 只是个起始位置不是要修改的位置
            for (let s = last.i; s < newCommentBox.length; s++) {
              newCommentBox[s].top -= difference;
            }
            top -= difference;
            lastBottom -= difference; 
            // 因为没有归零 所以 last 还要再放到 arr 里边去
            last.blankHeight -= difference;
            arr.push(last);
            difference = 0;
          } else {
            // 如果到这里 说明 最近的 这个空白已经不够用了
            for (let s = last.i; s < newCommentBox.length; s++) {
              newCommentBox[s].top -= last.blankHeight;
            }
            top -= last.blankHeight;
            lastBottom -= last.blankHeight;
            difference -= last.blankHeight; // 已经进来这里了就说明 blankHeight 不够用了 但是差值肯定就变了
            while(arr.length && difference > 0) {
              last = arr.pop()
              if (last) {
                if (last.blankHeight > difference) {
                  for (let s = last.i; s < newCommentBox.length; s++) {
                    newCommentBox[s].top -= difference;
                  }
                  top -= difference;
                  lastBottom -= difference; 
                  last.blankHeight -= difference;
                  arr.push(last);
                  difference = 0;
                } else {
                  for (let s = last.i; s < newCommentBox.length; s++) {
                    newCommentBox[s].top -= last.blankHeight;
                  }
                  top -= last.blankHeight;
                  lastBottom -= last.blankHeight;
                  difference -= last.blankHeight; 
                }
              }
            }
          }
        }
        // 此时应该重新分配之前批注的位置 优先确保这一页上没有空隙 然后再出现滚动条 当没有空隙的时候 position 也就没有用了
      }
    }
    this.commentBox = newCommentBox;
    
  }

  

  drawComment() {
    Renderer.save();
    const editor = this.editor;
    const left = this.left + this.width + 1
    let top = this.top;
    const COMMENT_LIST_WIDTH = Math.max(editor.config.comment.listWidth, COMMENT_LIST_MIN_WIDTH);
    // 绘制批注列表的白色背景
    Renderer.draw_rect_shadow(
      left,
      top,
      COMMENT_LIST_WIDTH,
      this.height,
      editor.config.comment.listBgColor
    );
    
    Renderer.clipRect(left, top, COMMENT_LIST_WIDTH, this.height);

    Comment.drawTitle(editor, left, top - this.scrollTop);

    // 设置总高度 ↓
    let totalHeight = this.commentBox.length * COMMENT_LIST_ITEM_SPACE_HEIGHT + COMMENT_LIST_TITLE_HEIGHT;
    this.commentBox.forEach((comment:any) => {
      totalHeight += comment.height;
    });
    this.commentTotalHeight = totalHeight;
    // 设置总高度 ↑
    
    for (let i = 0; i < this.commentBox.length; i++) {
      const commentBox = this.commentBox[i];
      commentBox.draw(this);
    }

    if ( this.commentTotalHeight > this.height) {
      editor.drawCommentScrollBar(this);
    }
    Renderer.restore();
  }


  drawWatermark(level: number) {
    if (this.editor.waterMarks.length) {
      const textList = [];
      // 先绘制图片再绘制文字，避免文字被图片遮挡
      for (let i = 0; i < this.editor.waterMarks.length; i++) {
        const waterMark: WaterMark = this.editor.waterMarks[i];
        if (waterMark.params.level) {
          if (waterMark.params.level !== level) continue
        } else {
          if (level !== 1) {
            continue
          }
        }
        let top = 0;
        if (waterMark.mode === "repeat") {
          top = this.top;
        } else {
          //单独模式下只绘制所在页的水印
          const real_page = this.editor.getPageByRealY(waterMark.params.real_y)
          if (real_page + 1 !== this.number) {
            continue
          }
        }
        if (waterMark.type === "textMark") {
          textList.push(waterMark);
        } else {
          waterMark.draw(this.editor, top);
        }
      }
      if (textList.length) {
        for (let i = 0; i < textList.length; i++) {
          const textMark = textList[i];
          let top = 0;
          if (textMark.mode === "repeat") {
            top = this.top;
          }
          textMark.draw(this.editor, top);
        }
      }
    }
  }

  drawContentShadow() {
    Renderer.save();
    Renderer.translate(this.left, this.top);
    Renderer.draw_shadow(
      0,
      this.header.header_outer_bottom,
      this.width,
      this.footer.footer_outer_top - this.header.header_outer_bottom,
      this.editor.config.page_color
    );

    const top_line = this.header.header_outer_bottom;
    const end_line = this.footer.footer_outer_top;

    Renderer.draw_line_dash(top_line, 0, this.width, "#000");
    Renderer.draw_line_dash(end_line, 0, this.width, "#000");
    Renderer.restore();
  }

  drawHeaderFooterShadow() {
    Renderer.save();
    Renderer.translate(this.left, this.top);
    Renderer.draw_shadow(0, 0, this.width, this.header.header_outer_bottom, this.editor.config.page_color);
    Renderer.draw_shadow(
      0,
      this.footer.footer_outer_top,
      this.width,
      this.height - this.footer.footer_outer_top,
      this.editor.config.page_color
    );

    if (this.header.editor.is_edit_hf_mode) {
      const top_line = this.header.header_outer_bottom;
      const end_line = this.footer.footer_outer_top;

      Renderer.draw_line_dash(top_line, 0, this.width, "#000");
      Renderer.draw_line_dash(end_line, 0, this.width, "#000");
    }

    Renderer.restore();
  }

  drawPageCornerLine() {
    const left_top = {
      x: this.left + this.editor.config.page_padding_left,
      y: this.top + this.editor.config.page_padding_top - 5
    };
    const left_top_start_path = {
      x: left_top.x - 25,
      y: left_top.y
    };

    const left_top_end_path = {
      x: left_top.x,
      y: left_top.y - 25
    };
    Renderer.drawCornerLine(left_top_start_path, left_top, left_top_end_path);
    const left_bottom = {
      x: this.left + this.editor.config.page_padding_left,
      y: this.top + this.height - this.editor.config.page_padding_bottom + 5
    };
    const left_bottom_start_path = {
      x: left_bottom.x - 25,
      y: left_bottom.y
    };

    const left_bottom_end_path = {
      x: left_bottom.x,
      y: left_bottom.y + 25
    };
    Renderer.drawCornerLine(
      left_bottom_start_path,
      left_bottom,
      left_bottom_end_path
    );
    const right_top = {
      x: this.left + this.width - this.editor.config.page_padding_right,
      y: this.top + this.editor.config.page_padding_top - 5
    };
    const right_top_start_path = {
      x: right_top.x + 25,
      y: right_top.y
    };

    const right_top_end_path = {
      x: right_top.x,
      y: right_top.y - 25
    };
    Renderer.drawCornerLine(
      right_top_start_path,
      right_top,
      right_top_end_path
    );
    const right_bottom = {
      x: this.left + this.width - this.editor.config.page_padding_right,
      y: this.top + this.height - this.editor.config.page_padding_bottom + 5
    };
    const right_bottom_start_path = {
      x: right_bottom.x + 25,
      y: right_bottom.y
    };

    const right_bottom_end_path = {
      x: right_bottom.x,
      y: right_bottom.y + 25
    };
    Renderer.drawCornerLine(
      right_bottom_start_path,
      right_bottom,
      right_bottom_end_path
    );
  }

  getRangeOfCommentScrollTop(): {max: number, min: number} {
    let min = 0;
    let max = 0;
    max = this.commentTotalHeight - this.height + COMMENT_LIST_ITEM_SPACE_HEIGHT;
    return { max, min };
  }

  
}
