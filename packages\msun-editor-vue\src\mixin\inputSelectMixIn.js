import pinyin from "js-pinyin";

const inputSelect = {
  data() {
    return {
      choiceIndex: 0,
      selectText: "",
      scrollTop: 0,
      maxScrollNum: 0,
      // forbidMouseEvent: false,
      scrollPageNum: 0,
      selectComSentencesList: [],
      userId: "comm",
      filterWords: [
        "，",
        ",",
        ".",
        "。",
        "！",
        "!",
        "?",
        "？",
        "、",
        ":",
        "：",
        "；",
        ";",
        "[",
        "]",
        " ",
      ],
      punctuationMap: {
        ".": "。",
        ",": "，",
        "?": "？",
        "!": "！",
        ":": "：",
        ";": "；",
        "(": "（",
        ")": "）",
      },
      commonSentences: [
        // { name: "aaaaa" },
        // { name: "bbb" },
        // { name: "cccc" },
        // { name: "ddddd" },
      ],
      showInputSelect: false,
      inputLocation: {
        left: 0,
        top: 0,
      },
    };
  },
  mounted() {
    this.commWordsData = [];
    setTimeout(() => {
      // 从localStorge中读取后加载到内存
      this.getCommWordsFromLocal();
    }, 1000);
  },
  methods: {
    quickInputSelect() {
      this.reInitAttrState();
      this.selectComSentencesList = [];
      let { text, lastChar } = this.editor.internal.getSearchStr({
        numberOfWords: 30,
        symbol: this.filterWords,
      });
      let searchText = text;
      if (this.showInsertSelectMenu(searchText)) {
        //当结尾为双斜线时默认触发编辑器内置的插入
        return;
      }
      if (this.filterLocalCommWords(searchText, lastChar)) {
        return;
      }
      const recursiveSearch = (searchText) => {
        // 递归退出条件：当searchText长度小于2时退出
        if (searchText.length < 2) {
          return;
        }
        // 模拟原循环中的事件发射
        this.editor.event.emit(
          "quickSelectInputList",
          searchText,
          (selectList) => {
            // 检查selectList是否为数组且长度大于0
            if (Array.isArray(selectList) && selectList.length) {
              this.choiceIndex = -1;
              // 截取selectList到200个元素
              this.selectComSentencesList =
                selectList.length > 200 ? selectList.slice(0, 200) : selectList;
              this.selectText = searchText;
            } else {
              // 递归调用，移除searchText的第一个字符
              recursiveSearch(searchText.slice(1));
            }
          }
        );
      };
      recursiveSearch(searchText);
      if (this.selectComSentencesList.length > 0) {
        this.showInputSelect = true;
        this.getInputLocation();
      } else {
        this.showInputSelect = false;
      }
    },
    showInsertSelectMenu(searchText) {
      const split = searchText.split("//");
      if (split.length < 2) {
        return false;
      }
      const lastText = split[split.length - 1];
      if (lastText > 5) {
        return false;
      }
      this.selectText = lastText;
      // 截取selectList到200个元素
      this.selectComSentencesList = this.insertMenu.children
        .filter((item) => {
          if (item.title.indexOf(lastText) > -1) {
            return true;
          }
          if (
            item.keyword
              .split(/[,，]/)
              .some((k) => k.indexOf(lastText.toLowerCase()) > -1)
          ) {
            return true;
          }
        })
        .map((item) => {
          return {
            name: item.title,
            keyword: item.keyword,
            node: item,
            type: "insertMenu",
          };
        });
      this.showInputSelect = true;
      this.getInputLocation();
      return true;
    },
    showDefaultInput() {
      this.selectComSentencesList = [];
      this.selectText = "";
      if (this.commonSentences.length > 200) {
        this.selectComSentencesList = this.commonSentences.slice(0, 200);
      } else {
        this.selectComSentencesList = this.commonSentences;
      }
      if (this.selectComSentencesList.length > 0) {
        this.showInputSelect = true;
        this.getInputLocation();
      } else {
        this.showInputSelect = false;
      }
    },
    getInputLocation() {
      this.$nextTick(() => {
        const view_path = this.editor.modelPath2viewPath(
          this.editor.selection.anchor
        );
        const absolute_xy =
          this.editor.getElementAbsolutePositionByViewPath(view_path);
        const focus_row = this.editor.selection.getFocusRow();
        const { x, y } = this.editor.getViewPositionByAbsolutePosition(
          absolute_xy.x,
          absolute_xy.y + focus_row.height - this.editor.scroll_top
        );

        let caretPath = { clientX: x, clientY: y };

        const inputSelectDom = this.$refs.inputSelect.$el;

        const height = inputSelectDom.offsetHeight;
        const width = inputSelectDom.offsetWidth;
        let { left, top } = this.calcRightClickMenuPosition(
          caretPath,
          height,
          width
        );

        const caret = this.editor.caret;
        const contentDom = this.$refs.content;
        if (
          contentDom.offsetHeight -
            (caret.y + caret.height - this.editor.scroll_top) *
              this.editor.viewScale <
          height
        ) {
          top = top - focus_row.height * this.editor.viewScale - height;
        }
        top += contentDom.offsetTop;
        this.inputLocation = {
          left: left + contentDom.offsetLeft,
          top,
        };
        let inputSelectChildNodes = this.$refs.inputSelect.$el.childNodes;
        let totalHeight = 0;
        for (let i = 0; i < inputSelectChildNodes.length; i++) {
          const inputHeight = inputSelectChildNodes[i].offsetHeight;
          totalHeight += inputHeight;
        }
        if (totalHeight > 155) {
          this.maxScrollNum = Math.floor(totalHeight / 155);
        } else {
          this.maxScrollNum = 0;
        }
      });
    },

    changeSelectIndex(val) {
      this.choiceIndex = val;
      const selectedData = this.selectComSentencesList[val];
      this.insertCommonWords(selectedData);
    },
    reInitAttrState() {
      this.choiceIndex = 0;
      this.scrollTop = 0;
      this.scrollPageNum = 0;
      this.maxScrollNum = 0;
      let dom = document.getElementById("inputSelect" + this.editorId);
      if (dom) {
        dom.scrollTop = 0;
      }
    },
    closeSelectPanel() {
      this.showInputSelect = false;
      this.reInitAttrState();
    },
    inputSelectKeyDown(e) {
      if (e.key === "Escape") {
        this.closeSelectPanel();
        return;
      }

      const keyCodeArr = [37, 38, 39, 40, 13, 9];
      const isExist = keyCodeArr.findIndex((item) => item === e.keyCode);
      if (isExist === -1) {
        return true;
      }

      if (this.showInputSelect) {
        let dom = document.getElementById("inputSelect" + this.editorId);
        let e1 = e;
        e1.preventDefault();

        // 根据按键执行相应的操作
        switch (e1.keyCode) {
          case 37: // 左箭头
            this.handleLeftArrow(dom);
            break;
          case 39: // 右箭头
            this.handleRightArrow(dom);
            break;
          case 38: // 上箭头
            this.handleUpArrow(dom);
            break;
          case 40: // 下箭头
          case 9: // Tab 键
            this.handleDownArrow(dom);
            break;
          case 13: // 回车
            this.handleEnter(dom);
            break;
        }

        return false;
      }
    },

    handleLeftArrow(dom) {
      if (this.scrollPageNum >= 1) {
        this.scrollPageNum -= 1;
        this.choiceIndex = 5 * this.scrollPageNum;
        this.scrollTop = 155 * this.scrollPageNum;
        dom.scrollTop = this.scrollTop;
      }
    },

    handleRightArrow(dom) {
      if (this.scrollPageNum < this.maxScrollNum) {
        this.scrollPageNum += 1;
        this.choiceIndex = 5 * this.scrollPageNum;
        this.scrollTop = 155 * this.scrollPageNum;
        dom.scrollTop = this.scrollTop;
      }
    },

    handleUpArrow(dom) {
      if (this.choiceIndex === 0) {
        this.choiceIndex = this.selectComSentencesList.length - 1;
        this.scrollTop = 155 * this.maxScrollNum;
        dom.scrollTop = this.scrollTop;
      } else {
        this.choiceIndex -= 1;
        this.scrollTop -= 31;

        // 确保选中的项可见
        this.ensureItemInView(dom);
      }
    },

    handleDownArrow(dom) {
      if (this.choiceIndex === this.selectComSentencesList.length - 1) {
        this.choiceIndex = 0;
        this.scrollTop = 0;
        dom.scrollTop = this.scrollTop;
      } else {
        this.choiceIndex += 1;
        this.scrollTop += 31;

        // 确保选中的项可见
        this.ensureItemInView(dom);
      }
    },

    handleEnter(dom) {
      if (this.choiceIndex === -1) {
        return true;
      }
      const selectedData = this.selectComSentencesList[this.choiceIndex];
      this.insertCommonWords(selectedData);
    },

    // 确保选中的项在可视范围内
    ensureItemInView(dom) {
      const selectedItem = dom.children[this.choiceIndex]; // 选中的子项
      if (selectedItem) {
        selectedItem.scrollIntoView({ behavior: "smooth", block: "nearest" });
      }
    },
    insertCommonWords(selectedData) {
      const { editor, EditorHelper } = this.instance;
      let searchTextLength = this.selectText.length;
      if (selectedData.type === "insertMenu") {
        searchTextLength += 2;
      }
      let end = [...editor.selection.para_focus];
      let start = [...end];
      start[start.length - 1] = start[start.length - 1] - searchTextLength;
      editor.selection.setSelectionByPath(start, end);
      EditorHelper.deleteBackward(editor);
      if (selectedData.type === "commWords") {
        editor.insertText(selectedData.name);
        if (selectedData.oriData) {
          if (!selectedData.oriData.use) {
            selectedData.oriData.use = 0;
          }
          selectedData.oriData.use += 1;
        }
      } else {
        editor.event.emit(
          "insertComSentence",
          this.selectComSentencesList[this.choiceIndex],
          selectedData
        );
      }

      this.closeSelectPanel();
      editor.focus();
    },
    // 处理数据转换时组装的文本
    handleAssembleText(text, rawField) {
      if (/^[0-9]+$/.test(text) && text.length < 4) {
        return;
      }
      // 按词拆分，去除标点符号
      const words = text
        .replace(/[，,。.!?！？：:；、]/g, " ")
        .split(/\s+/)
        .filter((word) => word.length > 0);
      const result = this.getCommWordsFromLocal();
      if (!result) return;
      for (let i = 0, len = words.length; i < len; i++) {
        const word = words[i];
        if (/^[0-9]+$/.test(word) && word.length < 4) {
          continue;
        }
        if (result.some((item) => item.text === word)) {
          continue;
        }
        const item = {
          user: this.userId,
          text: word,
          jp: pinyin.getCamelChars(word).toLowerCase(),
          qp: pinyin.getFullChars(word).toLowerCase(),
        };
        if (rawField && rawField.name) {
          item.field = rawField.name;
        }
        result.push(item);
      }
      this.setLocalCommWords();
    },
    filterLocalCommWords(searchText, lastChar) {
      if (!this.instance.localTest.useNew) {
        return false;
      }
      const symbolChar = this.punctuationMap[lastChar];
      if (symbolChar) {
        this.selectText = symbolChar;
        this.selectComSentencesList = [
          {
            type: "commWords",
            name: symbolChar,
          },
        ];
      } else {
        if (searchText.length < 2) {
          return false;
        }
        this.selectText = searchText;
        // 截取selectList到200个元素
        const selectList = this.commWordsData
          .filter((item) => {
            if (item.text.startsWith(searchText)) {
              return true;
            }
            if (item.jp.startsWith(searchText.toLowerCase())) {
              return true;
            }
            if (item.qp.startsWith(searchText.toLowerCase())) {
              return true;
            }
          })
          .map((item) => {
            return {
              type: "commWords",
              name: item.text,
              keyword: item.jp + item.qp,
              oriData: item,
            };
          });
        if (!selectList.length) {
          return this.filterLocalCommWords(searchText.slice(1));
        } else {
          this.selectComSentencesList =
            selectList.length > 200 ? selectList.slice(0, 200) : selectList;
        }
      }
      this.showInputSelect = true;
      this.getInputLocation();
      return true;
    },
    getCommWordsFromLocal() {
      if (
        !this.instance ||
        !this.instance.localTest.useNew ||
        this.editor.config.source === "design"
      ) {
        return;
      }
      if (Array.isArray(this.commWordsData) && this.commWordsData.length) {
        return this.commWordsData;
      }
      if (this.instance?.editor?.user) {
        this.userId = this.instance.editor.user.id;
      }
      this.commWordsData = JSON.parse(
        localStorage.getItem(this.userId + "_commWords")
      );
      if (!this.commWordsData) {
        this.commWordsData = [];
      }
      return this.commWordsData;
    },
    setLocalCommWords() {
      if (this.instance?.editor?.user) {
        this.userId = this.instance.editor.user.id;
      }
      if (this.commWordsData && this.commWordsData.length) {
        const limit = 10000;
        // 如果超长则删除条数
        if (this.commWordsData.length > limit) {
          this.commWordsData.splice(0, this.commWordsData.length - limit); // 删除多余的条数
        }
        localStorage.setItem(
          this.userId + "_commWords",
          JSON.stringify(this.commWordsData)
        );
      }
    },
  },
};
export default inputSelect;
