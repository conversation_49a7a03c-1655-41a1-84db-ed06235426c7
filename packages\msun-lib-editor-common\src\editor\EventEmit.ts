/**
 * 事件说明：
 * beforeContentChange: 编辑器内容修改之前调用事件
 * contentChanged: 编辑器内容修改之后事件
 * scroll: 编辑器滚动事件
 * click:编辑器点击事件
 * dblClick:编辑器双击事件
 * fieldSelectItemsRequest://文本域下拉数据源请求
 * editorBlur :编辑器失焦事件
 * editorFocus :编辑器失焦事件
 * menuClick: 右键菜单点击事件
 * beforeKeydown: 键盘事件触发之前事件
 * input: 非输入法输入事件
 * exeCommand: 执行编辑器命令
 * boxChecked: 复选框选中事件
 * message: 消息，可以是错误、警告等提示信息
  */
import Editor from "./Editor";
type eventName = "beforeContentChange" | "beforePointerDown" | "contentChanged" | "scroll" | "click" | "dblClick" | "tripleClick" | "fieldFormula"
  | "fieldSelectItemsRequest" | "pointerUp" | "pointerMove" | "editorBlur" | "editorFocus"
  | "menuClick" | "compositionStart" | "compositionEnd" | "beforeKeydown" | "exeCommand"
  | "input" | "boxChecked" | "message" | "alert" | "reInitRaw" | "addComment" | "beforePaste" | "beforeDrop" | "dragDrop" | "beforePrint" | "changeStyle" | "cusComment" | "clickComment"
  | "dcXml2RawData" | "modelData2Html" | "modelData2RawData" | "selectionData2RawData" | "clearDataTransTempInfo" | "initNewEditor" | "assemblePageJson" | "update"
  | "transReInitRaw" | "transCellInsertRaw" | "html2RawData" | "changeImageSize" | "assembleText" | "updateSide";
export default class EventEmit {
  listeners: Map<eventName, Function[]>;
  editor: Editor;

  constructor(editor: Editor) {
    this.listeners = new Map();
    this.editor = editor;
  }

  on(eventName: eventName, handler: Function) {
    if (typeof handler !== "function") {
      throw new Error("第二个参数必须是函数");
    }
    const listeners = this.listeners;
    if (listeners.has(eventName)) {
      const handlers = listeners.get(eventName)!;
      if (!(handlers as any).includes(handler)) {
        handlers.push(handler);
      }
    } else {
      listeners.set(eventName, [handler]);
    }
  }

  /**
     * 移除绑定的事件
     */
  remove(eventName: eventName) {
    this.listeners.delete(eventName);
  }

  exist(eventName: eventName) {
    const handlers = this.listeners.get(eventName);
    return !!handlers;
  }

  emit(eventName: eventName, ...args: any[]): boolean | undefined | string {
    const handlers = this.listeners.get(eventName);

    let result: any = "origin"; // 如果 result 不给赋初始值 就啥也不能干

    if (handlers?.length) {
      handlers.forEach((handler) => {
        const res = handler(...args);
        res === false && (result = false);
        if (result && res) {
          result = res;
        }
      });
    }
    return result;
  }
}
