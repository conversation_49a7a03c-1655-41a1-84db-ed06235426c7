const floatingMenuMixIn = {
  data() {
    return {
      showFloatMenu: false,
      floatingMenuCount: 1,
      initFloatMenuInfo: {
        activeIndex: -1,
        x: 0,
        y: 50,
      },
      commonMenu: {
        icon: "appstore",
        title: "常用",
        type: "menu",
        children: [
          {
            icon: "star",
            title: "清除选区空行",
            func: () => {
              this.editor.reformatParagraph([], false, false);
            },
          },
          {
            icon: "star",
            title: "清除空行+段落缩进",
            func: () => {
              this.editor.reformatParagraph([], true, false);
            },
          },
          {
            icon: "star",
            title: "清除空行+空格",
            func: () => {
              this.editor.reformatParagraph([], false, true);
            },
          },
        ],
      },
      insertMenu: {
        icon: "plus-square",
        title: "插入",
        type: "menu",
        children: [
          {
            icon: "icon-biaoge",
            keyword: "bg,biaoge",
            title: "表格",
            func: () => {
              this.showInsertTblModal();
            },
          },
          {
            icon: "icon-wenbenyu1",
            title: "文本域",
            keyword: "wby,wenbenyu",
            func: () => {
              this.insertField();
            },
          },
          {
            icon: "icon-wenbenyu1",
            title: "选择框",
            keyword: "xzk,xuanzekuang",
            func: () => {
              this.showChoiceModal();
            },
          },
          {
            icon: "icon-fengexian",
            title: "水平线",
            keyword: "spx,shuipingxian",
            func: () => {
              const lineHeight = 0.8; // 水平线线高，注意不是行高
              const lineColor = "#000"; // 水平线颜色
              this.instance.editor.insertLine(lineHeight, lineColor);
            },
          },
          {
            icon: "icon-danxuanxuanzhong",
            keyword: "dxk,danxuankuang",
            title: "单选框",
            func: () => {
              this.editor.insertSimpleWidget("radio");
            },
          },
          {
            icon: "icon-fuxuankuang",
            keyword: "fxk,fuxuankuang",
            title: "复选框",
            func: () => {
              this.editor.insertSimpleWidget("checkbox");
            },
          },
          {
            icon: "icon-charutupian",
            keyword: "tp,tupian",
            title: "图片",
            func: () => {
              this.insertLocalImage();
            },
          },
          {
            icon: "icon-fenyefu",
            keyword: "fyf,fenyefu",
            title: "分页符",
            func: () => {
              this.editor.insertPageBreak();
            },
          },
          {
            icon: "apartment",
            keyword: "fz,fenzu",
            title: "分组",
            func: () => {
              this.editor.insertGroup();
            },
          },
          {
            icon: "line",
            title: "形状编辑",
            keyword: "xzbj,xingzhuangbianji",
            func: () => {
              this.shapeMode();
            },
          },
          {
            icon: "icon-shuiyin",
            keyword: "sybj,shuiyinbianji",
            title: "水印编辑",
            func: () => {
              this.waterMarkModel();
            },
          },
        ],
      },
      tableMenu: {
        icon: "table",
        title: "表格",
        type: "menu",
        children: [
          {
            icon: "icon-biaoge",
            title: "插入表格",
            func: () => {
              this.editor.insertTable(3, 3);
            },
          },
          {
            icon: "icon-hebingdanyuange",
            title: "合并单元格",
            func: () => {
              this.editor.mergeCell();
            },
          },
          {
            icon: "icon-771bianjiqi_chaifendanyuange",
            title: "拆分单元格",
            func: () => {
              if (this.instance.localTest.useLocal) {
                this.showSplitCellModal();
              } else {
                if (this.editor.judgeIsCanSplitCell()) {
                  this.editor.splitCell();
                } else {
                  this.showSplitCellModal();
                }
              }
            },
          },
          {
            icon: "icon-insertrowabove",
            title: "上方插入行",
            func: () => {
              this.editor.addRowOrColInTbl(
                this.instance.builtInVariable.Direction.up
              );
            },
          },
          {
            icon: "icon-insertrowbelow",
            title: "下方插入行",
            func: () => {
              this.editor.addRowOrColInTbl(
                this.instance.builtInVariable.Direction.down
              );
            },
          },
          {
            icon: "icon-insertrowleft",
            title: "左侧插入列",
            func: () => {
              this.editor.addRowOrColInTbl(
                this.instance.builtInVariable.Direction.left
              );
            },
          },
          {
            icon: "icon-insertrowright",
            title: "右侧插入列",
            func: () => {
              this.editor.addRowOrColInTbl(
                this.instance.builtInVariable.Direction.right
              );
            },
          },
          {
            icon: "icon-shanchuhang1",
            title: "删除行",
            func: () => {
              this.editor.deleteRowFromTbl();
            },
          },
          {
            icon: "icon-shanchulie",
            title: "删除列",
            func: () => {
              this.editor.deleteColFromTbl();
            },
          },
        ],
      },
      tableBorderMenu: {
        icon: "border-left",
        title: "表格线",
        type: "menu",
        children: [
          {
            icon: "icon-suoyoukuangxian",
            title: "显示框线",
            func: () => {
              this.editor.cotrolTableLine("all_line");
            },
          },
          {
            icon: "icon-wuxiankuang",
            title: "隐藏框线",
            func: () => {
              this.editor.cotrolTableLine("no_line");
            },
          },
          {
            icon: "icon-shangkuangxian",
            title: "上框线",
            func: () => {
              this.editor.cotrolTableLine("top_line");
            },
          },
          {
            icon: "icon-xiakuangxian",
            title: "下框线",
            func: () => {
              this.editor.cotrolTableLine("under_line");
            },
          },
          {
            icon: "icon-zuokuangxian",
            title: "左框线",
            func: () => {
              this.editor.cotrolTableLine("left_line");
            },
          },
          {
            icon: "icon-youkuangxian",
            title: "右框线",
            func: () => {
              this.editor.cotrolTableLine("right_line");
            },
          },
          {
            icon: "icon-waicekuangxian",
            title: "外部框线",
            func: () => {
              this.editor.cotrolTableLine("out_line");
            },
          },
          {
            icon: "icon-neibukuangxian",
            title: "内部框线",
            func: () => {
              this.editor.cotrolTableLine("inside_line");
            },
          },
          {
            icon: "icon-neibuhengxiankuang",
            title: "内部横线",
            func: () => {
              this.editor.cotrolTableLine("inside_flat_line");
            },
          },
          {
            icon: "icon-neibushuxiankuang",
            title: "内部竖线",
            func: () => {
              this.editor.cotrolTableLine("inside_vertical_line");
            },
          },
          {
            icon: "icon-xieshangkuangxian",
            title: "斜上框线",
            func: () => {
              this.editor.cotrolTableLine("inside_inclined_bottom_line");
            },
          },
          {
            icon: "icon-shuangshangxiexian",
            title: "双斜上框线",
            func: () => {
              this.editor.cotrolTableLine("inside_inclined_bottom_line", 2);
            },
          },
          {
            icon: "icon-xiexiakuangxian",
            title: "斜下框线",
            func: () => {
              this.editor.cotrolTableLine("inside_inclined_top_line");
            },
          },
          {
            icon: "icon-shuangxiaxiexian",
            title: "双斜下框线",
            func: () => {
              this.editor.cotrolTableLine("inside_inclined_top_line", 2);
            },
          },
        ],
      },
      paraStyleMenu: {
        icon: "menu-unfold",
        title: "排版",
        type: "icon",
        children: [
          {
            title: "对齐方式",
            children: [
              {
                icon: "icon-ziyuan",
                title: "左对齐",
                func: () => {
                  this.editor.changeContentAlign("left");
                },
              },
              {
                icon: "icon-juzhongduiqi",
                title: "居中对齐",
                func: () => {
                  this.editor.changeContentAlign("center");
                },
              },
              {
                icon: "icon-youduiqi",
                title: "右对齐",
                func: () => {
                  this.editor.changeContentAlign("right");
                },
              },
              {
                icon: "icon-ziyuan1",
                title: "分散对齐",
                func: () => {
                  this.editor.changeContentAlign("dispersed");
                },
              },
              {
                icon: "icon-docuAlign",
                title: "字符对齐",
                func: () => {
                  this.editor.changeContentAlign("docuAlign");
                },
              },
              {
                icon: "icon-shangduiqi",
                title: "上对齐",
                func: () => {
                  this.editor.setVerticalAlign("top");
                },
              },
              {
                icon: "icon-shangxiajuzhong",
                title: "上下居中对齐",
                func: () => {
                  this.editor.setVerticalAlign("center");
                },
              },
              {
                icon: "icon-Q-xiaduiqi",
                title: "下对齐",
                func: () => {
                  this.editor.setVerticalAlign("bottom");
                },
              },
            ],
          },
          {
            title: "首行缩进",
            children: [
              {
                icon: "plus-circle",
                title: "增加",
                continuous: true,
                func: () => {
                  this.editor.firstRowIndentation(1, [], 1);
                },
              },
              {
                icon: "minus-circle",
                title: "缩小",
                continuous: true,
                func: () => {
                  this.editor.firstRowIndentation(1, [], 2);
                },
              },
              {
                icon: "reload",
                title: "重置",
                func: () => {
                  this.editor.firstRowIndentation(0);
                },
              },
            ],
          },
          {
            title: "段间距",
            children: [
              {
                icon: "plus-circle",
                title: "增加",
                continuous: true,
                func: () => {
                  this.editor.changeParaBeforSpacing(0.1, [], 1);
                },
              },
              {
                icon: "minus-circle",
                title: "缩小",
                continuous: true,
                func: () => {
                  this.editor.changeParaBeforSpacing(0.1, [], 2);
                },
              },
              {
                icon: "reload",
                title: "重置",
                func: () => {
                  this.editor.changeParaBeforSpacing(0, [], 0);
                },
              },
            ],
          },
          {
            title: "行间距",
            children: [
              {
                icon: "plus-circle",
                title: "增加",
                continuous: true,
                func: () => {
                  this.editor.changeRowRatio(0.1, [], 1);
                },
              },
              {
                icon: "minus-circle",
                title: "缩小",
                continuous: true,
                func: () => {
                  this.editor.changeRowRatio(0.1, [], 2);
                },
              },
              {
                icon: "reload",
                title: "重置",
                func: () => {
                  this.editor.changeRowRatio(
                    this.editor.config.row_ratio,
                    [],
                    0
                  );
                },
              },
            ],
          },
          {
            title: "单元格上边距",
            children: [
              {
                icon: "plus-circle",
                title: "增加",
                continuous: true,
                func: () => {
                  this.editor.setCellPadding(1, {
                    top: 1,
                  });
                },
              },
              {
                icon: "minus-circle",
                title: "缩小",
                continuous: true,
                func: () => {
                  this.editor.setCellPadding(2, {
                    top: 1,
                  });
                },
              },
              {
                icon: "reload",
                title: "重置",
                func: () => {
                  this.editor.setCellPadding(0, {
                    top: 0,
                  });
                },
              },
            ],
          },
          {
            title: "单元格左边距",
            children: [
              {
                icon: "plus-circle",
                title: "增加",
                continuous: true,
                func: () => {
                  this.editor.setCellPadding(1, {
                    left: 1,
                  });
                },
              },
              {
                icon: "minus-circle",
                title: "缩小",
                continuous: true,
                func: () => {
                  this.editor.setCellPadding(2, {
                    left: 1,
                  });
                },
              },
              {
                icon: "reload",
                title: "重置",
                func: () => {
                  this.editor.setCellPadding(0, {
                    left: 5,
                  });
                },
              },
            ],
          },
          {
            title: "单元格右边距",
            children: [
              {
                icon: "plus-circle",
                title: "增加",
                continuous: true,
                func: () => {
                  this.editor.setCellPadding(1, {
                    right: 1,
                  });
                },
              },
              {
                icon: "minus-circle",
                title: "缩小",
                continuous: true,
                func: () => {
                  this.editor.setCellPadding(2, {
                    right: 1,
                  });
                },
              },
              {
                icon: "reload",
                title: "重置",
                func: () => {
                  this.editor.setCellPadding(0, {
                    right: 5,
                  });
                },
              },
            ],
          },
          {
            title: "文本域最小宽度",
            children: [
              {
                icon: "plus-circle",
                title: "增加",
                continuous: true,
                func: () => {
                  const field = this.editor.focusElement.field;
                  if (field) {
                    const curWidth =
                      field.min_width < 100 ? 100 : field.min_width;
                    this.editor.reviseFieldAttr({ min_width: curWidth + 5 });
                  }
                },
              },
              {
                icon: "minus-circle",
                title: "缩小",
                continuous: true,
                func: () => {
                  const field = this.editor.focusElement.field;
                  if (field) {
                    const curWidth = field.min_width;
                    this.editor.reviseFieldAttr({
                      min_width: curWidth - 5 > 0 ? curWidth - 5 : 0,
                    });
                  }
                },
              },
              {
                icon: "reload",
                title: "重置",
                func: () => {
                  this.editor.reviseFieldAttr({
                    min_width: 0,
                  });
                },
              },
            ],
          },
        ],
      },
      fontStyleMenu: {
        icon: "font-size",
        title: "字体",
        type: "icon",
        children: [
          {
            title: "字形",
            children: [
              {
                icon: "bold",
                title: "加粗",
                func: () => {
                  const bold = this.editor.contextState.font.bold;
                  this.editor.change_font_style({ bold: !bold });
                },
              },
              {
                icon: "italic",
                title: "斜体",
                func: () => {
                  const italic = this.editor.contextState.font.italic;
                  this.editor.change_font_style({ italic: !italic });
                },
              },
              {
                icon: "reload",
                title: "重置",
                func: () => {
                  this.editor.change_font_style({
                    italic: false,
                    bold: false,
                  });
                },
              },
            ],
          },
          {
            title: "字体颜色",
            children: [
              {
                icon: "font-colors",
                title: "红色",
                color: "red",
                func: () => {
                  this.editor.change_font_style({ color: "red" });
                },
              },
              {
                icon: "font-colors",
                title: "蓝色",
                color: "blue",
                func: () => {
                  this.editor.change_font_style({ color: "blue" });
                },
              },
              {
                icon: "reload",
                title: "重置",
                func: () => {
                  this.editor.change_font_style({ color: "#000" });
                },
              },
            ],
          },
          {
            title: "背景颜色",
            children: [
              {
                icon: "bg-colors",
                title: "红色",
                color: "red",
                func: () => {
                  this.editor.change_font_style({ bgColor: "red" });
                },
              },
              {
                icon: "bg-colors",
                title: "蓝色",
                color: "yellow",
                func: () => {
                  this.editor.change_font_style({ bgColor: "yellow" });
                },
              },
              {
                icon: "reload",
                title: "重置",
                func: () => {
                  this.editor.change_font_style({ bgColor: null });
                },
              },
            ],
          },
          {
            title: "效果",
            children: [
              {
                icon: "strikethrough",
                title: "删除线",
                func: () => {
                  const strikethrough =
                    this.editor.contextState.font.strikethrough;
                  this.editor.change_font_style({
                    strikethrough: !strikethrough,
                  });
                },
              },
              {
                icon: "underline",
                title: "下划线",
                func: () => {
                  const underline = this.editor.contextState.font.underline;
                  this.editor.change_font_style({ underline: !underline });
                },
              },
              {
                icon: "icon-dblUnderLine",
                title: "双下划线",
                func: () => {
                  const dblUnderLine =
                    this.editor.contextState.font.dblUnderLine;
                  this.editor.change_font_style({
                    dblUnderLine: !dblUnderLine,
                  });
                },
              },
              {
                icon: "icon-shangbiao",
                title: "上标",
                func: () => {
                  const script = this.editor.contextState.font.script;
                  this.editor.change_font_style({
                    script: script !== 1 ? 1 : 3,
                  });
                },
              },
              {
                icon: "icon-xiabiao",
                title: "下标",
                func: () => {
                  const script = this.editor.contextState.font.script;
                  this.editor.change_font_style({
                    script: script !== 2 ? 2 : 3,
                  });
                },
              },
            ],
          },
          {
            title: "字号",
            children: [
              {
                icon: "icon-font_size_up",
                title: "增加",
                continuous: true,
                func: () => {
                  this.editor.setSelectionCharacterSize("bigger");
                  this.editor.refreshDocument();
                },
              },
              {
                icon: "icon-font_size_down",
                title: "缩小",
                continuous: true,
                func: () => {
                  this.editor.setSelectionCharacterSize("smaller");
                  this.editor.refreshDocument();
                },
              },
              {
                icon: "reload",
                title: "重置",
                func: () => {
                  this.editor.changeFontStyleBySelection({
                    height: this.editor.config.default_font_style.height,
                  });
                  this.editor.refreshDocument();
                },
              },
            ],
          },
          {
            title: "字符间距",
            children: [
              {
                icon: "plus-circle",
                title: "增加",
                continuous: true,
                func: () => {
                  const selection = this.editor.selection;
                  const all_chars = selection.selected_fields_chars.all_chars;
                  const font = all_chars[0].font;
                  let spacing;
                  if (font.characterSpacing) {
                    spacing = font.characterSpacing;
                  } else {
                    spacing = 0;
                  }
                  spacing += 1;
                  this.editor.change_font_style({ characterSpacing: spacing });
                },
              },
              {
                icon: "minus-circle",
                title: "缩小",
                continuous: true,
                func: () => {
                  const selection = this.editor.selection;
                  const all_chars = selection.selected_fields_chars.all_chars;
                  const font = all_chars[0].font;
                  let spacing;
                  if (font) {
                    spacing = font.characterSpacing;
                  } else {
                    spacing = 0;
                  }
                  spacing -= 1;
                  spacing = spacing >= 0 ? spacing : 0;
                  this.editor.change_font_style({ characterSpacing: spacing });
                },
              },
              {
                icon: "reload",
                title: "重置",
                func: () => {
                  this.editor.change_font_style({ characterSpacing: 0 });
                },
              },
            ],
          },
          {
            title: "清除样式",
            children: [
              {
                icon: "reload",
                title: "重置",
                func: () => {
                  this.instance.editor.deleteStyle();
                },
              },
            ],
          },
        ],
      },
      controlMenu: {
        icon: "control",
        title: "其他",
        type: "menu",
        children: [
          {
            icon: "pic-center",
            title: "页眉横线",
            func: () => {
              // 当前页眉水平线显示状态
              const headerLine = this.editor.config.show_header_line;
              // 当前页脚水平线显示状态
              const footerLine = this.editor.show_footer_line;
              // 获取到当前光标所在分组
              this.editor.headerFooterHorizontal(!headerLine, footerLine);
            },
          },
          {
            icon: "pic-center",
            title: "页脚横线",
            func: () => {
              // 当前页眉水平线显示状态
              const headerLine = this.editor.config.show_header_line;
              // 当前页脚水平线显示状态
              const footerLine = this.editor.config.show_footer_line;
              // 获取到当前光标所在分组
              this.editor.headerFooterHorizontal(headerLine, !footerLine);
            },
          },
          {
            icon: "icon-yemianfangxiang",
            title: "纸张方向",
            func: () => {
              const pageDirection =
                this.editor.config.page_direction === "vertical"
                  ? "horizontal"
                  : "vertical";
              this.editor.changePageDirection(pageDirection);
            },
          },
          {
            icon: "icon-icon-svg-qingchuyangshi",
            title: "清空正文",
            func: () => {
              this.editor.clearDocument();
            },
          },
          {
            icon: "icon-icon-svg-qingchuyangshi",
            title: "保存1",
            func: () => {
              this.userLogin({ id: "111", name: "李白" });
              const { success } = this.saveTraceInfo();
              if (success) {
                const raw = this.editor.getRawData();
                localStorage.setItem("saveTrace1", JSON.stringify(raw));
                this.$editor.info("保存成功！saveTrace1");
              }
            },
          },
          {
            icon: "icon-icon-svg-qingchuyangshi",
            title: "保存2",
            func: () => {
              this.userLogin({ id: "222", name: "杜甫" });
              const { success } = this.saveTraceInfo();
              if (success) {
                const raw = this.editor.getRawData();
                localStorage.setItem("saveTrace2", JSON.stringify(raw));
                this.$editor.info("保存成功！saveTrace2");
              }
            },
          },
          {
            icon: "icon-icon-svg-qingchuyangshi",
            title: "保存3",
            func: () => {
              this.userLogin({ id: "333", name: "王维" });
              const { success } = this.saveTraceInfo();
              if (success) {
                const raw = this.editor.getRawData();
                localStorage.setItem("saveTrace3", JSON.stringify(raw));
                this.$editor.info("保存成功！saveTrace3");
              }
            },
          },
          {
            icon: "icon-icon-svg-qingchuyangshi",
            title: "保存4",
            func: () => {
              this.userLogin({ id: "444", name: "辛弃疾" });
              const { success } = this.saveTraceInfo();
              if (success) {
                const raw = this.editor.getRawData();
                localStorage.setItem("saveTrace4", JSON.stringify(raw));
                this.$editor.info("保存成功！saveTrace4");
              }
            },
          },
          {
            icon: "icon-icon-svg-qingchuyangshi",
            title: "保存5",
            func: () => {
              this.userLogin({ id: "555", name: "李清照" });
              const { success } = this.saveTraceInfo();
              if (success) {
                const raw = this.editor.getRawData();
                localStorage.setItem("saveTrace5", JSON.stringify(raw));
                this.$editor.info("保存成功！saveTrace5");
              }
            },
          },
          {
            icon: "icon-icon-svg-qingchuyangshi",
            title: "对比1,2",
            func: () => {
              const raw1 = localStorage.getItem("saveTrace1");
              const raw2 = localStorage.getItem("saveTrace2");
              // const raw = this.newVersionCompareTraces(raw1, raw2, true);
              const raw = this.compareGroupTraces({
                rawData1: raw1,
                rawData2: raw2,
                showPersonInfo: true,
                groupId: "subDoc-0e1c0924",
                useNewVersion: false,
              });
              this.editor.reInitRaw(raw);
              this.instance.editor.view_mode = "person";
              this.editor.refreshDocument();
            },
          },
          {
            icon: "icon-icon-svg-qingchuyangshi",
            title: "对比2,3",
            func: () => {
              const raw1 = localStorage.getItem("saveTrace2");
              const raw2 = localStorage.getItem("saveTrace3");
              const raw = this.newVersionCompareTraces(raw1, raw2, true);
              this.editor.reInitRaw(raw);
              this.instance.editor.view_mode = "person";
              this.editor.refreshDocument();
            },
          },
          {
            icon: "icon-icon-svg-qingchuyangshi",
            title: "对比3,4",
            func: () => {
              const raw1 = localStorage.getItem("saveTrace3");
              const raw2 = localStorage.getItem("saveTrace4");
              const raw = this.newVersionCompareTraces(raw1, raw2, true);
              this.editor.reInitRaw(raw);
              this.instance.editor.view_mode = "person";
              this.editor.refreshDocument();
            },
          },
          {
            icon: "icon-icon-svg-qingchuyangshi",
            title: "对比4,5",
            func: () => {
              const raw1 = localStorage.getItem("saveTrace4");
              const raw2 = localStorage.getItem("saveTrace5");
              const raw = this.newVersionCompareTraces(raw1, raw2, true);
              this.editor.reInitRaw(raw);
              this.instance.editor.view_mode = "person";
              this.editor.refreshDocument();
            },
          },
          {
            icon: "icon-icon-svg-qingchuyangshi",
            title: "对比1,5",
            func: () => {
              // 这几个痕迹的保存人依次是：李白 杜甫 王维 辛弃疾 李清照
              const raw1 = localStorage.getItem("saveTrace1");
              const raw2 = localStorage.getItem("saveTrace5");
              let raw = this.newVersionCompareTraces(raw1, raw2, true);
              // let raw = this.compareTraces(raw1, raw2, true);
              this.editor.reInitRaw(raw);
              this.instance.editor.view_mode = "person";
              this.editor.refreshDocument();
            },
          },
        ],
      },
      helperDesc: {
        icon: "question-circle",
        title: "帮助",
        type: "desc",
        children: [
          {
            // icon: "mail",
            title: "快捷键",
            children: [
              {
                title: "* Tab键    快速追加一行（光标在表格最后一个单元格时）",
              },
              {
                title: "* ctrl+shift+v    纯文本粘贴",
              },
              {
                title:
                  "* ctrl+拖拽编辑器中内容    拖拽至新位置后不会删除原来的内容",
              },
              {
                title: "* ctrl+Delete    快捷删除表格行",
              },
              {
                title: "* shift+Delete    快捷删除表格列",
              },
              {
                title: "* alt+双击文本域    快捷打开文本域属性弹窗",
              },
              {
                title:
                  "* 编辑器中输入//    快捷弹出插入菜单，后继续输入可检索菜单",
              },
              {
                title: "* ctrl+拖拽悬浮球    悬浮球分身",
              },
            ],
          },
          {
            // icon: "mail",
            title: "特殊功能",
            children: [
              {
                title: "* 医学公式及公式计算功能",
              },
              {
                title: "* 文本域级联显隐功能",
              },
              {
                title: "* 文本域自动计算",
              },
              {
                title: "* 插入图形、水印",
              },
              {
                title: "* 内容压缩",
              },
            ],
          },
        ],
      },
      floatMenuList: [],
    };
  },
  mounted() {
    if (this.upConfig?.showWriteFloatingMenu) {
      this.floatMenuList = [
        this.commonMenu,
        this.paraStyleMenu,
        this.fontStyleMenu,
        this.helperDesc,
      ];
    } else {
      this.floatMenuList = [
        this.commonMenu,
        this.insertMenu,
        this.tableMenu,
        this.tableBorderMenu,
        this.paraStyleMenu,
        this.fontStyleMenu,
        this.controlMenu,
        this.helperDesc,
      ];
    }
    setTimeout(() => {
      if (
        this.upConfig?.showDesignFloatingMenu ||
        this.upConfig?.showWriteFloatingMenu ||
        Number(this.getOtherConfig("floatMenu")) === 1 ||
        (this.instance && this.instance.localTest.useLocal)
      ) {
        if (this.instance && this.instance.editor.readonly) {
          return;
        }
        this.initFloatMenu();
      }
    }, 500);
  },
  methods: {
    initFloatMenu() {
      if (!this.instance) return;
      const { editor } = this.instance;
      this.showFloatMenu = true;

      const _middleX = editor.page_left + editor.page_size.width / 2;
      const _x = editor.page_left + editor.page_size.width + 5;
      const _y =
        editor.internal.client_top + editor.pages[0].header.header_bottom;
      const { x: view_x, y: view_y } =
        this.editor.getAbsolutePositionByViewPosition(_x, _y);
      const { x: middleX } = this.editor.getAbsolutePositionByViewPosition(
        _middleX,
        0
      );
      if (this.$refs.content) {
        const domRect = this.$refs.content.getBoundingClientRect();
        const left = view_x + domRect.left;
        const top = view_y;
        const middleLeft = middleX + domRect.left;
        this.initFloatMenuInfo.x = left;
        this.initFloatMenuInfo.y = top + 60;
        this.initFloatMenuInfo.middleX = middleLeft;
      }
    },
    floatMenuStartDrag(event, info) {
      if (event.ctrlKey) {
        this.initFloatMenuInfo = info;
        this.floatingMenuCount++;
      }
    },
    exeFloatMenuCommand(item, cItem, ccItem) {
      if (ccItem) {
        ccItem.func();
      } else if (cItem) {
        cItem.func();
      } else {
        item.func();
      }
      this.editor.focus();
    },
  },
};
export default floatingMenuMixIn;
