<template>
  <modal
    class="table-modal"
    :title="title"
    :show="show"
    :width="width"
    :freePoint="true"
    :sessionMove="true"
    pointer-events:none
    @cancel="cancel"
  >
    <span class="editor-example-mention"
      ><a-popover :title="popTitle" :overlayStyle="{ zIndex: 999999 }">
        <template slot="content">
          <div style="width: 760px">
            <div class="textTitle">表头说明：</div>
            <div>
              &nbsp;&nbsp;&nbsp;&nbsp;(1)“全局”“首项”，表示匹配的范围。<br />
              &nbsp;&nbsp;&nbsp;&nbsp;(2)匹配包括“数字”“字母”“自定义”，其中“数字”和“字母”为预设，自定义支持输入正则。<br />
              &nbsp;&nbsp;&nbsp;&nbsp;(3)操作表示的如何改变匹配对象，这里需要特殊说明的是输入框中已经存在的“*”，该符号表示匹配到对象。<br />
              &nbsp;&nbsp;&nbsp;&nbsp;<span
                style="color: blue; font-weight: bold"
                >以“1.你好2.你好3.你好”为例：<br />&nbsp;&nbsp;&nbsp;&nbsp;（1）“全局+数字+<span
                  style="color: red; font-weight: bold"
                  >“a”</span
                >”，原字符串变为“<span style="color: red">a</span>.你好<span
                  style="color: red"
                  >a</span
                >.你好<span style="color: red">a</span
                >.你好”<br />&nbsp;&nbsp;&nbsp;&nbsp;（2）"首项+数字+<span
                  style="color: red; font-weight: bold"
                  >“a”</span
                >",原字符串变为“<span style="color: red">a</span
                >.你好2.你好3.你好”&nbsp;&nbsp;&nbsp;&nbsp;<br />&nbsp;&nbsp;&nbsp;&nbsp;（3）“全局+数字+<span
                  style="color: red"
                  >“a*”</span
                >，原字符串变为“<span style="color: red">a1</span>.你好<span
                  style="color: red"
                  >a2</span
                >.你好<span style="color: red">a3</span
                >.你好”<br />&nbsp;&nbsp;&nbsp;&nbsp;以“在2000-10-01”为例，展示<span
                  style="color: red"
                  >自定义输入正则</span
                >
                ： <br />&nbsp;&nbsp;&nbsp;&nbsp;“全局+<span style="color: red"
                  >“^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$”</span
                >+“某一天””，原字符串变为“在某一天”</span
              >
            </div>
            <div class="textTitle">示例说明：</div>
            <div>
              &nbsp;&nbsp;&nbsp;&nbsp;如果想检测现在的规则是否正确，可以通过在“示例”中，输入一个简单的字符串，根据匹配规则会将该字符串处理后的格式显示到后边文本框中。
            </div>
            <div class="textTitle">注意：</div>
            <div style="color: red">
              &nbsp;&nbsp;&nbsp;&nbsp;如果想或者操作换行符，可以用“\n”表示。还是以“1.你好2.你好3.你好”为例，如果想实现以<br />
              &nbsp;&nbsp;&nbsp;&nbsp;1.你好<br />
              &nbsp;&nbsp;&nbsp;&nbsp;2.你好<br />
              &nbsp;&nbsp;&nbsp;&nbsp;3.你好<br />
              &nbsp;&nbsp;&nbsp;&nbsp;的方式显示，首先选择“全局+数字+“\n*””。<br />
              &nbsp;&nbsp;&nbsp;&nbsp;如果需要第一行不换行，后边的换行，添加第二条规则，选择“首项+自定义（输入“\n”）+“””既可实现。
            </div>
          </div>
        </template>
        <div
          style="margin-bottom: 5px; margin-left: 10px; width: 20px"
          class="edit-btn"
        >
          <icon-common icon="icon-jieshi" style="cursor: pointer"></icon-common>
        </div> </a-popover
    ></span>
    <div class="editor-ruler-editor">
      <a-table
        :columns="fieldAutoColumns"
        bordered
        :dataSource="dataSource"
        :pagination="false"
        :scroll="{ y: 200 }"
      >
        <template slot="range" slot-scope="text, record">
          <a-select
            class="prop-select rulerEditorSelect"
            v-model="record.matchRange"
            dropdownClassName="xeditor-input-up"
            :style="{ width: '100%' }"
          >
            <a-select-option :value="0">全局</a-select-option>
            <a-select-option :value="1">首项</a-select-option>
          </a-select>
        </template>
        <template slot="match" slot-scope="text, record">
          <div
            v-if="record.match === 'custom'"
            style="display: flex; align-items: center"
          >
            <a-input
              v-model="record.matchValue"
              placeholder="匹配对象"
              class="rulerRange"
              :style="{ width: '60%' }"
            ></a-input>
            <a-select
              class="prop-select rulerEditorSelect"
              v-model="record.match"
              dropdownClassName="xeditor-input-up"
              :style="{ width: '40%' }"
            >
              <a-select-option :value="'number'">数字</a-select-option>
              <a-select-option :value="'letter'">字母</a-select-option>
              <a-select-option :value="'custom'">自定义</a-select-option>
            </a-select>
          </div>
          <div v-else>
            <a-select
              class="prop-select rulerEditorSelect"
              v-model="record.match"
              dropdownClassName="xeditor-input-up"
              :style="{ width: '100%' }"
            >
              <a-select-option :value="'number'">数字</a-select-option>
              <a-select-option :value="'letter'">字母</a-select-option>
              <a-select-option :value="'custom'">自定义</a-select-option>
            </a-select>
          </div>
        </template>
        <template slot="operation" slot-scope="text, record">
          <div style="display: flex; align-items: center">
            <a-input
              style="width: 50%"
              placeholder="*(匹配对象)"
              disabled
            ></a-input>
            <span style="width: 20%; text-align: center">=></span>
            <a-input
              style="width: 40%"
              placeholder="替换为"
              v-model="record.handledValue"
            ></a-input>
          </div>
        </template>
        <template slot="delete">
          <a
            href="#"
            @click="handleDelete"
            style="display: flex; justify-content: center"
            ><span>删除</span></a
          >
        </template>
      </a-table>
    </div>

    <div
      style="
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
      "
    >
      <div
        class="editor-given-example"
        style="width: 45%; display: flex; align-items: center; margin-right: 5%"
      >
        <span style="width: 30%">示例：</span>
        <a-input
          v-model="beforeHandledExample"
          style="margin-left: 10px"
        ></a-input>
      </div>
      <div
        class="editor-handled-example"
        style="width: 45%; display: flex; align-items: center"
      >
        <span style="width: 38%">处理后：</span>
        <a-input
          v-model="formattedText"
          type="textarea"
          :autoSize="{ minRows: 1, maxRows: 6 }"
          disabled
          style="margin-left: 10px"
        ></a-input>
      </div>
    </div>
    <div slot="editor-modal-footer" class="footer" style="padding-top: 0px">
      <div>
        <a-button type="default" @click="add">添加</a-button>
      </div>
      <div>
        <a-button type="default" @click="cancel">取消</a-button>
        <a-button type="primary" @click="submit">确定</a-button>
      </div>
    </div>
  </modal>
</template>

<script>
import modal from "./common/modal.vue";
import iconCommon from "./common/iconCommon.vue";

export default {
  name: "rulerEditor",
  components: { modal, iconCommon },
  data() {
    return {
      regArr: [],
      matchRange: 0,
      title: "规则编辑器",
      width: 600,
      popTitle: "展示规则说明",
      beforeHandledExample: "",
      afterHandledExample: "",
      fieldAutoColumns: [
        {
          title: "范围",
          dataIndex: "range",
          width: "12%",
          align: "center",
          scopedSlots: { customRender: "range" },
        },
        {
          title: "匹配",
          dataIndex: "match",
          width: "38%",
          align: "center",
          scopedSlots: { customRender: "match" },
        },
        {
          title: "操作",
          dataIndex: "operation",
          width: "40%",
          align: "center",
          scopedSlots: { customRender: "operation" },
        },
        {
          title: "删除",
          dataIndex: "delete",
          width: "10%",
          align: "center",
          scopedSlots: { customRender: "delete" },
        },
      ],
      dataSource: [
        {
          key: "1",
          matchRange: 0,
          match: "custom",
          matchValue: "",
          range: "",
          operation: "",
          delete: "",
          handledValue: "*",
        },
      ],
    };
  },

  props: {
    show: {
      type: Boolean,
      default: false,
    },
    field: {
      type: Object,
      default: () => {},
    },
    replaceRule: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    formattedText() {
      return this.afterHandledExample.replace(/\\n/g, "\n");
    },
  },
  watch: {
    show(val) {
      if (val) {
        if (this.replaceRule.length) {
          this.dataSource = [];
          const ruleList = this.replaceRule;
          ruleList.forEach((rule, index) => {
            let match;
            if (rule.rule === "\\d+") {
              match = "number";
            } else if (rule.rule === "[a-zA-Z]") {
              match = "letter";
            } else {
              match = "custom";
            }
            const newData = {
              key: index,
              matchRange: rule.flags === "g" ? 0 : 1,
              match: match,
              matchValue: rule.custom === "custom" ? rule.rule : "",
              range: "",
              operation: "",
              delete: "",
              handledValue: rule.replace,
            };
            this.dataSource.push(newData);
          });
        } else {
          this.dataSource = [
            {
              key: "1",
              matchRange: 0,
              match: "custom",
              matchValue: "",
              range: "",
              operation: "",
              delete: "",
              handledValue: "*",
            },
          ];
        }
      }
    },
    beforeHandledExample(val) {
      if (val) {
        let processedText = val;
        let infoArr = [];

        this.dataSource.forEach((rule) => {
          let curInfo = {};
          let reg;
          let value;
          if (rule.match === "number" && rule.handledValue !== "*") {
            reg = new RegExp("\\d+", rule.matchRange === 0 ? "g" : undefined);
            value = rule.handledValue;
          } else if (rule.match === "letter" && rule.handledValue !== "*") {
            reg = new RegExp(
              "[a-zA-Z]",
              rule.matchRange === 0 ? "g" : undefined
            );
            value = rule.handledValue;
          } else if (
            rule.match === "custom" &&
            rule.matchValue &&
            rule.handledValue !== "*"
          ) {
            reg = new RegExp(
              rule.matchValue,
              rule.matchRange === 0 ? "g" : undefined
            );
            value = rule.handledValue;
            curInfo.custom = "custom";
          }
          if (reg) {
            curInfo.reg = reg;
            curInfo.value = value.replace(/\*/g, "$$&");
            infoArr.push(curInfo);
          }
        });

        if (infoArr.length) {
          infoArr.forEach((info) => {
            if (info.custom && this.beforeHandledExample.includes("/\n")) {
              processedText = processedText.replace(
                info.reg.source,
                info.value
              );
            } else {
              processedText = processedText.replace(info.reg, info.value);
            }
          });
          this.afterHandledExample = processedText;
        }
      } else {
        this.afterHandledExample = "";
      }
    },

    immediate: true,
    deep: true,
  },
  methods: {
    cancel() {
      this.$emit("cancel");
    },
    submit() {
      const dataList = this.dataSource;
      const reg = [];
      for (let i = 0; i < dataList.length; i++) {
        const dataInfo = dataList[i];
        const curReg = {};
        const range = dataInfo.matchRange;
        const value = dataInfo.matchValue;
        const match = dataInfo.match;
        let handledValue = dataInfo.handledValue;

        if (match === "number" && handledValue !== "*") {
          if (!range) {
            curReg.rule = "\\d+";
            curReg.flags = "g";
          } else {
            curReg.rule = "\\d+";
          }
          curReg.replace = handledValue;
          reg.push(curReg);
        } else if (match === "letter" && handledValue !== "*") {
          if (!range) {
            curReg.rule = "[a-zA-Z]";
            curReg.flags = "g";
          } else {
            curReg.rule = "[a-zA-Z]";
          }
          curReg.replace = handledValue;
          reg.push(curReg);
        } else if (match === "custom" && handledValue !== "*") {
          curReg.rule = value;
          if (!range) {
            curReg.flags = "g";
          }
          curReg.replace = handledValue ?? "";
          curReg.custom = "custom";
          reg.push(curReg);
        }
      }

      this.$emit("submit", reg);
    },
    handleDelete() {
      this.dataSource.pop();
    },
    add() {
      const new_source_list = {
        key: this.editor.utils.getUUID(),
        matchRange: 0,
        match: "custom",
        matchValue: "",
        range: "",
        operation: "",
        delete: "",
        handledValue: "*",
      };

      this.dataSource.push(new_source_list);
    },
  },
};
</script>

<style>
.select-input .ant-select-dropdown-menu-item {
  padding: 2px 12px;
}
</style>

<style scoped>
.table-modal /deep/ .ant-input-sm {
  height: 30px;
}

.rulerRange {
  width: 100%;
}

.table-modal /deep/ .ant-table-tbody > tr > td {
  padding: 0px;
}

.table-modal /deep/ .ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  padding: 6px;
}

.editor-given-example {
  margin-top: 10px;
}

.editor-handled-example {
  margin-top: 10px;
}

.rulerEditorSelect {
  margin: auto;
}
.textTitle {
  color: black;
  font-weight: 700;
}

.footer {
  display: flex;
  justify-content: space-between;
  padding-top: 35px;
}
.editor-ruler-editor {
  margin-bottom: 10px;
}
.editor-example-mention {
  font-size: 10px;
  position: absolute;
  top: 10px;
  left: 78px;
}
</style>
