/** 装饰器定义文件  **/

import {
  addUndoStack, clearRedoStack, clearStack,
  commands,
  popUndoStack,
  restorePreviousCommand
} from "./Command";
import Editor from "./Editor";
import PathUtils from "./Path";
import Paragraph from "./Paragraph";
import { isParagraph, isRow, isTable, versionDiff } from "./Utils";
import EditorHelper from "./EditorHelper";
import EditorLocalTest from "../../localtest";
/**
 * 性能监测
 */
export function monitorPerformance() {
  return function (_: any, methodName: string, desc: PropertyDescriptor) {
    const oldFunction = desc.value; // 获取方法引用
    const newFunction = function (...args: any[]) {
      this.monitor.start(methodName);
      // this为Editor对象
      const res = oldFunction.call(this, ...args);
      this.monitor.end(methodName);
      return res;
    };
    desc.value = newFunction; // 替换原声明
  };
}

let isExeNestedFunction = false; // 是否执行的是嵌套装饰器函数
let isExeNestedFunction2 = false; // 是否执行的是嵌套装饰器函数
/**
 * 用于记录历史堆栈的装饰器   该方法生效必须要所修饰方法有返回值true
 * @param command 命令名称
 */
export function undoStackRecord(command: commands) {
  return function (_: any, methodName: string, desc: PropertyDescriptor) {
    const oldFunction = desc.value; // 获取方法引用
    const newFunction = function (...args: any[]) {
      try {
        let result = true;
        if (command !== commands.watermark && command !== commands.handle_shapes) {
          result = this.permitOperationValidation();
        }
        if (this.config.source !== "server" && (!beforeContentChange(this, methodName) || !result)) {
          return;
        }

        if (!isExeNestedFunction) {
          isExeNestedFunction = true;
        } else {
          return oldFunction.call(this, ...args);
        }

        // 如果允许记录最大条数小于1则不进行记录
        if (this.config.history_limit < 1) {
          const res = oldFunction.call(this, ...args);
          if (res) {
            contentChanged(this, methodName);
          }
          isExeNestedFunction = false;
          return res;
        }

        let new_raw = null;
        // TODO 有效率问题 待优化
        if (methodName === "insertText") {
          if (args[1] === "input") {
            new_raw = addUndoStack(commands.input, this);
          } else if (args[1] === "ime_input") {
            new_raw = addUndoStack(commands.ime_input, this);
          } else if (args[1] === "init") {
          // 当传入init时不计入撤销堆栈中
          } else {
            new_raw = addUndoStack(commands.insert, this);
          }
        } else {
          new_raw = addUndoStack(command, this);
        }
        // this为Editor对象
        const res = oldFunction.call(this, ...args);
        // 此处需装饰方法增加返回值后调用
        if (res) {
          clearRedoStack(this);
          contentChanged(this, methodName);
        } else {
          const undo_stack = this.history.getUndo();
          if (new_raw === undo_stack[undo_stack.length - 1]) {
            popUndoStack(this);
          }
        }
        isExeNestedFunction = false;
        return res;
      } catch (error:any) {
        isExeNestedFunction = false;
        this.event.emit("message", { type:"error",msg:error.message });
        console.error("编辑器内部报错", error)
        if(EditorLocalTest.useLocal){
          throw new Error(error)
        }
      }
    };
    desc.value = newFunction; // 替换原声明
  };
}

// 修正光标
export function correctCaret() {
  return function (_: any, methodName: string, desc: PropertyDescriptor) {
    const oldFunction = desc.value; // 获取方法引用
    const newFunction = function (...args: any[]) {
      const anchor = this.selection.anchor;
      const focus = this.selection.focus;
      // 如果是选区的时候就要判断这 anchor 和 focus 是否都有效
      if ((!PathUtils.modelPathIsValid(this, anchor) || !PathUtils.modelPathIsValid(this, focus))) {
        while (anchor[0] >= 0) {
          const rowOrTable = this.current_cell.children[anchor[0]];
          if (isTable(rowOrTable)) {
            this.selection.clearSelectedInfo();
            this.selection.setCursorPosition([anchor[0], 0, 0, 0]);
            break;
          } else if (isRow(rowOrTable)) {
            this.selection.clearSelectedInfo();
            this.selection.setCursorPosition([anchor[0], 0]);
            break;
          } else {
            anchor[0] -= 1;
          }
        }
      }

      const res = oldFunction.call(this, ...args);
      return res;
    };
    desc.value = newFunction; // 替换原声明
  };
}

/**
 * 编辑器内容修改事件之前
 */
function beforeContentChange(editor: Editor, methodName: string) {
  if (!isExeNestedFunction) {
    if (methodName !== "insertText" &&
      methodName !== "setSelectImageOrWidget" &&
      methodName !== "updateFieldText" &&
      methodName !== "reviseFieldAttr" &&
      methodName !== "insertField") {
      const result = EditorHelper.checkFormula(editor);
      if (!result) return;
    }

    if (editor.event.exist("beforeContentChange")) {
      return editor.event.emit("beforeContentChange");
    }
  }

  return true;
}

/**
 * 编辑器内容修改事件发生之后
 */
export function contentChanged(editor: Editor, methodName: string) {
  editor.is_modified = true;
  editor.monitor.start("contentChanged");
  // 此处捕获异常，防止出现报错后不再触发内容改变事件
  if (editor.is_comment_mode) {
    const current_group = editor.selection.getFocusGroup();
    editor.internal.handleCommentIDSet(current_group || "document");
  }
  if (editor.isCusCommentMode) {
    const current_group = editor.selection.getFocusGroup();
    editor.internal.handleCusCommentIDSet(current_group || "document");
  }

  for (let i = 0; i < editor.current_cell.paragraph.length; i++) {
    const currentParagraph = editor.current_cell.paragraph[i];
    if (isParagraph(currentParagraph) && currentParagraph.itemsWidth[0] === "x" && currentParagraph.characters.length > 1) {
      const paragraphs = editor.findContinuousParagraphs([currentParagraph]);
      editor.formatParagraph(paragraphs);
      i = paragraphs[paragraphs.length - 1].para_index + 1;
    }
  }
  const focus_field = editor.selection.getFocusField();
  //&& methodName !== "reviseFieldAttr"先注掉，忘了干什么的了
  if (focus_field && methodName !== "updateWidget" ) {
    // box类型走boxChecked事件
    // editor.updateFieldAdvancedFunctions(focus_field, true);
    if (
      focus_field.type !== "box" &&
        focus_field.cascade_list &&
        focus_field.cascade_list.length
    ) {
      editor.showOrHideField(focus_field);
    }
    if (focus_field.type !== "box") {
      editor.updateFieldAdvancedFunctions(focus_field);
    }
  }

  for (let i = 0; i < editor.root_cell.children.length; i++) {
    const table = editor.root_cell.children[i];
    if (isTable(table)) {
      const focus = editor.selection.focus;
      for (let j = 0; j < table.children.length; j++) {
        const cell = table.children[j];
        if (cell.aggregationMode) {
          editor.handleAggregationMode(cell);
        }
      }
    }
  }

  editor.event.emit("contentChanged");
  //放在contentChanged之后让文本域自动化后执行，让文本域公式先执行，避免自动化先执行导致联动不生效问题
  editor.monitor.end("contentChanged");
}

/**
 * 光标移动后还原记录的上一次命令
 */
export function resCommand(clear?: boolean) {
  return function (_: any, methodName: string, desc: PropertyDescriptor) {
    const oldFunction = desc.value; // 获取方法引用
    const newFunction = function (...args: any[]) {
      restorePreviousCommand(this);
      // this为Editor对象
      const res = oldFunction.call(this, ...args);
      if (clear) {
        clearStack(this);
      }
      return res;
    };
    desc.value = newFunction; // 替换原声明
  };
}

/**
 * 根据para_path重置选区中锚点与焦点
 */
export function resetSelectionFocus(updateCaret?: boolean) {
  return function (_: any, methodName: string, desc: PropertyDescriptor) {
    const oldFunction = desc.value; // 获取方法引用
    const newFunction = function (...args: any[]) {
      const para_anchor = [...this.selection.para_anchor];
      const para_focus = [...this.selection.para_focus];
      // this为Editor对象
      const res = oldFunction.call(this, ...args);
      if (methodName === "refreshDocument") {
        // 加这个判断纯粹为了不走下边的逻辑节省下性能
        if (!args[0]) return;
        // 到这儿就是强制更新了 因为经过 refreshDocument 的调用之后 数据可能会发生变化,之前保存的路径未必有效了,再继续下方的转化就会报错
        if (!PathUtils.isValid(this, para_anchor, "paraPath") || !PathUtils.isValid(this, para_focus, "paraPath")) return;
      }
      this.selection.anchor = this.paraPath2ModelPath(para_anchor);
      this.selection.focus = this.paraPath2ModelPath(para_focus);
      if (PathUtils.equals(para_focus, para_anchor) && updateCaret) {
        this.updateCaret();
        this.render();
      }
      return res;
    };
    desc.value = newFunction; // 替换原声明
  };
}

// 更正光标位置
export function resetFocus() {
  return function (_: any, methodName: string, desc: PropertyDescriptor) {
    const oldFunction = desc.value; // 获取方法引用
    const newFunction = function (...args: any[]) {
      const para_anchor = [...this.selection.para_anchor];
      // this为Editor对象
      const res = oldFunction.call(this, ...args);
      this.selection.setCursorPosition(this.paraPath2ModelPath(para_anchor));
      this.update();
      this.render();
      return res;
    };
    desc.value = newFunction; // 替换原声明
  };
}

/**
 * 记录当前光标所在filed的相对位置，操作后进行恢复
 */
export function resetFieldFocus() {
  return function (_: any, methodName: string, desc: PropertyDescriptor) {
    const oldFunction = desc.value; // 获取方法引用
    const newFunction = function (...args: any[]) {
      try {
        if (!isExeNestedFunction2) {
          isExeNestedFunction2 = true;
        } else {
          return oldFunction.call(this, ...args);
        }
        const field = args[0];
        const ori_scroll_top = this.scroll_top;
        // 首先记录相对文本域的坐标，用于操作之后光标位置的恢复
        const focus_field = this.selection.getFocusField();
        let field_char_index: number = -1;
        const is_same_field = field === focus_field;
        const para_focus = [...this.selection.para_focus];
        // 判断是否是选区状态，如果是选区状态
        if (focus_field) {
          const focus_para: Paragraph = this.selection.getFocusParagraph();
          const char = focus_para.characters[para_focus[para_focus.length - 1]];
          field_char_index = focus_field.getAllElements().indexOf(char);
        }
        // this为Editor对象
        const res = oldFunction.call(this, ...args);
        if (res) {
          // 操作完成后恢复光标位置
          if (field_char_index > -1 && is_same_field) {
            // 从传入的field中找到对应的字符下标
            const res_field_char = field.getAllElements()[field_char_index];
            let start_para: Paragraph = field.start_para;
            if(!start_para){
              // 主要处理自动化隐藏自己的父文本域的情况
              isExeNestedFunction2 = false;
              return res;
            }
            const res_para_path = start_para.cell.parent ? [para_focus[0], para_focus[1]] : [];
            let is_exist = false;
            while (start_para) {
              const res_para_char_index = start_para.characters.indexOf(res_field_char);
              if (res_para_char_index > -1) {
                res_para_path.push(start_para.para_index, res_para_char_index);
                is_exist = true;
                break;
              }
              const next_para = start_para.nextParagraph;
              if (isParagraph(next_para)) {
                start_para = next_para;
              } else {
                break;
              }
            }
            if (is_exist) {
              this.selection.setCursorPosition(this.paraPath2ModelPath(res_para_path));
            } else {
              this.locatePathInField(field);
            }
          } else {
            // 如果不是同一文本域，说明可能是主动调用接口,此时将光标设置到文本域末尾
            this.locatePathInField(field);
          }
          this.updateCaret();
          this.scroll_top = ori_scroll_top;
          this.render();
        }
        isExeNestedFunction2 = false;
        return res;
      } catch (error:any) {
        isExeNestedFunction2 = false;
        this.event.emit("message", { type:"error",msg:error.message });
        console.error("编辑器报错", error)
      }
    };
    desc.value = newFunction; // 替换原声明
  };
}

// let oriRawData: any = ""; // 记录隐藏输入域边框前的原始数据内容
// /**
//  * 还原编辑器内容
//  */
// export function restoreOriRaw (config:any) {
//   return function (target: any, methodName: any, desc: any) {
//     const oldFunction = target[methodName]; // 获取方法引用
//     const newFunction = function () {
//       if (config.show_field_symbol) {
//         oriRawData = modelDataToRawData(
//           this.header_cell,
//           this.root_cell,
//           this.footer_cell
//         );
//       }
//       oldFunction.call(this, oriRawData);
//     };
//     desc.value = newFunction; // 替换原声明
//   };
// }
