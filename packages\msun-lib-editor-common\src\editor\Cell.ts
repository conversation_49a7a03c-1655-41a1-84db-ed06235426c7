/* eslint-disable no-use-before-define */
import Table, { BreakType } from "./Table";
import Row from "./Row";
import Font from "./Font";
import Renderer from "./Renderer";
import Character from "./Character";
import {
  isBoolean ,
  isField ,
  isParagraph ,
  isRow ,
  isTable ,
  keepDecimal ,
  uuid ,
  isEmptyObj , isCharacter ,
  initCell , decodeBase64 , encodeBase64
} from "./Utils";
import { Config , editor_prompt_msg } from "./Config";
import Editor from "./Editor";
import ImageElement from "./Image";
import Paragraph from "./Paragraph";
import Widget from "./Widget";
import XField from "./XField";
import Group from "./Groups";
import {
  filterData,
  filterTypr,
  forEachFlatParagraph ,
  getCellIndex ,
  getRowObj,
  isBox ,
  isBoxField ,
  isImage , isUseCurrentLogic ,
  specialCharHandle ,
} from "./Helper";
import PathUtils, { Path } from "./Path";
import XSelection from "./Selection";
import BoxField from "./BoxField";
import Line from "./line";
import ImageTable from "./ImageTable";
import Box from "./Box";
import {
  CellUpdateChildrenParameter,
  Direction,DirectionType,
  ElementInParagraph,
  IncreaseType,
  RowLineType,
} from "./Definition";
import Button from "./Button";
import { VerticalAlign } from "./Constant";
import EditorLocalTest from "../../localtest";
import Fraction from "./Fraction";

export type HeaderFooterType = "header" | "footer" | "";

export default class Cell {
  editor: Editor;

  position: number[];

  is_with_caret: true | null = null; // 记录光标是否在该单元格上的一个属性

  children: (Table | Row)[] = [];

  vertical_align: VerticalAlign = VerticalAlign.TOP;

  parent: Table | null = null;

  rowLineType: RowLineType = RowLineType.VOID;

  aggregationMode: number = 0; // 聚合模式

  is_show_slash_up: number = 0; // 是否显示从左上角开始的斜线 (斜下)

  is_show_slash_down: number = 0; // 是否显示从左下角开始的斜线 (斜上)

  hf_part: HeaderFooterType = ""; // header 页眉部分  footer 页脚部分

  cursor_position: number; // 游标位置

  cursor_index: number; // 游标偏移索引

  colspan: number = 1;

  rowspan: number = 1;

  x: number = 0; // 偏移坐标

  y: number = 0; // 偏移坐标

  padding_left: number;

  padding_top: number = 0;

  padding_right: number;

  padding_bottom: number = 0;

  page_break_line: number = 0; // 页面分割位置

  page_break: boolean = false;

  page_break_type: BreakType = BreakType.soft;

  paragraph: (Paragraph | Table)[] = [];

  groups: Group[] = [];

  groupKey: undefined | string;

  origin: Cell | null = null;

  split_parts: Cell[] = [];

  split_row_indexes: number[] = []; // 被分割的行号数组

  id: string; // content- 正文 , header- 页眉 ,footer- 页脚

  meta: any = {};

  lock: Boolean = false; // 该单元格 是否锁定

  fields: XField[] = [];

  fixedHeight: number | undefined;

  scroll_cell_top: number = 0; // 表格内单元格滚动高度

  scroll_cell_bar_top: number = 0; // 滚动条距上方高度

  isHeaderCell: boolean = false; // 只在表格拆分的时候使用 不用考虑复制粘贴 不用考虑保存数据

  noWrap: boolean = false;

  set_cell_height: {
    // 表格内固定高度的展示形式
    type: "normal" | "scroll";
    height?: number;
  } = { type: "normal" };

  shouldPushSplitParts: boolean | undefined; // 被软分割的单元格 再次进入循环 会放到 preTable 里边去 (逻辑是直接放，但是它被拆分过，跟其他的单元格不一样，所以标记上 要做处理)

  style: { bgColor: string | null } = { bgColor: null };

  get index() {
    if (!this.parent) return -1;

    return this.parent.children.indexOf(this);
  }

  get realTop() {
    let top = this.top;
    if (this.parent) {
      top =
        this.top +
        this.parent.top +
        this.editor.pages[this.parent.page_number - 1].top;
    }
    return top;
  }
  get modelPath(){
    if(this.parent) {
      return [this.parent.cell_index,this.index,0,0]
    }else {
      return null
    }
  }

  get paraPath(){
    const modelPath = this.modelPath;
    if(modelPath){
      return this.editor.modelPath2ParaPath(modelPath)
    }else{
      return null;
    }
  }


  get realLeft() {
    let left = this.left;
    if (this.parent) {
      left = this.left + this.parent.left + this.editor.page_left;
    }
    return left;
  }

  static setCellPadding(
    editor: Editor,
    setType: IncreaseType = IncreaseType.fixed,
    paddingVal: {
      left?: number;
      right?: number;
      top?: number;
      bottom?: number;
    },
    cells: Cell[] = []
  ) {
    const isCollapsed = editor.selection.isCollapsed;
    if (!cells || !cells.length) {
      if (isCollapsed) {
        const focusCell = editor.selection.getFocusCell();
        if (focusCell) cells = [focusCell];
      } else {
        cells = editor.selection.selected_cells.map((c) => c.cell);
      }
    }
    const oriParaPath = editor.selection.para_focus;
    for (let cell of cells) {
      cell = cell.getOrigin();
      if (paddingVal.left !== undefined) {
        setType === IncreaseType.increase
          ? (cell.padding_left += Number(paddingVal.left))
          : setType === IncreaseType.decrease
            ? (cell.padding_left -= Number(paddingVal.left))
            : (cell.padding_left = Number(paddingVal.left));
      }
      if (paddingVal.right !== undefined) {
        setType === IncreaseType.increase
          ? (cell.padding_right += Number(paddingVal.right))
          : setType === IncreaseType.decrease
            ? (cell.padding_right -= Number(paddingVal.right))
            : (cell.padding_right = Number(paddingVal.right));
      }
      if (paddingVal.top !== undefined) {
        setType === IncreaseType.increase
          ? (cell.padding_top += Number(paddingVal.top))
          : setType === IncreaseType.decrease
            ? (cell.padding_top -= Number(paddingVal.top))
            : (cell.padding_top = Number(paddingVal.top));
      }
      if (paddingVal.bottom !== undefined) {
        setType === IncreaseType.increase
          ? (cell.padding_bottom += Number(paddingVal.bottom))
          : setType === IncreaseType.decrease
            ? (cell.padding_bottom -= Number(paddingVal.bottom))
            : (cell.padding_bottom = Number(paddingVal.bottom));
      }
      cell.update();
    }
    if (isCollapsed) {
      editor.selection.setCursorPosition(
        editor.paraPath2ModelPath(oriParaPath)
      );
      editor.updateCaret();
    }
    editor.update();
    editor.render();
    return true;
  }

  static getFocusCellByPath(editor: Editor, origin_path: Path): Cell {
    const path = origin_path.slice(0, -2);

    if (!path.length) return editor.current_cell; // path的长度为0，则确定为根node

    let container: Table | Cell = editor.current_cell;

    // 需要先取出长度值，否则在迭代中是变化的值
    const path_len = path.length;

    for (let i = 0; i < path_len; i++) {
      const index = path.shift()!;

      container = container.children[index] as Table | Cell;
    }
    return container as Cell;
  }

  static fixedHeight(
    editor: Editor,
    type: "scroll" | "normal",
    height?: number
  ) {
    const cells = [];
    const isCollapsed = editor.selection.isCollapsed;
    if (isCollapsed) {
      const focusCell = editor.selection.getFocusCell()?.getOrigin();
      if (focusCell) {
        cells.push(focusCell);
      } else {
        return;
      }
    } else {
      const select_cells = editor.selection.selected_cells;
      if (!select_cells.length) return;
      for (let i = 0; i < select_cells.length; i++) {
        cells.push(select_cells[i].cell);
      }
    }

    for (let i = 0; i < cells.length; i++) {
      const cell = cells[i];
      if (type === "scroll") {
        if (height) {
          cell.set_cell_height = { type: "scroll", height: height };
        } else {
          cell.set_cell_height = { type: "scroll", height: cell.height };
        }
      } else if (type === "normal") {
        cell.set_cell_height = { type: "normal" };
      }
      const focusTable = cell.parent!;
      if (height && height > 0) {
        // 要把所有高度都设置给最后一个 row_size
        const startRowIndex = cell.start_row_index;
        const endRowIndex = cell.end_row_index;
        let preMinRowSizeSum = 0;
        for (let i = startRowIndex; i < endRowIndex; i++) {
          preMinRowSizeSum += focusTable.min_row_size[i];
        }
        focusTable.min_row_size[endRowIndex] = height - preMinRowSizeSum;
        focusTable.refreshTableRowSize();
      }
    }
  }

  static attrJudgeUndefinedAssign(new_cell: Cell, cell: Cell) {
    if (cell.id !== undefined) new_cell.id = cell.id;
    if (cell.lock !== undefined) new_cell.lock = cell.lock;
    if (cell.fixedHeight !== undefined) new_cell.fixedHeight = cell.fixedHeight;
    if (cell.set_cell_height !== undefined)
      new_cell.set_cell_height = cell.set_cell_height;
    if (cell.scroll_cell_top !== undefined)
      new_cell.scroll_cell_top = cell.scroll_cell_top;
    if (cell.padding_left !== undefined)
      new_cell.padding_left = cell.padding_left;
    if (cell.padding_right !== undefined)
      new_cell.padding_right = cell.padding_right;
    if (cell.padding_top !== undefined) new_cell.padding_top = cell.padding_top;
    if (cell.padding_bottom !== undefined)
      new_cell.padding_bottom = cell.padding_bottom;
    if (cell.style !== undefined) new_cell.style = cell.style;
    if (cell.vertical_align !== undefined)
      new_cell.vertical_align = cell.vertical_align;
    if (cell.is_show_slash_down !== undefined)
      new_cell.is_show_slash_down = cell.is_show_slash_down;
    if (cell.is_show_slash_up !== undefined)
      new_cell.is_show_slash_up = cell.is_show_slash_up;
    if (cell.hf_part !== undefined) new_cell.hf_part = cell.hf_part;
    if (cell.noWrap !== undefined) new_cell.noWrap = cell.noWrap;
    if (cell.meta !== undefined) new_cell.meta = cell.meta;
    if (cell.rowLineType !== undefined) new_cell.rowLineType = cell.rowLineType;
    if (cell.aggregationMode !== undefined) new_cell.aggregationMode = cell.aggregationMode;
    if (cell.groupKey !== undefined) new_cell.groupKey = cell.groupKey;
  }

  static updateCellsAttr({
    cells,
    attr,
    editor,
  }: {
    editor: Editor;
    cells?: Cell[];
    attr: any;
  }) {
    if (attr.padding_left !== undefined && attr.padding_right !== undefined && attr.padding_top !== undefined && attr.padding_bottom !== undefined) {
      Cell.setCellPadding(editor, 0, {
        left: attr.padding_left,
        right: attr.padding_right,
        top: attr.padding_top,
        bottom: attr.padding_bottom,
      });
    }
    if (attr.cell_height !== undefined) {
      if (attr.cell_height) {
        Cell.fixedHeight(editor, "scroll", attr.cell_height);
      } else {
        Cell.fixedHeight(editor, "normal", attr.cell_height);
      }
    }

    if (!cells) {
      const current_cell = editor.selection.getFocusCell();
      const selected_cells = editor.selection.selected_cells;
      if (selected_cells.length > 0) {
        cells = selected_cells.map(({ cell }) => cell);
      } else if (current_cell) {
        cells = [current_cell];
      } else {
        cells = [];
      }
    }
    cells.forEach((cell) => {
      Object.assign(cell, attr);
      cell.update();
    });
    if (attr.noWrap === false) {
      editor.refreshDocument(true);
    }
    return true;
  }

  constructor(
    editor: Editor,
    position: number[],
    colspan: number,
    rowspan: number,
    parent: Table | null = null,
    id: string = "",
    hf_part: HeaderFooterType = ""
  ) {
    this.editor = editor;

    this.parent = parent;

    this.position = [...position];

    this.colspan = colspan;

    this.rowspan = rowspan;

    this.cursor_position = 0;

    this.cursor_index = 0;

    this.hf_part = hf_part;

    if (!parent) {
      this.padding_left = editor.config.page_padding_left;
      this.padding_top = editor.config.page_padding_top;
      this.padding_right = editor.config.page_padding_right;
      this.padding_bottom = editor.config.page_padding_bottom;
    } else {
      if (this.parent instanceof ImageTable) {
        this.padding_left = this.padding_right = 0;
      } else {
        this.padding_left = this.padding_right =
          editor.config.table_padding_horizontal;
      }
    }

    this.id = id || uuid("cell");
  }

  /**
   * 获取单元格宽度
   */
  get col_size(): number[] {
    const page_size = this.editor.config.getPageSize();
    return this.parent ? this.parent.col_size : [page_size.width];
  }

  /**
   * 获取行高度
   */
  get row_size(): number[] {
    return this.parent ? this.parent.row_size : [0];
  }

  /**
   * 获取行最小高度
   */
  get min_row_size(): number[] {
    return this.parent ? this.parent.min_row_size : [0];
  }

  // 该单元格 末尾行的行号
  get end_row_index(): number {
    return this.position[0] + this.rowspan - 1;
  }

  // 该单元格 起始行的行号
  get start_row_index() {
    return this.position[0];
  }

  // 该单元格 末尾列的列号
  get end_col_index() {
    return this.position[1] + this.colspan - 1;
  }

  // 该单元格 起始列的列号
  get start_col_index() {
    return this.position[1];
  }

  /**
   * 单元格左边距
   */
  get left() {
    return this.position[1] === 0
      ? 0
      : Math.floor(
        this.col_size
          .slice(0, this.position[1])
          .reduce(
            (total, current) =>
              keepDecimal(total, 2) + keepDecimal(current, 2),
            0
          )
      );
  }

  /**
   * 右边距
   */
  get right() {
    return Math.floor(this.left + this.width);
  }

  /**
   * 上边距
   */
  get top() {
    try {
      // TODO 遇到特殊表格分页时报错
      return this.position[0] === 0
        ? 0
        : this.row_size
          .slice(0, this.position[0])
          .reduce((total, current) => total + current);
    } catch (e) {
      return 0;
    }
  }

  /**
   * 单元格下边距离上边的距离
   */
  get bottom() {
    return this.top + this.height;
  }

  /**
   * 单元格高度
   */
  get height() {
    let result = 0;

    for (let i = 0; i < this.rowspan; i++) {
      result += this.row_size[this.position[0] + i];
    }
    return result;
  }

  /**
   * 单元格宽度
   */
  get width() {
    let result = 0;

    for (let i = 0; i < this.colspan; i++) {
      result += this.col_size[this.position[1] + i];
    }
    result = Math.round(result * 100) / 100;
    return result;
  }

  get start_path(): Path {
    const path: Path = [];
    if (this.parent) {
      path.push(this.parent.cell_index, getCellIndex(this));
    }
    path.push(0, 0);
    return path;
  }

  get end_path(): Path {
    const path: Path = [];
    if (this.parent) {
      path.push(this.parent.cell_index, getCellIndex(this));
    }
    path.push(
      this.children.length,
      this.children[this.children.length - 1].children.length
    );
    return path;
  }

  /**
   * 内容高度
   */
  get content_height() {
    return this.children.length
      ? this.children[this.children.length - 1].bottom
      : 0;
  }

  /**
   * 剩余高度
   */
  get blank_height() {
    return this.height - this.content_height;
  }

  // 该单元格内 宽度最大元素的宽度的值
  get element_max_width() {
    return this.getMaxHeightOrWidth("width");
  }

  // 该单元格内 高度最大元素的高度的值
  get element_max_height() {
    return this.getMaxHeightOrWidth("height");
  }

  getM2WResult(parameter: any = {}): any[]{
    const { rowNum, wordNum, tableType } = parameter;
    let id = 0
    const uniqueId = () => {
      return (
        Math.random().toString(36).substr(3, 3) +
        Number(`${Date.now()}${++id}`).toString(36)
      );
    };
    
    const result = [];
    const stack = [];
    let typeArr: any = []; // 保存同一类型的 口袋
    let resultLen = 0; // 初始的 result 里边的位置 因为一个 paragraph 可能出来好多 row
    let numInPara = 0;
    for (let j = 0; j < this.paragraph.length; j++) {
      const content = this.paragraph[j];
      if (isParagraph(content)) {
        resultLen = result.length;
        // 新的段一定是新的行
        typeArr = [];
        let obj: any = getRowObj(typeArr);

        // 循环第一个 row 判断里边有没有 文本域 有文本域 就在最后一个文本域的地方换行
        // 简单一点 如果是文本域 图片等等非文字的 就直接创建新的控件 否则 就直接 .concent 拼接起来 否则
        const firstRow = content.children[0] as Row;
        if (!firstRow.children.length) continue;
        let firstRowHasField = false;
        for (let i = 0; i < firstRow.children.length; i++) {
          const c = firstRow.children[i];
          if (isCharacter(c) && c.value) {
            if (!c.field_id) {
              const t = typeArr.pop();
              if (!t) {
                // 说明是个空的 根本不可能有同类型的  所以直接放进去
                typeArr.push({
                  type: "col",
                  startPosition: c.left,
                  _fc_drag_tag: "col",
                  hidden: false,
                  display: true,
                  props: {
                    span: 24
                  },
                  children: [{
                    type: "UniqueDescribe",
                    field: uniqueId(),
                    title: "",
                    native: false,
                    props: {
                      concent: c.value
                    },
                    hidden: false,
                    display: true,
                    _fc_drag_tag: "UniqueDescribe"
                  }]
                })
              } else if (t.children[0].type === "UniqueDescribe") {
                t.children[0].props.concent += c.value
                typeArr.push(t);
              } else {
                // 最后得把 pop 出来的这个东西 放进去
                typeArr.push(t)
                typeArr.push({
                  type: "col",
                  startPosition: c.left,
                  _fc_drag_tag: "col",
                  hidden: false,
                  display: true,
                  props: {
                    span: 24
                  },
                  children: [{
                    type: "UniqueDescribe",
                    field: uniqueId(),
                    title: "",
                    native: false,
                    props: {
                      concent: c.value
                    },
                    hidden: false,
                    display: true,
                    _fc_drag_tag: "UniqueDescribe"
                  }]
                })
              }
            }
          }
          if (c.field_id && c.field_position === "start" && !stack.length) {
            const field = this.editor.getFieldById(c.field_id);
            firstRowHasField = true;
            stack.push(field);
            const arr = filterData([field]);
            const item = filterTypr(arr[0]);
            item.title = "";
            typeArr.push({
              type: "col",
              startPosition: c.left,
              _fc_drag_tag: "col",
              hidden: false,
              display: true,
              props: {
                span: 24
              },
              children: [item]
            });
            continue;
          }
          if (c.field_id && c.field_position === "end" && c.field_id === stack[stack.length - 1]?.id) {
            stack.pop();
          }
          if (i === firstRow.children.length - 1) {
            // 到最后一个了 就要判断 文本域换行了没有
            if (firstRowHasField) {
              if (!stack.length) {
                // 如果堆栈空了 就要看最后一个是不是文本域 如果是文本域就不用管了 如果不是文本域就应该放到下一行里边去
                // 如果没有文本域 堆栈也会是空的 要特别注意 所以加上 firstRowHasField 这个东西做判断
                const t = typeArr.pop();
                if (t.children[0].type === "UniqueDescribe") {
                  // 第一行里边有文本域 但是最后是纯文本 所以这个纯文本就要单独成行了
                  result.push(obj);
                  t.startPosition = 0;
                  typeArr = [t];
                  obj = getRowObj(typeArr); // 创建新的一行 带着数据
                } else {
                  // 到本段落的最后一个字了 就应该是放进去完整的一行了 下一个段落重新开始了
                  typeArr.push(t);
                  result.push(obj);
                  typeArr = [];
                  obj = getRowObj(typeArr);
                }
              } else {
                // 堆栈没有清空 那么堆栈里边的就都得拿出来放到下一行里去
                if (stack.length === 1) continue; // 堆栈里就一个 而且这还是第一行 不需要放到下一行里边去
                const arr = [];
                for (let s = 0; s < stack.length; s++) {
                  const p = typeArr.pop();
                  if (p) {
                    if (s > 0){
                      p.startPosition = p.startPosition - arr[arr.length - 1].startPosition;
                    }
                    arr.push(p)
                  }
                }
                arr[0].startPosition = 0;
                result.push(obj);
                typeArr = arr;
                obj = getRowObj(typeArr);
              }
            }
          }
        }
        // 如果只有一行并且没有文本域
        if (content.children.length === 1 && !firstRowHasField) {
          result.push(obj);
          typeArr = [];
          obj = getRowObj(typeArr);
        }
        for (let n = 1; n < content.children.length; n++) {
          // 这里边一旦遇到文本域 那么 文本域就是单独成一行的
          const row = content.children[n];
          if (!row.children.length) continue;
          for (let i = 0; i < row.children.length; i++) {
            const c = row.children[i];
            if (isCharacter(c)) {
              if (!c.field_id) {
                const t = typeArr.pop();
                if (!t) {
                // 说明是个空的 根本不可能有同类型的  所以直接放进去
                  typeArr.push({
                    type: "col",
                    startPosition: c.left,
                    _fc_drag_tag: "col",
                    hidden: false,
                    display: true,
                    props: {
                      span: 24
                    },
                    children: [{
                      type: "UniqueDescribe",
                      startPosition: c.left,
                      field: uniqueId(),
                      title: "",
                      native: false,
                      props: {
                        concent: c.value
                      },
                      hidden: false,
                      display: true,
                      _fc_drag_tag: "UniqueDescribe"
                    }]
                  })
                } else if (t.children[0].type === "UniqueDescribe") {
                  t.children[0].props.concent += c.value
                  typeArr.push(t);
                } else {
                // 最后得把 pop 出来的这个东西 放进去
                  typeArr.push(t)
                  typeArr.push({
                    type: "col",
                    startPosition: c.left,
                    _fc_drag_tag: "col",
                    hidden: false,
                    display: true,
                    props: {
                      span: 24
                    },
                    children: [{
                      type: "UniqueDescribe",
                      field: uniqueId(),
                      title: "",
                      native: false,
                      props: {
                        concent: c.value
                      },
                      hidden: false,
                      display: true,
                      _fc_drag_tag: "UniqueDescribe"
                    }]
                  })
                }
              }
              if (c.field_id && c.field_position === "start" && !stack.length) {
                // if (i !== 0) { // TODO  i !== 0 这个判断也有点问题 如果正好文本域就在编辑器里边新的一行开始呢 转换的数据里边 可得是新的一行啊 所以我先不管 直接 push
                // 因为开头就是文本域的话 就没有必要换行了
                if (obj.children.length) {
                  result.push(obj);
                  typeArr = [];
                  obj = getRowObj(typeArr);
                }
                // }
                const field = this.editor.getFieldById(c.field_id);
                stack.push(field);
                const arr = filterData([field]);
                const item = filterTypr(arr[0]);
                item.title = "";
                typeArr.push({
                  type: "col",
                  startPosition: c.left,
                  _fc_drag_tag: "col",
                  hidden: false,
                  display: true,
                  props: {
                    span: 24
                  },
                  children: [item]
                });
                continue;
              }
              if (c.field_id && c.field_position === "end" && c.field_id === stack[stack.length - 1]?.id) {
                stack.pop();
                // 因为这个段落里 从第二行开始循环 遇到的所有文本域 都是单独一行的所以这里应该处理一下
                result.push(obj);
                typeArr = [];
                obj = getRowObj(typeArr);
              }
            }
            if (n === content.children.length - 1 && i === row.children.length - 1) {
              // 到最后一个了 就要判断 文本域换行了没有
              if (!stack.length && typeArr.length) {
                // 如果堆栈空了 就要看最后一个是不是文本域 如果是文本域就不用管了 如果不是文本域就应该放到下一行里边去
                // 如果没有文本域 堆栈也会是空的 要特别注意 所以加上 firstRowHasField 这个东西做判断
                const t = typeArr.pop();
                if (t.children[0].type === "UniqueDescribe" && t.children.length !== 1) {
                  // 第一行里边有文本域 但是最后是纯文本 所以这个纯文本就要单独成行了
                  result.push(obj);
                  t.startPosition = 0;
                  typeArr = [t];
                  obj = getRowObj(typeArr); // 创建新的一行 带着数据
                } else {
                  // 到本段落的最后一个字了 就应该是放进去完整的一行了 下一个段落重新开始了
                  typeArr.push(t);
                  result.push(obj);
                  typeArr = [];
                  obj = getRowObj(typeArr);
                }
              }
            }
          }
        }
        if (!this.parent) {
          if (result.length === resultLen + 1) {
            const last = result[result.length - 1];
            const colObj = last.children[0];
            if (colObj) {
              const widgetObj = colObj.children[0];
              if (colObj.children.length === 1 && widgetObj.type === "UniqueDescribe" && widgetObj.props.concent.length <= wordNum) {
                numInPara++
              } else {
                if (numInPara >= rowNum) {
                  // 好了 已经符合要求了 往前 num 个 r 都要合并成一个
                  const start = result.length - numInPara;
                  const first = result[start - 1];
                  for (let m = start; m < numInPara; m++) {
                    first.children[0].children[0].props.concent += result[m].children[0].children[0].props.concent
                  }
                  result.splice(start, numInPara - 1); // 删除 num 个就多删除了 因为起点加1了 起点+1 是因为要留一个
                }
                numInPara = 0;
              }
            }
          } else {
            if (numInPara >= rowNum) {
              // 好了 已经符合要求了 往前 num 个 r 都要合并成一个
              const start = result.length - numInPara - (result.length - resultLen) + 1;
              const first = result[start - 1];
              for (let m = start; m < numInPara; m++) {
                first.children[0].children[0].props.concent += result[m].children[0].children[0].props.concent
              }
              result.splice(start, numInPara - 1); // 删除 num 个就多删除了 因为起点加1了 起点+1 是因为要留一个
            }
            numInPara = 0;
          }
        }
      } else if (isTable(content)) {
        if (tableType === 1) {
          content.sortingCells(1)
        } else {
          content.sortingCells();
        }
        if (numInPara > 0 && numInPara >= rowNum) {
          // 好了 已经符合要求了 往前 num 个 r 都要合并成一个
          const start = result.length - numInPara + 1;
          const first = result[start - 1];
          for (let m = start; m < result.length; m++) {
            first.children[0].children[0].props.concent += result[m].children[0].children[0].props.concent
          }
          result.splice(start, numInPara - 1); // 删除 num 个就多删除了 因为起点加1了 起点+1 是因为要留一个
        }
        numInPara = 0;
        for (const cell of content.children) {
          const data = cell.getM2WResult(parameter);
          if (rowNum !== undefined && wordNum !== undefined) {
            // const rowNum = 3; // 大于等于 3 行的就放到一行里边去
            // const wordNum = 2; // 每行里边小于等于 5 个字 并且连续大于等于 3 行的时候这3行 15 个字要放在一行里
            let num = 0;
            for (let i = 0; i < data.length; i++) {
              const r = data[i];
              const colObj = r.children[0];
              const widgetObj = colObj.children[0];
              // 有一个致命的问题 有可能会跨 cell 了 不同的 cell 里边的可不能放一块
              if (colObj.children.length === 1 && widgetObj.type === "UniqueDescribe" && widgetObj.props.concent.length <= wordNum) {
              // 如果一行里只有一个 col 对象 一个 col 对象里边 又只有一个控件 并且这个控件还是描述控件 再判断描述控件里边几个字 符不符合要求
                num++
              } else {
                if (num >= rowNum) {
                  // 好了 已经符合要求了 往前 num 个 r 都要合并成一个
                  const start = i - num + 1;
                  const first = data[start - 1];
                  for (let m = start; m < num; m++) {
                    first.children[0].children[0].props.concent += data[m].children[0].children[0].props.concent
                  }
                  data.splice(start, num - 1); // 删除 num 个就多删除了 因为起点加1了 起点+1 是因为要留一个
                }
                num = 0;
              }
            }
            if (num >= rowNum) {
              // 如果循环完了 就说明在最后一行是符合条件的
              const start = data.length - num + 1;
              const first = data[start - 1];
              for (let m = start; m < num; m++) {
                first.children[0].children[0].props.concent += data[m].children[0].children[0].props.concent
              }
              data.splice(start, num - 1);
            }
            num = 0;
          }
          result.push(...data)
        }
      }
    }

    if (numInPara >= rowNum) {
      // 好了 已经符合要求了 往前 num 个 r 都要合并成一个
      const start = result.length - numInPara + 1;
      const first = result[start - 1];
      for (let m = start; m < result.length; m++) {
        first.children[0].children[0].props.concent += result[m].children[0].children[0].props.concent
      }
      result.splice(start, numInPara - 1); // 删除 num 个就多删除了 因为起点加1了 起点+1 是因为要留一个
    }
    
    const page = this.editor.pages[0];
    const rowWIdth = page.width - page.padding_left - page.padding_right;
    // const gridWidth = rowWIdth / 24;
    for (const r of result) {
      // result 内每一个对象是 一行
      if (r.children.length === 1) {
        // r.children[0].props.span = 24;
        // 因为默认就是 24 所以直接 continue 就行
        continue;
      }
      let  t = 0;
      for (let i = 0, len = r.children.length; i < len; i++) {
        const o = r.children[i];
        if (i === len - 1) {
          const v = 24 - t;
          if (v > 0) {
            // 如果还有空放最后一个控件就设置 span 
            r.children[i].props.span = v;
          } else {
            // 已经没有空放最后一个控件了 应该从前边取一个 span 分配给他 往前找第一个 span 大于1 的有值分配给他的就分配
            for (let m = i - 1; m >= 0; m--) {
              if (r.children[m].props.span > 1) {
                r.children[m].props.span -= 1;
                r.children[i].props.span = 1;
                break;
              }
            }
          }
        } else {
          const span = Math.ceil(24 * (r.children[i + 1].startPosition - r.children[i].startPosition) / rowWIdth);
          o.props.span = span < 0 ? 1 : span;
          t += o.props.span;
        }
      }
    }
    return result
  }

  getContentRawData() {
    const newRootCell = initCell(this.editor, "trans");
    const newHeaderCell = initCell(this.editor, "header_trans");
    const newFooterCell = initCell(this.editor, "footer_trans");
    newRootCell.paragraph = this.copy().paragraph;
    return this.editor.event.emit(
      "modelData2RawData",
      newHeaderCell,
      newRootCell,
      newFooterCell
    );
  }

  getLocation(): "header" | "footer" | "root" {
    const cell = this.getOrigin();
    if (cell.hf_part === "header" || cell.hf_part === "footer")
      return cell.hf_part;
    const table = cell.parent;
    if (table) {
      return table.parent.hf_part || "root";
    }
    return "root";
  }

  getInnerUndrawnHorizontalLines() {
    const arr = [];
    for (let j = 1; j < this.rowspan; j++) {
      for (let n = 0; n < this.colspan; n++) {
        arr.push([this.position[0] + j, this.position[1] + n]);
      }
    }
    return arr;
  }

  getInnerUndrawnVerticalLines() {
    let arr = [];
    for (let j = 1; j < this.colspan; j++) {
      for (let n = 0; n < this.rowspan; n++) {
        arr.push([this.position[1] + j, this.position[0] + n]);
      }
    }
    return arr;
  }

  getInnerUndrawnLines() {
    const rows = this.getInnerUndrawnHorizontalLines();
    const cols = this.getInnerUndrawnVerticalLines();
    return {
      rows,
      cols
    }
  }

  getAdjacentCell(direction:DirectionType){
    if(!this.parent) return;
    return this.parent.children.find((c:Cell)=>{
      if(this === c) return false;
      if(direction === "up"){
        if(this.top === c.bottom && c.left >= this.left && c.right <= this.right){
          return true;
        }
      }else if(direction === "left"){
        if(this.left === c.right && c.top >= this.top && c.bottom <= this.bottom){
          return true;
        }
      }else if(direction === "right"){
        if(this.right === c.left && c.top >= this.top && c.bottom <= this.bottom){
          return true;
        }
      }else {
        if(this.bottom === c.top && c.left >= this.left && c.right <= this.right){
          return true;
        }
      }
    })
  }


  getTop(rowSize?: number[]) {
    rowSize = rowSize || this.row_size;
    try {
      // TODO 遇到特殊表格分页时报错
      return this.position[0] === 0
        ? 0
        : rowSize
          .slice(0, this.position[0])
          .reduce((total, current) => total + current);
    } catch (e) {
      return 0;
    }
  }

  getHeight(rowSize?: number[]) {
    rowSize = rowSize || this.row_size;

    let height = 0;
    for (let i = 0; i < this.rowspan; i++) {
      height += rowSize[this.position[0] + i];
    }
    return height;
  }

  /**
   * 导航到该单元格
   * @param position "start" "end"
   * @returns
   */
  navigateToThisCell(position: "start" | "end" = "start") {
    const parent_table = this.parent;
    if (!parent_table) return;
    parent_table.sortingCells();

    const index = parent_table.children.findIndex(
      (cell) => cell.id === this.id
    );

    let model_path: Path = [];
    if (position === "start") {
      model_path = [parent_table.cell_index, index, 0, 0];
    } else if (position === "end") {
      const last_paragraph = this.paragraph[
        this.paragraph.length - 1
      ] as Paragraph;
      const last_row =
        last_paragraph.children[last_paragraph.children.length - 1];
      model_path = [
        parent_table.cell_index,
        index,
        last_row.cell_index,
        last_row.children.length,
      ];
    }
    this.editor.selection.setCursorPosition(model_path);
    this.editor.updateCaret();
    this.editor.scroll_by_focus();
    this.editor.render();
  }

  /**
   * 返回该单元格内 元素的最宽或者最高的值
   * @param widthOrHeight "width" | "height"
   * @returns 返回最大值
   */
  getMaxHeightOrWidth(widthOrHeight: string) {
    const paragraphs = this.paragraph as Paragraph[]; // 没考虑嵌套表格
    if (!paragraphs || paragraphs.length === 0) {
      return 0;
    }
    let max = 0;
    paragraphs.forEach((p) => {
      let characters = [];
      if (isParagraph(p)) {
        characters = p.characters;
      }
      characters.forEach((c) => {
        if (c[widthOrHeight] > max) {
          max = c[widthOrHeight];
        }
      });
    });
    return max;
  }

  contain_vertical(y: number) {
    return this.top <= y && y <= this.bottom;
  }

  contain_horizontal(x: number) {
    return this.left <= x && x <= this.right;
  }

  /**
   * 判断位置是否在单元格内部
   * @param x x轴坐标
   * @param y y轴坐标
   * @returns true\false
   */
  contain(x: number, y: number) {
    return this.contain_horizontal(x) && this.contain_vertical(y);
  }

  /**
   * 判断单元格的位置
   * @param cell 需要判断位置的单元格 cell 在 this 的前边、后边、原地
   * @returns front：cell 在该单元格的前边 behind：cell 在该单元格的后边 situ： cell 就是该单元格
   */
  getRelativePosition(cell: Cell): "front" | "behind" | "situ" {
    if (cell.position[0] < this.position[0]) {
      return "front";
    } else if (cell.position[0] > this.position[0]) {
      return "behind";
    } else {
      if (cell.position[1] < this.position[1]) {
        return "front";
      } else if (cell.position[1] > this.position[1]) {
        return "behind";
      }
    }
    return "situ";
  }

  /**
   * 替换 cell 的内容到当前单元格
   * @param cell 将这个 cell 内的内容 替换到调用该方法的 cell 内
   */
  replaceContentWith(cell: Cell) {
    this.paragraph = [...cell.paragraph];
    this.children = [...cell.children];
    this.fields = [...cell.fields];
    this.paragraph.forEach(
      (paragraph) => ((paragraph as Paragraph).cell = this)
    );
    this.handleFieldsAssignment(this.fields);
  }

  /**
   * 修改单元格内图片状态 show 的状态 只是改了属性值 没有 update render
   */
  updatePropsOfImg(parameter: Partial<ImageElement>) {
    const paragraph = this.paragraph as Paragraph[];
    paragraph.forEach((para) => {
      para.characters.forEach((img) => {
        if (isImage(img)) {
          img.updateProps(parameter);
        }
      });
    });
  }

  // 创建paragraph 给单元格内插入一个换行符
  insertEmptyParagraph(paraIndex?: number, nextCell?: Cell) {
    const para_id = uuid("para");
    const group_id = this.parent ? this.parent.group_id : null;
    const current_paragraph = new Paragraph(para_id, this, group_id);
    // 新new出来的段落为空的时候，需要添加一个默认的换行符

    let font = this.editor.fontMap.add(this.editor.config.default_font_style); // 不需要考虑FontMap 因为默认的样式 已经在读Config的时候存储好了
    if (paraIndex) {
      font = this.editor.contextState.getFontState();
    }
    const newEnterChar = new Character(font, "\n");
    current_paragraph.characters.push(newEnterChar);
    current_paragraph.createRow();
    if (paraIndex) {
      this.paragraph.splice(paraIndex, 0, current_paragraph);
      this.children.splice(paraIndex, 0, ...current_paragraph.children);
    } else {
      this.paragraph.push(current_paragraph);
      this.children.push(...current_paragraph.children);
    }

    this.cursor_index += current_paragraph.children.length;
    this.updateParaIndex();
    this.updateRowBounding(0, nextCell);

    return current_paragraph;
  }

  /**
   * 插入表格
   * table: 要插入的表格
   * paragraphIndex: 要插入表格时，段落的下标 也就是插入以后表格的下标, 如果需要换行就在调用该方法之前插入换行符，并且修改好paragraphIndex
   */
  insertTableByParagraphIndex(table: Table, paragraphIndex: number) {
    // table.group_id = this.paragraph[paragraphIndex].group_id; // 移到外边去
    // 不能在这里插入换行符 因为paragraphIndex会错乱
    let childrenIndex = 0; // 计算光标在this.children中的下标
    if (paragraphIndex) {
      // 只有paragraphIndex > 0 的时候才需要计算 否则 childrenIndex就是0
      // 因为到这儿 是插完换行符以后了 所以childrenIndex 是 上一个段落 最后一个row的下标加1
      childrenIndex =
        (
          this.paragraph[paragraphIndex - 1].children[
            this.paragraph[paragraphIndex - 1].children.length - 1
          ] as Row
        ).cell_index + 1; // row.cell_index 是row在this.children中的下标
    }
    this.children.splice(childrenIndex, 0, table);
    this.paragraph.splice(paragraphIndex, 0, table);
    this.updateRowBounding(childrenIndex);
    this.updateParaIndex();
    if (table.group_id) {
      const group = this.getGroupById(table.group_id);
      // refreshContentParaId 调用必须在模型数据中更新完成后
      group && group.refreshContentParaId();
    }
  }

  clearPadding() {
    this.padding_top = 0;
    this.padding_right = 0;
    this.padding_bottom = 0;
    this.padding_left = 0;
  }

  /**
   * 根据id获取文本域对象
   */
  getFieldById(id: string): XField | BoxField | null {
    const result = this.fields.find((ele) => ele.id === id);
    if (!result) {
      return null;
    } else {
      return result;
    }
  }

  /**
   * 根据名称获取文本域数组
   * @param name
   */
  getFieldsByName(name: string|string[]): (XField | BoxField)[] {
    return this.fields.filter((field) => {
      if(Array.isArray(name)){
        return name.indexOf(field.name) > -1
      }else{
        return field.name === name
      }
    });
  }

  /**
   * 根据名称获取box数组
   * @param name
   */
  getBoxesByName(
    name: string,
    cell: Cell = this.editor.root_cell,
    para_path: any = []
  ) {
    const boxes: any = [];
    for (let i = 0; i < cell.paragraph.length; i++) {
      const element = cell.paragraph[i];
      if (isParagraph(element)) {
        for (let j = 0; j < element.characters.length; j++) {
          const char = element.characters[j];
          if (isBox(char) && char.name === name) {
            const path = para_path.concat([i, j]);
            boxes.push({
              para_path: path,
              box: char,
            });
          }
        }
      } else if (isTable(element)) {
        for (let j = 0; j < element.children.length; j++) {
          const cell = element.children[j];
          const cell_index = getCellIndex(cell);
          const cell_boxes = this.getBoxesByName(name, cell, [i, cell_index]);
          boxes.push(...cell_boxes);
        }
      }
    }

    return boxes;
  }

  /**
   * 根据类型获取文本域数组
   * @param type
   */
  getFieldsByType(type: string): (XField | BoxField)[] {
    return this.fields.filter((field) => {
      return field.type === type;
    });
  }

  /**
   * 根据id获取分组信息
   * @param id
   */
  getGroupById(id: string): Group | undefined {
    return this.groups.find((group) => group.id === id);
  }

  /**
   * 初始化加载数据
   * @param node 原始节点数据
   * @param next_node 下一个节点数据 用于文本域渲染
   * @param groups 是否分支
   */
  insert_raw(node: any, nextNode: any, groups?: any[], nextCell?: any) {
    this.editor.event.emit("transCellInsertRaw", {
      cell: this,
      node,
      nextNode,
      groups,
      nextCell,
    });
  }

  /**
   * 插入分组数据
   * @param node 分组节点数据
   */
  insertGroup(node: any) {
    const new_group = new Group(node.id, this, node.date);
    new_group.lock = node.lock;
    new_group.name = node.name;
    new_group.content_para_id = node.content_para_id;
    new_group.header_info = node.header_info;
    new_group.replace = node.replace;
    new_group.page_break = node.page_break;
    new_group.new_page = node.new_page;
    new_group.meta = node.meta;
    new_group.is_form = node.is_form;
    if (!this.groups) this.groups = [];
    this.groups.push(new_group);
  }

  /**
   * 单元格内设置垂直对齐
   * @param alignDirection 对齐方向 top center bottom
   */
  setVerticalAlign(alignDirection: VerticalAlign) {
    this.vertical_align = alignDirection;
    for (const para of this.paragraph) {
      (para as Paragraph).setVerticalAlign(alignDirection);
    }
  }

  /**
   * 找到段落 或者 表格 所在的分组
   * @param para_tab_id 段落或者表格id
   * @param groups 分组
   * @returns 分组id
   */
  findGroupFromGroups(para_tab_id: string, groups: any[]) {
    let groups_id: string | null = null;
    if (!groups.length) return null;
    for (let i = 0; i < groups.length; i++) {
      const group = groups[i];
      for (let j = 0; j < group.content_para_id.length; j++) {
        const element = group.content_para_id[j];
        if (para_tab_id === element) {
          groups_id = group.id;
          return groups_id;
        }
      }
    }
    return groups_id;
  }

  /**
   * 放置生成的基础元素到文本域或段落
   * @param current_paragraph
   * @param parent_field
   * @param current_element
   */
  pushBaseElement(
    current_paragraph: Paragraph,
    parent_field: XField | null,
    current_element: ElementInParagraph
  ) {
    current_element.field_id = parent_field ? parent_field.id : null; // 如果有值代表文本域
    current_paragraph.characters.push(current_element);
    parent_field && parent_field.children.push(current_element);
  }

  /**
   * 组装段落字符
   * @param node
   * @param current_paragraph
   * @param nextNode
   * @param parent_field
   */
  assemblyParaContent(
    node: any,
    current_paragraph: Paragraph,
    nextNode: any,
    parent_field: XField | null
  ) {
    for (let i = 0; i < node.children.length; i++) {
      const child = node.children[i];
      let font: Font | undefined;
      if (child.font_id) {
        const fontMap = this.editor.fontMap.get();
        font = fontMap.get(child.font_id);
      }
      if (!font) {
        font = this.editor.contextState.getFontState();
      }
      if (child.type === "fraction") {
        const [int, numerator, denominator] = child.value.split("#");
        const fraction = new Fraction(this.editor, (int === "undefined" || int === "0") ? undefined : int, numerator, denominator, font);
        fraction.comment_id = child.comment_id;
        fraction.cusCommentId = child.cusCommentId;
        child.mark && (fraction.mark = child.mark);
        this.pushBaseElement(current_paragraph, parent_field, fraction);
      }
      if (child.type === "text") {
        // TODO 需确认，目前需注释掉解决跨页或表格存在应分割情况调用该方法，在光标处直接设置字体样式不生效问题
        // if (!child.value.length && node.type !== "field") {
        //   ContextState.setFontState(font);
        // }
        const chars = specialCharHandle.splitString(child.value);
        for (let j = 0; j < chars.length; j++) {
          const char = chars[j];
          const current_character = new Character(font, char);
          current_character.comment_id = child.comment_id;
          current_character.cusCommentId = child.cusCommentId;
          child.mark && (current_character.mark = child.mark);
          this.pushBaseElement(
            current_paragraph,
            parent_field,
            current_character
          );
        }
      }
      if (child.type === "widget") {
        const current_widget = new Widget(
          child.selected,
          child.height,
          font,
          child.widgetType,
          child.params,
          child.id
        );
        if (child.selectNum) {
          current_widget.selectNum = child.selectNum;
        }
        Widget.attrJudgeUndefinedAssign(current_widget, child);
        this.pushBaseElement(current_paragraph, parent_field, current_widget);
        font && (current_widget.font = font);
      }
      if (child.type === "image") {
        const current_image = new ImageElement(
          this.editor.internal.imageSrcObj?.[child.src] || child.src, // 或者的意思是：兼容下没用新代码保存过的老数据
          child.width,
          child.height,
          font,
          child.meta,
          child.id
        );
        ImageElement.attrJudgeUndefinedAssign(current_image, child);
        if (child.meta && child.meta.src) {
          const originImage = new ImageElement(
            child.meta.src,
            child.meta.originWidth,
            child.meta.originHeight,
            font,
            child.meta,
            child.id
          );
          this.editor.imageMap.addOnload(originImage);
        }
        current_image.rotation = 0;
        this.editor.imageMap.addOnload(current_image); // 这是加载的时候 不需要转换 白色背景了 因为已经转换完了 不走 addOnload ImageMap 里边没有东西 所以要调用 往里边放东西
        this.pushBaseElement(current_paragraph, parent_field, current_image);
        this.editor.internal.transformData.images.push(current_image);
        font && (current_image.font = font);
      }
      if (child.type === "box") {
        const current_box = new Box(child.content, child.height, child.name);
        // 级联功能历史数据存了大量无用字体样式，此处进行处理
        if(isUseCurrentLogic(this.editor)){
          const decodeData = decodeURIComponent(
            decodeBase64(current_box.content)
          );
          const arr = decodeData.split(Config.font_map_flag)
          const rawData = arr[0];
          const fontStr = arr.splice(1 , 1).join("");
          const fontObj = JSON.parse(fontStr);
          const newFontObj : any = {}
          for (const id in fontObj) {
            const style = fontObj[id];
            if (rawData.indexOf(id) > -1) {
              newFontObj[id] = style;
            }
          }
          const encodeBase = encodeBase64(encodeURIComponent(rawData + Config.font_map_flag + JSON.stringify(newFontObj) + Config.font_map_flag));
          current_box.content = encodeBase;
        }
        Box.attrJudgeUndefinedAssign(current_box, child);
        this.pushBaseElement(current_paragraph, parent_field, current_box);
        font && (current_box.font = font);
      }
      if (child.type === "line") {
        const current_line = new Line(
          this,
          child.height,
          child.line_height,
          child.color,
          child.form
        );
        Line.attrJudgeUndefinedAssign(current_line, child);
        this.pushBaseElement(current_paragraph, parent_field, current_line);
        font && (current_line.font = font);
      }
      if (child.type === "button") {
        const current_button = new Button(
          child.value,
          child.width,
          child.height,
          child.color
        );
        Button.attrJudgeUndefinedAssign(current_button, child);
        this.pushBaseElement(current_paragraph, parent_field, current_button);
        font && (current_button.font = font);
      }
      if (child.type === "field") {
        let current_field: XField | BoxField | null = this.getFieldById(
          child.id
        );
        if (!current_field) {
          const field_id = child.id ?? uuid("field");
          let field_style;
          if (child.font_id) {
            const fontMap = this.editor.fontMap.get();
            field_style = fontMap.get(child.font_id);
          }
          // 兼容老数据
          if (!field_style) {
            field_style = new Font(child.style);
          }
          if (child.field_type === "box") {
            current_field = new BoxField(field_id, field_style, this, false);
          } else {
            current_field = new XField(field_id, field_style, this, false);
          }
          this.fields.push(current_field);
          if (parent_field) {
            current_field.parent = parent_field;
            parent_field.children.push(current_field);
          }
          XField.attrJudgeUndefinedAssign(current_field, child);
          if (child.ext_cell && !isEmptyObj(child.ext_cell)) {
            XField.setAnchorExtCell(this.editor, current_field, { content: child.ext_cell })
          }
          if (child.field_type) {
            current_field.type = child.field_type; // 文本域类型
          }
          if (isBoxField(current_field)) {
            BoxField.attrJudgeUndefinedAssign(current_field, child);
          }
          current_field.generateMainProps();
          // 先是将文本域的开始字符放到当前段落中
          current_paragraph.characters.push(current_field.start_sym_char);
        } else if (
          parent_field &&
          !parent_field.children.find((field) => field === current_field)
        ) {
          current_field.parent = parent_field;
          parent_field?.children.push(current_field);
        }

        if (child.children.length) {
          this.assemblyParaContent(
            child,
            current_paragraph,
            nextNode,
            current_field
          );
        }
        const repairField = (cField: XField) => {
          // 先移除段落中的换行符
          current_paragraph.characters.pop();
          // 将文本域中对应的换行符也移除
          cField.removeElement(linebreakChar);
          linebreakChar.field_id = null;
          // 文本域结束图标
          current_paragraph.characters.push(cField.end_sym_char);
          this.editor.internal.transformData.fields.push(cField);
          current_paragraph.characters.push(linebreakChar);
        };
        const repairField2 = (nextNode:any,currentField:any,pFiledId:any) =>{
          if(!nextNode.children[1]){
            return
          }
          let pField: any = this.getFieldById(pFiledId);
          if(!pField){
            return
          }
          // 现在原始数据中存在这样一种情况，下一段本应该为嵌套结构结果数据为平铺,
          // 此时特殊处理 再判断
          for (let j = 1 ; j <  nextNode.children.length; j++) {
            const node = nextNode.children[j];
            if(node.id === pFiledId){
              pField.getAllElements().forEach((ele:any)=>{
                if(ele.field_id === pFiledId){
                  ele.field_id = null;
                  pField.start_sym_char.font.color = "red"
                }
              })
              pField.cell.fields.splice(pField.cell.fields.indexOf(pField),1)
              return true;
            }
          }
        }

        const linebreakChar = current_paragraph.characters.find(
          (char) => char.value === "\n"
        );
        // 每个段落一旦出现换行则表示已经结束
        if (linebreakChar) {
          // 当下一段文本域与当前换行符对应的文本域不存在父子关系时，则说明文本域出现了问题，此时启用自动修复逻辑，在换行符前自动拼上结束字符并且去掉该换行符上的文本域id
          if (
            linebreakChar.field_id &&
            EditorLocalTest.useLocal &&
            current_field
          ) {
            if (
              !nextNode ||
              !nextNode.children[0] ||
              nextNode.children[0].type !== "field"
            ) {
              repairField(current_field);
            } else if (
              nextNode &&
              nextNode.children[0] &&
              nextNode.children[0].type === "field"
            ) {
              const nextFId = nextNode.children[0].id;
              let pField: any = this.getFieldById(linebreakChar.field_id);
              let result = true;
              // 如果下一段开始的文本域id不是当前换行符一样的id并且不是当前文本域父id的话就进行修复
              while (pField) {
                pField = pField.parent;
                if (pField && pField.id === nextFId) {
                  result = false;
                  break;
                }
              }
              if (nextFId !== linebreakChar.field_id && result) {
                if(!repairField2(nextNode,current_field,linebreakChar.field_id)){
                  repairField(current_field);
                }
              }
            }
          }else{
            //TODO 临时处理现场数据错误的问题
            if (
              !nextNode ||
                !nextNode.children[0] ||
                nextNode.children[0].type !== "field"
            ) {
              repairField(current_field);
            }
          }
          return;
        }
        // 判断文本域中知否有内容,背景文本(label标签如果无内容需要显示背景文本)
        if (
          (current_field.type === "label" && !current_field.children.length) ||
          ((current_field.display_type !== "normal" ||
            this.editor.config.show_field_symbol) &&
            !current_field.children.length)
        ) {
          current_paragraph.characters.push(
            ...current_field.placeholder_characters
          );
        }
        // 文本域结束图标
        current_paragraph.characters.push(current_field.end_sym_char);
        this.editor.internal.transformData.fields.push(current_field);

      }
    }
  }

  /**
   * 更新 就是重新创建 row 重新调整位置等
   */
  update(callTypesetting: boolean = true) {
    forEachFlatParagraph(this, (paragraph) => {
      paragraph.updateChildren(undefined, undefined, callTypesetting);
    });
  }

  /**
   * 根据操作类型执行操作方法
   * @param param 实现功能需要的各个参数 封装的对象
   * @returns 执行成功
   */
  updateChildren({
    startRowIndex,
    updateRowCount,
    updateParagraphIndex,
    callTypesetting,
  }: CellUpdateChildrenParameter) {
    // 更新cell的children
    // 当前段落
    const currentParagraph = this.paragraph[updateParagraphIndex] as Paragraph;
    this.children.splice(
      startRowIndex,
      updateRowCount,
      ...currentParagraph.children
    ); // 因为该方法目前只在段落的 updateChildren 方法中调用 而且提前 createRow 了所以 currentParagraph 是更新后的 row
    // 所以到这里之前是在更新当前段落

    const previousParagraph = currentParagraph.previousParagraph;
    const updateIndex = previousParagraph
      ? isTable(previousParagraph)
        ? previousParagraph.cell_index
        : previousParagraph.children[0].cell_index
      : 0;
    this.updateParaIndex();
    this.updateRowBounding(updateIndex);
    callTypesetting && this.typesettingPart();
    return true;
  }

  refreshTypesetting() {
    if (!isTable(this.parent)) {
      return;
    }
    this.parent.callTypesetting();
  }

  /**
   * 插入回车
   * @param splitParagraphs 分割后的段落
   * @param rows 分割后的段落所有的行
   * @returns 位置坐标
   */
  enter2UpdateChildren(splitParagraphs: Paragraph[], rows: Row[]) {
    this.paragraph.splice(splitParagraphs[0].para_index, 1, ...splitParagraphs);
    if (splitParagraphs[1].group_id) {
      const group = this.getGroupById(splitParagraphs[1].group_id);
      group && group.refreshContentParaId();
    }
    const splice_length = splitParagraphs[0].splice_length;
    this.children.splice(rows[0].cell_index, splice_length, ...rows);
    this.updateParaIndex();
    this.updateRowBounding(rows[0].cell_index);
    this.typesettingPart();
    return {
      row_index:
        splitParagraphs[0].children[splitParagraphs[0].children.length - 1]
          .cell_index + 1,
      offset: 0,
    };
  }

  // 更新部分单元格的排版，只有当前操作的单元格影响到的并且不是上对齐的
  typesettingPart() {
    const parent = this.parent;
    const compareEndRowIndex = this.end_row_index;
    if (isTable(parent)) {
      for (const cell of parent.children) {
        const startRowIndex = cell.start_row_index;
        const endRowIndex = cell.end_row_index;
        if (
          startRowIndex <= compareEndRowIndex &&
          endRowIndex >= compareEndRowIndex
        ) {
          // 只有这些单元格的高度才会受到影响
          cell.vertical_align !== "top" && cell.typesetting();
        }
      }
    }
  }

  /**
   * 更新段落序号
   */
  updateParaIndex() {
    // 不同级别的上一段集合
    const same_level_last_para: Paragraph[] = [];
    for (let j = 0; j < this.paragraph.length; j++) {
      const paragraph = this.paragraph[j];
      paragraph.para_index = j;
      if (isTable(paragraph) || !paragraph.islist || !paragraph.isOrder) {
        continue;
      } else {
        // 同级别的上一段是否存在，如果存在，设置当前段落的listindex值，并且更新same_level_last_para数组
        const exist_not =
          same_level_last_para.length &&
          same_level_last_para.some((para, index) => {
            if (para.level === paragraph.level) {
              // 因为原来的时候是在 Pragraph 的构造函数中直接用 config 里边的配置赋值的 但是现在去掉了配置 那么复制粘贴和在列表里回车就走 Paragraph 里边默认的赋值了 那肯定不对
              // 所以要在这里更正 必须得是 > 1 不等于都不行 有两个列表的话  第二个列表的第一项会跟上一个列表一样 所以都得用 > 1
              if (para.list_index > 1 && paragraph.list_index > 1) {
                paragraph.listNumStyle = para.listNumStyle;
              }
              // 设置当前段的list_index
              paragraph.list_index = para.list_index + 1;
              // 如果是需要重新排序的话 那么 重置序号
              if (paragraph.restart_list_index) {
                paragraph.list_index = 1;
              }

              // 因为原来的时候是在 Pragraph 的构造函数中直接用 config 里边的配置赋值的 但是现在去掉了配置 那么复制粘贴和在列表里回车就走 Paragraph 里边默认的赋值了 那肯定不对
              // 所以要在这里更正 必须得是 > 1 不等于都不行 有两个列表的话  第二个列表的第一项会跟上一个列表一样 所以都得用 > 1
              if (para.list_index >= 1 && paragraph.list_index > 1) {
                paragraph.listNumStyle = para.listNumStyle;
              }

              // 更新 同级别上一段 数组
              same_level_last_para.splice(
                index,
                same_level_last_para.length,
                paragraph
              );
              // 返回结果
              return true;
            }
            return false;
          });
        // 如果当前级别在集合中不存在，那么push到数组中
        if (!exist_not) {
          same_level_last_para.push(paragraph);
          paragraph.list_index = 1;
        }
      }
    }
  }

  /**
   * 获取单元格内内容高度 最后一行的bottom
   * @returns 内容高度
   */
  getContentHeight() {
    const childrenLength = this.children.length;
    if (this.editor.document_meta.handleTableRowSize) {
      return childrenLength
        ? this.children[childrenLength - 1].bottom + this.padding_top
        : 0;
    }
    return childrenLength ? this.children[childrenLength - 1].bottom : 0;
  }

  // 获取该单元格内的真实内容高度
  getRealContentHeight() {
    const childrenLength = this.children.length;
    let beforeParaHeight = 0
    if (childrenLength) {
      // beforeParaHeight = this.paragraph.reduce((prev, para: any) => {
      //   if (para.children && para.children.length > 0 && para.children[0].height !== undefined) {
      //     return prev + para.before_paragraph_spacing * para.children[0].height;
      //   } else {
      //     return prev;
      //   }
      // }, 0);
      if (this.editor.document_meta.handleTableRowSize) {
        return (
          this.children.reduce((prev, row) => prev + row.height, 0) +
          this.padding_top +
          this.padding_bottom + beforeParaHeight
        );
      }
      return this.children.reduce((prev, row) => prev + row.height, 0) + beforeParaHeight;
    }
    if (this.editor.document_meta.handleTableRowSize) {
      return this.padding_top;
    } else {
      return 0;
    }
  }

  /**
   * 用于在非分组文档中添加分组，获取前一个分组的下标
   * @param focus_para_index 光标所在处下标
   * @returns 插入分组前一个分组在所有分组中的下标
   */
  groupIndexSection(focus_para_index: number) {
    const group_para_indexs: number[] = [];
    for (let i = 0; i < this.groups.length; i++) {
      const group = this.groups[i];
      // 各个分组对应的index
      const para_index: number = this.paragraph.findIndex(
        (item) => item.group_id === group.id
      );
      // 所有段落的index数组
      group_para_indexs.push(para_index);
    }
    // 找到正确的位置
    for (let j = 0; j < group_para_indexs.length; j++) {
      const para_index = group_para_indexs[j];
      // 跳过index小于
      if (focus_para_index > para_index) {
        if (j === group_para_indexs.length - 1) {
          // 代表在末尾添加 返回分组数组的长度
          return this.groups.length;
        }
        continue;
      } else {
        if (j === 0) return 0;
        const cur_index = j - 1;
        // 分组插入位置前一分组的id
        const para_group_id =
          this.paragraph[group_para_indexs[cur_index]].group_id;
        // 分组插入位置前一分组的index
        const result_group_index =
          this.groups.findIndex((item) => item.id === para_group_id) + 1;
        return result_group_index;
      }
    }
  }

  /**
   * 插入完毕字符需要重整cell中子元素的top,bottom,cell_index
   * 并计算是否超出原height值
   * 只有表格内的单元格调用时，index才能为0，root_cell调用时要计算从第几行开始的(在insert_raw里边 都传0没有问题，反正所有情况下传0肯定是有问题的)
   * 在 删除表格的时候 调用该方法传了 0 就导致 删除表格上方的 row 或者 table 的 top 值不对 页面错乱
   * @param index 从index行处开始整理, 为root_cell上的第几行 或者单元格上的第几个row
   * @param nextCell 下一个单元格，用于判断是否是最后一个单元格
   */
  updateRowBounding(index: number, nextCell?: any) {
    let cursor_position;
    if (index === 0) {
      if (this.parent) {
        cursor_position = this.padding_top;
      } else if (this.hf_part === "header") {
        cursor_position = this.editor.config.header_margin_top;
      } else {
        // 当页脚没有段落时兼容处理
        cursor_position = this.children[0] ? this.children[0].top : 0;
      }
    } else {
      // if (EditorLocalTest.transUse) {
      //   const curRow = this.children[index];
      //   if (isRow(curRow)) {
      //     cursor_position =
      //       this.children[index - 1].bottom +
      //       curRow.paragraph.before_paragraph_spacing * curRow.height;
      //   } else {
      //     cursor_position = this.children[index - 1].bottom;
      //   }
      // } else {
      cursor_position = this.children[index - 1].bottom;
      // }
    }

    const parent_table = this.parent;
    // 先调整表格的高度，，否则会用cell的默认高度
    if (parent_table && !nextCell) {
      parent_table.refreshTableRowSize();
    }
    // children的所有内容的高度
    let children_height = this.padding_bottom + this.padding_top;
    for (const child of this.children) {
      children_height += child.height;
    }

    // 对齐方式不同 计算方式不同
    if (this.vertical_align === VerticalAlign.CENTER) {
      index = 0;
      if (children_height <= this.height) {
        if (this.editor.document_meta.handleTableRowSize) {
          cursor_position =
            (this.height - children_height) / 2 + this.padding_top;
        } else {
          cursor_position = (this.height - children_height) / 2;
        }
      } else {
        if (this.editor.document_meta.handleTableRowSize) {
          cursor_position = this.padding_top;
        } else {
          cursor_position = 0;
        }
      }
    } else if (this.vertical_align === VerticalAlign.BOTTOM) {
      index = 0;
      if (children_height <= this.height) {
        if (this.editor.document_meta.handleTableRowSize) {
          cursor_position = this.height - children_height + this.padding_top;
        } else {
          cursor_position = this.height - children_height;
        }
      } else {
        if (this.editor.document_meta.handleTableRowSize) {
          cursor_position = this.padding_top;
        } else {
          cursor_position = 0;
        }
      }
    }
    for (let i = index; i < this.children.length; i++) {
      // if (EditorLocalTest.transUse) {
      //   const curRow = this.children[i]
      //   if (isRow(curRow)) {
      //     const curPara = curRow.paragraph
      //     if (curRow.row_index_in_para === 0) {
      //       if (index === 0) {
      //         cursor_position += curPara.before_paragraph_spacing * curRow.height
      //       } else {
      //         cursor_position = this.children[i - 1].bottom + curPara.before_paragraph_spacing * curRow.height
      //       }
      //     }
      //   }
      // }
      this.children[i].top = cursor_position;
      this.children[i].cell_index = i;
      cursor_position += this.children[i].height;
    }
    if (parent_table && !nextCell) {
      parent_table.refreshTableRowSize();
    }
  }

  // 改变每一个 row 的 top 值
  changeRowTop() {
    if (!this.parent || this.vertical_align === VerticalAlign.TOP) return;
    const rowHeight =
      this.children[this.children.length - 1].bottom - this.children[0].top; // getRealContentHeight 里边是不是就是这个意思 不用把所有 row 的 height 加一遍
    const cellHeight = this.parent.row_size
      .slice(this.start_row_index, this.end_row_index + 1)
      .reduce((p, c) => p + c, 0);
    let init = 0;
    if (this.vertical_align === VerticalAlign.CENTER) {
      init = (cellHeight - rowHeight) / 2;
    }
    if (this.vertical_align === VerticalAlign.BOTTOM) {
      init = cellHeight - rowHeight;
    }
    for (const row of this.children) {
      row.top = init;
      init += row.height;
    }
  }

  // 修改单个 单元格 某边线的显示隐藏 is_show: 是否显示单元格 边线 true为显示 否则为不显示
  toggleLineShowHide(direction: Direction | null, is_show: boolean) {
    const table = this.parent!;
    const changeOpacityRow = table.notAllowDrawLine.changeOpacityRow;
    const changeOpacityCol = table.notAllowDrawLine.changeOpacityCol;
    if (direction === null || direction === Direction.up) {
      if (!is_show) {
        // 如果不让显示上边线
        for (let i = 0; i < this.colspan; i++) {
          changeOpacityRow.push([this.position[0], this.position[1] + i]);
        }
      } else {
        for (let i = 0; i < this.colspan; i++) {
          for (let j = 0; j < changeOpacityRow.length;) {
            if (
              changeOpacityRow[j][0] === this.position[0] &&
              changeOpacityRow[j][1] === this.position[1] + i
            ) {
              changeOpacityRow.splice(j, 1);
            } else {
              j++;
            }
          }
        }
        // 从 changeOpacityRow 再找到那个下标 删除掉
      }
    }
    if (direction === null || direction === Direction.right) {
      if (!is_show) {
        // 如果不让显示右边线
        for (let i = 0; i < this.rowspan; i++) {
          changeOpacityCol.push([this.end_col_index + 1, this.position[0] + i]);
        }
      } else {
        for (let i = 0; i < this.rowspan; i++) {
          for (let j = 0; j < changeOpacityCol.length;) {
            if (
              changeOpacityCol[j][0] === this.end_col_index + 1 &&
              changeOpacityCol[j][1] === this.position[0] + i
            ) {
              changeOpacityCol.splice(j, 1);
            } else {
              j++;
            }
          }
        }
      }
    }
    if (direction === null || direction === Direction.down) {
      if (!is_show) {
        // 如果不让显示下边线
        for (let i = 0; i < this.colspan; i++) {
          changeOpacityRow.push([this.end_row_index + 1, this.position[1] + i]);
        }
      } else {
        for (let i = 0; i < this.colspan; i++) {
          for (let j = 0; j < changeOpacityRow.length;) {
            if (
              changeOpacityRow[j][0] === this.end_row_index + 1 &&
              changeOpacityRow[j][1] === this.position[1] + i
            ) {
              changeOpacityRow.splice(j, 1);
            } else {
              j++;
            }
          }
        }
        // 从 changeOpacityRow 再找到那个下标 删除掉
      }
    }
    if (direction === null || direction === Direction.left) {
      if (!is_show) {
        // 如果不让显示右边线
        for (let i = 0; i < this.rowspan; i++) {
          changeOpacityCol.push([this.position[1], this.position[0] + i]);
        }
      } else {
        for (let i = 0; i < this.rowspan; i++) {
          for (let j = 0; j < changeOpacityCol.length;) {
            if (
              changeOpacityCol[j][0] === this.position[1] &&
              changeOpacityCol[j][1] === this.position[0] + i
            ) {
              changeOpacityCol.splice(j, 1);
            } else {
              j++;
            }
          }
        }
      }
    }
  }

  /**
   * @param split_line 表格分割线 距离该单元格 顶部边界的距离
   * @returns
   */
  // TODO 感觉应该是判断 是否将该单元格进行软分割 为什么只考虑了第一行
  ifHardSplit(split_line: number) {
    const first_child = this.children[0];
    if (!first_child) return false;

    const top = 0;
    let bottom = first_child.bottom;
    // 对于上下居中的单元格，则要按照不居中时来判断是否需要硬分割
    if (first_child.top !== 0) {
      bottom = first_child.bottom - first_child.top;
    }

    return isRow(first_child) && top < split_line && bottom > split_line;
  }

  /**
   * 获取根单元格对象
   * @returns 单元格分割前的单元格对象
   */
  getOrigin(): Cell {
    let origin = this.origin || this;

    while (origin.origin) {
      origin = origin.origin;
    }

    return origin;
  }

  /**
   * 重置分割属性
   */
  resetSplitAttr() {
    this.page_break = false;
    this.split_parts = [];
    this.split_row_indexes = [];
  }

  // TODO 可能有错
  /**
   * 单元格在页面断页处，需要拆分成两个
   * @param split_line 断页线在表格中的位置
   * @param split_row_index 被分割的行号
   * @param parent1 被拆分时上面的表格的父元素
   * @param parent2 被拆分时下面的表格的父元素
   * @param type 拆分类型 "soft" | "hard"
   */
  split(
    split_line: number,
    split_row_index: number,
    parent1: Table,
    parent2: Table,
    type = BreakType.soft,
    unassignedRowSize?: number[]
  ): Cell[] {
    let top;
    if (unassignedRowSize) {
      top =
        this.position[0] === 0
          ? 0
          : unassignedRowSize
            .slice(0, this.position[0])
            .reduce((total, current) => total + current);
    } else {
      top = this.top;
    }
    const cell_split_line = split_line - top;

    const cell1 = new Cell(
      this.editor,
      this.position,
      this.colspan,
      this.rowspan,
      parent1
    );
    const cell2 = new Cell(
      this.editor,
      this.position,
      this.colspan,
      this.rowspan,
      parent2
    );
    if (this.editor.document_meta.handleTableRowSize) {
      cell1.padding_top = this.padding_top;
      cell2.padding_bottom = this.padding_bottom;
    }

    cell1.is_show_slash_up = cell2.is_show_slash_up = this.is_show_slash_up;
    cell1.is_show_slash_down = cell2.is_show_slash_down =
      this.is_show_slash_down;

    cell1.colspan = cell2.colspan = this.colspan;
    cell2.rowspan = cell2.rowspan = this.rowspan;
    cell1.style = cell2.style = this.style;

    const origin = this.getOrigin();

    cell1.position = [...this.position];
    cell2.position = [0, this.position[1]];

    if (type === BreakType.soft) {
      cell1.rowspan = split_row_index - this.position[0] + 1;
      cell2.rowspan = this.rowspan + 1 - cell1.rowspan;
      // 硬分割时，分界线在split_row_index上方
    } else {
      cell1.rowspan = split_row_index - this.position[0];
      cell2.rowspan = this.rowspan - cell1.rowspan;
    }
    for (let i = 0; i < this.children.length; i++) {
      const child = this.children[i] as Row; // 暂时视为row

      if (child.bottom <= cell_split_line) {
        const new_row = child.copy(cell1);
        new_row.id = child.id;
        cell1.children.push(new_row);
      }
      // 当child 的top值与cell_split_line相等时，也需要设置split_row_indexes值，否则路径转换出错
      if (
        (child.top <= cell_split_line && child.bottom > cell_split_line) ||
        child.top > cell_split_line
      ) {
        const new_row = child.copy(cell2);
        new_row.id = child.id;
        cell2.children.push(new_row);
      }
    }
    this.resetSplitCell(cell2);

    if (this.page_break) {
      // if (EditorLocalTest.transUse) {
      //   origin.split_parts.push(cell1, cell2); // 这里全都 push 进去,在外层再根据需要删除一个
      // } else {
      origin.split_parts.splice(-1, 1, cell1, cell2);
      // }
    } else {
      origin.split_parts = [cell1, cell2];
    }

    cell1.generateParagraphByRow();
    cell2.generateParagraphByRow();
    cell1.origin = cell2.origin = origin;
    cell1.page_break = cell2.page_break = true;
    return [cell1, cell2];
  }

  /**
   * 重置分割行下标
   */
  resetSplitRowIndexs() {
    const originCell = this.getOrigin();
    originCell.split_row_indexes = [];
    if (originCell.split_parts.length) {
      let last_length = 0;
      for (let i = 0; i < originCell.split_parts.length - 1; i++) {
        const childrenLength = originCell.split_parts[i].children.length;
        last_length += childrenLength;
        originCell.split_row_indexes.push(last_length);
      }
    }
  }

  /**
   * 处理该单元格内文本域的赋值
   * @param fields 新赋值的文本域
   */
  handleFieldsAssignment(fields: XField[]) {
    this.fields = [...fields];
    this.fields.forEach((field) => {
      field.cell = this;
    });
  }

  /**
   * 单元格拆分功能
   * @param parent
   */
  splitCell(parent: Table, is_not_real_split_cell: boolean = false): Cell[] {
    // 配合mergeCell 如果不能拆分就返回自己
    if (this.rowspan === 1 && this.colspan === 1) {
      return [this];
    }
    const cells: Cell[] = [];
    const should_total_cells_num = this.rowspan * this.colspan;
    const init_position = this.position;
    let posi1 = init_position[0];
    let posi2 = init_position[1];
    const initPosi2 = posi2;
    for (let i = 0; i < should_total_cells_num; i++) {
      const tmpCell = new Cell(
        this.editor,
        [posi1, posi2],
        this.colspan,
        this.rowspan,
        parent
      );
      tmpCell.is_show_slash_up = this.is_show_slash_up;
      tmpCell.is_show_slash_down = this.is_show_slash_down;

      posi2++;
      if (posi2 >= this.colspan + initPosi2) {
        // 说明位置该换行了
        posi1++;
        posi2 = initPosi2;
      }
      tmpCell.colspan = 1;
      tmpCell.rowspan = 1;
      if (i !== 0) {
        // 第一个单元格要插入内容
        tmpCell.insertEmptyParagraph();
      }
      cells.push(tmpCell);
    }
    // 要找到要的线 从notAllowDrawLine.row/col里边删除掉 ↓
    if (is_not_real_split_cell) {
      return cells; // 如果不是真正的拆分单元格的操作 就不用操作表格线
    }
    const first_row = this.start_row_index;
    const last_row = this.end_row_index;
    const first_col = this.start_col_index;
    const last_col = this.end_col_index;
    const rows = this.parent!.notAllowDrawLine.row;
    const cols = this.parent!.notAllowDrawLine.col;
    // 循环rows 从rows里删除
    for (let i = 0; i < rows.length;) {
      const numbers = rows[i];
      const row = numbers[0];
      const col = numbers[1];
      if (
        row > first_row &&
        row <= last_row &&
        col >= first_col &&
        col <= last_col
      ) {
        rows.splice(i, 1);
      } else {
        i++;
      }
    }
    // 循环cols 从cols里删除
    for (let i = 0; i < cols.length;) {
      const numbers = cols[i];
      const col = numbers[0];
      const row = numbers[1];
      if (
        col > first_col &&
        col <= last_col &&
        row >= first_row &&
        row <= last_row
      ) {
        cols.splice(i, 1);
      } else {
        i++;
      }
    }
    cells[0].handleFieldsAssignment(this.fields);
    // 要找到要的线 ↑
    return cells;
  }

  // 获取该单元格 上下左右 四条边线的编号集合
  getNumbersOfFourLine(): {
    top: number[][];
    right: number[][];
    bottom: number[][];
    left: number[][];
    } {
    const top = [];
    const bottom = [];
    const left = [];
    const right = [];
    const start_row_index = this.start_row_index;
    const start_col_index = this.start_col_index;
    const end_row_index = this.end_row_index;
    const end_col_index = this.end_col_index;
    for (let i = 0; i < this.colspan; i++) {
      top.push([start_row_index, i + start_col_index]);
      bottom.push([end_row_index + 1, i + start_col_index]);
    }
    for (let i = 0; i < this.rowspan; i++) {
      left.push([start_col_index, i + start_row_index]);
      right.push([end_col_index + 1, i + start_row_index]);
    }
    return {
      top,
      right,
      bottom,
      left,
    };
  }

  // 获取该单元格 内部所有线的编号
  getInsideLineNumber() {
    const res_rows: number[][] = [];
    const res_cols: number[][] = [];
    // 要找到要的线 从notAllowDrawLine.row/col里边删除掉 ↓
    const first_row = this.start_row_index;
    const last_row = this.end_row_index + 1;
    const first_col = this.start_col_index;
    const last_col = this.end_col_index + 1;

    for (let i = 0; i < this.rowspan; i++) {
      const row = first_row + i;
      for (let j = 0; j < this.colspan; j++) {
        const col = first_col + j;
        if (
          row > first_row &&
          row < last_row &&
          col >= first_col &&
          col <= last_col
        ) {
          res_rows.push([row, col]);
        }
      }
    }

    for (let i = 0; i < this.colspan; i++) {
      const col = first_col + i;
      for (let j = 0; j < this.rowspan; j++) {
        const row = first_row + j;
        if (
          col > first_col &&
          col < last_col &&
          row >= first_row &&
          row <= last_row
        ) {
          res_cols.push([col, row]);
        }
      }
    }

    return {
      res_rows,
      res_cols,
    };
  }

  /**
   * 重置分割的单元格
   * @param cell 目标单元格
   */
  resetSplitCell(cell: Cell) {
    let cursor_position = 0;

    for (let j = 0; j < cell.children.length; j++) {
      const child = cell.children[j]; // TODO 暂时不考虑嵌套表格

      child.top = cursor_position;

      cursor_position += child.height;
    }
  }

  /**
   * 复制自己
   * @param parent 该单元格属表格
   * @returns 复制的单元格
   */
  copy(parent: Table | null = null, changeId: boolean = true) {
    const cell = new Cell(
      this.editor,
      this.position,
      this.colspan,
      this.rowspan,
      parent,
      this.id // 用于匹配对应的单元格
    );
    // 单元格属性赋值
    Cell.attrJudgeUndefinedAssign(cell, this);
    const canNotBeCopiedFieldId: any = [];
    this.paragraph.forEach((para) => {
      if (isParagraph(para)) {
        // 因为此处不需要先进行createRow，所以未直接使用copy
        const characters = para.characters.map((c) => c.copy());
        for (let i = characters.length - 1; i >= 0; i--) {
          const char = characters[i];
          if (char.field_id) {
            if ((canNotBeCopiedFieldId as any).includes(char.field_id)) {
              characters.splice(i, 1);
              continue;
            }
            const parentField = this.editor.getFieldById(char.field_id);
            if (parentField && !parentField.canBeCopied) {
              canNotBeCopiedFieldId.push(char.field_id);
              characters.splice(i, 1);
            }
          }
        }
        let p;
        if (changeId) {
          p = new Paragraph(uuid("para"), cell);
        } else {
          p = new Paragraph(para.id, cell);
        }
        p.setParagraphAttr(para);
        p.title_length = para.title_length;
        p.characters = [...characters];
        cell.paragraph.push(p);
      } else {
        const new_para = para.copy(cell);
        new_para.refreshTableRowSize();
        // cell.children.push(new_para as Table); // 不能在这儿往cell.children 里边放入表格
        cell.paragraph.push(new_para as Table);
      }
    });
    // 先处理文本域再createRow,否则分散对齐会有问题
    this.handleCellFieldsByPara(cell, parent);
    cell.paragraph.forEach((para) => {
      if (isParagraph(para)) {
        // 因为该函数中使用到了文本域相关信息，所以该函数的调用放在文本域处理逻辑之后。
        para.createRow();
        cell.children.push(...para.children);
      } else {
        cell.children.push(para); // 已经在上边forEach循环里边 else内 copy过了 所以这里就不用copy了
      }
    });
    cell.updateParaIndex();
    let nextCell;
    if (this.parent) {
      const index = this.parent.children.findIndex((c) => c === this);
      if (index > -1) {
        nextCell = this.parent.children[index + 1];
      }
    }
    cell.updateRowBounding(0, nextCell);
    // 将id重新赋值;
    cell.children.forEach((row, index) => {
      row.id = this.children[index].id;
    });
    return cell;
  }

  // 简单 copy 初次创建是表格分页时使用
  simpleCopy(parentTable: Table) {
    const copiedCell = new Cell(
      this.editor,
      [...this.position],
      this.colspan,
      this.rowspan,
      parentTable,
      this.id
    );
    Cell.attrJudgeUndefinedAssign(copiedCell, this);
    copiedCell.children = this.children;
    copiedCell.paragraph = this.paragraph;
    copiedCell.origin = this.getOrigin();
    copiedCell.page_break = this.page_break;
    return copiedCell;
  }

  /**
   * 根据段落组成文本域
   */
  handleCellFieldsByPara(cell: Cell, parent: Table | null) {
    const paragraphs = cell.paragraph;
    const fieldMap: any = {};
    this.getOrigin().fields.forEach((field: XField | BoxField) => {
      // 因为后续字符全部使用段落中的，所以此处使用简单复制，节省性能
      const copy_field = parent ? field.copy(cell) : field.simpleCopy(cell);
      cell.fields.push(copy_field);
      fieldMap[field.id] = copy_field;
    });
    paragraphs.forEach((para) => {
      if (isParagraph(para)) {
        para.characters.forEach((item) => {
          if (item.field_id) {
            const ori_field = this.getOrigin().getFieldById(item.field_id)!;
            const cur = fieldMap[item.field_id];
            const p_field = ori_field.parent
              ? fieldMap[ori_field.parent.id]
              : null;
            if (p_field && !p_field.children.includes(cur)) {
              p_field.children.push(cur);
              cur.parent = p_field; // 很重要
            }
            if (item.field_position === "start") {
              cur.start_sym_char = item;
            } else if (item.field_position === "end") {
              cur.end_sym_char = item;
            } else if (item.field_position === "placeholder") {
              if (
                cur.placeholder_characters.length === cur.placeholder.length
              ) {
                cur.placeholder_characters = [];
              }
              cur.placeholder_characters.push(item);
            } else {
              cur.children.push(item);
            }
          }
        });
      }
    });
  }

  // 竖线左右拖动的时候重新排版,合并拆分
  typesetting(nextCell?: Cell) {
    this.scroll_cell_top = 0;
    for (let i = 0; i < this.paragraph.length; i++) {
      const paragraph = this.paragraph[i];
      if (isParagraph(paragraph)) {
        paragraph.createRow();
      }
    }
    this.updateParaToCell(nextCell);
  }

  /**
   * 将修改后的段落内容更新到cell上
   */
  updateParaToCell(nextCell?: Cell) {
    const rows = [];
    for (const para of this.paragraph) {
      if (isParagraph(para)) {
        rows.push(...para.children);
      } else {
        rows.push(para);
      }
    }
    this.children = rows;
    this.updateParaIndex();
    this.updateRowBounding(0, nextCell);
  }

  getCellMinSize() {
    let cell_min_size: number = 0;
    for (let i = 0; i < this.paragraph.length; i++) {
      const para = this.paragraph[i] as Paragraph;
      const row_min_width =
        para.characters[0].width + para.children[0].padding_left;
      if (para.islist && row_min_width > cell_min_size) {
        cell_min_size = row_min_width;
      }
    }
    return cell_min_size;
  }

  /**
   * 调整单元格尺寸 如果是分页的话 此时的 cell 是分页后 split_parts 中的单元格 不是 modelData 中的数据
   * left_col 距离拖动线最近的左侧的表格线
   * right_col 距离拖动线最近的右侧的表格线
   * Direction.down 鼠标落在了线的上方
   */
  resize(cell_position: Direction, left_col: number, right_col: number) {
    // 调用该方法的单元格 就是鼠标拖动线的时候所在的单元格
    const origin_cell = this.getOrigin();
    const resize_row_index =
      cell_position === Direction.down
        ? origin_cell.end_row_index
        : origin_cell.position[0] - 1; // 获取需要调整位置的边线

    // 计算要调整的竖线的下标
    const resize_col_index =
      cell_position === Direction.left // 该单元格在拖动线的左侧 就是该单元跟的末尾列
        ? origin_cell.end_col_index
        : origin_cell.position[1] - 1; // 该单元格在拖动线的右侧 就是该单元格的起始列
    // 上下拖动和左右拖动区分开
    if (cell_position === Direction.down || cell_position === Direction.up) {
      const table = origin_cell.parent!;
      if (table.split_parts.length === 0) {
        table.resizeRowSizeByRowIndex(resize_row_index);
      } else {
        const split_row_index =
          cell_position === Direction.down
            ? this.end_row_index
            : this.position[0] - 1;
        const now_talbe = this.parent!; // 被拆分后的 拖动表格线的 当前页的表格
        now_talbe.resizeRowSizeByRowIndex(split_row_index);
      }
    } else {
      origin_cell.parent!.resizeColSizeByColIndex(
        resize_col_index,
        left_col,
        right_col
      );
    }
  }

  /**
   * 清空当前cell内容,只保留一个换行符
   */
  clear() {
    this.fields = [];
    this.paragraph.splice(1);
    const first_paragraph = this.paragraph[0] as Paragraph;
    first_paragraph.characters = [first_paragraph.characters.pop()];
    first_paragraph.characters[0]?.field_id &&
      (first_paragraph.characters[0].field_id = null);
    // TODO 不加参数 就有问题
    first_paragraph.updateChildren(0, this.children.length);
  }

  /**
   * 删除单个元素逻辑
   * @param para_path 段落坐标
   */
  deleteElement(
    para_path: number[],
    selection: XSelection
  ): boolean | Path | undefined {
    const start_para_index = para_path[para_path.length - 2]; // 开始段落下标 因为有可能是[段落,文字]或者[表格，cell，段落，文字] 所以用.length减2
    const start_char_index = para_path[para_path.length - 1]; // 开始字符所在段落下标
    // 当前段落数据信息
    const cur_paragraph = this.paragraph[start_para_index] as Paragraph;
    // cell最头部 并且不是list类型
    if (PathUtils.isStartPathInCell(para_path) && !cur_paragraph.islist) {
      return false;
    }
    // 当前段落上一段落,因为不是在单元格最头部，所以此处一定有上一个段落
    const previous_paragraph = cur_paragraph.previousParagraph as Paragraph;
    // 在段落开始位置，并且上一段分组与当前组不在一组或者上一段为表格则停止删除
    if (
      PathUtils.isStartPathInPara(para_path) &&
      (previous_paragraph.group_id !== cur_paragraph.group_id ||
        isTable(previous_paragraph))
    ) {
      return false;
    }
    // 判断是否为段首位置
    if (PathUtils.isStartPathInPara(para_path)) {
      let cur_field: XField | null = null;
      // 将对应的文本域中的字符删除
      if (isParagraph(previous_paragraph)) {
        // 获取需要删除的换行符
        const cur_character =
          previous_paragraph.characters[
            previous_paragraph.characters.length - 1
          ];
        if (cur_character.field_id) {
          // 删除文本域中的换行符
          cur_field = this.getFieldById(cur_character.field_id)!;
          if (cur_field.isReadonly) {
            this.editor.event.emit("message", editor_prompt_msg.readonly_field);
            return para_path;
          }
          cur_field.removeElement(cur_character);
        }
      }

      const ori_char_index = previous_paragraph.characters.length - 1;
      const characters = cur_paragraph.characters;
      // 将当前段全部删除
      cur_paragraph.remove();
      // 合并字符时需要移除上一段落末尾的换行符
      previous_paragraph.spliceCharactersByPath(
        para_path,
        para_path,
        characters
      );
      // 如果实在分组中删除，则将分组信息刷新
      if (previous_paragraph.group_id) {
        const group = this.getGroupById(previous_paragraph.group_id);
        group && group.refreshContentParaId();
      }
      // 合并后的坐标位置
      para_path[para_path.length - 2] = previous_paragraph.para_index;
      para_path[para_path.length - 1] = ori_char_index;

      // 如果是输入域中内容删除操作则判断是否还有子内容，如果没有则重新展示背景文本
      if (cur_field && !cur_field.children.length) {
        cur_field.reShowPlaceholder();
      }
    } else {
      // 获取需要删除的字符
      const cur_character = cur_paragraph.characters[start_char_index - 1];
      // 如果是删除文本域中的字符
      if (cur_character.field_id) {
        const resInfo = this.deleteElementInField(
          cur_character,
          cur_paragraph,
          para_path,
          selection
        );
        return resInfo;
      } else if (
        selection.editor.view_mode !== "form" ||
        this.parent?.editableInFormMode ||
        this.editor.adminMode
      ) {
        // 普通段落中字符删除
        cur_paragraph.spliceCharactersByPath(para_path, para_path);
        PathUtils.movePathCharNum(para_path, -1);
      }
    }
    return para_path;
  }

  /**
   * 删除文本域中的单个字符
   * @param cur_character 要删除的字符
   * @param cur_paragraph 要删除的字符所在的段落
   * @param para_path 选区路径
   * @param selection 选区
   */
  deleteElementInField(
    cur_character: Character,
    cur_paragraph: Paragraph,
    para_path: Path,
    selection: XSelection
  ): Path | undefined | boolean {
    const field_id: string = cur_character.field_id!;
    const cur_field = this.getFieldById(field_id)!;
    if (selection.editor.view_mode === "form" && !selection.editor.adminMode) {
      const res_success = this.validateDeleteFormMode(
        null,
        null,
        cur_character
      );
      if (!res_success) {
        return;
      }
    }
    if (cur_character.field_position !== "normal") {
      // 如果文本域是只读则将文本域看成一个整体进行删除, 光标定位到文本域开始位置时不能删除
      if (
        cur_field.readonly &&
        !cur_field.parent?.isReadonly &&
        cur_character.field_position !== "start"
      ) {
        if (!cur_field.deletable) {
          this.editor.event.emit(
            "message",
            editor_prompt_msg.cur_field_forbid_del
          );
          return para_path;
        }
        return cur_field.remove(selection);
      }
      // 当前输入域存在子内容或者当前字符为文本域开始字符
      if (
        cur_field.children.length ||
        cur_character.field_position === "start"
      ) {
        // 继续找到前一个能够删除的字符
        PathUtils.movePathCharNum(para_path, -1);
        return this.deleteElement(para_path, selection);
      }
      // 当前文本域无内容并且当前字符为末尾字符
      if (
        !cur_field.children.length &&
        cur_character.field_position === "end"
      ) {
        if (!cur_field.deletable) {
          this.editor.event.emit(
            "message",
            editor_prompt_msg.cur_field_forbid_del
          );
          return para_path;
        }
        para_path = [...cur_field.start_para_path];
        // 删除整个文本域
        cur_field.remove(selection);
      }
    } else {
      if (cur_field.isReadonly && !this.editor.adminMode) {
        if (cur_field.type === "label") {
          if (!cur_field.deletable) {
            this.editor.event.emit("message", editor_prompt_msg.forbid_del_field);
            return false;
          }
        } else {
          this.editor.event.emit("message", editor_prompt_msg.readonly_field);
          return false;
        }
        return para_path;
      }
      cur_field.removeElement(cur_character);
      // 普通段落中字符删除
      cur_paragraph.spliceCharactersByPath(para_path, para_path);
      PathUtils.movePathCharNum(para_path, -1);
      if (!cur_field.children.length) {
        cur_field.reShowPlaceholder();
      }
    }
    return para_path;
  }

  /**
   * 表单模式下的删除验证
   */
  validateDeleteFormMode(
    field_chars: any,
    all_chars: any,
    cur_char?: any
  ): boolean {
    // 如果不是选区模式,判断当前要删除的char是否为开始字符
    if (cur_char) {
      if (
        cur_char.field_position === "start" ||
        cur_char.field_position === "end"
      ) {
        // 判断是否为最外层，如果是则不能再删除
        const cur_field = this.getFieldById(cur_char.field_id);
        if (cur_field?.cell && isTable(cur_field.cell.parent) && cur_field.cell.parent.editableInFormMode) {
          return true;
        }
        if (!cur_field?.parent) {
          return false;
        }
      }
    } else if (all_chars && all_chars.length) {
      // 选中的不全是文本域字符时
      if (field_chars.length !== all_chars.length && field_chars.length === 0) {
        return false;
      }

      // 选中的是全是文本域字符，但是文本域为label，并且不在文本域中时
      const field = this.getFieldById(field_chars[0].field_id)!;
      if (field) {
        if (!field.parent) {
          if (field.type === "label" || field.isCompleteField(all_chars)) {
            return false;
          }
        }
      } else {
        return false;
      }
    }
    return true;
  }

  /**
   * 选区删除前验证并且将文本域对象中数据修改
   * @param selection
   */
  beforeDeleteSelFieldInfo(
    selection: XSelection,
    selected_info: any
  ): {
    success: boolean;
    start_field: XField | null;
    end_field: XField | null;
  } {
    // 删除文本域信息返回值信息
    const resInfo: {
      success: boolean;
      start_field: XField | null;
      end_field: XField | null;
    } = {
      success: true, // 文本域信息是否删除成功，默认为true,删除异常时改为false
      start_field: null, // 开始的文本域信息
      end_field: null, // 结束的文本域信息
    };
    const { fieldIdVsChars, field_chars, all_chars } = selected_info;
    // 选区删除(因为此处有可能是其他方法调用)
    if (selection.isCollapsed) {
      return resInfo;
    }
    if (field_chars.length) {
      const start_field = (resInfo.start_field = this.getFieldById(
        all_chars[0].field_id
      ));
      const end_char = all_chars[all_chars.length - 1];
      resInfo.end_field = this.getFieldById(end_char.field_id);
      const isValiSuccess = this.validateSelectedFields(fieldIdVsChars);
      if (!isValiSuccess) {
        resInfo.success = false;
        return resInfo;
      }
      for (const fieldId in fieldIdVsChars) {
        const field = this.getFieldById(fieldId);
        if (!field) continue;
        if (field.isCompleteField(field_chars)) {
          field.remove();
          if (start_field === field) {
            resInfo.start_field = field.parent;
          }
          if (resInfo.end_field === field) {
            resInfo.end_field = field.parent;
          }
        } else {
          const chars = fieldIdVsChars[fieldId];
          for (let i = 0; i < chars.length; i++) {
            // TODO 文本域中选区末尾如果是换行符，选区字符中没有该换行符导致删除不掉的问题（共3处，第3处，需注释以下代码）
            // 当选区末尾选中了换行符，此时段落中是不将末尾换行符删除的，所以文本域中也不能将此换行符删除。（换行符不是文本域最后一个字符时）
            if (
              end_char.value === "\n" &&
              chars[i] === end_char &&
              field.children[field.children.length - 1] !== end_char
            ) {
              continue;
            }
            field.removeElement(chars[i]);
          }
        }
      }
    }
    return resInfo;
  }

  /**
   * 选区段落中内容删除后,将其中多删除的文本域开始或结尾字符还原
   * @param start_field
   * @param end_field
   * @param fieldChars
   * @param start_paragraph
   * @param end_paragraph
   * @param res_path
   */
  afterDeleteResSymbol(
    start_field: XField | null,
    end_field: XField | null,
    fieldChars: [],
    start_paragraph: Paragraph | Table | null,
    end_paragraph: Paragraph | Table | null,
    res_path: Path
  ) {
    // 记录多删除的文本域边框字符，用于恢复
    const need_re_ins_symbols = [];

    if (start_field) {
      const parents = start_field.getAllParents();
      for (let i = 0; i < parents.length; i++) {
        const p_field = parents[i] as XField;
        if (
          p_field.hasEndSymInChars(fieldChars) &&
          !p_field.isCompleteField(fieldChars)
        ) {
          need_re_ins_symbols.push(p_field.end_sym_char);
        }
      }
    }
    if (end_field) {
      const parents = end_field.getAllParents();
      for (let i = parents.length - 1; i >= 0; i--) {
        const p_field = parents[i] as XField;
        if (
          p_field.hasStartSymInChars(fieldChars) &&
          !p_field.isCompleteField(fieldChars)
        ) {
          need_re_ins_symbols.push(p_field.start_sym_char);
        }
      }
    }
    if (need_re_ins_symbols.length) {
      if (isParagraph(start_paragraph)) {
        start_paragraph.characters.splice(
          res_path[res_path.length - 1],
          0,
          ...need_re_ins_symbols
        );
        start_paragraph.updateChildren();
      } else if (isParagraph(end_paragraph)) {
        end_paragraph.characters.splice(
          res_path[res_path.length - 1],
          0,
          ...need_re_ins_symbols
        );
        end_paragraph.updateChildren();
      }
    }
    if (start_field && !start_field.children.length) {
      start_field.reShowPlaceholder();
    }
    if (end_field && !end_field.children.length && end_field !== start_field) {
      end_field.reShowPlaceholder();
    }
  }

  /**
   * 根据开始与结束坐标删除元素
   * @param para_start_path
   * @param para_end_path
   * @param isTableInner 是否为表格内操作
   */
  deleteBySelection(
    para_start_path: number[],
    para_end_path: number[],
    selection: XSelection
  ): Path | undefined {
    const selected_info = selection.selected_fields_chars;
    // 表单模式下 部分单元格要能够编辑 已经处理过了选中多个单元格删除的逻辑 这里就处理好 单个单元格内的删除逻辑就可以了
    // 逻辑是：如果在单个单元格内 而且该单元格还允许编辑 那么就不进这个判断
    if (
      selection.editor.view_mode === "form" &&
      !selection.editor.adminMode &&
      (PathUtils.isSameCell(para_start_path, para_end_path)
        ? !this.parent?.editableInFormMode
        : true)
    ) {
      // 表单模式下删除验证
      const res_success = this.validateDeleteFormMode(
        selected_info.field_chars,
        selected_info.all_chars
      );
      if (!res_success) {
        return;
      }
    }
    // 验证选区内是否存在多个分组
    if (
      selected_info.paragraphs &&
      !this.validateSelectedGroups(selected_info.paragraphs, selection)
    ) {
      return;
    }

    // 选区内 如果有 禁止删除的表格 就不能继续删除 ↓↓↓↓↓↓↓↓↓↓
    const selected_cells = selection.selected_cells;
    for (const { cell } of selected_cells) {
      const table = cell.parent as Table;
      if (table.isCanNotDelTbl) {
        return;
      }
    }
    // 选区内 如果有 禁止删除的表格 就不能继续删除 ↑↑↑↑↑↑↑↑↑↑↑

    // 先处理文本域
    const { success, start_field, end_field } = this.beforeDeleteSelFieldInfo(
      selection,
      selected_info
    );
    if (!success) {
      return para_start_path;
    }
    const { start_paragraph, end_paragraph, res_path } =
      this.handleDeleteParagraphs(
        para_start_path,
        para_end_path,
        selected_info.end_linebreak
      );
    this.afterDeleteResSymbol(
      start_field,
      end_field,
      selected_info.field_chars,
      start_paragraph,
      end_paragraph,
      res_path
    );
    // all_chars可能不存在（6.28修改表单模式下删除文本域报错），  获取选区中的所有字符
    const all_chars = selected_info.all_chars;
    if (
      selection.editor.view_mode === "form" &&
      !selection.editor.adminMode &&
      all_chars &&
      all_chars[0]?.field_position === "start"
    ) {
      res_path[res_path.length - 1] += 1;
    }
    return res_path;
  }

  /**
   * 删除时处理段落信息
   * @param para_start_path
   * @param para_end_path
   * @param end_linebreak 是否选中末尾换行符
   */
  handleDeleteParagraphs(
    para_start_path: Path,
    para_end_path: Path,
    end_linebreak: boolean = false
  ): {
    start_paragraph: Paragraph | Table | null;
    end_paragraph: Paragraph | Table | null;
    res_path: Path;
  } {
    let res_path = [...para_start_path];
    let start_para_index = para_start_path[para_start_path.length - 2]; // 开始段落下标
    let end_para_index = para_end_path[para_end_path.length - 2]; // 结束段落下标
    if (!this.parent) {
      start_para_index = para_start_path[0];
      end_para_index = para_end_path[0];
    }
    // 开始段落数据信息
    let start_paragraph: Table | Paragraph | null =
      this.paragraph[start_para_index];
    let end_paragraph: Table | Paragraph | null =
      this.paragraph[end_para_index];
    // 如果开始段落为
    if (isTable(start_paragraph)) {
      res_path = [para_start_path[0], 0];
      // 当开始为表格并且结束为普通段落时不截取最后一段
      if (isParagraph(end_paragraph)) {
        end_para_index--;
      }
    } else {
      // 如果开始在段首并且最后包含换行符，并且当前段落不在表格中间、开始段落不为第一个段落、结束段落不为最后一个段落
      // 并且开始段落首字符与结束段落末尾字符文本域id相同且都是普通字符时 则将整段删除
      if (
        !(
          end_linebreak &&
          !start_paragraph.group_id &&
          PathUtils.isStartPathInPara(para_start_path) &&
          start_paragraph.previousParagraph &&
          isParagraph(end_paragraph) &&
          end_paragraph.nextParagraph &&
          isParagraph(end_paragraph.nextParagraph) &&
          start_paragraph.characters[0].field_id ===
          end_paragraph.lastCharacter.field_id &&
          start_paragraph.characters[0].field_position === "normal" &&
          end_paragraph.lastCharacter.field_position === "normal"
        )
      ) {
        start_para_index++;
      }
    }
    // 在最后一段删除前将其剩余字符截取出来
    let characters: Character[] = [];
    if (isParagraph(end_paragraph)) {
      const end_char_index = para_end_path[para_end_path.length - 1]; // 结束字符所在段落下标
      // TODO 文本域中选区末尾如果是换行符，选区字符中没有该换行符导致删除不掉的问题（共3处，第2处）
      // if (end_linebreak) {
      //   end_char_index++;
      // }
      characters = end_paragraph.characters.slice(end_char_index);
    }
    // 开始与结束位置所有的段落信息
    const paragraphs = this.paragraph.slice(
      start_para_index,
      end_para_index + 1
    );
    // 遍历段落
    for (let i = 0; i < paragraphs.length; i++) {
      const paragraph = paragraphs[i];
      paragraph === start_paragraph && (start_paragraph = null);
      paragraph === end_paragraph && (end_paragraph = null);
      paragraph.remove();
    }
    if (isParagraph(start_paragraph)) {
      start_paragraph.spliceCharactersByPath(
        para_start_path,
        para_end_path,
        characters
      );
    } else if (isParagraph(end_paragraph)) {
      // 表格下方是右对齐的签名时 删除表格和下行空白 导致程序崩溃bug 修复 ↓↓↓↓↓↓↓↓↓↓↓
      if (para_start_path.length === 4) {
        para_start_path.fill(0);
      }
      // 表格下方是右对齐的签名时 删除表格和下行空白 导致程序崩溃bug 修复 ↑↑↑↑↑↑↑↑↑↑↑
      end_paragraph.spliceCharactersByPath(
        para_start_path,
        para_end_path,
        characters
      );
    }
    // 刷新分组内对应的段落id
    if (start_paragraph && start_paragraph.group_id) {
      const group = this.getGroupById(start_paragraph.group_id);
      group && group.refreshContentParaId();
    }
    return { start_paragraph, end_paragraph, res_path };
  }

  /**
   * 检验选区内容是否在同一分组内,并判断分组数据是否允许删除
   * @param paragraphs
   */
  validateSelectedGroups(paragraphs: Paragraph[], selection: XSelection) {
    let group_ids = paragraphs.map((container) => {
      if (container.cell.parent) {
        return container.cell.parent.group_id;
      } else {
        return container.group_id;
      }
    });
    // 去重
    group_ids = Array.from(new Set(group_ids));
    if (group_ids.length === 1) {
      // 如果分组内只有一组
      if (group_ids[0]) {
        const group = selection.getGroupByGroupId(group_ids[0]);
        if (group?.lock && !this.editor.adminMode) {
          return false;
        }
      }
      return true;
    }
  }

  /**
   * 校验选中的文本域
   * @param selectedFields
   * @return true验证通过 false 验证未通过
   */
  validateSelectedFields(selectedFields: any): boolean {
    for (const field_id in selectedFields) {
      const field = this.editor.getFieldById(field_id)!;
      // 目前能获取到表格中的文本域，暂不处理
      if (!field) {
        continue;
      }
      const chars = selectedFields[field_id];
      const isComplete = field.isCompleteField(chars);
      if (isComplete && !field.deletable && !this.editor.adminMode) {
        this.editor.event.emit("message", editor_prompt_msg.forbid_del_field);
        return false;
      }
      if (!isComplete && field.isReadonly && !this.editor.adminMode) {
        this.editor.event.emit(
          "message",
          editor_prompt_msg.readonly_field_may_father
        );
        return false;
      }
    }
    return true;
  }

  /**
   *单元格分割后根据行生成段落l
   */
  generateParagraphByRow() {
    const rows = this.children;
    const paragraphs: Paragraph[] = [];
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i] as Row;
      const rowPara = row.paragraph;
      if (i === 0 || (rows[i - 1] as Row).linebreak) {
        const para = new Paragraph(uuid("para"), this, rowPara.group_id);
        para.setParagraphAttr(rowPara);
        paragraphs.push(para);
      }
      if (row.children.length) {
        paragraphs[paragraphs.length - 1].characters.push(...row.children);
        paragraphs[paragraphs.length - 1].children.push(row);
      }
      if (row.linebreak) {
        paragraphs[paragraphs.length - 1].characters.push(row.linebreak);
      }
    }
    this.paragraph = paragraphs;
  }

  // 绘制单元格的四条边线 暂时别删除
  // drawLine (color: string, alpha: number) {
  //   // 绘制上边线
  //   if (this.is_show_line_top) {
  //     Renderer.draw_line([0, 0], [this.width, 0], color, alpha, this.top === 0 ? 1 : 0.5);
  //   }

  //   // 绘制右边线 /* 最右边的单元格 才能绘制右边线 */
  //   if (this.is_show_line_right /* && this.right === this.parent?.width */) {
  //     Renderer.draw_line(
  //       [this.width, 0],
  //       [this.width, this.height],
  //       color,
  //       alpha,
  //       this.right === this.parent?.width ? 1 : 0.5
  //     );
  //   }

  //   // 绘制下边线 /** 表格最下边的单元格 才能绘制下边线 */
  //   if (this.is_show_line_bottom /* && this.bottom === this.parent?.height */) {
  //     Renderer.draw_line(
  //       [0, this.height],
  //       [this.width, this.height],
  //       color,
  //       alpha,
  //       this.bottom === this.parent?.height ? 1 : 0.5
  //     );
  //   }

  //   // 绘制左边线
  //   if (this.is_show_line_left) {
  //     Renderer.draw_line([0, 0], [0, this.height], color, alpha, this.left === 0 ? 1 : 0.5);
  //   }
  // }

  // 绘制单元格的两条斜线
  drawSlash(color: string, alpha: number) {
    if (this.is_show_slash_up) {
      // 斜下
      if (isBoolean(this.is_show_slash_up) || this.is_show_slash_up === 1) {
        Renderer.draw_line([0, 0], [this.width, this.height], color, alpha);
      }
      if (this.is_show_slash_up === 2) {
        // 都在线的 三分之二 处
        Renderer.draw_line(
          [0, 0],
          [this.width * (1 / 2), this.height],
          color,
          alpha
        );
        Renderer.draw_line(
          [0, 0],
          [this.width, this.height * (1 / 2)],
          color,
          alpha
        );
      }
    }
    if (this.is_show_slash_down) {
      // 从左下到右上 斜上
      if (isBoolean(this.is_show_slash_down) || this.is_show_slash_down === 1) {
        Renderer.draw_line([0, this.height], [this.width, 0], color, alpha);
      }
      if (this.is_show_slash_down === 2) {
        Renderer.draw_line(
          [0, this.height],
          [this.width * (1 / 2), 0],
          color,
          alpha
        );
        Renderer.draw_line(
          [0, this.height],
          [this.width, this.height * (1 / 2)],
          color,
          alpha
        );
      }
    }
  }

  /**
   * 绘制临时边框
   * @param direction 绘制边框的方向
   */
  drawTempBorder(direction: "left" | "right" | "top" | "bottom" | "border") {
    const { topLeftCornerX, topLeftCornerY } = this.getTopLeftCornerXY();
    const editor = this.editor;
    Renderer.save();
    Renderer.get().scale(
      editor.config.devicePixelRatio * editor.viewScale,
      editor.config.devicePixelRatio * editor.viewScale
    );
    Renderer.translate(topLeftCornerX, topLeftCornerY);
    if (direction === "left") {
      Renderer.draw_line([0, 0], [0, this.height], "#95b7ee", 1, 3);
    } else if (direction === "right") {
      Renderer.draw_line(
        [this.width, 0],
        [this.width, this.height],
        "#95b7ee",
        1,
        3
      );
    } else if (direction === "border") {
      Renderer.draw_line([0, 0], [0, this.height], "#95b7ee", 1, 3); // 左
      Renderer.draw_line([0, 0], [this.width, 0], "#95b7ee", 1, 3); // 右
      Renderer.draw_line(
        [this.width, 0],
        [this.width, this.height],
        "#95b7ee",
        1,
        3
      ); // 上
      Renderer.draw_line(
        [0, this.height],
        [this.width, this.height],
        "#95b7ee",
        1,
        3
      ); // 下
    }
    Renderer.restore();
  }

  // 绘制单元格
  draw(editor: Editor) {
    // TODO 有性能问题
    if (this.parent) {
      for (const row of this.children) {
        if (
          row.children[0]?.width >
          this.width - this.padding_left - this.padding_right
        ) {
          Renderer.clipCell(0, 0, this.width - 1, this.height);
          break;
        }
      }
    }

    // 暂时 只让正文的表格能绘制斜线
    if (this.parent?.parent === editor.root_cell) {
      this.drawSlash("#000", 1);
    }
    if (this.style.bgColor) {
      Renderer.save();
      Renderer.draw_rectangle(
        0,
        0,
        this.width,
        this.height,
        this.style.bgColor
      );

      Renderer.restore();
    }

    if (this.parent && this.lock && editor.config.cellLockBgColor) {
      Renderer.save();
      Renderer.draw_rectangle(
        0,
        0,
        this.width,
        this.height,
        editor.config.cellLockBgColor
      );

      Renderer.restore();
    }
    
    for (let i = 0; i < this.children.length; i++) {
      this.children[i].draw(editor);
    }
    if (this.parent && this.set_cell_height.type === "scroll") {
      this.drawCellScroll();
    }
    if (this.parent && this.groupKey && editor.config.isShowCellGroupSign) {
      if (editor.activeGroupKey === this.groupKey) {
        this.drawGroupSign("rgba(0, 0, 255, 1)")
      } else {
        this.drawGroupSign("rgba(170,170,170, 1)")
      }
    }
  }

  drawGroupSign(color: string) {
    const radius = 4;
    const ctx = Renderer.get();
    ctx.save();
    ctx.fillStyle = color;
    const centerX = this.width;
    const centerY = 0;
    ctx.beginPath()
    ctx.moveTo(centerX, centerY);
    ctx.arc(centerX, centerY, radius, Math.PI / 2, Math.PI);
    ctx.closePath();
    ctx.fill();
    ctx.restore();
  }
  
  /**
   *  绘制cell的滚动条
   * @param height 滚动条的高度
   * @param scroll_top  滚动条滚动的高度
   */
  drawCellScroll() {
    const _this = this.getOrigin();
    if (!_this.children.length) return;
    const total_height = _this.children[_this.children.length - 1].bottom;
    if (total_height < _this.height) return;
    // 不滚动时删除cell内行会改变total_height的大小
    // if (total_height <= _this.height + _this.scroll_cell_top) {
    //   total_height = _this.height + _this.scroll_cell_top;
    // }
    const height = (_this.height - 3) * (_this.height / total_height);
    const scroll_top =
      (_this.scroll_cell_top / (total_height - _this.height)) *
      (_this.height - height - 3);
    Renderer.save();
    Renderer.draw_rectangle(
      _this.width - 5,
      0,
      4,
      _this.height,
      "rgba(246,246,246,0.5)"
    );
    Renderer.drawArc(
      _this.width - 3,
      scroll_top + 4,
      1.5,
      Math.PI,
      0,
      "rgba(196,196,196,0.5)",
      0.5,
      false,
      "fill"
    );
    Renderer.draw_rectangle(
      _this.width - 5,
      scroll_top + 3.5,
      4,
      height - 4,
      "rgba(196,196,196,0.5)"
    );
    Renderer.drawArc(
      _this.width - 3,
      scroll_top + height - 1,
      1.5,
      Math.PI,
      0,
      "rgba(196,196,196,0.5)",
      0.5,
      true,
      "fill"
    );

    Renderer.restore();
  }

  /**
   * 获取当前段内容的纯文本默认包含文本域边框字符
   * @returns 纯文本
   */
  getStr(noSymbol: boolean = false): string {
    let str = "";
    this.paragraph.forEach((p) => {
      str += p.getStr(noSymbol);
    });
    return str;
  }

  /**
   * 获取该 Cell 左上角的 x y 坐标
   * @param page Page 当前页
   * @returns 返回左上角的坐标
   */
  getTopLeftCornerXY(): { topLeftCornerX: number; topLeftCornerY: number } {
    const table = this.parent;
    if (!table) {
      return { topLeftCornerX: 0, topLeftCornerY: 0 };
    }
    const page = this.editor.pages[0];
    const topLeftCornerX = page.left + table.left + this.left;
    const topLeftCornerY =
      page.top + table.top + this.top - this.editor.scroll_top;
    return { topLeftCornerX, topLeftCornerY };
  }

  /**
   * 追加文本内容
   * @param text
   */
  appendText(text: string) {
    const para = this.paragraph[this.paragraph.length - 1];
    if (isParagraph(para)) {
      const chars_length = para.characters.length;
      para.insertText(
        text,
        chars_length - 1,
        para.characters[chars_length - 1].font
      );
    }
    this.editor.render();
  }

  // 往该单元格内追加元素
  appendElements(
    elements: (Character | ImageElement | Widget | Line | Box | XField)[]
  ) {
    const lastParagraph = this.paragraph[
      this.paragraph.length - 1
    ] as Paragraph;
    const arr = [];
    for (let i = 0; i < elements.length; i++) {
      const elem = elements[i];
      if (!isField(elem)) {
        arr.push(elem);
      } else {
        arr.push(elem.start_sym_char);
        arr.push(...elem.placeholder_characters);
        arr.push(elem.end_sym_char);
        elem.cell = this;
        this.fields.push(elem);
      }
    }
    lastParagraph.insertElements(
      arr,
      lastParagraph.characters.length - 1,
      null
    );

    // if (xfield) {
    //   xfield.cell = this;
    // }
    // editor.selection.anchor = editor.selection.focus = [this.parent?.cell_index!, this.parent?.children.indexOf(this)!, this.children.length - 1, this.children[this.children.length - 1].children.length - 1];
    // const lastParagraph = this.paragraph[this.paragraph.length - 1] as Paragraph;
    // lastParagraph.align = align;
    // const image = new ImageElement(src, width, height);
    // lastParagraph.insertImage(image, 0, 0);
    // ImageMap.addOnload(src);
    // editor.insertText("\n");
    // const field = editor.insertField() as any;
    // field.replaceText(id);
    // editor.selection.setCursorPosition([this.parent?.cell_index!, this.parent?.children.indexOf(this)!, this.children.length - 1, this.children[this.children.length - 1].children.length]);
    // editor.insertField(xfield?.copy(this));
  }

  /**
   * 追加一个空段落
   */
  appendEmptyPara() {
    const para = this.paragraph[this.paragraph.length - 1];
    let group_id = null;
    if (para) {
      group_id = para.group_id;
    }
    // 后续新的段落
    const new_para = new Paragraph(uuid("para"), this, group_id);
    const character = new Character(
      this.editor.fontMap.add(this.editor.config.default_font_style),
      "\n"
    );
    new_para.characters = [character];
    this.paragraph.push(new_para);
    if (isParagraph(para)) {
      new_para.setParagraphAttr(para);
    }
    new_para.updateChildren(0);
  }
}
