import { Config } from "./Config";
import Editor from "./Editor";
import Element from "./Element";
import Font from "./Font";
import { getFocusImage, getImageParaPath, setFocusImage, setImageParaPath } from "./ImageEditing";
import ImageRect from "./ImageRect";
import PathUtils from "./Path";
import Row from "./Row";
import { getImage, uuid } from "./Utils";
export default class Image extends Element {
  id: string;

  src: string;

  url: string | undefined = "";

  ori_width: number;

  ori_height: number;

  show: boolean = false;

  value: string = "";

  field_id: string | null = null;

  field_position: string = "normal";

  font: Font ;

  meta: any = {};
  // TODO 应该没用 xzq
  level: number = 0; // 该字符对象是哪一个级别的 0级代表不是list对象
  constructor (src: string, width: number, height: number, font:Font, meta: any = {}, id?: string) {
    super(width, height);
    this.id = id ?? uuid("image");
    this.src = src;
    this.meta = meta;
    this.ori_width = width;
    this.ori_height = height;
    this.font = font;
  }

  static insert (editor: Editor, src: string, other_param: any = {}) {
    const para_anchor = editor.modelPath2ParaPath(editor.selection.start);
    const _this = editor;
    const other_params = {
      meta: other_param.meta,
      devicePixelRatio: other_param.devicePixelRatio,
      width: other_param.width,
      height: other_param.height,
      loadCompleted () {
        const para_anchor_next = [...para_anchor];
        setImageParaPath(para_anchor);
        PathUtils.movePathCharNum(para_anchor_next, 1);
        _this.selection.setCursorPosition(_this.paraPath2ModelPath(para_anchor_next));
        _this.update();
        _this.render();
      }
    };
    editor.insertImageByParaPath(para_anchor, src, other_params);
    const pragraph = para_anchor.length > 3
      ? (editor.current_cell.paragraph as any)[para_anchor[0]].children[para_anchor[1]].paragraph[para_anchor[2]]
      : editor.current_cell.paragraph[para_anchor[0]];
    return pragraph;
  }

  static insertByParaPath (
    editor: Editor,
    para_path: any[],
    src: string,
    other_params: {
      meta?: any,
      width?: number,
      height?: number,
      devicePixelRatio?: number,
      loadCompleted?: (image: Image, focus_row: Row) => any
    } = {}
  ) {
    // 分组如果锁定 则不能编辑
    const html_image = getImage();
    html_image.src = src;
    const devicePixelRatio = other_params.devicePixelRatio ? other_params.devicePixelRatio : 1;
    html_image.onload = () => {
      const width = (other_params.width ? other_params.width : html_image.width) / devicePixelRatio;
      const height = (other_params.height ? other_params.height : html_image.height) / devicePixelRatio;
      if (!editor.delSectionRecordStack()) {
        return false;
      }
      // para_path位置所在行
      const para_row = editor.selection.getRowByPath(editor.paraPath2ModelPath(para_path));
      // para_path位置所在的文本域
      const para_field = editor.selection.getFieldByPath(para_path);

      if (para_row) {
        // imageMap中放入图片src

        const adjustment_size = editor.imageIsOverSize(width, height, para_row);
        const adjustment_width = adjustment_size.width;
        const adjustment_height = adjustment_size.height;
        const font = editor.contextState.getFontState();
        const image = new Image(src, adjustment_width, adjustment_height, font, other_params.meta ? other_params.meta : {});
        editor.imageMap.addOnload(image);
        // paragraph中按para_path位置插入图片
        para_row.paragraph.insertImage(
          image,
          para_path[para_path.length - 2],
          para_path[para_path.length - 1],
          para_field
        );
        // 设置点击图片的图片变量
        // 编辑状态的小方块展示置为false
        setFocusImage(null);
        other_params.loadCompleted && other_params.loadCompleted(image, para_row);
      }
    };
  }

  static isOverSize (editor: Editor, width: number, height: number, para_row: Row) {
    const header_height = editor.pages[0].header.header_outer_bottom;
    const footer_height = editor.pages[0].height - editor.pages[0].footer.footer_outer_top;
    // 图片宽度大于所在row宽度时，设置图片宽高按宽等比例缩放至row的宽度
    // 可允许的图片最大宽与最大高的比率
    const max_width = para_row.width;
    const max_height = editor.page_size.height - header_height - footer_height - Config.img_margin * 2 - 150;
    const aspect_ratio = max_width / max_height;
    const real_radio = width / height;
    if (width >= max_width && height >= max_height) {
      if (real_radio >= aspect_ratio) {
        width = max_width;
        height = max_width / real_radio;
      } else {
        width = max_height * real_radio;
        height = max_height;
      }
    } else if (width >= max_width && height < max_height) {
      width = max_width;
      height = max_width / real_radio;
    } else if (width < max_width && height >= max_height) {
      width = max_height * real_radio;
      height = max_height;
    }

    return { width, height };
  }

  static changeSize (editor: Editor, width: number, height: number) {
    const image = getFocusImage();

    if (image) {
      const para_path = getImageParaPath();
      if (para_path) {
        const row = editor.selection.getFocusRow();

        image.width = width;
        image.height = height;

        // 最大宽度
        const max_width = row.width - row.padding_left - Config.img_margin * 2;
        if (width < Config.rect_width * 4) {
          image.width = Config.rect_width * 4;
        } else if (width > max_width) {
          image.width = max_width;
        } else {
          image.width = width;
        }
        // 最大高度
        let max_height =
          editor.page_size.height -
          editor.config.page_padding_top -
          editor.config.page_padding_bottom -
          Config.img_margin * 2;
        // 在表格内的最大高度
        if (para_path.length > 2) {
          max_height =
            editor.page_size.height -
            editor.config.page_padding_top -
            editor.config.page_padding_bottom -
            editor.config.table_padding_horizontal * 2 -
            Config.img_margin * 2;
        }

        if (height < Config.rect_width * 4) {
          image.height = Config.rect_width * 4;
        } else if (height > max_height) {
          image.height = max_height;
        } else {
          image.height = height;
        }
        const paragraph = editor.selection.getParagraphByPath(
          para_path
        );
        // 更新段落，此方法可能会造成段落行数变换，更新后需同时更新坐标
        paragraph.updateChildren();
        // 还原坐标
        editor.selection.setCursorPosition(editor.paraPath2ModelPath(para_path));

        editor.update(...editor.getUpdateParamsByContainer(row));
        editor.event.emit("changeImageSize",image)
        return true;
      }
    }
  }

  static attrJudgeUndefinedAssign (newModel: Image, raw: any) {
    if (raw.field_id !== undefined) newModel.field_id = raw.field_id;
    if (raw.ori_height !== undefined) newModel.ori_height = raw.ori_height;
    if (raw.ori_width !== undefined) newModel.ori_width = raw.ori_width;
    if (raw.url !== undefined) newModel.url = raw.url;
  }

  copy (): Image {
    const image = new Image(this.src, this.width, this.height, this.font, this.meta);
    image.field_id = this.field_id;
    image.url = this.url;
    image.show = this.show;
    image.field_position = this.field_position;
    return image;
  }

  /**
   * 更新该图片的属性值
   * @param parameter 要修改的属性和值的对象
   */
  updateProps (parameter: Partial<Image>) {
    for (const key in parameter) {
      // @ts-ignore
      this[key] = parameter[key];
    }
  }

  /**
   * 图片中添加小方块
   */
  draw (height: number) {
    const imageRect = new ImageRect(this.left, this.top + height - this.height, this.width, this.height);

    imageRect.draw();
  }
}
