const rulerEditorMixIn = {
  data() {
    return {
      showRulerEditorModal: false,
      replaceRule: [],
    };
  },
  methods: {
    rulerEditorSubmit(reg) {
      const editor = this.instance.editor;
      this.showRulerEditorModal = false;
      if (this.fieldSource === "field") {
        this.reg = reg;
        this.$refs.fieldProperty.$el.style.display = "block";
      } else if (this.fieldSource === "side") {
        if (editor.focusElement["field"]) {
          editor.focusElement["field"].replaceRule = reg;
        }
      }
    },
    rulerEditorCancel() {
      this.showRulerEditorModal = false;
      if (this.fieldSource === "field") {
        this.$refs.fieldProperty.$el.style.display = "block";
      }
    },
  },
};
export default rulerEditorMixIn;
