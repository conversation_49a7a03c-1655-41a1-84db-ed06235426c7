const fileBtnMixIn = {
  data() {
    return {
      fileBtn: [
        {
          type: "textIcon",
          icon: "icon-shanchu",
          title: "清空文档",
          func: this.clear,
        },
        // {
        //   type: "textIcon",
        //   icon: "icon-baocuncunchu",
        //   title: "保存",
        //   func: this.save,
        // },
        {
          type: "textIcon",
          icon: "icon-dakai",
          title: "打开文件",
          func: this.openFile,
        },
        {
          type: "textIcon",
          icon: "icon-daochu",
          title: "导出",
          children: [
            {
              type: "textIcon",
              icon: "icon-jichuzutaitubiao-29",
              title: "导出JSON",
              func: this.saveAs,
            },
            {
              type: "textIcon",
              icon: "icon-PDF",
              title: "导出PDF",
              func: this.downloadPDF,
            },
          ],
        },
        {
          type: "textIcon",
          icon: "icon-print1",
          title: "打印",
          children: [
            {
              type: "textIcon",
              icon: "icon-print1",
              title: "打印",
              func: () => {
                this.immediatelyPrint("json");
              },
            },
            {
              type: "textIcon",
              icon: "icon-print",
              title: "打印预览",
              func: this.systemPrint,
            },
          ],
        },
        // {
        //   type: "textIcon",
        //   icon: "icon-VueFlyDialog",
        //   title: "编辑器弹窗",
        //   func: this.openEditorModal,
        // },
        {
          type: "textIcon",
          icon: "icon-guanlishujuji",
          title: "数据集管理",
          func: this.manageDataSet,
        },
        {
          type: "textIcon",
          icon: "icon-ziduanguanli",
          title: "自定义字段",
          func: this.openCreateFields,
        },
      ],
    };
  },
  methods: {
    openFile() {
      this.instance.openFile("useConfig");
    },
    clear() {
      const { editor } = this.instance;
      editor.clearDocument(true);
    },
    downloadPDF() {
      this.instance.convertPDF();
    },
    manageDataSet() {
      this.openManageDataSetModal();
    },
  },
};
export default fileBtnMixIn;
