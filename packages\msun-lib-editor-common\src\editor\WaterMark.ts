import Renderer from "./Renderer";
import Editor from "./Editor";
import { getImage, uuid, keepDecimal } from "./Utils";
import ImageRect from "./ImageRect";
import { isInImageRect, setMarkShadowSize } from "./ImageEditing";
import { FontStyle } from "./Font";
export interface markParams {
  src?: string
  value?: string
  fontStyle?: any
  real_y?: number
  padding?: boolean
  level?: number
}
export default class WaterMark {
  type: string;
  is_edit: boolean;// 是否处于编辑状态
  width: number;
  height: number;
  params: any;
  start: any;
  mode: string;
  name: string;

  constructor(type: string = "imageMark", mode: string = "single", width: number = 200, height: number = 200, start: any = { x: 0, y: 0 }, is_edit: boolean = false, params: markParams = {}, name: string = "") {
    this.type = type || "imageMark";
    this.is_edit = is_edit;
    this.width = width || 200;
    this.height = height || 200;
    this.params = params;
    this.start = start;
    this.mode = mode;
    this.name = name
  }
  static insertImage(src: string, editor: Editor, imgParams?: any) {
    if (!editor.waterMark) return;
    editor.internal.is_mark_text = false;
    WaterMark.putMarkInputInCanvas(editor);
    const image = getImage();
    image.src = src;
    if (imgParams && imgParams.width) {
      image.width = Number(imgParams.width)
    } else {
      image.width = 300
    }
    if (imgParams && imgParams.height) {
      image.height = Number(imgParams.height)
    } else {
      image.height = 150
    }

    const params = {
      src: src,
      padding: false
    };
    editor.imageMap.addOnload(params);
    let x = (editor.page_size.width - image.width) > 0 ? (editor.page_size.width - image.width) / 2 : 0;
    let re_y = 200 + editor.scroll_top;
    let y = editor.getSpacingPageTopByY(200 + editor.scroll_top);
    if (imgParams && imgParams.location && imgParams.location.length) {
      x = imgParams.location[0]
      y = editor.getSpacingPageTopByY(imgParams.location[1] + editor.config.editor_padding_top)
      re_y = imgParams.location[1] + editor.config.editor_padding_top
    }
    let mode = "repeat"
    if (imgParams && imgParams.mode === "single") {
      mode = "single"
    }
    if (imgParams && imgParams.padding) {
      x -= editor.config.page_padding_left
      y -= editor.config.page_padding_top
      re_y -= editor.config.page_padding_top
      params.padding = true
    }

    const watermark = new WaterMark("imageMark", mode, image.width, image.height, { x: x, y: y }, true, params, imgParams?.name);
    if (imgParams && imgParams.level) {
      watermark.params.level = imgParams.level
    }
    editor.waterMarks.push(watermark);
    editor.internal.focusMark = watermark;
    editor.internal.focusMark.params.real_y = re_y;
    editor.render();

    return watermark;
  }
  static updateCustomMarkImage(editor: Editor, param: any) {
    if (editor.internal.focusMark) {
      this.changeMarkProp(editor, editor.internal.focusMark, param)
      editor.refreshDocument()
      return
    }
    for (let i = 0; i < editor.waterMarks.length; i++) {
      const watermark = editor.waterMarks[i];
      if (watermark.name === param.name) {
        this.changeMarkProp(editor, watermark, param)
      }
    }
    editor.refreshDocument()
  }

  static changeMarkProp(editor: Editor, watermark: WaterMark, param: any) {
    let top = 0
    if (watermark.mode === "repeat" && param && param.mode === "single") {
      top = editor.config.editor_padding_top
    } else if (watermark.mode === "single" && param && param.mode === "repeat") {
      top = -editor.config.editor_padding_top
    }
    const params = {
      src: param.src ? param.src : watermark.params.src,
    };
    editor.imageMap.addOnload(params);
    watermark.params.src = params.src
    watermark.params.padding = param.padding ? param.padding : watermark.params.padding
    watermark.params.level = param.level ? param.level : watermark.params.level
    watermark.name = param.name ? param.name : watermark.name
    watermark.width = param.width ? param.width : watermark.width
    watermark.height = param.height ? param.height : watermark.height
    watermark.mode = param.mode ? param.mode : watermark.mode
    watermark.start.y = watermark.start.y + top
  }
  static toggleMode(editor: Editor, open: boolean) {
    WaterMark.closeMarkEdit(editor);
    if (open) {
      // editor.setViewMode(ViewMode.VIEW);
      editor.setReadonly(true);
      editor.waterMark = true;
    } else {
      // editor.setViewMode(ViewMode.NORMAL);
      editor.setReadonly(false);
      WaterMark.putMarkInputInCanvas(editor);
      editor.waterMark = false;
    }
    editor.render();
  }

  static changeFontStyle(editor: Editor, style: Partial<FontStyle>) {
    const markDom: HTMLElement = editor.internal.markInput;
    // 如果是水印模式且字体框存在，改变字体框中的字体样式
    if (editor.waterMark && markDom.style.display === "block") {
      // 合并属性后给markInputStyle赋值
      editor.markInputStyle = Object.assign({}, editor.markInputStyle, style);
      const inputStyle = editor.markInputStyle;
      markDom.style.font = `${inputStyle.italic ? "italic " : ""}${inputStyle.bold ? 700 : 400} ${inputStyle.height}px  ${inputStyle.family}`;
      markDom.style.color = inputStyle.color;
      markDom.style.lineHeight = inputStyle.height * 1.2 + "px";

      const mark = WaterMark.isSameMarkId(editor.internal.markId, editor);
      if (mark) {
        mark.params.fontStyle = inputStyle;
        mark.params.value = markDom.innerText;
        editor.updateMarkParameter(mark.params.value, mark, markDom.offsetLeft - editor.init_canvas.getBoundingClientRect().left, markDom.offsetTop - editor.init_canvas.getBoundingClientRect().top);
      }
      return true;
    }
  }

  /**
   * 通过原始数据获取waterMarks
   * @param waterMarks
   */
  static getDataByRawData(editor: Editor, waterMarks = editor.raw.waterMarks) {
    if (!waterMarks) return [];
    const marks_list: any = [];
    for (let i = 0; i < waterMarks.length; i++) {
      const waterMark = waterMarks[i];

      const params: any = {};

      for (const mark_param in waterMark) {
        params[mark_param] = waterMark[mark_param];
      }
      params.is_edit = false;
      const newWaterMark = new WaterMark(params.type, params.mode, params.width, params.height, params.start, false, params.params);
      if (params.params.src) {
        editor.imageMap.addOnload(params.params);
      }
      marks_list.push(newWaterMark);
    }
    return marks_list;
  }

  // 改变水印模式，单页/全页
  static changeMarkMode(mode: string, editor: Editor) {
    if (!editor.waterMark) return;
    const mark = editor.internal.focusMark;
    if (!mark) {
      editor.event.emit("message", "不存在聚焦的水印")
      return
    }
    const markShow = editor.internal.markInput.style.display;
    // 如果水印文字在选中状态
    if (markShow === "block") {
      for (let i = 0; i < editor.waterMarks.length; i++) {
        const watermark = editor.waterMarks[i];
        if (watermark.type === "textMark" && editor.internal.markId === watermark.params.id) {
          if (mark.mode === "single" && mode === "repeat") {
            mark.start.y = editor.getSpacingPageTopByY(mark.start.y);
          } else if (mark.mode === "repeat" && mode === "single") {
            mark.start.y = mark.params.real_y;
          }
          watermark.mode = mode;
          break;
        }
      }
    } else {
      if (mark.type === "imageMark") {
        if (mark.mode === "single" && mode === "repeat") {
          mark.start.y = editor.getSpacingPageTopByY(mark.start.y);
        } else if (mark.mode === "repeat" && mode === "single") {
          mark.start.y = mark.params.real_y;
        }
        mark.mode = mode;
      } else {
        editor.event.emit("message", "选中状态下可改变水印重复状态");
      }
    }

    editor.render();
  }

  static isSameMarkId(id: string, editor: Editor) {
    for (let i = 0; i < editor.waterMarks.length; i++) {
      const mark = editor.waterMarks[i];
      if (id === mark.params.id) {
        return mark;
      }
    }
  }

  static isInTextMark(x: number, y: number, editor: Editor) {
    y += editor.scroll_top;
    const si_y = y;
    const rep_y = editor.getSpacingPageTopByY(y);

    if (!editor.waterMarks.length) return;
    editor.internal.is_edit_mark = undefined;
    editor.internal.is_drag_mark = false;

    for (let i = editor.waterMarks.length - 1; i >= 0; i--) {
      const mark = editor.waterMarks[i];
      let re_y = si_y;
      if (mark.mode === "repeat") {
        re_y = rep_y;
      }
      if (mark.type === "textMark" && mark.start.x + editor.page_left - 5 <= x && x <= mark.start.x + mark.width + editor.page_left + 5 && mark.start.y - 5 <= re_y && re_y <= mark.start.y + mark.height + 5) {
        return mark;
      }
    }
  }

  /**
 *  鼠标点击时处理 mark的方法
 * @param x
 * @param y
 * @param editor
 * @returns
 */
  static pointerDownMarkDeal(x: number, y: number, editor: Editor) {
    if (!editor.waterMarks.length) return;
    y += editor.scroll_top;
    const si_y = y;
    const rep_y = editor.getSpacingPageTopByY(y);
    editor.internal.is_edit_mark = undefined;
    editor.internal.is_drag_mark = false;
    let pointMark: any = "";
    for (let i = editor.waterMarks.length - 1; i >= 0; i--) {
      const mark = editor.waterMarks[i];
      let re_y = si_y;
      if (mark.mode === "repeat") {
        re_y = rep_y;
      }
      let left = mark.start.x + editor.page_left;
      let top = mark.start.y
      if (mark.params.padding) {
        left = mark.start.x + editor.page_left + editor.config.page_padding_left
        top = mark.start.y + editor.config.page_padding_top
      }
      // x,y是否在，mark内
      if (left - 5 <= x && x <= left + mark.width + 5 && top - 5 <= re_y && re_y <= top + mark.height + 5) {
        // 缓存移动前的位置
        editor.internal.mark_before_move_xy = { x: x, y: y };
        editor.internal.focusMark = mark as WaterMark;
        // 在鼠标单纯点击mark没有拖动时需要给mark_shadow_size赋值，否则图片宽高会被重置为0
        const mark_size = {
          x: left,
          y: top,
          width: mark.width,
          height: mark.height
        };
        // 给鼠标抬起时改变mark大小的属性赋值
        setMarkShadowSize(mark_size);
        if (mark.type === "imageMark" && !editor.internal.is_mark_text) {
          // 如果mark的模式是重复模式的话，给real_y赋值
          if (mark.mode === "repeat") {
            const page = editor.getPageByRealY(si_y);
            editor.internal.focusMark.params.real_y = page * (editor.page_size.height + editor.config.page_margin_bottom) + editor.config.editor_padding_top + mark.start.y;
          } else {
            editor.internal.focusMark.params.real_y = mark.start.y;
          }

          // 点击哪个mark，哪个mark提到最上方
          pointMark = mark;
          editor.waterMarks.splice(i, 1);
          editor.waterMarks.push(pointMark);
          mark.is_edit = true;
          // 是否点击到mark图片的编辑框内
          const result = isInImageRect(x, re_y, left, top, mark.width, mark.height, "mark");
          editor.internal.is_edit_mark = result;
          if (result === undefined) {
            editor.internal.is_drag_mark = true;
          }
        }
        break;
      } else {
        editor.internal.focusMark = null
      }
    }
  }

  // 获取拖拽时mark的位置信息
  static getDragMarkLocation(x: number, y: number, editor: Editor) {
    y += editor.scroll_top;

    const move_x = keepDecimal(x - editor.internal.mark_before_move_xy.x, 3);
    const move_y = keepDecimal(y - editor.internal.mark_before_move_xy.y, 3);
    if (!editor.internal.focusMark) return
    if (editor.internal.focusMark.start.y + move_y <= 0 && move_y < 0) return
    editor.internal.focusMark.start.x += move_x;
    editor.internal.focusMark.start.y += move_y;
    editor.internal.focusMark.params.real_y += move_y;
    editor.internal.mark_before_move_xy = { x: x, y: y };
  }

  // 插入水印文字模式
  static openInsertTextMode(openMarkText: boolean, editor: Editor) {
    const markDom: HTMLElement = editor.internal.markInput;
    markDom.innerText = "";
    markDom.style.display = "none";
    if (editor.waterMark) {
      WaterMark.closeMarkEdit(editor);
      editor.internal.is_mark_text = openMarkText;
      if (editor.internal.is_mark_text) {
        editor.internal.is_edit_mark = undefined;
      }
      editor.render();
    } else {
      editor.internal.is_mark_text = false;
    }
  }

  // 判断X,Y是否在mark中的方法
  static isInMark(x: number, y: number, editor: Editor) {
    if (!editor.waterMark) return;
    let re_y = y;
    const rep_y = editor.getSpacingPageTopByY(y);
    if (!editor.waterMarks.length) return;

    for (let i = editor.waterMarks.length - 1; i >= 0; i--) {
      const mark = editor.waterMarks[i];
      if (mark.type === "imageMark" && !editor.internal.is_mark_text) {
        if (mark.mode === "repeat") {
          re_y = rep_y;
        } else {
          re_y = y;
        }
        let left = mark.start.x + editor.page_left;
        let top = mark.start.y
        if (mark.params.padding) {
          left = mark.start.x + editor.page_left + editor.config.page_padding_left
          top = mark.start.y + editor.config.page_padding_top
        }
        if (left - 5 <= x && x <= left + mark.width + 5 && top - 5 <= re_y && re_y <= top + mark.height + 5) {
          return mark;
        }
      }
    }
  }

  static insertItalicMark(text: string, params: any, editor: Editor) {
    if (text) {
      editor.internal.italicWatermark.text = text
    }
    if (params.direction) {
      editor.internal.italicWatermark.direction = params.direction
    }

    editor.internal.italicWatermark.module = params.module

    if (params.font) {
      editor.internal.italicWatermark.font = params.font
    }
    editor.render()
  }

  // 删除mark
  static deleteWaterMark(editor: Editor) {
    if (editor.waterMark && editor.waterMarks.length && editor.internal.focusMark && editor.internal.focusMark.is_edit) {
      const index = editor.waterMarks.indexOf(editor.internal.focusMark);
      if (index > -1) {
        editor.waterMarks.splice(index, 1);
        editor.update();
        editor.render();
        return true;
      }
    }
    return false;
  }

  // 关闭mark的编辑模式
  static closeMarkEdit(editor: Editor, all: boolean = true) {
    for (let i = 0; i < editor.waterMarks.length; i++) {
      const mark = editor.waterMarks[i];
      if (!all) {
        if (mark.type === "imageMark") {
          mark.is_edit = false;
        }
      } else {
        mark.is_edit = false;
      }
    }
  }

  // 将input框中编辑完的mark放到canvas上
  static putMarkInputInCanvas(editor: Editor) {
    if (!editor.waterMark) return;
    editor.showWartFontEditor = false;
    const markInput = editor.internal.markInput;
    const showDom = markInput.style.display;
    // 判断input框内是否有和marks相同的id，有则更新marks数据，没有则新插入mark
    const mark = WaterMark.isSameMarkId(editor.internal.markId, editor);
    if (showDom === "block" && markInput.innerText !== "") {
      if (!mark) {
        editor.insertWaterMarkText(markInput.innerText);
        editor.internal.markId = "";
      } else {
        mark.params.value = markInput.innerText;
        editor.updateMarkParameter(mark.params.value, mark);
      }

      markInput.innerText = "";
      markInput.style.display = "none";
      WaterMark.closeMarkEdit(editor);
    } else if (showDom === "block" && markInput.innerText === "") {
      if (mark) {
        const result = editor.waterMarks.indexOf(mark);
        if (result !== -1) {
          editor.waterMarks.splice(result, 1);
        }
        editor.internal.markId = "";
      }
      markInput.style.display = "none";
    }
    return true;
  }

  // 插入水印文字
  static insertText(text: string, editor: Editor) {
    const markDom: HTMLElement = editor.internal.markInput;
    const font = editor.markInputStyle;
    font.height = parseInt(markDom.style.fontSize);
    const textList = text.split("\n");
    const numTextList = [];
    Renderer.save();
    Renderer.get().font = `${font.italic ? "italic " : ""}${font.bold ? 700 : 400} ${font.height}px ${font.family}`;
    for (let i = 0; i < textList.length; i++) {
      numTextList.push(Math.round(Renderer.get().measureText(textList[i]).width * 10) / 10);
    }
    Renderer.restore();
    const { x, y } = editor.getNeedXYbyXY(markDom.getBoundingClientRect().left, markDom.getBoundingClientRect().top, 3);
    const width = Math.max(...numTextList);
    const height = Math.round(textList.length * font.height * 10) / 10;
    const real_y = Math.round((y + font.height * 0.1 + 6) * 10) / 10 + editor.scroll_top;
    const params = {
      value: text,
      id: uuid("mark"),
      fontStyle: font,
      real_y: real_y
    };

    const draw_x = x + 7 - editor.page_left;
    const draw_y = editor.getSpacingPageTopByY(real_y);

    const watermark = new WaterMark("textMark", "repeat", width, height, { x: draw_x, y: draw_y }, false, params);
    editor.waterMarks.push(watermark);
    editor.render();
    return true;
  }

  // 更新mark参数的方法
  static updateMarkParameter(text: string, mark: WaterMark, editor: Editor, x: number, y: number) {
    const font = mark.params.fontStyle;
    const textList = text.split("\n");
    const numTextList = [];
    Renderer.save();
    for (let i = 0; i < textList.length; i++) {
      Renderer.get().font = `${font.italic ? "italic " : ""}${font.bold ? 700 : 400} ${font.height}px ${font.family}`;
      numTextList.push(Math.round(Renderer.get().measureText(textList[i]).width * 10) / 10);
    }
    Renderer.restore();
    const width = Math.max(...numTextList);
    const height = textList.length * font.height;
    mark.width = width;
    mark.height = height;
    if (x && y) {
      mark.start.x = x + 6 - editor.page_left;
      const real_y = y + editor.scroll_top + font.height * 0.1 + 6;
      if (mark.mode === "repeat") {
        mark.start.y = editor.getSpacingPageTopByY(real_y);
      } else {
        mark.start.y = real_y;
      }
    }
  }

  draw(editor: Editor, top: number = 0) {

    if (this.type === "imageMark") {
      const img_map = editor.imageMap.get();
      let markLeft = this.start.x + editor.page_left
      let markTop = this.start.y
      if (this.params.padding) {
        markLeft = this.start.x + editor.page_left + editor.config.page_padding_left
        markTop = this.start.y + editor.config.page_padding_top
      }
      Renderer.draw_image(img_map.get(this.params.src), markLeft, markTop + this.height + top, this.width, this.height);
      if (this.is_edit) {
        const imgRect = new ImageRect(markLeft, markTop + top, this.width, this.height, "mark");
        imgRect.draw();
      }
      if (editor.waterMark && !editor.internal.is_drag_mark) {
        Renderer.draw_stroke_rect(markLeft - 10, markTop + top - 10, this.width + 20, this.height + 20, "rgb(150,150,150)", "dotted");
      }
    } else if (this.type === "textMark") {
      const textList = this.params.value.split("\n");
      const font = editor.fontMap.add(this.params.fontStyle);
      font.bgColor = null;
      if (!this.is_edit) {
        for (let i = 0; i < textList.length; i++) {
          const text = textList[i];

          Renderer.draw_text(font, text, this.start.x + editor.page_left, this.start.y + top + this.params.fontStyle.height * (1.2 * i + 1));
        }
      }
      if (editor.waterMark && !editor.internal.is_drag_mark) {
        Renderer.draw_stroke_rect(this.start.x - 10 + editor.page_left, this.start.y + top - 10 - this.params.fontStyle.height * 0.1, this.width + 20, this.params.fontStyle.height * 1.2 * textList.length + 20, "rgb(150,150,150)", "dotted");
      }
    }
  }
}
