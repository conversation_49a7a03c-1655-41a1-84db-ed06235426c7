<template>
  <svg :style="iconStyle" aria-hidden="true">
    <use :xlink:href="`#` + icon"></use>
  </svg>
</template>

<script>
export default {
  props: {
    icon: String,
    iconStyle: {
      type: Object,
      default: () => ({
        width: "16px",
        height: "16px",
        fontSize: "14px",
        verticalAlign: "middle",
        fill: "rgba(0, 0, 0, 0.4)",
        overflow: "hidden",
      }),
    },
  },
};
</script>

<style lang="less" scoped></style>
