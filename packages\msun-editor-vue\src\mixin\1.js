import { diff_match_patch } from "diff_match_patch";

import { getUUID } from "../assets/js/utils";
const traceContrast = {
  data() {
    return {
      traceImageMap: {},
      traceCaliperMap: {},
      traceId: "",
      currentUser: null,
      traceInfo: [],
      dmp: new diff_match_patch(),
      fontStyleList: [],
      bgColorList: [
        "rgba(250,227,113,1)",
        "rgba(174,221,129,1)",
        "rgba(217,104,49,1)",
        "rgba(69,137,148,1)",
        "rgba(222,156,83,1)",
        "rgba(230,179,61,1)",
        "rgba(178,190,126,1)",
        "rgba(251,178,159,1)",
        "rgba(252,157,154,1)",
        "rgba(179,214,110,1)",
      ],
    };
  },
  mounted() {
    //不同颜色代表的样式的数组，数组内对象包含font_id和fontStyle
    for (let i = 0; i < this.bgColorList.length * 2 + 1; i++) {
      const fontId = getUUID("font-default");
      let deleteLine = false;
      if (i < this.bgColorList.length || i === this.bgColorList.length * 2) {
        deleteLine = false;
      } else {
        deleteLine = true;
      }
      this.fontStyleList.push({
        fontId: fontId,
        fontStyle: {
          family: "宋体",
          height: 16,
          bold: false,
          italic: false,
          underline: false,
          strikethrough: deleteLine,
          script: 3,
          color: "#000",
          bgColor: null,
          temp_word_bgColor: null,
        },
      });
      if (i < this.bgColorList.length) {
        this.fontStyleList[i].fontStyle.temp_word_bgColor = this.bgColorList[i];
      } else if (
        i >= this.bgColorList.length &&
        i < this.bgColorList.length * 2
      ) {
        this.fontStyleList[i].fontStyle.temp_word_bgColor =
          this.bgColorList[i - this.bgColorList.length];
      }
    }
  },
  methods: {
    // openTraceContrast() {
    //   const traceRawData = this.compareTraces(rawData3, rawData4, true);
    //   this.instance.editor.reInitRaw(traceRawData);
    //   this.instance.editor.update();
    //   this.instance.editor.render();
    // },
    //比较段落文本
    generateReplaceArray() {
      const replaceArr = [];
      for (let charCode = 0x3040; charCode <= 0x30ff; charCode++) {
        const char = String.fromCharCode(charCode);
        replaceArr.push(char);
      }

      return replaceArr;
    },
    diffParaText(str1, str2, all) {
      const dmp = this.dmp;
      const pattern = /\$\$\$(.*?)\$\$\$/g;
      let matches1 = str1.match(pattern) || [];
      let matches2 = str2.match(pattern) || [];
      let correspondence = {};

      const replaceArr = this.generateReplaceArray();
      correspondence = this.prepareCorrespondence(
        matches1,
        matches2,
        replaceArr
      );
      str1 = this.replaceString(str1, matches1, correspondence);
      str2 = this.replaceString(str2, matches2, correspondence);

      const diff = dmp.diff_main(str1, str2);
      diff.forEach((e) => {
        for (let key in correspondence) {
          const regex = new RegExp(correspondence[key], "g");
          e[1] = e[1].replace(regex, () => `${key}`);
        }
      });
      // dmp.diff_cleanupSemantic(diff); // 他会修改 diff结果 导致对比痕迹出错 所以注释掉
      const traces = [];
      let offset = 0;
      diff.forEach((ele) => {
        if (all || ele[0] !== 0) {
          traces.push({ 0: ele[0], 1: offset, 2: ele[1] });
        }
        offset += ele[0] === -1 ? 0 : this.handleImageOffset(ele[1]).length;
      });
      return traces;
    },
    prepareCorrespondence(matches1, matches2, replaceArr) {
      let correspondence = {};
      let usedIndex = 0;

      // 先标记所有matches，以确保相同的元素使用相同的替换字符
      [...new Set([...matches1, ...matches2])].forEach((match) => {
        if (!correspondence[match]) {
          correspondence[match] = replaceArr[usedIndex++];
        }
      });

      return correspondence;
    },
    replaceString(str, matches, correspondence) {
      matches.forEach((match) => {
        // 使用正则表达式确保能匹配整个字符串
        // 添加对特殊字符的转义处理
        let escapedMatch = match.replace(/[-/\\^$*+?.()|[\]{}]/g, "\\$&");
        let regex = new RegExp(escapedMatch, "g");
        str = str.replace(regex, correspondence[match]);
      });
      return str;
    },
    //痕迹对比主入口
    mainDiff(paraId, str1, str2, traceInfo) {
      const diffVersion = this.diffParaText(str1, str2, true);
      if (!traceInfo || !traceInfo.length) {
        return diffVersion;
      }
      const finalResult = [];
      this.traceInfo = traceInfo;
      for (let i = 0; i < diffVersion.length; i++) {
        const ele = diffVersion[i];
        if (ele[0] === 0) {
          finalResult.push(ele);
        } else {
          const res = this.traceSource(
            ele[0],
            ele[2],
            ele[1],
            paraId,
            traceInfo
          );
          finalResult.push(...res);
        }
      }
      //设置正确的偏移
      let realOffset = 0;
      finalResult.forEach((ele) => {
        ele[1] = realOffset;
        realOffset += this.handleImageOffset(ele[2]).length;
      });
      return finalResult;
    },

    traceSource(addOrDel, changeContent, location, paragraphId, traceInfo) {
      let content = changeContent;
      let sourceInfo = []; // 溯源信息,可能多条
      let sliceContent = ""; // 目标字符串截取剩余的部分
      let finalIndex = 0; // 位置信息
      const paragraphTrace = []; // 段落修改信息
      // 获取到对象名为id的比对信息
      for (let i = 0; i < traceInfo.length; i++) {
        const element = traceInfo[i];
        if (!element.traces[paragraphId]) continue;
        if (element.traces[paragraphId].length) {
          paragraphTrace.push({
            record: element.traces[paragraphId],
            meta: element.meta,
          });
        }
      }

      // 循环内容，并且逐次递减
      a: while (content) {
        // 当前段的所有修改信息中提取出来 增删一致 内容包含的所有数据
        const records = [];
        for (const item of paragraphTrace) {
          const tempRecord = item.record.filter(
            (re) => addOrDel === re[0] && re[2].indexOf(content) > -1
          );
          if (tempRecord.length) {
            records.push({
              record: tempRecord,
              meta: item.meta,
            });
          }
        }

        // 循环所有符合第一步条件的数据
        for (let i = 0; i < records.length; i++) {
          const element = records[i];
          // 如果里面只有一条数据，表示只有一个人修改过该处内容，那么就是找到了所需要的数据，直接进入下一步，封装并返回结果
          // 如果有多条数据，那么就需要判断改条数据是不是想要的那一条数据
          const offset_result =
            records.length === 1
              ? true
              : this.handleOffset(location, content, element, paragraphTrace);
          if (offset_result) {
            // 位置坐标，如果没有截取，说明全部都已经匹配成功了
            finalIndex = sourceInfo.length
              ? location + this.handleImageOffset(content).length
              : location;
            // 比对结果
            const contrastResult = {
              0: addOrDel,
              1: finalIndex,
              2: content,
              meta: element.meta,
            };
            sourceInfo.push(contrastResult);
            // 没有裁剪过内容，代表匹配完全了
            if (sliceContent === "") {
              return sourceInfo;
            }
            break a;
          }
        }
        // 目标字符串截取剩余的部分收集起来
        sliceContent = content.slice(content.length - 1) + sliceContent;
        content = content.slice(0, content.length - 1);
      }

      // 截取收敛后的操作
      if (sliceContent && sourceInfo.length) {
        // 位置修改
        const sliceLocation =
          location + changeContent.length - sliceContent.length;
        // 比对结果
        const surplusResult = this.traceSource(
          addOrDel,
          sliceContent,
          sliceLocation,
          paragraphId,
          traceInfo
        );
        sourceInfo.push(...surplusResult);
      }
      return sourceInfo;
    },

    handleOffset(location, content, record, paragraphTrace) {
      for (let index = 0; index < record.record.length; index++) {
        const element = record.record[index];
        let isJump = true; // 找到对比开始的位置
        let offset = element[2].indexOf(content) + element[1];
        // 从匹配到的位置开始
        for (let i = 0; i < paragraphTrace.length; i++) {
          const trace = paragraphTrace[i].record; // 这里还是一个数组
          // 需要确定当前记录在该段所有记录中的index
          for (let j = 0; j < trace.length; j++) {
            // 同一段的某个修改记录
            const para_record = trace[j];
            if (
              para_record[0] === element[0] &&
              para_record[1] === element[1] &&
              para_record[2] === element[2]
            ) {
              isJump = false;
            }
            // 新增的内容，偏移之后的会对其位置有影响
            // 删除的内容，偏移之前的会对其位置有影响
            if (
              (isJump && element[0] === 1) ||
              (!isJump && element[0] === -1)
            ) {
              continue;
            }
            // 修改记录在偏移之前的才会都目标内容产生影响
            if (para_record[1] < offset) {
              if (para_record[0] === -1) {
                // 匹配到的修改记录的所有内容，在某个节点完全删除了，那么这个记录不是所要找的记录
                if (
                  offset === para_record[1] &&
                  para_record[2].length >= element[2].length &&
                  element[0] === 1
                ) {
                  return false;
                }
                // 删除的情况和新增字符的情况相反
                if (element[0] === -1) {
                  offset += para_record[2].length;
                } else {
                  offset -= para_record[2].length;
                }
              } else if (para_record[0] === 1) {
                // 新增字符的情况
                if (element[0] === -1) {
                  offset -= para_record[2].length;
                } else {
                  offset += para_record[2].length;
                }
              }
            }
          }
        }
        if (offset === location) {
          return true;
        } else {
          return false;
        }
      }
    },
    handleImageOffset(str) {
      for (const imageId in this.traceImageMap) {
        str = str.replace("$$$" + imageId + "$$$", " ");
      }
      for (const widgetId in this.traceCaliperMap) {
        str = str.replace("$$$" + widgetId + "$$$", " ");
      }
      return str;
    },
    /**
     * 用户登录
     * @param userInfo
     */
    userLogin(userInfo) {
      this.traceId = getUUID("trace");
      this.currentUser = userInfo;
      this.editor.userLogin(userInfo);
    },
    getStrByRawPara(para, rawData) {
      let str = "";
      para.children.forEach((o) => {
        if (o.type === "image") {
          str += `$$$${o.id}$$$`;
          const srcData = rawData.imageSrcObj
            ? rawData.imageSrcObj[o.src]
            : null;
          this.traceImageMap[o.id] = {
            type: o.type,
            src: srcData ?? o.src,
            meta: o.meta,
            width: o.width,
            height: o.height,
          };
        } else if (o.type === "widget") {
          if (o.widgetType === "caliper") {
            str += `$$$${o.font_id}$$$`;
            this.traceCaliperMap[o.font_id] = {
              type: o.type,
              widgetType: "caliper",
              params: o.params,
              height: o.height,
              font_id: o.font_id,
              field_id: o.field_id,
              field_position: o.field_position,
              selectNum: o.selectNum,
            };
          } else if (o.widgetType === "radio") {
            str += `$$$${o.id + "-" + o.selected}$$$`;
            this.traceCaliperMap[o.id + "-" + o.selected] = {
              type: o.type,
              widgetType: "radio",
              params: o.params,
              height: o.height,
              font_id: o.font_id,
              field_id: o.field_id,
              field_position: o.field_position,
              selectNum: o.selectNum,
              selected: o.selected,
              disabled: 1,
            };
          } else if (o.widgetType === "checkbox") {
            str += `$$$${o.id + "-" + o.selected}$$$`;
            this.traceCaliperMap[o.id + "-" + o.selected] = {
              type: o.type,
              widgetType: "checkbox",
              params: o.params,
              height: o.height,
              font_id: o.font_id,
              field_id: o.field_id,
              field_position: o.field_position,
              selectNum: o.selectNum,
              selected: o.selected,
              disabled: 1,
            };
          } else {
            str += "  ";
          }
        } else if (o.type === "text") {
          str += o.value;
        }
        if (o.children) {
          str += this.getStrByRawPara(o, rawData);
        }
      });
      return str;
    },
    /**
     * 获取简单的数据结构
     * @param data rawData.content(段落和表格的集合)或者cell.children
     */
    getSimpleParaArr(data, rawData) {
      const arr = [];

      // 遍历 data 数组
      data.forEach((d) => {
        if (d.type === "p") {
          // 处理段落
          arr.push({
            id: d.id,
            value: this.getStrByRawPara(d, rawData),
            structure: "para",
          });
        } else if (d.type === "table") {
          // 对表格单元格按位置排序，确保顺序一致
          d.cells.sort((a, b) => {
            const [ax, ay] = a.pos;
            const [bx, by] = b.pos;
            if (ax !== bx) {
              return ax - bx;
            }
            return ay - by;
          });

          // 简化单元格数据
          const cells = d.cells.map((cell) => {
            return {
              ...cell,
              children: this.getSimpleParaArr(cell.children, rawData),
            };
          });

          // 添加处理后的表格到结果数组
          arr.push({ ...d, cells, structure: "table", tableId: d.id });
        }
      });
      // const traceInfo = rawData.meta.traceInfo;
      // if (traceInfo.length) {
      //   const paras = traceInfo[traceInfo.length - 1].paras;
      //   if (paras) {
      //     paras.forEach((para) => {
      //       if (para.id) {
      //         arr.push({
      //           id: para.id,
      //           value: para.value,
      //           structure: "para",
      //         });
      //       }
      //     });
      //   }
      // }

      return arr;
    },

    getSimpleArr(rawData1, rawData2) {
      // 要对比出来那个版本是前边的哪个版本是后边的 原理是谁的traceInfo长 谁就是后边的版本
      if (rawData1?.meta?.traceInfo && rawData2?.meta?.traceInfo) {
        // 第一： 如果两个rawData都有traceInfo 那么比较长短 长的在后边 短的在前边
        if (rawData1.meta.traceInfo.length > rawData2.meta.traceInfo.length) {
          [rawData1, rawData2] = [rawData2, rawData1]; // 交换他俩的位置 rawData2赋值给rawData1 rawData1赋值给了rawData2
        }
      } else {
        // 都没有 或者一个有 一个没有
        // ① 都没有 那么就按照原来的位置 不需要更换
        // ② 一个有 一个没有  前边的没有也不需要换
        // ③ 就只有后边的没有 并且前边的有 才需要更换位置
        if (rawData1?.meta?.traceInfo && !rawData2?.meta?.traceInfo) {
          [rawData1, rawData2] = [rawData2, rawData1];
        }
      }
      const getRes1 = this.getSimpleParaArr(rawData1.content, rawData1);
      const getRes2 = this.getSimpleParaArr(rawData2.content, rawData2);
      const headerRes1 = this.getSimpleParaArr(rawData1.header, rawData1);
      const headerRes2 = this.getSimpleParaArr(rawData2.header, rawData2);
      return [getRes1, getRes2, headerRes1, headerRes2];
    },
    // 第二步：根据ID值是否一致, 完善两个数据结构, 获取两个数据的全集 标记状态(增加，删除，不变) 并返回
    getDiffStructure(d1, d2) {
      let i = 0;
      // base.length和compare.length不一致 要进循环 i不等于base.length-1并且也不等于base.length-1也要进循环
      while (!(d1.length === d2.length && i === d1.length)) {
        if (!d1[i] || !d2[i]) {
          // 两个数据结构长度 有可能是不一致的 已经不一致了 就说明有一个到头了 就不用继续下边的判断ID了
          if (!d1[i] && d2[i]) {
            // 如果d1[i]没有 那么就是d1得加上 不用重新生成id，因为在保存修改痕迹的时候会记录这个id，再重新生成就找不到了
            d1.splice(i, 0, {
              ...d2[i],
              /* id: uuid(d2[i].type), */ type: 1,
              i,
            });
          }
          if (d1[i] && !d2[i]) {
            d2.splice(i, 0, { id: "placeholder" });
            d1[i]["type"] = -1;
            d1[i]["i"] = i;
          }
          i++;
          continue;
        }

        d1[i]["type"] = 0;

        if (d1[i].id !== d2[i].id) {
          // 不相等 就有两种情况 ①base中有 compare中没有 那么就该画删除线 ② base中没有 compare中有 那么就该加上
          // ① compare里边有没有
          const isExist = d2.find((compareData) => compareData.id === d1[i].id);
          if (!isExist) {
            // 如果compare里边没有  那么他就应该是加删除线的
            d1[i]["type"] = -1;
            d1[i]["i"] = i;
            d2.splice(i, 0, { id: "占位" });
            i++;
            continue;
          }
          // ② compare中有 就是说base中没有 得添加上
          d1.splice(i, 0, { ...d2[i], /* id: uuid(d2[i].type), */ type: 1, i });
          i++;
          continue;
        }
        i++;
      }
      return { d1, d2 };
    },
    getDiffData(data1, data2, traceInfo, createDate, userName) {
      let { d1, d2 } = this.getDiffStructure(data1, data2);
      let diffData = d1;
      for (let i = 0; i < d1.length; i++) {
        if (diffData[i].type === 0) {
          if (diffData[i].id.startsWith("para-")) {
            // 处理段落变更
            const change = this.mainDiff(
              d1[i].id,
              d1[i]["value"],
              d2[i]["value"],
              traceInfo
            );
            diffData[i] = { id: diffData[i].id, structure: "para", change };
          } else {
            // 处理表格变更
            const cells1 = diffData[i].cells.map((cell) => ({
              ...cell,
              structure: "cell",
            }));
            const cells2 = d2[i].cells.map((cell) => ({
              ...cell,
              structure: "cell",
            }));
            const { d1: diffCells1, d2: diffCells2 } = this.getDiffStructure(
              cells1,
              cells2
            );

            const updatedCells = [];

            // 处理单元格变更
            for (let j = 0; j < diffCells1.length; j++) {
              if (diffCells1[j]["type"] === 0) {
                const { d1: paraDiff1, d2: paraDiff2 } = this.getDiffStructure(
                  diffCells1[j].children,
                  diffCells2[j].children
                );

                for (let p = 0; p < paraDiff1.length; p++) {
                  if (paraDiff1[p]["type"] === 0) {
                    const change = this.mainDiff(
                      paraDiff1[p].id,
                      paraDiff1[p]["value"],
                      paraDiff2[p]["value"],
                      traceInfo
                    );
                    paraDiff1[p] = {
                      id: paraDiff1[p].id,
                      structure: "para",
                      change,
                    };
                  } else {
                    const change = this.mainDiff(
                      paraDiff1[p].id,
                      "",
                      paraDiff2[p]["value"] ?? "",
                      traceInfo
                    );
                    const meta = change.length
                      ? change
                      : [
                          {
                            0: paraDiff1[p].type,
                            1: 0,
                            2: paraDiff1[p].value,
                            meta: this.getMeta(
                              traceInfo,
                              paraDiff1[p].id,
                              paraDiff1[p].type
                            ),
                          },
                        ];
                    paraDiff1[p] = {
                      id: paraDiff1[p].id,
                      structure: "para",
                      change: meta,
                    };
                  }
                }
                updatedCells.push({
                  ...diffCells2[j],
                  structure: "cell",
                  children: paraDiff1,
                });
              } else if (diffCells1[j]["type"] === 1) {
                const { d1: newParas } = this.getDiffStructure(
                  diffCells1[j].children,
                  diffCells2[j].children
                );
                const meta = this.getMeta(traceInfo, newParas[0].id, 1);
                const children = newParas.map((d) => ({
                  id: d.id,
                  structure: "para",
                  change: [{ 0: 1, 1: 0, 2: d.value, meta }],
                }));
                updatedCells.push({
                  ...diffCells2[j],
                  structure: "cell",
                  children,
                });
              }
            }
            diffData[i] = { ...d2[i], structure: "table", cells: updatedCells };
          }
        } else {
          const meta = this.getMeta(
            traceInfo,
            diffData[i].id,
            diffData[i].type,
            createDate,
            userName
          );
          if (diffData[i].id.startsWith("para")) {
            let value = diffData[i].value;
            diffData[i] = {
              id: diffData[i].id,
              structure: "para",
              change: [{ 0: diffData[i].type, 1: 0, 2: value, meta }],
            };
          } else {
            const updatedCells = diffData[i].cells.map((cell) => {
              const updatedChildren = cell.children.map((p) => ({
                id: p.id,
                structure: "para",
                change: [{ 0: diffData[i].type, 1: 0, 2: p.value, meta }],
              }));
              return {
                ...cell,
                type: diffData[i].type,
                children: updatedChildren,
              };
            });
            diffData[i] = {
              ...diffData[i],
              structure: "table",
              cells: updatedCells,
              meta,
            };
          }
        }
      }

      return diffData;
    },

    /**
     * 保存修改痕迹接口
     * isMerge 是否合并同一用户同一次登录的痕迹，传入true时会每次保存时获取上一次的痕迹信息id比对，如果相同则将上次痕迹移除再保存当前对比的痕迹。
     */
    saveTraceInfo(isMerge) {
      this.traceImageMap = {};
      this.traceCaliperMap = {};
      const { editor } = this.instance;
      const exception_msg = "内容未修改，无需保存痕迹！";
      //如果内容未变化，则不进行痕迹保存
      if (!editor.is_modified) {
        return { success: false, msg: exception_msg };
      }
      // 获取到需要对比的两个rawData
      const rawData1 = editor.raw;
      const rawData2 = editor.getRawData();
      const allParaId = [];
      const paragraph = editor.root_cell.paragraph;
      paragraph.forEach((para) => {
        allParaId.push(para.id);
      });
      // 通过对比rawData返回异同的痕迹信息
      const traceInfo = this.getDiffTraceInfoByRaw(rawData1, rawData2);
      traceInfo.allParaId = allParaId;
      // 进行验证，当痕迹对比后发现没有不同内容时,不进行保存
      if (!traceInfo.paras.length && !Object.keys(traceInfo.traces).length) {
        return { success: false, msg: exception_msg };
      }
      //如果是合并的话
      if (isMerge) {
        if (editor.document_meta && editor.document_meta.traceInfo) {
          const trace = editor.document_meta.traceInfo.pop();
          if (trace.meta.id !== this.traceId) {
            editor.document_meta.traceInfo.push(trace);
          }
        }
      }
      this.saveEditTrace(traceInfo);
      editor.raw = rawData2;
      return { success: true };
    },

    /**
     *  通过rawData对比出异常痕迹主要方法
     *  @param rawData1
     *  @param rawData2
     */
    getDiffTraceInfoByRaw(rawData1, rawData2) {
      const traceInfo = {};
      const res = this.getSimpleArr(rawData1, rawData2);
      const { d1, d2 } = this.getDiffStructure(res[0], res[1]);
      // const contentRes = this.getDiffStructure(res[0], res[1]);
      const headerRes = this.getDiffStructure(res[2], res[3]);
      const d3 = headerRes.d1;
      const d4 = headerRes.d2;
      let needRecord = []; // 只需要记录增加或者删除的段落(表格)
      traceInfo.paras = [];
      traceInfo.traces = {};
      for (let i = 0; i < d1.length; i++) {
        if (d1[i]["type"] === 0) {
          if (d1[i].structure !== "table") {
            const change = this.diffParaText(d1[i]["value"], d2[i]["value"]);
            change?.length && (traceInfo.traces[d1[i].id] = change);
          } else {
            // 如果是表格的话
            // 1、先对比单元格 是否有新增的单元格和删除的单元格 单元格只记录新增和修改的 删除的就不管了 这是讨论过后的原则
            const { d1: cells1, d2: cells2 } = this.getDiffStructure(
              d1[i].cells,
              d2[i].cells
            );
            for (let j = 0; j < cells1.length; j++) {
              const { d1: paras1, d2: paras2 } = this.getDiffStructure(
                cells1[j].children,
                cells2[j].children || []
              );
              if (cells1[j]["type"] === 0) {
                // 2、该单元格是原来就有的 那么判断 里边的段落 是否有新增 删除 或者修改的
                for (let m = 0; m < paras1.length; m++) {
                  if (paras1[m]["type"] === 0) {
                    // 3、对比段落的痕迹
                    const a = this.diffParaText(
                      paras1[m]["value"],
                      paras2[m]["value"]
                    );
                    a?.length && (traceInfo.traces[paras1[m].id] = a);
                  } else {
                    needRecord.push({ id: paras1[m].id, type: paras1[m].type });
                    traceInfo.traces[paras1[m].id] = [
                      { 0: paras1[m].type, 1: 0, 2: paras1[m].value },
                    ];
                  }
                }
              } else if (cells1[j]["type"] === 1) {
                // 该单元格是新增的 那么里边所有的段落 都属于新增的
                paras1.forEach((p) => (p["type"] = 1));
                needRecord = [...needRecord, ...paras1];
              }
            }
          }
        } else {
          needRecord.push(d1[i]);
        }
      }
      for (let i = 0; i < d3.length; i++) {
        if (d3[i]["type"] === 0) {
          if (d3[i].structure !== "table") {
            const change = this.diffParaText(d3[i]["value"], d4[i]["value"]);
            change?.length && (traceInfo.traces[d3[i].id] = change);
          } else {
            // 如果是表格的话
            // 1、先对比单元格 是否有新增的单元格和删除的单元格 单元格只记录新增和修改的 删除的就不管了 这是讨论过后的原则
            const { d1: cells1, d2: cells2 } = this.getDiffStructure(
              d3[i].cells,
              d4[i].cells
            );
            for (let j = 0; j < cells1.length; j++) {
              const { d1: paras1, d2: paras2 } = this.getDiffStructure(
                cells1[j].children,
                cells2[j].children || []
              );
              if (cells1[j]["type"] === 0) {
                // 2、该单元格是原来就有的 那么判断 里边的段落 是否有新增 删除 或者修改的
                for (let m = 0; m < paras1.length; m++) {
                  if (paras1[m]["type"] === 0) {
                    // 3、对比段落的痕迹
                    const a = this.diffParaText(
                      paras1[m]["value"],
                      paras2[m]["value"]
                    );
                    a?.length && (traceInfo.traces[paras1[m].id] = a);
                  } else {
                    needRecord.push({ id: paras1[m].id, type: paras1[m].type });
                    traceInfo.traces[paras1[m].id] = [
                      { 0: paras1[m].type, 1: 0, 2: paras1[m].value },
                    ];
                  }
                }
              } else if (cells1[j]["type"] === 1) {
                // 该单元格是新增的 那么里边所有的段落 都属于新增的
                paras1.forEach((p) => (p["type"] = 1));
                needRecord = [...needRecord, ...paras1];
              }
            }
          }
        } else {
          needRecord.push(d3[i]);
        }
      }
      traceInfo.paras = needRecord;
      traceInfo.meta = {
        id: this.traceId ?? getUUID("trace-"),
        userId: this.currentUser ? this.currentUser.id : "-1", // -1 代表未登录
        date: Date.now(),
      };
      return traceInfo;
    },
    /**
     * 保存修改痕迹
     * @param traceInfo
     */
    saveEditTrace(traceInfo) {
      const { document_meta } = this.instance.editor;
      if (!document_meta.traceInfo) {
        document_meta.traceInfo = [];
      }
      document_meta.traceInfo.push(traceInfo);
    },
    setPageDirection(traceRawData, direction) {
      // traceRawData 是引用类型 修改完了不用返回
      if (traceRawData.config) {
        traceRawData.config.direction = direction;
      } else {
        traceRawData.config = {};
        traceRawData.config.direction = direction;
      }
    },
    compareGroupTraces(rawData, rawData2, showPersonInfo = false, groupId) {
      //TODO  tang 研究研究
      const editor1 = this.instance.editor.copyEditor();
      const editor2 = this.instance.editor.copyEditor();
      editor1.reInitRaw(rawData);
      editor2.reInitRaw(rawData2);
      const date = new Date();

      let group1 = editor1.selection.getGroupByGroupId(groupId);
      let group2 = editor2.selection.getGroupByGroupId(groupId);
      let groupData1;
      let groupData2;
      let createDate;
      let userId;
      groupData1 = group1 ? editor1.getGroupRawData(group1) : "";
      groupData2 = group2 ? editor2.getGroupRawData(group2) : "";
      if (!group1) {
        group1 = editor1.createElement("group", { groupId, date });
        groupData1 = editor1.getGroupRawData(group1);
        createDate = group2.date;
        userId = group2.meta.createUserId;
      }
      if (!group2) {
        group2 = editor2.createElement("group", { groupId, date });
        groupData2 = editor2.getGroupRawData(group2);
        createDate = group1.date;
        userId = group1.meta.createUserId;
        if (group1) {
          if (groupData1.content.length) {
            groupData2.content = JSON.parse(JSON.stringify(groupData1.content));
            groupData2.content = [groupData2.content.pop()];
            if (groupData2.content) {
              groupData2.content[0].children.forEach((child) => {
                child.value = "";
              });
            }
          }
        }
      }
      const raw = this.compareTraces(
        groupData1,
        groupData2,
        showPersonInfo,
        createDate,
        userId
      );
      if (raw.content.length === 0)
        [(raw.content = this.editor.config.rawData.content)];
      return raw;
    },
    compareTraces(
      rawData,
      rawData2,
      showPersonInfo = false,
      createDate,
      userId,
      useNewCompare
    ) {
      this.traceImageMap = {};
      this.traceCaliperMap = {};
      if (Object.prototype.toString.call(rawData) !== "[object Object]") {
        rawData = JSON.parse(rawData);
      }
      if (Object.prototype.toString.call(rawData2) !== "[object Object]") {
        rawData2 = JSON.parse(rawData2);
      }
      this.editorId = getUUID("editor");
      let swapPositionRawData1 = rawData;
      let swapPositionRawData2 = rawData2;

      const traceInfo1 = rawData?.meta?.traceInfo;
      const traceInfo2 = rawData2?.meta?.traceInfo;
      if (traceInfo1 && traceInfo2) {
        if (traceInfo1.length > traceInfo2.length) {
          [swapPositionRawData1, swapPositionRawData2] = [rawData2, rawData]; // 交换他俩的位置 rawData2赋值给rawData1 rawData1赋值给了rawData2
        }
      } else {
        if (!traceInfo1) {
          // traceInfo1 不存在 不管traceInfo2 有没有 都不用交换位置
          [swapPositionRawData1, swapPositionRawData2] = [rawData, rawData2];
        } else if (!traceInfo2) {
          // traceInfo1 存在 那么 traceInfo2 没有的话 才需要交换位置
          [swapPositionRawData1, swapPositionRawData2] = [rawData2, rawData];
        }
      }

      const res = this.getSimpleArr(swapPositionRawData1, swapPositionRawData2);
      const preTraceInfo = swapPositionRawData1?.meta?.traceInfo || [];
      const nextTraceInfo = swapPositionRawData2?.meta?.traceInfo || [];
      const lastTraceInfo = nextTraceInfo.slice(
        preTraceInfo.length,
        nextTraceInfo.length + 1
      );
      let contentRes = this.getDiffData(
        res[0],
        res[1],
        lastTraceInfo,
        createDate,
        userId
      );
      const headerRes = this.getDiffData(res[2], res[3], lastTraceInfo);

      if (useNewCompare) {
        // 获得所有添加或者删除的段落;
        let allChangeParas = this.handlePastTracePara(lastTraceInfo);
        contentRes = this.getParaDiffData(
          lastTraceInfo,
          allChangeParas,
          contentRes
        );
      }

      F: for (const data of contentRes) {
        if (!data.cells) {
          for (const o of data.change) {
            if (o["0"] !== 0 && !o.meta) {
              showPersonInfo = false;
              break F;
            }
          }
        } else {
          for (const cell of data.cells) {
            for (const obj of cell.children) {
              for (const o of obj.change) {
                if (o["0"] !== 0 && !o.meta) {
                  showPersonInfo = false;
                  break F;
                }
              }
            }
          }
        }
      }
      F: for (const data of headerRes) {
        if (!data.cells) {
          for (const o of data.change) {
            if (o["0"] !== 0 && !o.meta) {
              showPersonInfo = false;
              break F;
            }
          }
        } else {
          for (const cell of data.cells) {
            for (const obj of cell.children) {
              for (const o of obj.change) {
                if (o["0"] !== 0 && !o.meta) {
                  showPersonInfo = false;
                  break F;
                }
              }
            }
          }
        }
      }
      const traceRawData = this.showCompareTraces(
        contentRes,
        swapPositionRawData2,
        showPersonInfo
      );
      const traceHeaderRawData = this.showCompareTraces(
        headerRes,
        swapPositionRawData2,
        showPersonInfo,
        "header"
      );
      traceRawData.header = traceHeaderRawData.header;
      traceRawData.meta.traceInfo = [
        ...traceRawData.meta.traceInfo,
        ...traceHeaderRawData.meta.traceInfo,
      ];
      if (showPersonInfo) {
        this.instance.editor.view_mode = "person";
      } else {
        this.instance.editor.view_mode = "noPerson";
      }
      // 对比结果的页面方向按照最新数据的页面来判断
      if (swapPositionRawData2?.config?.direction === "horizontal") {
        this.setPageDirection(traceRawData, "horizontal");
      } else {
        this.setPageDirection(traceRawData, "vertical");
      }
      return traceRawData;
    },

    // 痕迹只有新增和删除
    // 新增：
    // 最新版跟次新版对比出来新增的痕迹 在跟更次新版对比的时候应该当做不存在 因为次新版里没有
    // 删除
    // 最新版跟次新版对比出来删除的痕迹 在跟更次新版对比的时候 每个字都应该参与到跟更次新版对比中去 因为这个痕迹补全了才是完整的次新版的内容
    // 而这个删除的痕迹对象应当视为次新版中本来就有的数据去做处理 就是说应该被拆分 补全 什么的 正常处理这个 obj 对象 但是原来的痕迹样式还是要保留的
    // 所有新增都当不存在 但是所有删除都要保留下来
    // 再明确一下：所有新增都不参与 所有删除都要保留并且参与计算位置 直到变成新增(或者直到最早的一次版本中就有)为止 那么在某一次新增的时候要知道 新增的是不是就是后边删除的这个数据
    // 比如中间新增了 3 个 4 但是后边又删除了 一个 4 删除的这个 4 已经显示了 那么我应该在新的对比中其实就只新增了两个 4 怎么搞定这个
    // 不对 从再明确一下开始 想的就不对 新增了 3 个 4 就要显示 3 个 4 不能显示 两个 4 即便最后一个 4 被后边给删了 因为我要完整记录这次修改的痕迹 我要完整记录每次的修改痕迹
    // 但是  所有新增都不参与 所有删除都要保留并且参与计算位置 直到变成新增(或者直到最早的一次版本中就有)为止 这句话还是对的
    newVersionCompareTraces(rawData, rawData2, showPersonInfo = false) {
      // 我始终都是基于 rawData 修改 rawData.content 里边的每个对象
      this.traceImageMap = {};
      this.traceCaliperMap = {};
      if (Object.prototype.toString.call(rawData) !== "[object Object]") {
        rawData = JSON.parse(rawData);
      }
      if (Object.prototype.toString.call(rawData2) !== "[object Object]") {
        rawData2 = JSON.parse(rawData2);
      }
      this.editorId = getUUID("editor");
      let swapPositionRawData1 = rawData;
      let swapPositionRawData2 = rawData2;

      const traceInfo1 = rawData?.meta?.traceInfo;
      const traceInfo2 = rawData2?.meta?.traceInfo;
      if (traceInfo1 && traceInfo2) {
        if (traceInfo1.length > traceInfo2.length) {
          [swapPositionRawData1, swapPositionRawData2] = [rawData2, rawData]; // 交换他俩的位置 rawData2赋值给rawData1 rawData1赋值给了rawData2
        }
      } else {
        if (!traceInfo1) {
          // traceInfo1 不存在 不管traceInfo2 有没有 都不用交换位置
          [swapPositionRawData1, swapPositionRawData2] = [rawData, rawData2];
        } else if (!traceInfo2) {
          // traceInfo1 存在 那么 traceInfo2 没有的话 才需要交换位置
          [swapPositionRawData1, swapPositionRawData2] = [rawData2, rawData];
        }
      }

      // 创建删除和新增的样式 ↓
      // 获取修改用户信息
      const userInfoList = rawData2?.meta?.userInfo || [];
      // 各用户展示颜色，数组后两位，null代表默认背景颜色，red代表删除颜色，其他颜色则为多用户各自颜色

      // 用户信息和对应的样式id的数组
      const userAddStyleList = [];
      const userDeleteStyleList = [];

      if (showPersonInfo && userInfoList && userInfoList.length) {
        for (let j = 0; j < userInfoList.length; j++) {
          this.fontStyleList[j % 10].fontStyle.dblUnderLine = true;
          userAddStyleList.push({
            id: userInfoList[j].id,
            name: userInfoList[j].name,
            font_id: this.fontStyleList[j % 10].fontId,
          });
          userDeleteStyleList.push({
            id: userInfoList[j].id,
            name: userInfoList[j].name,
            font_id:
              this.fontStyleList[(j % 10) + this.bgColorList.length].fontId,
          });
        }
      }
      // 创建删除和新增的样式 ↑

      swapPositionRawData2 = JSON.parse(JSON.stringify(swapPositionRawData2));

      // 接下来就是 在 swapPositionRawData2 的基础上还原成 swapPositionRawData1

      const preTraceInfo = swapPositionRawData1?.meta?.traceInfo || [];
      const nextTraceInfo = swapPositionRawData2?.meta?.traceInfo || [];

      const needTraceInfo = nextTraceInfo.slice(
        preTraceInfo.length,
        nextTraceInfo.length + 1
      ); // 需要处理的痕迹对比 要截取两个版本之间的对比痕迹 进行恢复
      const resRawData = JSON.parse(JSON.stringify(swapPositionRawData2)); // 这个是最终要返回出去的 修改的就是这个数据
      while (needTraceInfo.length) {
        resRawData.meta.traceInfo.length = 0;
        // 一个循环就是一次两个版本的痕迹对比 第一版跟第三版比较 length 就是 2 了 所以这个需要处理的痕迹的长度就是要对比的版本的个数
        const lastTraceInfo = needTraceInfo.pop();
        for (const paraId in lastTraceInfo.traces) {
          // 每次的痕迹对比中所有痕迹都存在了 traces 里边 所以再循环 .traces 按照段落记录的 所以循环处理所有的段落 将所有的段落都处理好就完了 每个循环修改一个段落
          const traces = lastTraceInfo.traces[paraId]; // 这个段落这次痕迹对比记录的所有的 修改痕迹 增删改查(只有增加和删除，改查也可以理解为增加和删除)
          // 我为啥要循环 traces 呢 我应该循环段落 然后去找 traces 这样位置就都是对的 反正我要按照段落进行修复
          let currentTraceIndex = 0; // 是这个段落中所有修改痕迹的下标 依次处理 处理完一个痕迹 traceIndex 就要 +1 再修改下一个
          let currentTrace = traces[currentTraceIndex]; // 每一个痕迹记录 没修正一个就更新
          const para = resRawData.content.find((para) => para.id === paraId); // 这是要修改的
          const readPara = swapPositionRawData2.content.find(
            // 这个 readPara 还有要修改的 para 都是新版的 都是在新版的结果上恢复成旧版的样子 展示出来痕迹
            (para) => para.id === paraId
          ); // 这个不能修改只能用来循环 因为循环修改的 para 下标容易搞错
          let index = 0; // 记录的是这个段落中循环过的每个字(每个元素)的下标 也就是 readPara.children 的下标 readPara 是只读的 跟 痕迹对比里边记录的下标能对应上 也就是说循环段落中的每个东西都要 +1
          // 因为要恢复这个段落到上一个版本所以循环这个段落的 children 恢复的话，也是恢复段落的 children
          // 根据记录的痕迹 处理好 删除的内容 增加的内容  修改的内容等 也就是说我每次只处理一个里边的 obj
          let paraChildrenIndex; // 这个是 para.children 因为是若干个对象 这个值记录的是这个对象的下标 不是 readPara.children 中对象的下标
          for (let i = 0; i < readPara.children.length && currentTrace; i++) {
            let initIndex = index;
            // 之所以要循环 readPara.children 是想要将 para.children 恢复成跟次新版一样 不管使用 for 循环还是要 while 循环 我就是要循环 段落中的每个 obj 再循环 obj 里边的每个字 这个是不变的 要一步一步的走完 段落中的每个字，就是说 index 要从头走到尾
            // TODO 估计这个得跟 下边改为 while 一样 也不能用 for 循环
            let obj = readPara.children[i]; // 循环该段落中的每个对象，样式一样的文字都在一个对象里边了 同类型的也在一个对象里边了 所以我要处理每个 obj 然后用 下边 newObjs 里边的替换掉这个
            const newObjs = []; // 这个是最终处理好的也就是要用的 obj 就不再使用了
            if (obj.type === "text") {
              if (obj.continue) {
                // 这里的是 次新版里没有最新版里有的 新增的数据 所以次新版跟再次新版对比的时候 就应该直接略过 不参与 index 下标的计算
                paraChildrenIndex++;
                continue;
              }
              let processedIndex = 0; // 记录的是 新版的段落 obj.value 已经处理到第几个字儿了 index 是段落中总的 这个 recordIndex 是  obj.value 中的下标 跟 j 一样 只不过 这个是记录的我处理到第几个了
              let objValueIndex = 0; // j 代表 readPara.children (不是 para.children) 里边的对象 obj.value 里边每个字的下标
              // processedIndex 跟 objValueIndex 都是 obj.value 的下标 只不过 objValueIndex 是循环到的每一个 recordIndex 是记录的处理到哪儿了
              while (currentTrace) {
                // 我之所以要 while 循环痕迹 不是 for 循环 obj.value 中的每个字是因为
                // obj.value 因为如果一个100个字的段落删除到只剩俩字 这个 obj.value.length 就是2 了 会有其他痕迹进不来 所以得用 while 循环 实际上这个 while 循环就是循环的 obj.value 中的每个字 objValueIndex 就是每个字的下标
                // 但是 objValueIndex 还有 index 肯定还是要按照 obj.value 中的每个字 一步一步的加 processedIndex 也是要修正
                // 所以在捋逻辑的时候 这个 while 循环就理解成 循环 obj.value 中的每个字就可以了
                // 如果只有 currentTrace 也不行 因为比如 这一个 obj.value 里边就俩字 还有痕迹应该是下一个 obj 里边的了 就循环不到了 应该跳出这个 while 去循环下一个 obj 去 所以再加一个 && currentTrace["1"] <= obj.value.length + index
                // 先去掉 currentTrace["1"] <= obj.value.length + index 这个并且的判断条件 后边再处理跳过 obj 的情况
                // 每次都是 index++ 直到遇到了 痕迹的下标跟 index 一样 就需要处理了 没遇到就继续 index++ 找下一个字
                if (currentTrace["1"] === index) {
                  // 只有 -1 和 1 这两种情况
                  // 只处理有这个下标的就可以了
                  if (currentTrace["0"] === -1) {
                    // 说明是有内容被删除了 所以要补回去 此时就应该将 obj 一拆为 2 然后中间再加上补回去的内容
                    // 说明是第一次进行拆分
                    const pre = {
                      ...obj,
                      value: obj.value.slice(processedIndex, objValueIndex),
                    };
                    const currentUser = userDeleteStyleList.find(
                      (user) => user.id === lastTraceInfo.meta.userId
                    );
                    const current = {
                      ...obj,
                      keep: true, // 所有删除的都要保存下来 后边版本多了 一个 obj 就会同时有 continue 和 keep 应该优先使用 continue 因为再跟往前的版本里比 就是新增了 只是这儿对比是删除
                      font_id: currentUser.font_id,
                      value: currentTrace["2"],
                      reduce: true,
                      meta: JSON.parse(JSON.stringify(lastTraceInfo.meta)),
                    };
                    newObjs.push(pre, current);
                    processedIndex = objValueIndex;
                    currentTrace = traces[++currentTraceIndex];
                    if (!currentTrace) {
                      // 如果这个段落的痕迹都已经处理过了 那么就可以跳出循环了
                      // 并且还要再补上后边的
                      const last = {
                        ...obj,
                        value: obj.value.slice(objValueIndex),
                      };
                      newObjs.push(last);
                      break;
                    }
                  } else if (currentTrace["0"] === 1) {
                    // 新增的 para.children 里边是有的
                    const pre = {
                      ...obj,
                      value: obj.value.slice(processedIndex, objValueIndex),
                    };
                    const currentUser = userAddStyleList.find(
                      (user) => user.id === lastTraceInfo.meta.userId
                    );
                    const current = {
                      ...obj,
                      keep: false, // 已经变成新增了  keep 就不需要保留了
                      font_id: currentUser.font_id, // TODO 暂时这么写死 font_id 后边再改
                      value: currentTrace["2"].slice(
                        0,
                        obj.value.length - processedIndex
                      ),
                      continue: true, // 所有新增的内容都不计入下次痕迹对比的下标计算中 就当这儿的对象不存在 意思是过滤的 index 这个 index 的值在循环到当前 obj 的时候不能有任何变化
                      add: true,
                      meta: JSON.parse(JSON.stringify(lastTraceInfo.meta)),
                    };
                    newObjs.push(pre, current);
                    let handledNum = current.value.length; // 就是 currentTrace["2"] 已经处理了这么多个了
                    if (currentTrace["2"].length > current.value.length) {
                      // 此时也就是说 currentTrace 里边还有值没有处理 但是 当前的 obj 肯定是用完了 需要用到下边的 obj 了
                      // 因为 currentTrace["2"] 那么下边紧挨着的 obj 里边的内容 就一定是处理的 这个新增的 内容
                      let needDeleteNum = 1;
                      for (let j = i + 1; j < readPara.children.length; j++) {
                        needDeleteNum++;
                        // 我循环这个的目的是处理完 currentTrace 就是处理完当前的痕迹
                        const o = readPara.children[j];
                        if (o.continue || !o.value) {
                          newObjs.push(o);
                          continue;
                        } else if (o.reduce) {
                          // TODO 这个删除一定是删除的新增的 这个没问题 但是我在这儿处理没处理完的 currentTrace 就一定对吗？
                          const lCurrent = {
                            // 小 current
                            ...obj,
                            keep: false, // 已经变成新增了  keep 就不需要保留了
                            font_id: currentUser.font_id, // TODO 暂时这么写死 font_id 后边再改
                            value: currentTrace["2"].slice(
                              handledNum,
                              handledNum + o.value.length
                            ),
                            continue: true, // 所有新增的内容都不计入下次痕迹对比的下标计算中 就当这儿的对象不存在 意思是过滤的 index 这个 index 的值在循环到当前 obj 的时候不能有任何变化
                            add: true,
                            meta: JSON.parse(
                              JSON.stringify(lastTraceInfo.meta)
                            ),
                          };
                          newObjs.push(lCurrent, o);
                          handledNum += lCurrent.value.length;
                        } else {
                          const lCurrent = {
                            ...obj,
                            keep: false,
                            font_id: currentUser.font_id,
                            value: currentTrace["2"].slice(handledNum), // 此时应该就直接到最后了吧
                            continue: true,
                            add: true,
                            meta: JSON.parse(
                              JSON.stringify(lastTraceInfo.meta)
                            ),
                          };
                          newObjs.push(lCurrent);
                          handledNum += lCurrent.value.length;
                        }
                        if (handledNum >= currentTrace["2"].length) {
                          index += handledNum;
                          i = j;
                          obj = o;
                          break;
                        }
                      }
                      paraChildrenIndex = paraChildrenIndex || 1;
                      paraChildrenIndex += needDeleteNum;
                    }

                    if (obj.keep) {
                      // 如果需要保留的话 就应该放进去
                      newObjs.push({
                        ...obj,
                        value: obj.value.slice(
                          objValueIndex,
                          objValueIndex + current.value.length
                        ),
                      });
                    }
                    processedIndex = objValueIndex + current.value.length;
                    const nextTrace = traces[++currentTraceIndex];
                    // 不管是修改还是单纯的新增内容 index 都应该直接过好几个字
                    // 如果是修改的话 会先走删除 走删除的时候会判断是否跟后边痕迹位置一样 决定 index 是否++
                    index += current.value.length - 1; // 因为下边还有 index++ 和 j++ 所以这里减 1
                    objValueIndex += current.value.length - 1;

                    // 还有一种情况就是 这个新增的内容在后边的版本中有部分给删除了 比如 这次新增了 444 这三个字 在后边版本中 中间的 4 删除改成了 5
                    // 那么这一次增加了 3 个 4 就不能放一块了
                    // 应该第一个 4 是新增的 第二个 4 是删除的 还有个 5 是新增的 再后边还有个 4 是新增的 这样才对
                    // 问题来了 我怎么判断这 3 个 4 要不要都放在这儿呢？
                    // 我猜 问题应该在 index 的下标的值上 也就是说 index 不能直接如下 += current.value.length - 1
                    // 因为 current.value.length 的个数中有的可能不参与下标计算
                    // index 本来应该是下一个 obj 的第一个字的下标 但是已经超出了 index 就有计算错的风险 所以 index 要限制
                    // 小于等于 initIndex + obj.value.length 里边 如果超出了 就不对 就需要单独处理
                    // 如果 index 在这个范围内还会有问题吗？
                    // 不会有问题 原因都是按照 obj 处理的 一个 obj 里边的文字类型是一样的 要么都是新增 要么都是删除 要么都是未改变的 所以在这里边不会有问题
                    // 那么我就应该根据 index 继续处理 后边的 obj 然后修改 i 的值 再续上继续处理后边的 obj
                    if (index + 1 > initIndex + obj.value.length) {
                      // 此时就说明 index 已经跑到下一个 obj 里边去 并且还不是 下一个 obj 的第一个字的下标了
                      // index 的值应该是 initIndex + obj.value.length 才对
                      // 这里还能继续处理 current
                      // 还有一个思路就是拆分 痕迹 拆成俩 继续循环 这个再说吧先实现这个
                    }

                    currentTrace = nextTrace;

                    if (!currentTrace) {
                      // 如果这个段落的痕迹都已经处理过了 那么就可以跳出循环了
                      // 并且还要再补上后边的
                      const last = {
                        ...obj,
                        value: obj.value.slice(processedIndex),
                      };
                      newObjs.push(last);
                      break;
                    }
                  }
                }

                // 这儿的 currentTrace 实际上已经是下一个了 如果下一个痕迹还跟当前处理的痕迹的下标一样 那么就是修改 index 是不能动的 所以加判断只有不是修改的时候才 ++
                if (currentTrace && currentTrace["1"] !== index) {
                  // 如果有修改的情况 那么痕迹对比里边记录的下标是一样的 所以这个时候 index 就不能++ 了 否则就会死循环 objValueIndex 一样的
                  obj.value.length && index++; // 只有真正的经历了一个字之后才能 ++ 如果是个空的 obj 那就没必要 ++
                  obj.value.length && objValueIndex++;
                  // 初始的下标 + obj.value.length 再减去 1 就是当前 obj 最后一个字的下标 比如
                  // 初始下标为 0 obj 里边就一个字 0 + 1 = 1 然后 1 - 1 = 0 就是那里边一个字的下标 如果 index 已经等于那个字的下标了 就该下一个 obj 了
                  if (index > initIndex + obj.value.length - 1) {
                    // 这个时候也会漏掉一部分 所以追加进去
                    const last = {
                      ...obj,
                      value: obj.value.slice(processedIndex),
                    };
                    newObjs.push(last);
                    // 如果还有痕迹 但是段落已经循环完了 痕迹不能丢 所以还是得塞进去
                    if (i >= readPara.children.length - 1) {
                      if (currentTrace["0"] === -1) {
                        const current = {
                          ...obj,
                          keep: true,
                          font_id: userDeleteStyleList[0].font_id,
                          value: currentTrace["2"],
                          reduce: true,
                          meta: JSON.parse(JSON.stringify(lastTraceInfo.meta)),
                        };
                        newObjs.push(current);
                      }
                    }

                    // 此时的 index 就应该指向下一个 obj 了 break 之后就会跳出这个 while 循环
                    break;
                  }
                  // 不能无限制的往下加 因为 index 有可能已经大于 obj.value 的长度了 但是还有 currentTrace index 再往下加没问题 但是就不是当前的这个 obj 了
                  // 应该是下一个 obj 了 所以这种情况 需要处理一下 while 的判断条件好像也不对了
                  // 也就是说 我要确保 currentTrae 要在当前的 obj 里边 其实我只要判断出来 index 已经超出当前的 obj 了就可以了我就跳出该循环 那么 while 循环里边的判断条件也用不着了
                }
              }
            }
            paraChildrenIndex = paraChildrenIndex || i; // 第一次是 i 但是下次就不一定是 i 了 所以处理过一次就要修改 paraChildrenIndex 的值
            if (newObjs.length > 0) {
              para.children.splice(paraChildrenIndex, 1, ...newObjs);
            }

            paraChildrenIndex += newObjs.length || 1; // 至少得增加 1 个吧
          }

          const traceInfo = {
            id: paraId,
            structure: "para",
            change: [],
          };
          let p = 0; // 记录 para.children 中每个字的下标
          for (let c = 0; c < para.children.length; c++) {
            const obj = para.children[c];
            // 有的 obj 会既有 add 又有 reduce 但是 add 要靠前
            if (obj.add) {
              traceInfo.change.push({
                0: 1,
                1: p,
                2: obj.value,
                meta: obj.meta,
              });
            } else if (obj.reduce) {
              traceInfo.change.push({
                0: -1,
                1: p,
                2: obj.value,
                meta: obj.meta,
              });
            } else {
              traceInfo.change.push({
                0: 0,
                1: p,
                2: obj.value,
                meta: obj.meta,
              });
            }
            p += obj.value.length;
          }

          resRawData.meta.traceInfo.push(traceInfo);
        }
        const font_style = {};
        for (let i = 0; i < this.fontStyleList.length; i++) {
          font_style[this.fontStyleList[i].fontId] =
            this.fontStyleList[i].fontStyle;
        }
        resRawData.fontMap = Object.assign({}, resRawData.fontMap, font_style);
        swapPositionRawData2 = JSON.parse(JSON.stringify(resRawData));
      }
      return resRawData;
    },
    getParaDiffData(lastTraceInfo, allChangeParas, contentRes) {
      let allTraceParaId = [];
      lastTraceInfo.forEach((trace) => {
        const allParaId = trace.allParaId;
        if (allParaId && allParaId.length) {
          allParaId.forEach((id) => {
            allTraceParaId.push(id);
          });
        }
      });
      let paraTraceData = [];
      allTraceParaId = Array.from(new Set(allTraceParaId));

      for (let i = 0; i < allChangeParas.length; i++) {
        const changePara = allChangeParas[i];
        const addOrDelInfo = {};
        addOrDelInfo.id = changePara.id;
        if (addOrDelInfo.id.startsWith("para-")) {
          addOrDelInfo.structure = "para";
          addOrDelInfo.change = [];

          // 处理 change 里面的信息
          const changeInfo = {};
          changeInfo[0] = changePara[0] ?? changePara.type;
          let offset = 0;
          if (paraTraceData.length) {
            paraTraceData.forEach((para) => {
              if (para.id === changePara.id) {
                offset += changePara.value.length;
              }
            });
          }
          changeInfo[1] = offset;
          changeInfo[2] = changePara.value;
          changeInfo.meta = changePara.meta;

          // 到这里 change 的信息组装完毕
          addOrDelInfo.change.push(changeInfo);
          paraTraceData.push(addOrDelInfo);
        }
      }
      //把同名的段落改动情况合并到一个段落信息里
      paraTraceData = this.mergeParaChange(paraTraceData);
      if (paraTraceData.length) {
        paraTraceData.forEach((data) => contentRes.push(data));
      }

      //现在拿到了合并后的段落信息和改变之后的表格信息，如果最后一次也变动了表格就把所有改动合并到现在的表格里
      contentRes = this.sortRecordsById(contentRes, allTraceParaId);
      //把组装好的信息返回出去
      return contentRes;
    },
    // handleAllTraceData(contentRes, paraTraceData, allTraceParaId) {
    //   for (let i = 0; i < contentRes.length; i++) {
    //     const content = contentRes[i];
    //     for (let j = 0; j < paraTraceData.length; j++) {
    //       const paraTrace = paraTraceData[j];
    //       if (content.id === paraTrace.id) {
    //         if (content.id.startsWith("table-")) {
    //           const cells = content.cells;
    //           cells.forEach((cell, cellIndex) => {
    //             const traceCells = paraTrace.cells;
    //             for (let m = 0; m < traceCells.length; m++) {
    //               const traceCell = traceCells[m];
    //               if (cell.id === traceCell.id) {
    //                 cells[cellIndex] = this.copyCellChange(
    //                   cell,
    //                   traceCell,
    //                   allTraceParaId
    //                 );
    //               }
    //             }
    //           });
    //         }
    //       }
    //     }
    //   }
    //   return contentRes;
    // },

    // copyCellChange(cell, copyCell, allTraceParaId) {
    //   const children = cell.children;
    //   let childrenToAdd = [];

    //   const existingChildIds = new Set(children.map((child) => child.id));

    //   for (let i = 0; i < children.length; i++) {
    //     const child = children[i];
    //     const traceChildren = copyCell.children;

    //     for (let j = 0; j < traceChildren.length; j++) {
    //       const traceChild = traceChildren[j];

    //       if (traceChild.id === child.id) {
    //         traceChild.change.forEach((c) => {
    //           if (c.meta) {
    //             const childCHange = child.change;
    //             let flag = false;
    //             childCHange.forEach((cc) => {
    //               if (cc[0] === c[0] && cc[2] === c[2]) {
    //                 flag = true;
    //               }
    //             });
    //             if (!flag) {
    //               child.change.push(c);
    //             }
    //           }
    //         });
    //       } else {
    //         if (
    //           traceChild.change.length > 1 &&
    //           !existingChildIds.has(traceChild.id)
    //         ) {
    //           childrenToAdd.push(traceChild);
    //           existingChildIds.add(traceChild.id); // Add the new child's id to the set to avoid duplicates
    //         }
    //       }
    //     }
    //   }

    //   // 添加需要新增的元素
    //   childrenToAdd.forEach((childToAdd) => {
    //     children.push(childToAdd);
    //   });

    //   cell.children = this.sortRecordsById(children, allTraceParaId);
    //   return cell;
    // },
    sortRecordsById(records, order) {
      // 创建一个字典，用于快速查找顺序数组中的位置
      const orderIndexMap = order.reduce((acc, id, index) => {
        acc[id] = index;
        return acc;
      }, {});
      // 对 records 进行排序
      return records.sort((a, b) => {
        // 获取 a 和 b 的 id 在 order 中的位置
        const indexA = orderIndexMap[a.id];
        const indexB = orderIndexMap[b.id];
        // 如果 a 和 b 的 id 都存在于 order 中，按顺序排列
        if (indexA !== undefined && indexB !== undefined) {
          return indexA - indexB;
        }
        // 如果 a 的 id 存在于 order 中而 b 的 id 不存在，a 优先
        if (indexA !== undefined) {
          return -1;
        }
        // 如果 b 的 id 存在于 order 中而 a 的 id 不存在，b 优先
        if (indexB !== undefined) {
          return 1;
        }
        // 如果 a 和 b 的 id 都不存在于 order 中，保持原样
        return 0;
      });
    },
    mergeParaChange(data) {
      if (!data.length) return [];
      const mergedData = {};
      data.forEach((record) => {
        const recordId = record.id;
        if (recordId.startsWith("para-")) {
          if (!mergedData[recordId]) {
            // 如果这个 id 还没有被添加到字典中，初始化一个新的记录
            mergedData[recordId] = {
              id: recordId,
              structure: record.structure,
              change: [],
            };
            // 将当前记录的 change 合并到对应的 id 下
          } else if (recordId.startsWith("table-")) {
            return data;
          }
        }
        mergedData[recordId].change = mergedData[recordId].change.concat(
          record.change
        );
      });
      // 将合并后的字典转换回列表
      return Object.values(mergedData);
    },
    handlePastTracePara(lastTraceInfo) {
      let allChange = []; // 初始化存储所有变化的数组
      let allParaChange = {};
      const traceInfoCopy = lastTraceInfo.slice(0, lastTraceInfo.length - 1); // 创建一个不包含最后一个元素的副本

      traceInfoCopy.forEach((perTraceInfo) => {
        const paras = perTraceInfo.paras; // 获取段落信息
        const meta = perTraceInfo.meta; // 获取元数据

        if (paras.length) {
          for (let i = 0; i < paras.length; i++) {
            const para = paras[i];
            const paraId = para.id;
            if (allParaChange[paraId]) {
              allParaChange[paraId].push({ ...para, meta });
            } else {
              allParaChange[paraId] = [];
              allParaChange[paraId].push({ ...para, meta });
            }
          }
        }
      });
      for (let key in allParaChange) {
        if (allParaChange[key].length === 2) {
          allParaChange[key].forEach((change) => {
            allChange.push(change);
          });
        }
      }

      return allChange; // 返回包含所有变化的数组
    },
    // 获取最后的修改记录 如果整个表格的增加和删除 会把表格里边的段落拿出来平铺 返回
    getLastModificationRecord(rawData) {
      const lastTraceInfo =
        rawData.meta.traceInfo[rawData.meta.traceInfo.length - 1];
      const paras = lastTraceInfo.paras;
      for (let i = 0; i < paras.length; ) {
        const para = paras[i];
        const tableParas = [];
        if (para.cells) {
          for (const cell of para.cells) {
            cell.children.forEach((o) => {
              tableParas.push({ ...o, type: para.type });
            });
          }
          paras.splice(i, 1, ...tableParas);
          i += tableParas.length;
        } else {
          i++;
        }
      }
      const traces = lastTraceInfo.traces;
      return { paras, traces };
    },
    getMeta(traceInfo, id, type, createDate, userId) {
      if (!traceInfo) return null;
      for (const trace of traceInfo) {
        // 如果拿对比后的结果做对比 直接return null 不返回人的信息
        if (trace.change) return null;
      }
      let meta = null;
      for (let t = traceInfo.length - 1; t >= 0; t--) {
        const everyRecord = traceInfo[t];
        if (!everyRecord.paras.length) {
          meta = traceInfo[0].meta;
          if (createDate) {
            meta.date = createDate;
            meta.userId = userId;
          }
        }
        for (let p = everyRecord.paras.length - 1; p >= 0; p--) {
          const para = everyRecord.paras[p];
          if (para.id === id && para.type === type) {
            meta = everyRecord.meta;
            break;
          }
        }
      }
      return meta;
    },
    showCompareTraces(data, rawData2, showPersonInfo, compareType = "content") {
      //获取修改用户信息
      const userInfoList = rawData2?.meta?.userInfo || [];
      //各用户展示颜色，数组后两位，null代表默认背景颜色，red代表删除颜色，其他颜色则为多用户各自颜色

      //用户信息和对应的样式id的数组
      const userAddStyleList = [];
      const userDeleteStyleList = [];

      if (showPersonInfo && userInfoList && userInfoList.length) {
        for (let j = 0; j < userInfoList.length; j++) {
          this.fontStyleList[j % 10].fontStyle.dblUnderLine = true;
          userAddStyleList.push({
            id: userInfoList[j].id,
            name: userInfoList[j].name,
            font_id: this.fontStyleList[j % 10].fontId,
          });
          userDeleteStyleList.push({
            id: userInfoList[j].id,
            name: userInfoList[j].name,
            font_id:
              this.fontStyleList[(j % 10) + this.bgColorList.length].fontId,
          });
        }
      }
      //需要组合的rawData中的content
      const content = [];
      const header = [];
      //需要组合的rawData.fontMap中的font_style
      const font_style = {};
      //循环获取到的对比数据
      for (let i = 0; i < data.length; i++) {
        const para_traces = data[i];
        if (para_traces.structure === "para") {
          if (compareType === "header") {
            header.push({
              id: para_traces.id,
              type: "p",
              align: "left",
              deepNum: 0,
              islist: false,
              isOrder: false,
              indentation: 0,
              dispersed_align: false,
              before_paragraph_spacing: 0, // 段落前的间距 表示1倍的行高
              row_ratio: 1.6, // 行间距
              page_break: false,
              children: [],
              level: 0,
              title_length: 0,
              content_padding_left: 0,
              vertical_align: "top",
            });
            //给content的段落children赋值
            this.paraCyclicAssignment(
              header[i],
              para_traces.change,
              userAddStyleList,
              userDeleteStyleList
            );
          } else {
            if (compareType === "header") {
              //添加段落样式，children暂时为[]
              header.push({
                id: para_traces.id,
                type: "p",
                align: "left",
                deepNum: 0,
                islist: false,
                isOrder: false,
                indentation: 0,
                dispersed_align: false,
                before_paragraph_spacing: 0, // 段落前的间距 表示1倍的行高
                row_ratio: 1.6, // 行间距
                page_break: false,
                children: [],
                level: 0,
                title_length: 0,
                content_padding_left: 0,
                vertical_align: "top",
              });
              //给content的段落children赋值
              this.paraCyclicAssignment(
                header[i],
                para_traces.change,
                userAddStyleList,
                userDeleteStyleList
              );
            } else {
              //添加段落样式，children暂时为[]
              content.push({
                id: para_traces.id,
                type: "p",
                align: "left",
                deepNum: 0,
                islist: false,
                isOrder: false,
                indentation: 0,
                dispersed_align: false,
                before_paragraph_spacing: 0, // 段落前的间距 表示1倍的行高
                row_ratio: 1.6, // 行间距
                page_break: false,
                children: [],
                level: 0,
                title_length: 0,
                content_padding_left: 0,
                vertical_align: "top",
              });
              //给content的段落children赋值
              this.paraCyclicAssignment(
                content[i],
                para_traces.change,
                userAddStyleList,
                userDeleteStyleList
              );
            }
          }
        } else if (para_traces.structure === "table") {
          //如果type是表格，则循环data数据，获取data的表格结构，拼接为table
          const table = {
            cells: [],
            col_size: para_traces.col_size,
            id: para_traces.id,
            min_row_size: para_traces.min_row_size,
            name: para_traces.name,
            notAllowDrawLine: para_traces.notAllowDrawLine,
            row_size: para_traces.row_size,
            type: para_traces.structure,
          };
          for (let j = 0; j < para_traces.cells.length; j++) {
            const cell = para_traces.cells[j];
            table.cells[j] = {
              children: [],
              colspan: cell.colspan,
              id: cell.id,
              is_show_slash_down: cell.is_show_slash_down,
              is_show_slash_up: cell.is_show_slash_up,
              padding_bottom: cell.padding_bottom,
              padding_left: cell.padding_left,
              padding_right: cell.padding_right,
              set_cell_height: cell.set_cell_height,
              padding_top: cell.padding_top,
              pos: cell.pos,
              rowspan: cell.rowspan,
              split_parts: cell.split_parts,
              vertical_align: cell.vertical_align,
            };
            for (let k = 0; k < cell.children.length; k++) {
              const p = cell.children[k];
              table.cells[j].children[k] = {
                id: p.id,
                type: "p",
                align: "left",
                deepNum: 0,
                islist: false,
                isOrder: false,
                indentation: 0,
                dispersed_align: false,
                before_paragraph_spacing: 0, // 段落前的间距 表示1倍的行高
                row_ratio: 1.4, // 行间距
                page_break: false,
                children: [],
                level: 0,
                title_length: 0,
                content_padding_left: 0,
                vertical_align: "top",
              };
              this.paraCyclicAssignment(
                table.cells[j].children[k],
                p.change,
                userAddStyleList,
                userDeleteStyleList
              );
            }
          }
          if (compareType === "header") {
            header.push(table);
          } else {
            content.push(table);
          }
        }
      }
      //给fontMap赋值样式
      for (let i = 0; i < this.fontStyleList.length; i++) {
        font_style[this.fontStyleList[i].fontId] =
          this.fontStyleList[i].fontStyle;
      }
      const rawData = JSON.parse(JSON.stringify(rawData2));
      if (compareType === "header") {
        rawData.header = header;
      } else {
        rawData.content = content;
      }
      rawData.fontMap = Object.assign({}, rawData.fontMap, font_style);
      if (rawData?.meta?.traceInfo) {
        rawData.meta.traceInfo = [...data];
      } else {
        rawData["meta"] = {
          traceInfo: [],
        };
      }
      rawData.groups = [];
      return rawData;
    },
    //段落内容循环赋值的方法
    paraCyclicAssignment(para, data, userAddStyleList, userDeleteStyleList) {
      //循环给p.children赋值
      for (let n = 0; n < data.length; n++) {
        const e = data[n];
        let val = e[2] ? e[2].replace(/\n/g, "") : "";
        const splitValArr = val.split("$$$");

        for (let i = 0; i < splitValArr.length; i++) {
          val = splitValArr[i];
          const splitVal = val.split("-");
          let booleanValue;
          if (splitVal.length && splitVal[0] === "widget") {
            // 重新拼接成字符串
            if (splitVal[splitVal.length - 1] === "true") {
              booleanValue = true;
            } else if (splitVal[splitVal.length - 1] === "false") {
              booleanValue = false;
            }
          }
          let image;
          if (val.startsWith("image-")) {
            image = this.traceImageMap[val];
            if (!image) val = "";
          }
          const caliper = this.traceCaliperMap[val];
          if (caliper) {
            caliper.selected = booleanValue;
            let font_id = "";
            if (e[0] === -1) {
              if (userDeleteStyleList.length > 0) {
                //循环用户样式数组，如果meta携带的用户id和数组用户id匹配，则给content的段落children赋值赋样式
                for (let j = 0; j < userDeleteStyleList.length; j++) {
                  if (e.meta.userId === userDeleteStyleList[j].id) {
                    font_id = userDeleteStyleList[j].font_id;
                  }
                }
              }
            } else if (e[0] === 1) {
              if (userAddStyleList.length > 0) {
                //循环用户样式数组，如果meta携带的用户id和数组用户id匹配，则给content的段落children赋值赋样式
                for (let j = 0; j < userAddStyleList.length; j++) {
                  if (e.meta.userId === userAddStyleList[j].id) {
                    font_id = userAddStyleList[j].font_id;
                  }
                }
              }
            } else {
              font_id = caliper.font_id;
            }
            caliper.font_id = font_id;
            para.children.push(caliper);
            continue;
          } else if (image) {
            let font_id = "";
            if (e[0] === -1) {
              if (userDeleteStyleList.length > 0) {
                //循环用户样式数组，如果meta携带的用户id和数组用户id匹配，则给content的段落children赋值赋样式
                for (let j = 0; j < userDeleteStyleList.length; j++) {
                  if (e.meta.userId === userDeleteStyleList[j].id) {
                    font_id = userDeleteStyleList[j].font_id;
                  }
                }
              }
            } else if (e[0] === 1) {
              if (userAddStyleList.length > 0) {
                //循环用户样式数组，如果meta携带的用户id和数组用户id匹配，则给content的段落children赋值赋样式
                for (let j = 0; j < userAddStyleList.length; j++) {
                  if (e.meta.userId === userAddStyleList[j].id) {
                    font_id = userAddStyleList[j].font_id;
                  }
                }
              }
            } else {
              font_id = image.font_id;
            }
            image.font_id = font_id;
            para.children.push(image);
            continue;
          }
          if (e[0] === 0) {
            //如果对比没有变化，则font_id赋予没改变的样式
            para.children.push({
              type: "text",
              value: val,
              font_id: this.fontStyleList[this.fontStyleList.length - 1].fontId,
            });
          } else if (e[0] === 1) {
            //如果新增则赋予新增样式
            if (userAddStyleList.length > 0) {
              let font_id = "";
              //循环用户样式数组，如果meta携带的用户id和数组用户id匹配，则给content的段落children赋值赋样式
              for (let j = 0; j < userAddStyleList.length; j++) {
                if (e.meta.userId === userAddStyleList[j].id) {
                  font_id = userAddStyleList[j].font_id;
                }
              }
              para.children.push({
                type: "text",
                value: val,
                font_id: font_id,
              });
            } else {
              para.children.push({
                type: "text",
                value: val,
                font_id: this.fontStyleList[0].fontId,
              });
            }
          } else if (e[0] === -1) {
            //如果新增则赋予新增样式
            if (userDeleteStyleList.length > 0) {
              let font_id = "";
              //循环用户样式数组，如果meta携带的用户id和数组用户id匹配，则给content的段落children赋值赋样式
              for (let j = 0; j < userDeleteStyleList.length; j++) {
                if (e.meta.userId === userDeleteStyleList[j].id) {
                  font_id = userDeleteStyleList[j].font_id;
                }
              }
              para.children.push({
                type: "text",
                value: val,
                font_id: font_id,
              });
            } else {
              para.children.push({
                type: "text",
                value: val,
                font_id:
                  this.fontStyleList[(this.fontStyleList.length - 1) / 2]
                    .fontId,
              });
            }
          }
        }
      }
    },
  },
};
export default traceContrast;
