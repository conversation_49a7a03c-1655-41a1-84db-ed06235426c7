/**
 * 编辑器触发函数
 */
const editorEventMixIn = {
  methods: {
    bindInstanceEvent(Instance) {
      const editor = Instance.editor;
      editor.event.on("pointerUp", () => {
        this.setHeaderBtnStatus();
      }); // 编辑器双击事件
      editor.event.on("contentChanged", () => {
        this.wordStatisticsFun();
        if (this.isPrintDesign) {
          clearTimeout(this.saveLocalDataTimeout);
          this.saveLocalDataTimeout = setTimeout(() => {
            this.saveLocalData();
          }, 0.5 * 60 * 1000);
        }
      }); // 编辑器内容改变事件
      editor.event.on("exeCommand", (e) => {
        if (e.command === "caretMove") {
          if (!editor.formula_mode) {
            this.updateSideData();
            this.updateSideDataSource();
          }
        }
        this.setFontStyle();
      });
      editor.event.on("beforeDrop", () => {
        if (!this.dragInfo || !this.dragInfo.data) {
          return "origin";
        }
        const resInfo = {
          customCallBack: () => {
            this.dragDrop();
          },
        };
        return resInfo;
      });
      editor.event.on("beforePrintView", () => {
        if (this.oriDataSet) {
          const newPrintEditor = editor.copyEditor(editor.getRawData());
          this.fillContentByReceiveJsonData(newPrintEditor);
          return { newPrintEditor };
        }
      });
      editor.event.on("insertComSentence", (item) => {
        if (item && item.type === "field") {
          this.insertFieldByQuickInputSelect(item);
        }
      });
      editor.event.on("quickSelectInputList", (text, resolve) => {
        return this.searchFieldByKeyText(text, resolve);
      });
      //更新右侧边栏
      editor.event.on("updateSide", () => {
        if (!editor.formula_mode) {
          this.updateSideData();
          this.updateSideDataSource();
        }
      });
    },
  },
};
export default editorEventMixIn;
