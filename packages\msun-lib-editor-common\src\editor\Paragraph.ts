import Font, { FontStyle } from "./Font";
import {
  getInsertIndexInCurrent<PERSON>ell<PERSON>ields,
  initCell,
  is<PERSON>haracter,
  isField,
  isParagraph,
  isTable,
  isTibetan,
  keepDecimal,
  numberTo<PERSON>hinese,
  props2Obj,
  uuid,
  versionDiff,
} from "./Utils";
import Editor from "./Editor";
import Cell from "./Cell";
import Character from "./Character";
import Image from "./Image";
import XField from "./XField";
import Row from "./Row";
import Renderer from "./Renderer";
import PathUtils, { Path } from "./Path";
import Table from "./Table";
import Widget from "./Widget";
import { getCellIndex, isBoxField, isImage, isLine, isWidget, specialCharHandle } from "./Helper";
import BoxField from "./BoxField";
import Line from "./line";
import Box from "./Box";
import Group from "./Groups";
import { fontHeightAsc } from "./Config";
import ParaStyle, { ParagraphStyle } from "./ParagraphStyle";
import { default_para_style } from "./ContextState";
import {
  alignType,
  ElementInParagraph,
  IncreaseType,
  MarkType,
  widgetType,
} from "./Definition";
import Button from "./Button";
import EditorHelper from "./EditorHelper";
import { MENSTRUAL_EXPRESSION, VerticalAlign, ViewMode } from "./Constant";

export default class Paragraph {
  id: string;

  group_id: string | null;

  para_index: number = 0;

  children: Row[] = [];

  characters: any[] = [];

  isNumTypesetting: boolean = true;

  IsCommaTypesetting: boolean = true;

  align: alignType = "left"; // docuAlign

  vertical_align: VerticalAlign = VerticalAlign.TOP;

  dispersed_align: boolean = false;

  isOrder: boolean = false; // 是否是有序列表的意思

  islist: boolean = false; // 是否是列表的意思

  listNumStyle: "number" | "chinese" = "number"; // 是汉字(一/二)还是数字(1/2)

  level: number = 0; // 第一层值为 1, 第二层值为 2,以此类推

  list_index: number = 0; // 在该列表中第几个,从1开始的

  restart_list_index: boolean = false;

  before_paragraph_spacing: number = 0; // 段落前的间距 表示1倍的行高

  after_paragraph_spacing: number = 0; // 段落后的间距 表示1倍的行高

  indentation: number = 0; // 段落首行缩进

  row_ratio: number; // 行间距

  first_row_cellindex: number = 0;

  first_row_pagenum: number = 1;

  first_row_pageindex: number = 0;

  /** ****************** 文档对齐**************** */
  title_length: number = 0;

  content_padding_left: number = 0; // 段落内各行的缩进

  splice_length: number = 0;

  cell: Cell;

  width: number;

  page_break: boolean = false;

  tempNotDel: boolean = false; // 临时用的 段落重排的时候判断是否该段落是否可删除

  itemsWidth: any = []; // 四个一行的功能 记录每一项的宽度数组(这是之前的) 现在用来记录是否是中草药四个一行的内容(当做一个标识)

  static changeStyle(editor: Editor) {
    // 刷格式前判断格式刷是否为选区格式刷，若为选区，只刷文字样式，不刷段落样式
    if (editor.internal.brush_selection) {
      return;
    }
    // 分组如果锁定 则不能编辑
    if (!editor.operableOrNot(["cell", "group"])) return false;
    const paragraph_style = editor.contextState.getParagraphState();
    const paragraphs = editor.selection.selected_para_info;
    if (paragraphs.paragraphs.length === 0) {
      paragraphs.paragraphs = [
        editor.selection.getParagraphByPath(
          editor.modelPath2ParaPath(editor.selection.focus)
        ),
      ];
    }
    for (let i = 0; i < paragraphs.paragraphs.length; i++) {
      const paragraph = paragraphs.paragraphs[i];
      paragraph.align = paragraph_style.align;

      paragraph.before_paragraph_spacing =
        paragraph_style.before_paragraph_spacing;
      paragraph.after_paragraph_spacing =
        paragraph_style.after_paragraph_spacing;
      paragraph.indentation = paragraph_style.indentation;
      paragraph.row_ratio = paragraph_style.row_ratio;
      paragraph.isOrder = paragraph_style.isOrder;
      paragraph.islist = paragraph_style.islist;
      paragraph.level = paragraph_style.level;
      paragraph.updateChildren();
    }
    editor.update();
    editor.render();
    return true;
  }

  static setState(editor: Editor) {
    const focus_para = editor.selection.getParagraphByPath(
      editor.modelPath2ParaPath(editor.selection.focus)
    );
    const selected_para_style: ParagraphStyle = new ParaStyle(
      default_para_style
    );

    // 样式赋值
    selected_para_style.align = focus_para.align;
    selected_para_style.before_paragraph_spacing =
      focus_para.before_paragraph_spacing;
    selected_para_style.after_paragraph_spacing =
      focus_para.after_paragraph_spacing;
    selected_para_style.indentation = focus_para.indentation;
    selected_para_style.row_ratio = focus_para.row_ratio;
    selected_para_style.isOrder = focus_para.isOrder;
    selected_para_style.islist = focus_para.islist;
    selected_para_style.level = focus_para.level;
    // 设置对应样式
    editor.contextState.setParagraphState(selected_para_style);
  }

  static getStrWithCaret(editor: Editor) {
    const path = editor.selection.para_anchor;
    const current_para = editor.current_cell.paragraph[path[0]];
    let str = "";
    if (isParagraph(current_para)) {
      str = current_para.getStr(true);
    }
    return str;
  }

  static setDispersedAlign(editor: Editor) {
    // 分组如果锁定 则不能编辑
    if (!editor.operableOrNot(["cell", "group"])) return false;
    let selected_para = editor.selection.selected_para_info.paragraphs;
    selected_para =
      selected_para.length === 0
        ? [editor.selection.getFocusParagraph()]
        : selected_para;
    selected_para.forEach((e) => {
      e.setDispersed();
    });
    editor.update();
    editor.render();
    return true;
  }

  static reFormat(
    editor: Editor,
    paragraphs: (Paragraph | Table)[] = [],
    textIndent: boolean = true,
    clearSpace: boolean = true
  ) {
    // TODO 段落重排当选区从表格下一行开始选,下边选中一个表格的时候,这时候段落重排就会使两个表格挨着了
    if (!paragraphs.length && !editor.selection.isCollapsed) {
      // 两个表格中间全是空段 而且全部选中了空段进行段落重排也没关系,就会走更下方原来的逻辑 会保留一个吧(这个没看因为啥,但是测试了不用管)
      const paraStart = editor.selection.para_start;
      const paraEnd = editor.selection.para_end;
      if (paraStart.length < 3 || paraEnd.length < 3) {
        paragraphs = editor.current_cell.paragraph.slice(
          paraStart[0],
          paraEnd[0] + 1
        );

        // 现在处理的是选区中有表格的情况,不能删除到两个表格挨着
        let hasTable = false;
        let hasContentParagraph = false; // 有没有有内容的段落
        const nextParagraphs = []; // 表格后边的所有段落
        for (let i = 0; i < paragraphs.length; i++) {
          const current = paragraphs[i];
          // 先只管表格的前部分
          if (!hasTable) {
            // 如果还没遇到表格
            if (!isTable(current)) {
              // 如果当前也不是表格
              if (current.characters.length > 1) {
                hasContentParagraph = true;
              }
            } else {
              hasTable = true;
              // 此时选区就已经到表格了
              if (hasContentParagraph) {
                // 如果有有内容的段落 前半部分就不用管了
                hasContentParagraph = false;
              } else {
                // 如果没有有内容的段落 那么就要往上找看选区中第一个段落的前一段落是否是表格 如果是表格那么就必须有一个段落不能删除 否则就不用管了
                if (
                  isTable(
                    editor.current_cell.paragraph[paragraphs[0].para_index - 1]
                  )
                ) {
                  (paragraphs[0] as Paragraph).tempNotDel = true;
                }
              }
            }
          } else {
            // 此时说明选区中至少有一个表格了 就要找到最后一个表格往后是不是都是空段 在判断最后一个空段在文中是否紧挨着表格 如果紧挨着就要留一个 否则就不管了
            if (isTable(current)) {
              // 又遇到了一个表格 就不管了
              hasContentParagraph = false;
              nextParagraphs.length = 0;
            } else {
              // 如果没有遇到表格
              if (current.characters.length > 1) {
                hasContentParagraph = true;
              }
              nextParagraphs.push(current);
            }
          }
        }
        if (!hasContentParagraph && nextParagraphs.length) {
          if (
            isTable(
              editor.current_cell.paragraph[
                paragraphs[paragraphs.length - 1].para_index + 1
              ]
            )
          ) {
            (paragraphs[paragraphs.length - 1] as Paragraph).tempNotDel = true;
          }
        }
      } else {
        // 走到这儿说明 要么选区都在表格里 要么首尾都是表格
        paragraphs = editor.selection.selected_para_info.paragraphs;
        if (
          (paragraphs[0] as Paragraph).cell.parent !==
          (paragraphs[paragraphs.length - 1] as Paragraph).cell.parent
        ) {
          // 说明是选中了两个表格 选区不全在一个表格里边 如果选区全在一个表格里就不管了
          paragraphs = paragraphs = editor.current_cell.paragraph.slice(
            paraStart[0],
            paraEnd[0] + 1
          );
        }
      }
    }

    const newParagraphs: Paragraph[] = [];
    const start: any = {
      table: undefined,
      index: undefined,
    };
    for (let i = 0; i < paragraphs.length; i++) {
      const current = paragraphs[i];

      // 记录第一个表格
      if (isTable(current) && !start.table) {
        start.table = current;
        start.index = i;
        continue;
      }

      // 再遇到就替换掉第一个表格
      if (start.table && isTable(current)) {
        start.table = current;
        start.index = i;

        const paragraph = newParagraphs.find(
          (paragraph) => paragraph.characters.length > 1
        );

        // 如果全部都是空段 那么就要留一个
        if (!paragraph && newParagraphs.length) {
          (paragraphs[i - 1] as Paragraph).tempNotDel = true;
        }
        newParagraphs.length = 0;
        continue;
      }

      if (start.table) {
        newParagraphs.push(current as Paragraph);
      }
    }
    // 增加支持可传入表格
    const paragraph: Paragraph[] = [];
    paragraphs.forEach((item) => {
      if (isTable(item)) {
        item.children.forEach((c: Cell) => {
          paragraph.push(...(c.paragraph as Paragraph[]));
        });
      } else {
        paragraph.push(item);
      }
    });

    // 清空选区信息，之前会保留上次选区信息，重复调用该接口的时候会多次执行
    editor.selection.clearSelectedInfo();
    if (!paragraph.length) {
      const focus_para = editor.selection.getFocusParagraph();
      if (focus_para.characters.length === 1) return false;
      paragraph.push(focus_para);
    }
    for (let i = 0; i < paragraph.length; ) {
      const para = paragraph[i];
      // 去掉表格内，锁定段落以及列表段落
      if ((para.paraGroup && para.paraGroup.lock) || para.islist) {
        i++;
        continue;
      }
      // 段落重排
      if (para.characters.length === 1 && !para.tempNotDel) {
        if (paragraph.length === 1) {
          // 当剩余最后一个段落时并且是空行则不进行移除，否则光标无法放置
          para.reformat(textIndent, clearSpace, false); // 虽然只有一段,但是该缩进就缩进,该删除空格就删除空格
          break;
        }

        // 删除空行
        para.remove();
        paragraph.splice(i, 1);
        continue;
      } else {
        let canBeDelete: boolean = true; // 能否被删除
        // 当是最后一个段落时不能删除
        if (paragraph.length === 1 || para.tempNotDel) {
          canBeDelete = false;
        }
        // 缩进及空格的处理
        const res = para.reformat(textIndent, clearSpace, canBeDelete);
        if (res) {
          paragraph.splice(i, 1);
        } else {
          i++;
        }
        para.tempNotDel = false;
      }
    }
    const para_path = paragraph[0].start_para_path;
    editor.selection.setCursorPosition(editor.paraPath2ModelPath(para_path));
    editor.update();
    editor.scroll_by_focus();
    editor.render();
    return true;
  }

  static attrJudgeUndefinedAssign(newModel: Paragraph, raw: any) {
    if (raw.align !== undefined) newModel.align = raw.align;
    if (raw.isOrder !== undefined) newModel.isOrder = raw.isOrder;
    if (raw.vertical_align !== undefined)
      newModel.vertical_align = raw.vertical_align;
    if (raw.islist !== undefined) newModel.islist = raw.islist;
    if (raw.level !== undefined) newModel.level = raw.level;
    if (raw.indentation !== undefined) newModel.indentation = raw.indentation;
    if (raw.content_padding_left !== undefined)
      newModel.content_padding_left = raw.content_padding_left;
    if (raw.before_paragraph_spacing !== undefined)
      newModel.before_paragraph_spacing = raw.before_paragraph_spacing;
    if (raw.row_ratio !== undefined) newModel.row_ratio = raw.row_ratio;
    if (raw.dispersed_align !== undefined)
      newModel.dispersed_align = raw.dispersed_align;
    if (raw.title_length !== undefined)
      newModel.title_length = raw.title_length;
    if (raw.page_break !== undefined) newModel.page_break = raw.page_break;
    if (raw.restart_list_index !== undefined)
      newModel.restart_list_index = raw.restart_list_index;
    if (raw.listNumStyle !== undefined)
      newModel.listNumStyle = raw.listNumStyle;
    if (raw.itemsWidth !== undefined) newModel.itemsWidth = raw.itemsWidth;
  }

  static setFirstRowIndent(
    editor: Editor,
    indentation: number,
    paragraphs: Paragraph[] = [],
    setType: IncreaseType = IncreaseType.fixed
  ) {
    let selected_para = editor.selection.isCollapsed
      ? paragraphs
      : editor.selection.selected_para_info.paragraphs;
    selected_para =
      selected_para.length === 0
        ? [editor.selection.getFocusParagraph()]
        : selected_para;
    let value = indentation;
    selected_para.forEach((e) => {
      if (setType === IncreaseType.increase) {
        value = e.indentation + indentation;
      }
      if (setType === IncreaseType.decrease) {
        value = e.indentation - indentation;
      }
      e.firstRowIndentation(value);
    });
    const isTable = selected_para[0].children[0].parent.parent;
    if (isTable) {
      editor.update(
        isTable.cell_index,
        isTable.page_number,
        isTable.page_index
      );
    } else {
      editor.update(
        selected_para[0].children[0].cell_index,
        selected_para[0].children[0].page_number,
        selected_para[0].children[0].page_index
      );
    }
    editor.scroll_by_focus();
    editor.render();
    return true;
  }

  static setRowIndetWhenTabDown() {}

  static changeRowRatio(
    editor: Editor,
    row_ratio: number,
    paragraphs: Paragraph[] = [],
    setType: IncreaseType = IncreaseType.fixed
  ) {
    let selected_para = editor.selection.isCollapsed
      ? paragraphs
      : editor.selection.selected_para_info.paragraphs;
    selected_para =
      selected_para.length === 0
        ? [editor.selection.getFocusParagraph()]
        : selected_para;
    let value = row_ratio;
    selected_para.forEach((e) => {
      if (setType === IncreaseType.increase) {
        value = e.row_ratio + row_ratio;
      }
      if (setType === IncreaseType.decrease) {
        value = e.row_ratio - row_ratio;
      }
      // 最小为 1倍行间距
      if (value <= 1) value = 1;
      e.changeRowRatio(value);
    });
    editor.update();
    editor.scroll_by_focus();
    editor.render();
    return true;
  }

  static changeParagraphBeforeSpace(
    editor: Editor,
    before_paragraph_spaces: number,
    paragraphs: Paragraph[] = [],
    setType: IncreaseType = IncreaseType.fixed
  ) {
    let selected_para = editor.selection.isCollapsed
      ? paragraphs
      : editor.selection.selected_para_info.paragraphs;
    selected_para =
      selected_para.length === 0
        ? [editor.selection.getFocusParagraph()]
        : selected_para;
    let value = before_paragraph_spaces;
    selected_para.forEach((e) => {
      if (setType === IncreaseType.increase) {
        value = e.before_paragraph_spacing + before_paragraph_spaces;
      }
      if (setType === IncreaseType.decrease) {
        value = e.before_paragraph_spacing - before_paragraph_spaces;
      }
      e.changeParaBeforSpacing(value);
    });
    //这个  ↓↓↓↓↓↓ update不能传参数
    editor.update();
    editor.scroll_by_focus();
    editor.render();
    return true;
  }

  constructor(id: string, cell: Cell, group_id: string | null = null) {
    this.id = id;

    this.group_id = group_id ?? null;

    this.cell = cell;

    this.row_ratio = cell.editor.config.row_ratio; // 行间距

    if (cell.editor.document_meta.isNumTypesetting === false) {
      this.isNumTypesetting = false;
    }
    if (cell.editor.document_meta.IsCommaTypesetting === false) {
      this.IsCommaTypesetting = false;
    }

    this.width =
      this.cell.width - this.cell.padding_left - this.cell.padding_right;
  }

  // 获取段落高度
  get height() {
    let h = 0;
    h =
      this.children.length &&
      this.children.reduce((prev, row) => prev + row.height, 0);
    return h;
  }

  /**
   * 获取段落路径
   */
  get start_para_path(): Path {
    // 说明在表格中
    if (this.cell.parent) {
      return [
        this.cell.parent.para_index,
        getCellIndex(this.cell),
        this.para_index,
        0,
      ];
    } else {
      return [this.para_index, 0];
    }
  }

  get end_para_path(): Path {
    if (this.cell.parent) {
      return [
        this.cell.parent.para_index,
        getCellIndex(this.cell),
        this.para_index,
        this.characters.length,
      ];
    } else {
      return [this.para_index, this.characters.length];
    }
  }

  get top() {
    let top = 0;
    let page_num = 0;
    const row = this.children[0];
    if (row.parent.parent) {
      page_num = row.parent.parent.page_number - 1;
      top =
        this.cell.editor.config.editor_padding_top +
        (this.cell.editor.config.page_margin_bottom +
          this.cell.editor.page_size.height) *
          page_num +
        row.top +
        row.parent.top +
        row.parent.parent.top;
    } else {
      page_num = row.page_number - 1;
      top =
        this.cell.editor.config.editor_padding_top +
        (this.cell.editor.config.page_margin_bottom +
          this.cell.editor.page_size.height) *
          page_num +
        row.top;
    }

    return top;
  }

  // 距页面左侧left
  get real_left() {
    let left = this.cell.editor.config.page_padding_left;

    const row = this.children[0];

    if (row.parent.parent) {
      left = row.parent.parent.left + row.parent.left + row.left;
    }

    return left;
  }

  /**
   * 过去下一段
   */
  public get nextParagraph(): Paragraph | Table | null {
    let nextParagraph: Paragraph | Table | null = null;
    if (this.cell.paragraph.length > this.para_index) {
      nextParagraph = this.cell.paragraph[this.para_index + 1];
    }
    return nextParagraph;
  }

  /**
   * 获取前一段
   */
  public get previousParagraph(): Paragraph | Table | null {
    let nextParagraph: Paragraph | Table | null = null;
    if (this.para_index > 0) {
      nextParagraph = this.cell.paragraph[this.para_index - 1];
    }
    return nextParagraph;
  }

  get lastCharacter() {
    return this.characters[this.characters.length - 1];
  }

  /**
   * 只拿该段落内含有的文本域开始字符 的文本域 id
   */
  get fieldsId(): string[] {
    const result_ids: string[] = [];
    for (const character of this.characters) {
      if (character.field_position === "start") {
        result_ids.push(character.field_id);
      }
    }
    return result_ids;
  }

  /**
   * 段内第一行
   */
  get firstRow(): Row {
    return this.children[0];
  }

  /**
   * 段内最后一行
   */
  get lastRow(): Row {
    return this.children[this.children.length - 1];
  }

  // 判断是否是空段
  get isEmpty(): boolean {
    const character = this.characters[0];
    return (
      this.characters.length === 1 &&
      isCharacter(character) &&
      character.value === "\n"
    );
  }

  /**
   * 列表之间不同级别的错位间隔
   */
  public get listLevelDislocation() {
    let symbol_font: any = {};
    const contextState = this.cell.editor.contextState;
    symbol_font =
      this.characters[0] && isCharacter(this.characters[0])
        ? this.characters[0].font
        : this.cell.editor.fontMap.add(contextState.getFontState());
    const chinese_num_width = Renderer.measure(
      symbol_font,
      "一",
      this.cell.editor
    );
    return { chinese_num_width: chinese_num_width.width };
  }

  public get paraGroup(): Group | null | undefined {
    const group = this.group_id ? this.cell.getGroupById(this.group_id) : null;
    return group;
  }

  /**
   * 在段落中添加 character
   * @param elements 要插入的元素
   * @param index 在什么位置添加
   * @param field 插入位置处是否是文本域
   * @param all_elements 用于文本域中插入的所有元素
   */
  insertElements(
    elements: (Character | Image | Widget | Line | Box | XField | Button)[],
    index: number,
    field: XField | null,
    all_elements: (
      | Character
      | Image
      | Widget
      | Line
      | Box
      | XField
      | Button
    )[] = elements
  ) {
    let temp;
    // 跟文本域挂钩
    if (field) {
      const fields: any[] = all_elements.filter((item) => isField(item));
      // 修复复制跨段三层嵌套文本域往另一个三层嵌套文本域粘贴时重复插入问题
      const all_child_fields = field.getFieldInChildren();
      let exist_field: boolean = false;
      for (let i = 0; i < fields.length; i++) {
        if ((all_child_fields as any).includes(fields[i])) {
          exist_field = true;
          break;
        }
      }
      if (!exist_field) {
        if (!field.children.length) {
          if (this.cell.editor.hold_mouse) {
            // 只有点击没松开的时候才能走这里
            // 判断children中是否有内容 如果没有 显示的内容就是 placeholder 需要删除
            if (this.characters[index] === field.end_sym_char) {
              // TODO 不能直接这么判断 因为嵌套文本域[ab(3)] character_index 为4的时候 正好等于文本域的end_sym_char 但是不需要减 placeholder
              // 说明点在了文本域末尾边框字符前边 插入新字符的位置就应该减去 placeholder 的长度
              temp = index - field.placeholder.length;
            } else {
              // 说明点在了文本域里边某个 placeholder 上 原来的插入位置跟点在的placeholder 的字符在 placeholder 中的位置 的差值就应该是新插入字符的位置
              const currentPlaceholderIndex =
                field.placeholder_characters.indexOf(this.characters[index]);
              if (currentPlaceholderIndex > -1) {
                temp = index - currentPlaceholderIndex;
              }
            }
          }
          field.removeParaPlaceholder();
        }
        // 插入位置的前一个字符
        let field_pre_character = this.characters[index - 1];
        if (index === 0) {
          // 如果是在开头，那么找上一段的最后一个字符，一定存在
          field_pre_character = (this.previousParagraph as Paragraph)
            .characters[
              (this.previousParagraph as Paragraph).characters.length - 1
            ];
        }
        // 获取文本域children中插入文字的位置
        const field_index = field.getCharIndexInField(field_pre_character);
        field.children.splice(field_index, 0, ...all_elements);
      }
    }

    // 修改水平线的宽度
    elements.forEach((char) => {
      if (isLine(char)) {
        if (this.cell.parent) {
          char.width =
            this.cell.width - this.cell.padding_left - this.cell.padding_right;
        } else {
          char.width =
            this.cell.width -
            this.cell.editor.config.page_padding_left -
            this.cell.editor.config.page_padding_right;
        }
      }
    });

    // 因为表单模式下 pointerDown 生成的 path 不一定对 pointerUp 修正的才是对的 所以如果 在一个只有 placeholder 的文本域中快速点击 然后输入内容 index 就用了 pointerDown 里边的坐标
    // 导致结果不对 所以要修正 index
    this.characters.splice(temp ?? index, 0, ...elements);
    // 在下步生成row之前 需要拿到正确的list_index
    this.cell.updateParaIndex(); // TODO 在该段落插入元素也没必要全量的更新段落下标吧
    // 生成row
    this.updateChildren();
  }

  /**
   * 生成行
   */
  createRow(obj?: any) {
    let colMaxWidth;
    if (obj?.colMaxWidth) {
      colMaxWidth = obj.colMaxWidth;
    }
    // 记录第渲染之后的page数据
    if (this.children[0]) {
      this.first_row_cellindex = this.children[0].cell_index;
      this.first_row_pageindex = this.children[0].page_index;
      this.first_row_pagenum = this.children[0].page_number;
    }
    // 在生成row之前先要删除段落中现有的
    this.children.length = 0;
    // 定义一个当前行的变量
    let current_row: Row;
    let row_index: number = 0; // 当前行在段落中的下标
    // 当前行在cell中的下标
    let cell_index: number = this.previousParagraph
      ? isTable(this.previousParagraph)
        ? this.previousParagraph.cell_index + 1
        : this.previousParagraph.lastRow!.cell_index + 1
      : 0;
    // 给当前行中排列字符对象的时候剩余的元素
    let rest_characters:
      | (Character | Image | Widget | Line | Box | Button)[]
      | null = null;
    // 获取需要插入的数据
    let insert_elements = this.characters;
    const pre_character: any = this.characters[this.characters.length - 2]; // 获取最后一个字符，因为最后一个本来是换行符所以 - 2 才是真正的字符
    // 前一个 字符 存在  那么段落末尾换行符永远和前一个字符保持一致
    // TODO 复制门诊两个字的时候 这里边将诊字的背景色设置的和门一样了
    if (
      pre_character &&
      isCharacter(pre_character) &&
      pre_character.field_position === "normal"
    ) {
      this.lastCharacter.font = pre_character.font;
    }

    const maxRowWidth =
      this.cell.width - this.cell.padding_left - this.cell.padding_right;
    // 如果单元格存在不换行属性，则将整段字符宽度压缩，此处处理字符宽度
    this.handleAllCharactersWidth(maxRowWidth);
    // 缓存根据字符获取到的文本域
    const fieldMap = {};
    // 循环生成行
    do {
      // 生成第一行
      current_row = new Row(
        this.cell, // 当前单元格
        this.cell.padding_left, // 单元个的偏移量 用作确定行的位置
        0,
        maxRowWidth // 行宽
      );

      // 当前行的段id
      current_row.para_id = this.id;
      // 当前行在段落中的下标
      current_row.row_index_in_para = row_index;
      current_row.cell_index = cell_index;
      // 当前行属于当前段
      current_row.paragraph = this;
      // 这里设置的情况是末尾敲回车的时候设置下一行的行高
      current_row.height =
        this.characters.length === 1 || !isCharacter(pre_character)
          ? this.lastCharacter.font.height * this.row_ratio
          : pre_character.font.height * this.row_ratio;
      current_row.page_break = false;
      // list类型的绘制
      if (this.islist) {
        if (this.isOrder && this.listNumStyle === "chinese") {
          const symbol = numberToChinese(this.list_index);
          current_row.padding_left =
            this.level * this.listLevelDislocation.chinese_num_width +
            symbol.length * this.listLevelDislocation.chinese_num_width;
        } else if (this.isOrder && this.listNumStyle === "number") {
          let numWidth;
          let numLength;
          let version = this.cell.editor.document_meta.versionList?.[0]?.version;
          if (version && versionDiff(version, "10.10.1") >= 0) {
            numWidth = this.listLevelDislocation.chinese_num_width / 2;
            if ((this.list_index + "").length === 1) {
              numLength = 2;
            } else {
              numLength = (this.list_index + "").length;
            }
          } else {
            numWidth = this.listLevelDislocation.chinese_num_width;
            numLength = (this.list_index + "").length;
          }
          current_row.padding_left =
            this.level * numWidth + numLength * numWidth;
        } else {
          current_row.padding_left =
            this.level * this.listLevelDislocation.chinese_num_width;
        }
      }
      // 首行缩进 用空格的宽度作为单位 缩进值为0的时候不走这部分逻辑
      if (this.children.length === 0 && this.indentation !== 0) {
        const contextState = this.cell.editor.contextState;
        // 确定当前行第一个字符的样式 如果没有那么就是获取到的上下文样式
        const space_width_font = this.characters[0].FontStyle
          ? this.cell.editor.fontMap.add(this.characters[0].FontStyle)
          : contextState.getFontState();
        // 确定空格字符的宽度
        const indentation_width: number = Renderer.measure(
          space_width_font,
          " ",
          this.cell.editor
        ).width;
        // 两个空格为一个汉字字符
        if (this.title_length || this.content_padding_left === 0) {
          current_row.padding_left += this.indentation * indentation_width * 2;
        }
      }
      const prePara = this.previousParagraph as Paragraph;
      // 获取上一段落的content_padding_left，如果不为0则将上一段落赋值给当前段落的content_padding_left
      if (
        prePara &&
        !this.title_length &&
        prePara.align === "docuAlign" &&
        this.align === "docuAlign" &&
        this.cell.editor.document_meta.newDocuAlign
      ) {
        this.content_padding_left = prePara.content_padding_left;
      }
      // this.content_padding_left > 0代表是字符对齐    this.title_length === 0代表是字符对齐的子段  this.children.length >= 1 代表是本段的非首行
      if (
        this.align === "docuAlign" &&
        (this.title_length === 0 || this.children.length >= 1)
      ) {
        if (this.children.length >= 1 && this.title_length) {
          // 当title_length中获取不到对应字符时说明可能删除过字符，此时取消字符对齐
          const element = this.characters[this.title_length];
          if (!element) {
            this.title_length = 0;
          } else {
            this.content_padding_left = element.left;
          }
        }
        current_row.padding_left += this.content_padding_left;
      }

      if (colMaxWidth) {
        let index = 0;
        let recordWidth = 0;
        for (let i = this.title_length; i < insert_elements.length; i++) {
          const currentCharacter = insert_elements[i];
          if (isCharacter(currentCharacter)) {
            if (currentCharacter.value === " ") {
              currentCharacter.width = colMaxWidth[index] - recordWidth;
              recordWidth = 0;
              index++;
            } else {
              recordWidth += currentCharacter.width;
            }
          }
        }
      }

      const rest_characters = current_row.insertCharacters(
        insert_elements,
        fieldMap
      );

      // 文字对齐方式设置
      if (this.dispersed_align && current_row.children.length > 0) {
        this.modifyAlignmentToDispersed(current_row);
        // 修复文本域选区删除报错问题（因未重置换行符left值，选区会被选中）
        if (!rest_characters) {
          (current_row.linebreak as Character).left =
            current_row.children[current_row.children.length - 1].right;
        }
      } else if (this.align === "center" && current_row.children.length > 0) {
        this.modifyAlignmentToCenter(current_row);
        if (!rest_characters) {
          (current_row.linebreak as Character).left =
            current_row.children[current_row.children.length - 1].right;
        }
      } else if (this.align === "right") {
        if (!rest_characters) {
          this.modifyAlignmentToRight(current_row, true);
        } else {
          this.modifyAlignmentToRight(current_row);
        }
        if (!rest_characters) {
          (current_row.linebreak as Character).left = current_row.width;
        }
      }
      // 当前段的children
      this.children.push(current_row);

      if (rest_characters) {
        // 下一行数据
        insert_elements = rest_characters;
        row_index += 1;
        cell_index += 1;
      } else {
        insert_elements = [];
      }
    } while (insert_elements.length);
    if (this.title_length && this.align === "docuAlign") {
      // 当title_length中获取不到对应字符时说明可能删除过字符，此时取消字符对齐
      const element = this.characters[this.title_length];
      if (!element) {
        this.title_length = 0;
        this.content_padding_left = 0;
        this.align = "left";
      } else {
        this.content_padding_left = element.left;
      }
    }
    if (this.page_break) {
      this.children[this.children.length - 1].page_break = true;
    }
    this.children[0].page_number = this.first_row_pagenum;
    this.children[0].page_index = this.first_row_pageindex;
  }

  handleAllCharactersWidth(rowWidth: number) {
    if (!this.cell.noWrap) {
      return;
    }
    const totalWidth = this.characters.reduce((total, a) => {
      if (
        this.cell.noWrap &&
        (a.field_position === "start" || a.field_position === "end")
      ) {
        a.ori_width = 0.001;
      }
      if (isWidget(a)) {
        a.width = a.height;
        return a.height + total;
      }
      a.width = a.ori_width;
      return a.ori_width + total;
    }, 0);
    if (isNaN(totalWidth) || totalWidth <= rowWidth) {
      return;
    }
    const r = rowWidth / totalWidth;
    this.characters.forEach((e) => {
      if (isWidget(e)) {
        e.width = keepDecimal(e.height * r, 3);
      } else {
        e.width = keepDecimal(e.ori_width * r, 3);
      }
    });
  }

  /**
   * 更新段落，重新生成 row
   * @param previousParagraphLastRowIndex 前一个段落最后一行的下标
   * @param updateRowCount 更新的长度
   */
  updateChildren(
    previousParagraphLastRowIndex: number = this.children[0].cell_index,
    updateRowCount: number = this.children.length,
    callTypesetting: boolean = true
  ) {
    // TODO 这个方法调用的地方太多
    this.createRow();
    this.cell.updateChildren({
      // 这个 cell.updateChildren 只在这里调用了
      updateParagraphIndex: this.para_index,
      updateRowCount: updateRowCount,
      startRowIndex: previousParagraphLastRowIndex,
      callTypesetting,
    });
  }

  /**
   * 刷新替换操作后的段落内容
   * @param field 段落中要替换的field
   */
  refreshFieldCharacters(field: XField, start_index: number) {
    // 当前段要更新的位置
    const refresh_location = start_index;
    let field_characters: any[] = [];
    // 文本域所有的字符对象
    field_characters = field.getAllElements();
    // 去除开始与结束字符
    field_characters.shift();
    field_characters.pop();
    // 字符中会出现\n 有可能会出现多段落的情况
    const field_sections: any[][] = [[]];
    // 分段之后的下标
    let section_index: number = 0;
    for (let i = 0; i < field_characters.length; i++) {
      const char = field_characters[i];
      if (char.value === "\n") {
        // 如果出现换行符,将换行符之前,上一个换行符之后的内容放到对应的第n个数组中
        if (section_index === 0) {
          field_sections[section_index].push(...field_characters.slice(0, i));
        } else {
          // 这种情况出现在需要替换的内容有多段的时候
          field_sections[section_index].push(
            ...field_characters.slice(
              // 截取的是上一个换行符的 下一位 开始到 换行符 之间的字符
              field_sections[section_index - 1].length + 1,
              i
            )
          );
        }
        section_index++;
      }
    }
    if (field_sections.length === 1) {
      // 不跨段的情况 直接是替换掉段落中文本域的内容
      this.characters.splice(refresh_location, 0, ...field_characters);
      this.updateChildren();
    } else {
      for (let j = 0; j < field_sections.length; j++) {
        // 分段的情况 循环上面处理好的所有字符
        const field_chars = field_sections[j];
        if (j === 0) {
          // 第一段
          this.characters.splice(refresh_location, 0, ...field_chars);
          this.updateChildren();
        } else {
          // 后续新的段落
          const new_para = new Paragraph(
            uuid("para"),
            this.cell,
            this.group_id
          );
          this.cell.paragraph.splice(this.para_index + j - 1, 0, new_para);
          new_para.setParagraphAttr(this);
          new_para.characters = field_chars;
          new_para.updateChildren();
        }
      }
    }
  }

  // 查找指定字符的位置
  findSpecifyCharacterIndex(value: string) {
    return this.characters.findIndex((character) => character.value === value);
  }

  /**
   * 获取当前段内容的纯文本默认包含文本域边框字符
   * @returns 纯文本
   */
  getStr(
    noSymbol: boolean = false, 
    needReadIndex: boolean = false, 
    params: {
      excludeUnselected: boolean
    } = {
      excludeUnselected: true
    }
  ): any {
    const { excludeUnselected } = params;
    const characters = this.characters;
    let str = "";
    const indexArray = [];
    const editor = this.cell.editor;
    const internal = editor.internal;

    for (let i = 0, len = characters.length; i < len; i++) {
      const char = characters[i];
      if (!char.value) {
        if (isImage(char)) {
          const { formula_type, params } = char.meta ;
          if (MENSTRUAL_EXPRESSION.has(formula_type)) {
            const p = MENSTRUAL_EXPRESSION.get(formula_type)!;
            for (let i = 0; i < p.length; i++) {
              str += (p[i] || "") + ":" + (params[i] || "") + (i === p.length - 1 ? "" : "，")
            }
          } else {
            params && Array.isArray(params) && (formula_type !== undefined) && (str += JSON.stringify(params));
          }
        }
        continue;
      }
      if (noSymbol) {
        if (char.field_position === "normal") {
          if (!excludeUnselected) {
            // 不排除未选中的时 就全都要了
            str += char.value;
            indexArray.push(i);
          } else {
            // 排除未选中的
            // 1. 有文本域 id
            if (char.field_id) {
              // 1.1 跟记录的是否一致
              if (char.field_id === internal.currentFieldInfo.boxFieldId) {
                // 1.1.1 如果已经判断过有这个复选框的文本域了就要判断是否选中
                if (internal.currentFieldInfo.checked) {
                  str += char.value
                  indexArray.push(i);
                } else {
                  // 1.1.2 没有选中的也就不要了
                  continue;
                }
              } else {
                // 1.2 还没有记录 可能是普通文本域 也可能是复选框文本域
                // 1.2.1 先判断是否是记录好的其他文本域
                if (internal.currentFieldInfo.otherFieldId.has(char.field_id)) {
                  str += char.value;
                  indexArray.push(i);
                } else {
                  // 1.2.2 不是记录好的其他文本域 就要根据 id 获取文本域判断是否是复选框
                  const field = editor.getFieldById(char.field_id);
                  if (isBoxField(field)) {
                    // 1.2.2.1 如果是复选框 就要记录
                    internal.currentFieldInfo.boxFieldId = char.field_id;
                    internal.currentFieldInfo.checked = !!field.box_checked;
                    if (field.box_checked) {
                      str += char.value;
                      indexArray.push(i);
                    }
                  } else {
                    // 1.2.3 不是复选框文本域
                    internal.currentFieldInfo.otherFieldId.set(char.field_id, true);
                    str += char.value;
                    indexArray.push(i);
                  }
                }
              }
            } else {
              // 2. 没有文本域 id 的也是都要的 但是不要位置了
              str += char.value;
              indexArray.push(i);
            }
          }

        }
      } else {
        if (!excludeUnselected) {
          str += char.value;
        } else {
          // 排除未选中的
          // 1. 有文本域 id
          if (char.field_id) {
            // 1.1 跟记录的是否一致
            if (char.field_id === internal.currentFieldInfo.boxFieldId) {
              // 1.1.1 如果已经判断过有这个复选框的文本域了就要判断是否选中
              if (internal.currentFieldInfo.checked) {
                str += char.value
              } else {
                // 1.1.2 没有选中的也就不要了
                continue;
              }
            } else {
              // 1.2 还没有记录 可能是普通文本域 也可能是复选框文本域
              // 1.2.1 先判断是否是记录好的其他文本域
              if (internal.currentFieldInfo.otherFieldId.has(char.field_id)) {
                str += char.value;
              } else {
                // 1.2.2 不是记录好的其他文本域 就要根据 id 获取文本域判断是否是复选框
                const field = editor.getFieldById(char.field_id);
                if (isBoxField(field)) {
                  // 1.2.2.1 如果是复选框 就要记录
                  internal.currentFieldInfo.boxFieldId = char.field_id;
                  internal.currentFieldInfo.checked = !!field.box_checked;
                  if (field.box_checked) {
                    str += char.value;
                  }
                } else {
                  // 1.2.3 不是复选框文本域
                  internal.currentFieldInfo.otherFieldId.set(char.field_id, true);
                  str += char.value;
                }
              }
            }
          } else {
            // 2. 没有文本域 id 的也是都要的 但是不要位置了
            str += char.value;
          }
        }

      }
    }
    if (needReadIndex) {
      return { str, indexArray };
    }
    return str;
  }

  getRawData() {
    const saveData = [this.copy(this.cell, true)]; // 循环 当前段落或者表格
    const newRootCell = initCell(this.cell.editor, "trans");
    const newHeaderCell = initCell(this.cell.editor, "header_trans");
    const newFooterCell = initCell(this.cell.editor, "footer_trans");
    newRootCell.paragraph = saveData;
    // return rawDataTrans.modelDataToRawData(
    //   newHeaderCell,
    //   newRootCell,
    //   newFooterCell
    // );
    this.cell.editor.event.emit(
      "modelData2RawData",
      newHeaderCell,
      newRootCell,
      newFooterCell
    );
  }

  replaceWith(...rawDataArr: any[]) {
    const editor = this.cell.editor;
    const originViewMode = editor.view_mode;
    editor.view_mode = ViewMode.NORMAL; // 不用 setViewMode() 因为里边会调用 refreshDocument(true) 改变了 页眉页脚的编辑状态 挪到进入编辑页眉页脚模式上边就没事了 还是只用属性赋值，避免性能消耗

    const hf_part = this.cell.getLocation();
    if (hf_part !== "root") {
      editor.enterEditHeaderAndFooterMode(hf_part);
    }

    const resInfo = this.cell.editor.deleteContentByPath(
      [this.para_index, 0],
      [this.para_index, this.characters.length]
    );
    if (Array.isArray(resInfo)) {
      // 将para_path再转为model_path进行光标位置设置
      this.cell.editor.selection.setCursorPosition(
        this.cell.editor.paraPath2ModelPath(resInfo)
      );
      for (const rawData of rawDataArr) {
        EditorHelper.insertTemplateData(this.cell.editor, rawData);
      }
    }

    if (hf_part !== "root") {
      editor.quitEditHeaderAndFooterMode();
    }

    editor.setViewMode(originViewMode);
  }

  /**
   * 插入换行符
   * @param index 位置
   * @param newEnterChar 回车符
   * @param field 可能是在文本域中
   */
  insertEnterChar(
    index: number,
    newEnterChar: Character,
    field: XField | null
  ) {
    const result_rows: Row[] = [];
    if (field) {
      if (!field.children.length) {
        // 判断children中是否有内容 如果没有 显示的内容就是 placeholder 需要删除
        field.removeParaPlaceholder();
      }
      newEnterChar.field_id = field.id;
      // 插入位置的前一个字符
      let field_pre_character = this.characters[index - 1];
      if (index === 0) {
        // 如果是在开头，那么找上一段的最后一个字符
        field_pre_character = (this.previousParagraph as Paragraph).characters[
          (this.previousParagraph as Paragraph).characters.length - 1
        ];
      }
      // 获取文本域children中插入文字的位置
      const field_index = field.getCharIndexInField(field_pre_character);
      field.children.splice(field_index, 0, newEnterChar);
    }
    const result_paras = this.splitParagraph(index, newEnterChar);
    result_paras.forEach((para) => {
      result_rows.push(...para.children);
    });
    return this.cell.enter2UpdateChildren(result_paras, result_rows);
  }

  /**
   * 插入文字方法
   * @param value 插入的文字内容
   * @param index 在该位置处插入 value
   * @param font 文字样式
   * @param field 是否在文本域内插入
   * @returns 是否执行成功
   */
  insertText(
    value: string,
    index: number,
    font: Font,
    field: XField | null = null
  ) {
    if (font.height === 0)
      font.height = this.cell.editor.config.default_font_style.height;

    const characters: Character[] = [];
    if (value === "\n") {
      const newEnterChar = new Character(font, "\n");
      this.insertEnterChar(index, newEnterChar, field);
      return true;
    }
    // TODO 先拿到光标前一个字符，然后拼到value中进行转换，转换出来的第一个字符如何跟传入的字符值相同，
    //  则说明不是堆叠的藏文，如果不同则说明是堆叠藏文，可连续，此时将新的字符属性赋值给前一个字符（保持前一个字符的引用地址不变，否则需考虑文本域）
    // 插入位置的前一个字符
    let preCharacter = this.characters[index - 1];
    let isHandleTibetan = false;
    if (
      isCharacter(preCharacter) &&
      isTibetan(preCharacter.value) &&
      isTibetan(value[0])
    ) {
      value = preCharacter.value + value;
      isHandleTibetan = true;
    }
    const chars = specialCharHandle.splitString(value); // 就是因为特殊字符 .length 属性不对，所以在循环之前用别的字符代替一下，在循环里边再代替回来，canvas 绘制是没问题的
    for (let i = 0; i < chars.length; i++) {
      const char = chars[i];
      const character = new Character(font, char);
      character.field_id = field ? field.id : null;
      if (i === 0 && isHandleTibetan) {
        // TODO  说明是处理藏文堆叠逻辑 ， 这只处理了从前往后打的情况， 是否需要处理后面拼接的情况？
        if (preCharacter.value !== char) {
          preCharacter.value = char;
          preCharacter.width = character.width;
          preCharacter.draw_width = character.draw_width;
        }
        continue;
      }
      characters.push(character);
    }
    return this.insertElements(characters, index, field);
  }

  /**
   * 插入文字方法
   * @param value 插入的文字内容
   * @param character_index 段落中的位置
   * @param font 文字样式
   * @returns 是否执行成功
   */
  insertButton(
    value: string,
    character_index: number,
    field: XField | null = null,
    color: string,
    buttonWidth?: number,
    height?: number
  ) {
    const editor = this.cell.editor;
    const buttons: Button[] = [];

    const font = new Font(
      Object.assign({}, editor.contextState.getFontState())
    );
    let realHight = font.height;
    if (height) {
      if (height > 80) {
        editor.event.emit("message", {
          type: "warning",
          msg: "按钮高度设置超过最大高度，最大高度为80",
        });
        return false;
      }
      realHight = height;
      font.height = (height / 4) * 3;
    }
    const { width } = Renderer.measure(font, value);
    // +10给文字两遍留下空隙
    const space = 10;
    let realWidth = width + space;
    if (buttonWidth) {
      let texts = "";
      if (realWidth > buttonWidth) {
        for (let i = 0; i < value.length; i++) {
          const text = value[i];
          const { width } = Renderer.measure(font, texts + text + "..");
          if (width + space > buttonWidth) {
            value = texts + "..";
            break;
          }
          texts += text;
        }
      }
      realWidth = buttonWidth;
    }
    if (realWidth > this.width) {
      editor.event.emit("message", {
        type: "warning",
        msg: "按钮宽度大于行宽，请减小按钮宽度",
      });
      return false;
    }
    const button = new Button(value, realWidth, realHight, color);
    button.field_id = field ? field.id : null;
    buttons.push(button);
    this.insertElements(buttons, character_index, field);
    return button;
  }

  /**
   * 插入图片
   * @param src 图片路径
   * @param width 图片宽度
   * @param height 高度
   * @param para_index 段落序号
   * @param character_index 段落中的位置
   * @param focus_field 光标所在处文本域
   * @returns 是否执行成功
   */
  insertImage(
    image: Image,
    para_index: number,
    character_index: number,
    focus_field: XField | null = null
  ) {
    if (focus_field) {
      this.insertElementToField(focus_field, character_index, image);
    }
    // 当前段落中插入图片
    this.characters.splice(character_index, 0, image);
    // 生成row
    this.updateChildren();
    return true;
  }

  /**
   * 段落中插入复选框
   * @param selected 是否默认选中
   * @param height 复选框的高度
   * @param character_index 复选框插入位置
   * @param focus_field 光标处的文本域 可能不存在 即null
   * @param border 要插入复选框的边框样式
   * @returns 执行成功否
   */
  insertWidget(
    selected: boolean,
    height: number,
    widget_type: widgetType,
    character_index: number,
    border: string,
    editor: Editor,
    focus_field: XField | null = null,
    params?: any
  ) {
    let font = editor.contextState.getFontState();
    if (widget_type === "caliper") {
      const fontStyle = {
        family: "宋体", // 默认字体 宋体 勿动
        height: 12,
        bold: false,
        italic: false,
        underline: false,
        strikethrough: false,
        dblUnderLine: false,
        script: 3,
        characterSpacing: 0,
        color: "#000",
        bgColor: null,
        highLight: null,
      };
      font = editor.fontMap.add(fontStyle);
    }

    const widget = new Widget(selected, height, font, widget_type, params);
    widget.border = border;
    if (focus_field) {
      this.insertElementToField(focus_field, character_index, widget);
    }
    // 当前段落中插入widget
    this.characters.splice(character_index, 0, widget);
    // 生成row
    this.updateChildren();
    return widget;
  }

  insertBox(
    editor: Editor,
    content: any,
    character_index: number,
    focus_field: XField | null = null,
    name: string
  ) {
    const focus_row = editor.selection.getFocusRow();

    const newBox = new Box(content, focus_row.height, name);
    if (focus_field) {
      this.insertElementToField(focus_field, character_index, newBox);
    }
    // 当前段落中插入widget
    this.characters.splice(character_index, 0, newBox);
    // 生成row
    this.updateChildren();
    return true;
  }

  insertLine(
    editor: Editor,
    line_hight: number,
    color: string,
    character_index: number,
    focus_field: XField | null = null,
    form: string
  ) {
    const focus_row = editor.selection.getFocusRow();

    const newLine = new Line(
      this.cell,
      focus_row.height,
      line_hight,
      color,
      form
    );
    if (focus_field) {
      this.insertElementToField(focus_field, character_index, newLine);
    }
    // 当前段落中插入widget
    this.characters.splice(character_index, 0, newLine);
    // 生成row
    this.updateChildren();
    return true;
  }

  /**
   * 在文本域中插入图片或小组件
   * @param focus_field 目标文本域
   * @param character_index 位置
   * @param element 待插入的元素
   */
  insertElementToField(
    focus_field: XField,
    character_index: number,
    element: Image | Widget | Line | Box | Button
  ) {
    if (focus_field.children.length === 0) {
      // 如果是空的 那么删除对应的提示字符
      this.characters.splice(
        character_index,
        focus_field.placeholder_characters.length
      );
    }
    let field_pre_character = this.characters[character_index - 1];
    if (character_index === 0) {
      const previousParagraph = this.previousParagraph as Paragraph;
      field_pre_character =
        previousParagraph.characters[previousParagraph.characters.length - 2];
      // 带有级联文本域都昌xml病历转换报错
      if (!field_pre_character) {
        field_pre_character = previousParagraph.characters[0];
      }
    }
    element.field_id = focus_field.id;
    focus_field.children.splice(
      focus_field.getCharIndexInField(field_pre_character),
      0,
      element
    );
  }

  /**
   * 段落中插入文本域
   * @param index 段落中的位置 在该位置处 插入文本域
   * @param focus_field 光标所在处的文本域
   * @param style 文本域样式 一般取 editor.contextState.getFontState()
   * @param props 文本域属性
   * @returns 执行成功否
   */
  insertField(
    index: number,
    focus_field: XField | null,
    style: FontStyle,
    props?: any
  ): XField | BoxField {
    const field_id = uuid("field");
    let new_field;
    if (props && props.type === "box") {
      new_field = new BoxField(field_id, style, this.cell);
    } else {
      new_field = new XField(field_id, style, this.cell); // new XField 在文本域上保存 id style cell 并从 Editor.config 中取 边框样式和颜色保存上
    }
    if (props && props.type === "anchor") {
      props.style = props.style || {};
      props.style.height = 0;
    }
    if (props) {
      // 设置文本域对应的属性
      if (props.style) {
        // 因为传入的 style 是通过 editor.contextState.getFontState() 获取的 font 里边已经有 id 了,但是又修改了 props.style 那么就应该重新判断有没有这样的样式,再决定是否重新生成一个新的 id
        props.style = { ...new_field.style, ...props.style };
        props.style.id = uuid("font");
        this.cell.editor.fontMap.add(props.style, props.style.id);
      }
      props2Obj(new_field, props);
      new_field.generateMainProps();
    }
    if (focus_field) {
      const currentCharacter = this.characters[index];
      const insert_index = focus_field.children.indexOf(currentCharacter);
      if (insert_index >= 0) {
        focus_field.children.splice(insert_index, 0, new_field);
      } else {
        // 如果找不到就只有两种情况
        if (currentCharacter === focus_field.end_sym_char) {
          // 第一种情况 不可能有等于前边边框的时候 因为那会跑到别的文本域里边去
          focus_field.children.splice(
            focus_field.children.length,
            0,
            new_field
          );
        } else {
          for (let i = 0, len = focus_field.children.length; i < len; i++) {
            const current = focus_field.children[i];
            if (isField(current)) {
              if (current.start_sym_char === currentCharacter) {
                // 第二种情况 不可能有等于后边边框的情况 因为那就在另外一个文本域里边了 进不了这里的子集
                focus_field.children.splice(i, 0, new_field);
                i++;
              }
            }
          }
        }
      }
      new_field.parent = focus_field;
    }
    let ins_characters = new_field.placeholder_characters;
    if (props && props.label_text) {
      ins_characters = new_field.textToFieldCharacter(
        props.label_text
      ) as Character[];
      new_field.children = [...ins_characters];
    }
    // 拼接字符串，插入对应当前段落中
    const characters_field: (Character | Image | Button)[] = [
      new_field.start_sym_char,
      ...ins_characters,
      new_field.end_sym_char,
    ];
    this.characters.splice(index, 0, ...characters_field);

    // 不应该 直接 push 应该看存在的文本域的位置 开头比插入新文本域的光标处还大就在这个文本域前边插入新的
    // 调用该方法处 都用了 selection 里边的路径 没有指定的路径 所以可以直接用 selection.focus 等
    const fields = this.cell.fields;
    const insert_index = getInsertIndexInCurrentCellFields(
      [this.para_index, index],
      fields
    );

    fields.splice(insert_index, 0, new_field);

    this.updateChildren();
    return new_field;
  }

  /**
   * 输入回车 分割段落
   * @param index 段落中的当前字符下标
   * @returns 分割后的两个数组
   */
  splitParagraph(
    index: number,
    character: Character | null = null
  ): Paragraph[] {
    const para_id = uuid("para");
    // 生成新段
    const new_paragraph = new Paragraph(para_id, this.cell, this.group_id);
    // 分割的成两段，将两段的children分别映射到cell的children上 需要将原先的对应行由新生成的对应行替换，这个变量需要作为参数记录下来
    this.splice_length = this.children.length;
    new_paragraph.para_index = this.para_index + 1;
    new_paragraph.setParagraphAttr(this);
    if (this.islist && this.isOrder) {
      new_paragraph.list_index = this.list_index + 1;
    }
    new_paragraph.characters = this.characters.slice(index);
    new_paragraph.createRow();
    this.characters.splice(index, this.characters.length);
    this.characters.push(character);
    this.createRow();
    return [this, new_paragraph];
  }

  /**
   * 统一的给新的段落赋值
   * @param afferentPara 传入的旧段落 或者 原始数据
   * @returns 赋值之后的段落
   */
  setParagraphAttr(afferentPara: Paragraph) {
    this.align = afferentPara.align;
    this.isOrder = afferentPara.isOrder;
    this.vertical_align = afferentPara.vertical_align;
    this.islist = afferentPara.islist;
    this.level = afferentPara.level;
    this.indentation = afferentPara.indentation;
    this.content_padding_left =
      afferentPara.content_padding_left ?? this.content_padding_left;
    // this.title_length = afferentPara.title_length ?? this.title_length; // 字符对齐出现第一行bug
    this.before_paragraph_spacing = afferentPara.before_paragraph_spacing;
    this.row_ratio = afferentPara.row_ratio;
    this.dispersed_align = afferentPara.dispersed_align;
  }

  /**
   * 转换列表有序无序
   * @param isOrder 有序无序列表
   */
  toListOrOrdinary(isOrder: boolean) {
    // 刚开始如果是普通段落 即this.isOrder = false this.islist = false
    // 如果是有序无序之间的转换 那么初始状态有
    // 1、this.isOrder = false this.islist = true
    // 2、this.isOrder = true this.islist = true
    if (this.isOrder === isOrder) {
      this.islist = !this.islist; // list转换
    } else {
      this.isOrder = isOrder;
      this.islist = true;
    }
    this.level = this.level ? this.level : 1; // 初始级别设置
    this.list_index = this.list_index ? this.list_index : 1;
    // 生成row
    this.cell.updateParaIndex();
    this.updateChildren();
  }

  /**
   * 调整list级别
   * @param num list的级别
   * @returns 是否执行成功
   */
  adjustlevel(num: number) {
    if (this.level > this.cell.editor.config.list_max_level && num > 0) {
      return;
    }
    // 最低级 不再是list类型
    if (num === -1 && this.level === 1) {
      this.islist = false;
    }
    this.level += num;
    this.updateChildren();
  }

  /**
   * 修改文字对齐方向
   * @param aline 对其方向
   */
  changeAlign(aline: alignType) {
    if (aline === this.align && aline !== "dispersed") this.align = "left";
    if (aline === "dispersed") {
      this.dispersed_align = true;
    } else {
      this.dispersed_align = false;
    }
    this.align = aline;
    this.content_padding_left = 0;
    this.updateChildren();
  }

  /**
   * 设置单元格内段落的垂直对齐方式
   * @param alignDirection 方向 top bottom center
   */
  setVerticalAlign(alignDirection: VerticalAlign) {
    this.vertical_align = alignDirection;
    this.updateChildren();
  }

  /**
   * 设置段落的分散对齐
   */
  setDispersed() {
    this.content_padding_left = 0;
    this.dispersed_align = !this.dispersed_align;
    this.updateChildren();
  }

  /**
   * 设置行倍距
   * @param row_ratio 行倍距
   */
  changeRowRatio(row_ratio: number) {
    this.row_ratio = row_ratio;
    this.updateChildren();
  }

  /**
   * 设置段前间距
   * @param before_paragraph_spacing 段前间距
   */
  changeParaBeforSpacing(before_paragraph_spacing: number) {
    this.before_paragraph_spacing = before_paragraph_spacing;
    this.updateChildren();
  }

  /**
   * 内容右对齐方式
   * @param row 段落末尾行
   */
  modifyAlignmentToRight(row: Row, isLast: boolean = false) {
    const characters = row.children;
    // 倒着循环一遍
    for (let i = characters.length - 1; i >= 0; i--) {
      const character = characters[i];

      // 剩余宽度
      let residueWidth: number = row.width;
      if (characters[i + 1]) {
        // 如果下一个字符存在 剩余宽度就是下一个字符的left
        residueWidth = characters[i + 1].left;
        character.left =
          residueWidth - character.width - row.tail_width / row.children.length;
        if (isLast) {
          character.left = residueWidth - character.width;
        }
      } else {
        character.left = residueWidth - character.width;
      }
    }
  }

  /**
   * 内容居中对齐方式
   * @param row 段落末尾行
   */
  modifyAlignmentToCenter(row: Row) {
    const characters = row.children;
    // 如果只有换行符没有其他字符的情况下
    if (characters.length) {
      const margin_left =
        (row.width - row.children[row.children.length - 1].right) / 2;
      // 循环一遍所有字符
      for (let i = 0; i < characters.length; i++) {
        const char = characters[i];
        char.left = char.left + margin_left;
      }
    } else {
      row.linebreak!.left = (row.width - row.linebreak!.right) / 2;
    }
  }

  /**
   * 分散对齐方式
   * @param row 段落末尾行
   */
  modifyAlignmentToDispersed(row: Row) {
    const characters = row.children;
    if (characters.length) {
      const tail_width =
        row.width - row.children[row.children.length - 1].right;
      // 字符数量和文本域内字符数量 同文本域内的字符算一个
      let char_num = 0;
      // 同一个文本域内的字符
      const field_chars = [];
      // const fields_ids: any[] = [];
      // 这个循环计算数量
      for (let i = 0; i < characters.length; i++) {
        const char = characters[i];
        if (char.field_id) {
          // const id_index = fields_ids.findIndex((id) => char.field_id === id);
          // if (id_index > -1) {
          //   fields_ids.push(fields_ids[id_index]);
          // } else {

          // }

          field_chars.push(char);
          if (char.field_position === "end" || characters.length - 1 === i) {
            const field = this.cell.getFieldById(char.field_id);
            if (field?.parent) {
              continue;
            }
            char_num += 1;
            field_chars.length = 0;
          }
        } else {
          if (field_chars.length) {
            char_num += 2;
            field_chars.length = 0;
          } else {
            char_num += 1;
          }
        }
      }
      // 文本域嵌套，在文本域中换行会出现这种情况
      if (field_chars.length) {
        char_num += 1;
        field_chars.length = 0;
      }
      if (char_num === 1) return;
      // 计算在分散分布的情况下各个部分的左偏移量
      const margin_left = tail_width / (char_num - 1);
      // 每个字符的偏移量倍数
      let field_start_index = 0;
      for (let i = 0; i < characters.length; i++) {
        const char = characters[i];
        if (!char.field_id) {
          char.left += margin_left * field_start_index;
          // 第一个字符左对齐，所以在设置后再加一
          field_start_index += 1;
        } else {
          char.left += margin_left * field_start_index;
          // 文本域结束字符 下一个代表上方char_num中的另一个了
          if (char.field_position === "end" || i === characters.length - 1) {
            const field = this.cell.getFieldById(char.field_id);
            if (field?.parent) {
              continue;
            }
            field_start_index += 1;
          }
        }
      }
    }
  }

  /**
   * 首行缩进
   * @param indentation 缩进字符数
   */
  firstRowIndentation(indentation: number) {
    const editor = this.cell.editor;
    const paragraphs = editor.selection.selected_para_info.paragraphs;
    if (paragraphs.find((para) => para.start_para_path.length === 4)) return;
    this.indentation = indentation;
    this.updateChildren();
  }

  /**
   * 修改样式
   * @param start_index 起始位置
   * @param end_index 结束位置
   * @param style 需要修改的样式
   * @returns true / false 执行成功或者失败
   */
  changeStyle(
    start_index: number,
    end_index: number,
    style: Partial<FontStyle>
  ) {
    if (this.height === 0) return;
    const bg_color = style?.bgColor;
  
    for (let i = start_index; i < end_index; i++) {
      const element = this.characters[i];
      const character_font_style = this.cell.editor.fontMap.add({
        ...element.font,
        ...style,
      });
      const contextState = this.cell.editor.contextState;
      contextState.setFontState(character_font_style);
      if (isCharacter(element)) {
        element.setFont(character_font_style, this.cell.editor);
      } else {
        element.font = character_font_style;
      }
      if (isWidget(element)) {
        element.height = character_font_style.height ?? 16;
        element.width = character_font_style.height ?? 16;
      }
  
      if (isWidget(element) && bg_color !== undefined) {
        if (bg_color) {
          element.color = bg_color;
        } else if (bg_color === null) {
          element.color = "#ffffff";
        }
      }
      // 如果包含文本域开始字符，则将文本域字符也进行设置
      if (element.field_position !== "normal") {
        // 只有文本域的边框是 start 和 end 文本域里边的字符，也是 normal
        const field = this.cell.getFieldById(element.field_id!)!;
        field.changeFontStyle(style);
      }
    }
    // 生成行
    this.updateChildren();
    
  }

  /**
   * 设置 character 的尺寸，更大或者更小
   * @param start 开始字符在该段落中的位置
   * @param end 结束字符在该段落中的位置
   * @param type 修改的类型 是更大还是更小
   * @returns
   */
  setCharacterSize(
    start: number,
    end: number,
    type: "bigger" | "smaller" | "base",
    baseFontHeight?: number,
    maxFontHeight?: number
  ) {
    let minniNum = 0;
    for (let i = start; i < end; i++) {
      const character = this.characters[i];
      if (
        (!isCharacter(character) && !isWidget(character)) ||
        (character as Character).mark === MarkType.NO_ZOOM
      )
        continue; // 暂时只修改 character 的大小就可以了 有标记的也不修改

      const originHeight = character.font.height;
      const originHeightIndex = fontHeightAsc.findIndex(
        (fontHeight) => fontHeight === originHeight
      );
      let newHeight = originHeight;
      if (type === "bigger") {
        newHeight = fontHeightAsc[originHeightIndex + 1];
      } else if (type === "smaller") {
        newHeight = fontHeightAsc[originHeightIndex - 1];
      } else if (
        type === "base" &&
        baseFontHeight &&
        (fontHeightAsc as any).includes(baseFontHeight)
      ) {
        newHeight = baseFontHeight;
      }
      if (!newHeight || (maxFontHeight && newHeight > maxFontHeight)) {
        minniNum++;
        continue;
      } // 不能 return 如果选中了一个完整的文本域一直增加 那么在前一个文本域边框的时候就已经将该文本域样式修改完了 后边边框就找不到下一个高度了 就是 undefined 了但是后边的逻辑肯定还是要走的 最起码得 updateChildren 还有选区后变得 内容

      // 因为文本域是统一设置的 但是该方法又要在原来的基础上变大变小 所以为了避免多个文本域 越来越大或者越来越小 临时存储一下 已经修改过的就不再修改了 在外部做了清空
      if (character.field_position !== "normal") {
        const field = this.cell.getFieldById(character.field_id!)!;
        if (this.cell.editor.internal.tempFields.get(field)) {
          minniNum++;
          continue;
        }
        field.changeFontStyle({ height: newHeight });
        this.cell.editor.internal.tempFields.set(field, true);
      } else {
        // 不等于 normal 就只调用 field.changeFontStyle 就可以了，它就是改边框的
        const newFontStyle = this.cell.editor.fontMap.add({
          ...character.font,
          height: newHeight,
        });
        if (isWidget(character)) {
          character.font = newFontStyle;
          character.width = newFontStyle.height ?? 16;
          character.height = newFontStyle.height ?? 16;
        } else {
          character.setFont(newFontStyle, this.cell.editor);
        }
      }
    }
    this.updateChildren();
    if (minniNum === end - start) {
      return true;
    }
  }

  /**
   *  截取并拼接段落字符然后重新生成row更新cell内容(主要用于内容删除时)
   * @param start_p_path
   * @param end_p_path
   * @param characters 如果存在则会将传入字符拼接到当前段落字符后
   */
  spliceCharactersByPath(
    start_p_path: number[],
    end_p_path: number[],
    characters: Character[] = []
  ) {
    let start_char_index = start_p_path[start_p_path.length - 1];
    const end_char_index = end_p_path[end_p_path.length - 1];
    // 当传入字符数组时说明是在段首删除或者是选区删除的一种情况
    if (characters.length) {
      // 如果characters = 0则是段首删除
      if (
        PathUtils.isStartPathInPara(start_p_path) &&
        PathUtils.equals(start_p_path, end_p_path)
      ) {
        // 删除当前段落换行符,因为传入字符中最后有换行符,然后追加传入字符
        this.characters.splice(this.characters.length - 1, 1, ...characters);
      } else {
        this.characters.splice(
          start_char_index,
          this.characters.length,
          ...characters
        );
      }
      this.updateChildren();
      return;
    }
    // 如果是回退键删除光标前一个元素
    if (PathUtils.equals(start_p_path, end_p_path)) {
      if (end_char_index === this.characters.length) {
        start_char_index--;
      }
      this.characters.splice(start_char_index - 1, 1);
    } else {
      let chars: Character[] = [];
      if (!PathUtils.isTablePath(start_p_path)) {
        chars = chars.concat(this.characters.slice(0, start_char_index));
      }
      if (!PathUtils.isTablePath(end_p_path)) {
        chars = chars.concat(this.characters.splice(end_char_index));
      }
      // 保留最后的换行符
      chars.push(this.lastCharacter);
      this.characters = chars;
    }
    this.updateChildren();
  }

  /**
   * 从当前单元格容器中移除当前段落
   */
  remove() {
    // 当cell中只存在一个段落时不允许删除
    if (this.cell.paragraph.length > 1) {
      this.characters = []; // 好像没有必要
      const start_row_index = this.children[0].cell_index;
      const update_length = this.children.length;
      this.cell.children.splice(start_row_index, update_length);
      this.cell.paragraph.splice(this.para_index, 1);
      this.cell.updateParaIndex();
      this.cell.updateRowBounding(start_row_index);
      // 段落如果存在group_id,则刷新分组信息
      if (this.group_id) {
        const group = this.cell.getGroupById(this.group_id);
        group?.refreshContentParaId();
      }
    } else {
      this.group_id = null;
      this.cell.clear();
    }
    // 解决设置为居中的单元格不刷新的问题
    // this.cell.refreshTypesetting();
  }

  /**
   * 段落重排，删除开头多余空格  \t制表符 删除空行
   * @param textIndent 首行缩进两字符
   * @param clearSpace 清除空格
   * @param canBeDelete 能否删除
   * @return true 将段落删除
   */
  reformat(
    textIndent: boolean,
    clearSpace: boolean,
    canBeDelete: boolean
  ): boolean {
    if (clearSpace) {
      const new_characters: any = [];
      for (let i = 0; i < this.characters.length; i++) {
        const character = this.characters[i];
        // 如果字符值为空格并且改空格没有设置过下划线、删除线、双下划线样式的时候跳过
        const font = character.font;
        if (
          character.value === " " &&
          !font.dblUnderLine &&
          !font.underline &&
          !font.strikethrough
        ) {
          continue;
        }
        // 这里等需求来的时候去掉 '+ 1'
        new_characters.push(character);
      }
      this.characters = new_characters;
    }

    // 删除完所有的空格和制表符之后，如果段落空了，那么就删除该段空行
    if (this.characters.length <= 1 && canBeDelete) {
      this.remove();
      return true;
    } else if (textIndent) {
      const condition =
        this.characters[0].value === " " || this.characters[0].value === "\t";
      if (condition) {
        for (let i = 0; i < this.characters.length; i++) {
          const character = this.characters[i];
          if (character.value !== " " && character.value !== "\t" && i !== 0) {
            // 这里等需求来的时候去掉 '+ 1'
            this.characters.splice(0, i);
            break;
          }
        }
      }
      this.indentation = 2;
      this.updateChildren();
    } else {
      this.updateChildren();
    }
    return false;
  }

  /**
   * 根据元素删除其在段落中的字符
   * @param element
   */
  removeElement(element: ElementInParagraph) {
    const del_index = this.characters.indexOf(element);
    if (del_index > -1) {
      this.characters.splice(del_index, 1);
    }
  }

  /**
   * 清空段落内容
   */
  clear() {
    this.characters = [this.characters.pop()];
    this.updateChildren();
  }

  // 复制自己
  copy(new_cell: Cell, is_save_id: boolean = false) {
    const characters = this.characters.map((c) => c.copy());
    const p = new Paragraph(is_save_id ? this.id : uuid("para"), new_cell);
    p.setParagraphAttr(this);
    p.isNumTypesetting = this.isNumTypesetting;
    p.IsCommaTypesetting = this.IsCommaTypesetting;
    p.title_length = this.title_length;
    p.characters = [...characters];
    p.createRow();
    return p;
  }
}
