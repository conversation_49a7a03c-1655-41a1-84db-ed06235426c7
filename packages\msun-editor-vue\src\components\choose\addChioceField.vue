<template>
  <modal
    :show="isShowChoiceModal"
    :width="modal_width"
    :freePoint="true"
    :title="title"
    @cancel="handleCancel"
  >
    <div class="prop">
      <div class="prop-div" v-if="choicedScopeInfo.isToBeGroup">
        <label>分组名称：</label>
        <a-input
          class="prop-input"
          id="groupName"
          type="text"
          placeholder="可为空"
          v-model="choicedScopeInfo.groupName"
        />
      </div>
      <div class="prop-div">
        <label>是否成组：</label>
        <a-select
          class="prop-select"
          @change="toBeGroup"
          :value="choicedScopeInfo.isToBeGroup"
          placeholder="请选择是否成组"
          dropdownClassName="xeditor-input-up"
        >
          <a-select-option :value="1">成组</a-select-option>
          <a-select-option :value="0">不成组</a-select-option>
        </a-select>
      </div>
      <div class="prop-div" v-if="choicedScopeInfo.isToBeGroup">
        <label>选择类型：</label>
        <a-select
          class="prop-select"
          v-model="choicedScopeInfo.type"
          placeholder="请选择是否多选"
          dropdownClassName="xeditor-input-up"
        >
          <a-select-option value="plural">多选</a-select-option>
          <a-select-option value="single">单选</a-select-option>
        </a-select>
      </div>
      <div class="prop-div">
        <label>选择框类型：</label>
        <a-select
          class="prop-select"
          v-model="choicedScopeInfo.choiceBoxType"
          placeholder="请选择图标类型"
          dropdownClassName="xeditor-input-up"
        >
          <a-select-option value="checkbox_rimless">
            矩形无边框：
            <icon-common icon="icon-danyehuaban"></icon-common>
          </a-select-option>
          <a-select-option value="checkbox">
            矩形：
            <icon-common icon="icon-fuxuankuang"></icon-common>
          </a-select-option>
          <a-select-option value="radio">
            圆形：
            <icon-common icon="icon-radio-checked"></icon-common>
          </a-select-option>
        </a-select>
      </div>
      <div class="prop-div">
        <label>是否可删除：</label>
        <a-select
          class="prop-select"
          v-model="choicedScopeInfo.isDeleteAble"
          placeholder="请选择是否可删除"
          dropdownClassName="xeditor-input-up"
        >
          <a-select-option :value="1"> 可删除 </a-select-option>
          <a-select-option :value="0"> 不可删除 </a-select-option>
        </a-select>
      </div>
      <div class="prop-div">
        <label>选择框位置：</label>
        <a-select
          class="prop-select"
          v-model="choicedScopeInfo.position"
          placeholder="请选择选择框位置"
          dropdownClassName="xeditor-input-up"
        >
          <a-select-option :value="0"> 前 </a-select-option>
          <a-select-option :value="1"> 后 </a-select-option>
        </a-select>
      </div>
      <div class="prop-div">
        <label>是否隐藏边框：</label>
        <a-select
          class="prop-select"
          v-model="choicedScopeInfo.isHideSymbol"
          placeholder="请选择是否隐藏边框"
          dropdownClassName="xeditor-input-up"
        >
          <a-select-option :value="1"> 隐藏 </a-select-option>
          <a-select-option :value="0"> 显示 </a-select-option>
        </a-select>
      </div>
      <div class="prop-div">
        <label>分隔符号：</label>
        <a-select
          class="prop-select"
          v-model="choicedScopeInfo.checkBoxSeparator"
          placeholder="分隔符"
          dropdownClassName="xeditor-input-up"
        >
          <a-select-option :value="' '"> 空格 </a-select-option>
          <a-select-option :value="'\n'"> 换行 </a-select-option>
        </a-select>
      </div>
      <div class="prop-div" style="display: flex; line-height: 32px">
        <label>是否必填：</label>
        <a-switch v-model="choicedScopeInfo.required" />
      </div>
      <div class="prop-div" style="display: flex; line-height: 32px">
        <label>文本域自动化：</label>
        <div @click="openFieldAdvanced">
          <icon-common icon="icon-bianji" style="cursor: pointer"></icon-common>
        </div>
      </div>
    </div>
    <div class="choiceBox">
      <a-table
        :bordered="true"
        :data-source="dataSource"
        :columns="columns"
        size="small"
        :pagination="false"
      >
        <template slot="name" slot-scope="text, record">
          <a-input
            v-model="record.name"
            placeholder="可为空"
            @focus="getFocusDom($event)"
          />
        </template>
        <template slot="value" slot-scope="text, record">
          <a-input v-model="record.value" @focus="getFocusDom($event)" />
        </template>
        <template slot="field_names" slot-scope="text, record">
          <a-input
            class="cascadeInput"
            v-model="record.field_names"
            placeholder="Name值,多个以“,”分隔"
            @focus="getFocusDom($event)"
          />
        </template>
        <template slot="formula_value" slot-scope="text, record">
          <a-input
            v-model="record.formula_value"
            @focus="getFocusDom($event)"
          />
        </template>
        <template slot="disabled" slot-scope="text, record">
          <a-select
            class="prop-select"
            :default-value="text"
            v-model="record.disabled"
            dropdownClassName="xeditor-input-up"
          >
            <a-select-option :value="0">正常</a-select-option>
            <a-select-option :value="1">禁用</a-select-option>
          </a-select>
        </template>
        <template slot="operation" slot-scope="text, record">
          <div class="editable-row-operations">
            <a @click="() => deleteRow(record.key)">删除</a>
          </div>
        </template>
      </a-table>
    </div>
    <div class="attention">
      注意：插入完所有的级联文本域，对当前选择框进行点击操作后级联规则生效。
    </div>
    <div slot="editor-modal-footer" class="footer">
      <div v-if="choicedScopeInfo.isToBeGroup">
        <a-button type="default" @click="handleAdd">添加</a-button>
      </div>
      <div class="submit">
        <a-button type="default" @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSubmit">确定</a-button>
      </div>
    </div>
  </modal>
</template>

<script>
import modal from "../common/modal.vue";
import { getUUID } from "../../assets/js/utils";
import iconCommon from "../common/iconCommon.vue";
// import BUS from "@/assets/js/eventBus";
export default {
  name: "addChioceField",
  components: {
    modal,
    iconCommon,
  },
  props: {
    isShowChoiceModal: {
      type: Boolean,
      default: false,
    },
    editorId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      required: false,
      modal_width: 720,
      title: "插入选择框",
      // focusDom: null,
      dataSource: [
        {
          key: getUUID("choice"),
          name: "",
          value: "",
          field_names: "",
          disabled: 0,
          formula_value: null,
        },
      ],
      count: 1,
      columns: [
        {
          title: "选项编码",
          width: "20%",
          align: "center",
          dataIndex: "name",
          scopedSlots: { customRender: "name" },
        },
        {
          title: "选项内容",
          align: "center",
          width: "20%",
          dataIndex: "value",
          scopedSlots: { customRender: "value" },
        },
        {
          title: "级联文本域",
          align: "center",
          width: "30%",
          dataIndex: "field_names",
          scopedSlots: { customRender: "field_names" },
        },
        {
          title: "值",
          align: "center",
          width: "10%",
          dataIndex: "formula_value",
          scopedSlots: { customRender: "formula_value" },
        },
        {
          title: "是否禁用",
          width: "10%",
          align: "center",
          dataIndex: "disabled",
          scopedSlots: { customRender: "disabled" },
        },
        {
          title: "操作",
          width: "10%",
          align: "center",
          dataIndex: "operation",
          scopedSlots: { customRender: "operation" },
        },
      ],
      // boxField: [],
      choicedScopeInfo: {
        isToBeGroup: 1, // 是否成组
        type: "plural", // plural 多选  single 单选
        choiceBoxType: "checkbox", // radio 圆形  checkbox矩形
        isDeleteAble: 1, // 是否可删除
        isHideSymbol: 0, // 是否隐藏边框
        position: 0, // 0 是前 1 是后 默认是 0
        groupName: "",
        automation_list: [],
        checkBoxSeparator: " ",
        datasource: [
          {
            key: getUUID("choice"),
            name: "",
            value: "",
            field_names: "",
            disabled: 0,
            formula_value: null,
          },
        ],
        required: false,
      },
    };
  },
  mounted() {},
  watch: {
    isShowChoiceModal: {
      handler(val) {
        if (val) {
          const { editor } = this.editor;
          if (editor.internal.altX && !editor.selection.isCollapsed) {
            // 新功能 ↓
            // 因为这个是有记忆功能的 所以我要在这里进行重置
            this.choicedScopeInfo = {
              isToBeGroup: 1, // 是否成组
              type: "single", // plural 多选  single 单选
              choiceBoxType: "checkbox", // radio 圆形  checkbox矩形
              isDeleteAble: 1, // 是否可删除
              isHideSymbol: 0, // 是否隐藏边框
              position: null, // 0 是前 1 是后 默认是 0
              groupName: "",
              automation_list: [],
              checkBoxSeparator: " ",
              datasource: [],
            };
            const splitSymbols = new Map([
              ["，", true],
              ["。", true],
              [" ", true],
              ["！", true],
              [",", true],
              ["", true],
              ["!", true],
              ["？", true],
              ["：", true],
              [":", true],
            ]);
            const { isCharacter, isWidget, isParagraph } =
              this.editor.TypeJudgment;

            const { paragraphs, start_end } =
              editor.selection.selected_para_info;
            let groupName = "";
            let firstStr = "";
            for (let i = 0; i < paragraphs[0].characters.length; i++) {
              const c = paragraphs[0].characters[i];
              if (isCharacter(c)) {
                if (c.value !== "\n") {
                  if (c.value === " ") {
                    firstStr += "$$$";
                  } else {
                    firstStr += c.value;
                  }
                  !splitSymbols.has(c.value) && (groupName += c.value);
                }

                if (
                  splitSymbols.has(c.value) &&
                  i !== paragraphs[0].characters.length - 2
                ) {
                  groupName = "";
                }
              } else if (isWidget(c)) {
                groupName = "";
                break;
              }
            }
            if (groupName.length) {
              this.choicedScopeInfo.groupName = groupName;
            } else {
              // 如果没有 name 那么就要找有没有冒号 我就找最后一个冒号
              let str = "";
              for (let i = 0; i < paragraphs[0].characters.length; i++) {
                const c = paragraphs[0].characters[i];
                if (isWidget(c)) break;
                if (isCharacter(c) && [":", "："].includes(c.value)) {
                  // 不能在这儿给 groupName 赋值 因为下边都是用的 groupName 做的判断
                  this.choicedScopeInfo.groupName = str;
                  break;
                }
                str += c.value;
              }
            }
            let str = "";
            let anotherStr = firstStr;
            for (let i = groupName.length ? 1 : 0; i < paragraphs.length; i++) {
              if (isParagraph(paragraphs[i])) {
                let startJ = i === 0 ? start_end[0] : 0;
                let len =
                  i === paragraphs.length - 1
                    ? start_end[1]
                    : paragraphs[i].characters.length;
                let hasColon = false;
                for (let j = startJ; j < len; j++) {
                  const c = paragraphs[i].characters[j];
                  if (isCharacter(c)) {
                    if (c.value === "\n") {
                      continue;
                    }
                    str += c.value;
                    // 需要加个判断有 groupName 为空 但是有分组 name 的情况 就是第一段有 widget 也有冒号的情况 这个时候就需要将 name 给去掉
                    if (
                      [":", "："].includes(c.value) &&
                      i === 0 &&
                      this.choicedScopeInfo.groupName.length &&
                      !groupName.length &&
                      !hasColon
                    ) {
                      str = "";
                      hasColon = true;
                    }
                    c.value === " "
                      ? (anotherStr += "$$$")
                      : (anotherStr += c.value);
                  } else if (isWidget(c)) {
                    str += "$$$";
                  }
                }
              }
            }
            let arr = str.split("$$$");
            !arr.length;
            if (!arr.length || arr.length === 1) {
              arr = anotherStr.split("$$$");
              this.choicedScopeInfo.groupName = "";
            }
            if (arr[0] === "") {
              // 说明在前
              this.choicedScopeInfo.position = 0;
            } else {
              // 说明在后
              this.choicedScopeInfo.position = 1;
            }
            let formula_value = 0;
            let startI = this.choicedScopeInfo.position === 0 ? 1 : 0;
            let len = startI === 0 ? arr.length - 1 : arr.length;
            for (let i = startI; i < len; i++) {
              this.choicedScopeInfo.datasource.push({
                key: getUUID("choice"),
                name: "",
                value: arr[i],
                field_names: "",
                disabled: 0,
                formula_value: formula_value++,
              });
            }
            this.dataSource = this.choicedScopeInfo.datasource;
            // 新功能 ↑
            return;
          }
          this.handleShowChoiceModal();
        } else {
          document.removeEventListener("click", this.clickModule);
        }
      },
    },
  },
  methods: {
    handleShowChoiceModal() {
      // 获取当前选择复选框文本域最外层的文本域
      const choicedField =
        this.editor.editor.selection.getFocusField()?.parent_box;
      this.boxField = choicedField;
      if (choicedField && choicedField.type === "box") {
        this.choicedScopeInfo.edit = true;
        this.choicedScopeInfo.choicedField = choicedField;
        this.choicedScopeInfo.isDeleteAble = choicedField.deletable;
        this.choicedScopeInfo.groupName = choicedField.name;
        this.choicedScopeInfo.isHideSymbol = choicedField.start_symbol ? 0 : 1;
        this.choicedScopeInfo.position = choicedField.position;
        this.choicedScopeInfo.required = choicedField.required ? true : false;
        this.choicedScopeInfo.type = choicedField.box_multi
          ? "plural"
          : "single";
        this.choicedScopeInfo.datasource = [];
        this.choicedScopeInfo.automation_list = [];
        for (let i = 0; i < choicedField.children.length; i++) {
          const element = choicedField.children[i];
          if (i === 0 && element.field_id) {
            // 不成组的情况
            this.choicedScopeInfo.isToBeGroup = 0;
            this.choicedScopeInfo.choiceBoxType =
              element.border === "dotted"
                ? "checkbox_rimless"
                : element.widgetType;
            const field_names = this.getCascadeNamesByText(
              choicedField.text,
              choicedField.cascade_list
            );
            const data = {
              key: getUUID("choice"),
              name: choicedField.name,
              value: choicedField.text,
              field_names: field_names,
              disabled: element.disabled,
            };
            this.choicedScopeInfo.datasource.push(data);
            break;
          } else if (
            choicedField.children[0].children.length &&
            !element.value
          ) {
            // 成组的情况
            this.choicedScopeInfo.isToBeGroup = 1;
            this.choicedScopeInfo.choiceBoxType =
              element.children[0].border === "dotted"
                ? "checkbox_rimless"
                : element.children[0].widgetType;
            const field_names = this.getCascadeNamesByText(
              element.text,
              choicedField.cascade_list
            );
            const data = {
              key: getUUID("choice"),
              name: element.name,
              value: element.text,
              field_names: field_names,
              disabled: element.children[0].disabled,
              formula_value: element.formula_value,
            };
            this.choicedScopeInfo.datasource.push(data);
          }
        }
        this.dataSource = this.choicedScopeInfo.datasource;
      }
      document.addEventListener("click", this.clickModule);
    },
    getFocusDom(event) {
      // 获取点击的 td 元素
      this.focusDom = event.target.parentNode;
    },
    openFieldAdvanced() {
      if (this.boxField) {
        this.boxField.box_multi =
          this.choicedScopeInfo.type === "plural" ? 1 : 0;
      }
      this.$el.style.display = "none";
      this.$emit("openFieldAdvanced", "fieldAuto", "checkbox", this.boxField);
    },
    clickModule(e) {
      const nodes = document.getElementsByClassName("cascadeInput");
      for (let i = 0; i < nodes.length; i++) {
        const node = nodes[i];
        if (node === e.target) {
          this.focusDom = node.parentNode;
          return;
        }
      }
      if (!(e.target.nodeName === "CANVAS")) {
        this.focusDom = null;
      }
    },

    getCascadeNamesByText(text, cascade_list) {
      let show_names = "";
      for (let i = 0; i < cascade_list.length; i++) {
        const e = cascade_list[i];
        if (text == e.text) {
          show_names = e.show_field_names.join(",");
          break;
        }
      }
      return show_names;
    },
    deleteRow(key) {
      if (this.dataSource.length === 1) {
        return;
      }
      const dataSource = [...this.dataSource];
      this.dataSource = dataSource.filter((item) => item.key !== key);
    },
    handleAdd() {
      const { count, dataSource } = this;
      const newData = {
        key: getUUID("choice"),
        name: "",
        value: "",
        field_names: "",
        disabled: 0,
      };
      this.dataSource = [...dataSource, newData];
      this.count = count + 1;
    },
    handleSubmit() {
      this.editor.editor.internal.altX = false;
      this.choicedScopeInfo.datasource = this.dataSource;
      this.$emit("submit", this.choicedScopeInfo);
      this.clear();
    },
    handleCancel() {
      this.editor.editor.internal.altX = false;
      this.clear();
      this.$emit("cancel", false);
    },
    insertFieldName(name) {
      if (!this.focusDom) return;
      //获取当前聚焦的input在整个table的第几行，然后给cascade_list的show_field_names赋值
      this.focusDom.childNodes[0].focus();
      const td = this.focusDom;
      const trs = td.closest("table").getElementsByTagName("tr");
      let tdIndex = -1;
      for (var i = 0; i < trs.length; i++) {
        var tds = trs[i].getElementsByTagName("td");
        // 检查当前行的每个单元格是否与目标单元格相同
        for (var j = 0; j < tds.length; j++) {
          if (tds[j] === td) {
            tdIndex = i - 1; // 找到目标行，记录行数
            break; // 结束内层循环
          }
        }

        if (tdIndex !== -1) {
          break; // 结束外层循环
        }
      }
      let field_name = this.dataSource[tdIndex].field_names;
      const result = field_name.endsWith(",") || field_name.endsWith("，");
      if (result) {
        this.dataSource[tdIndex].field_names += name + ",";
      } else {
        if (!field_name) {
          this.dataSource[tdIndex].field_names += name + ",";
        } else {
          this.dataSource[tdIndex].field_names += "," + name + ",";
        }
      }
    },
    // 清空组件编辑内容
    clear() {
      //记录上一次选择，方便连续添加
      this.choicedField = null;
      this.choicedScopeInfo.groupName = "";
      this.choicedScopeInfo.datasource = [
        {
          key: getUUID("choice"),
          name: "",
          value: "",
          field_names: "",
          disabled: 0,
        },
      ];
      this.dataSource = this.choicedScopeInfo.datasource;
      this.choicedScopeInfo.edit = false;
    },
    /**
     * 是否成组
     * val: 0 不成组
     * val: 1 成组
     */
    toBeGroup(val) {
      if (val) {
        this.columns.push({
          title: "操作",
          width: "10%",
          align: "center",
          dataIndex: "operation",
          scopedSlots: { customRender: "operation" },
        });
        this.choicedScopeInfo.isToBeGroup = val;
      } else if (this.dataSource.length === 1) {
        this.choicedScopeInfo.isToBeGroup = val;
        this.columns = this.columns.filter(
          (item) => item.dataIndex !== "operation"
        );
      } else {
        // 不成组的时候只能有1条选项
        this.choicedScopeInfo.isToBeGroup = 1;
        this.$editor.info("不成组只能有1条选项！");
      }
    },
  },
};
</script>

<style lang="less" scoped>
.footer {
  position: relative;
  display: flex;
}

.choiceBox {
  max-height: 400px;
  overflow-y: auto;
  background: white;
}

/deep/ .ant-table-small {
  border-radius: 0px;

  .ant-table-thead {
    background: rgb(240, 240, 240);
    opacity: 0.8;
  }
}

/deep/ .ant-table-small > .ant-table-content > .ant-table-body {
  margin: 0;
}

.attention {
  color: red;
  text-align: center;
}

.submit {
  position: relative;
  left: 485px;
}
</style>
