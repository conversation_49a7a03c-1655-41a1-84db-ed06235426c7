<template>
  <modal
    title="注意：点击编辑器页面插入要计算的文本域"
    :show="show"
    :freePoint="true"
    pointer-events:none
    :sessionMove="true"
    @cancel="cancel"
    @submit="submit"
    :width="520"
  >
    <div class="formula-symbol">
      <div style="display: flex">
        <div style="width: 50px; margin-left: 8px; margin-top: 10px">
          公式：
        </div>
        <div
          class="scrollable-content"
          @blur="divBlur()"
          ref="formulaEdit"
          :contenteditable="true"
          @input="insertText"
        ></div>
        <a-button
          type="primary"
          size="small"
          style="margin-top: 10px; margin-left: 10px"
          @click="clearInput"
          >清空</a-button
        >
      </div>
      <div style="display: flex; justify-content: center; align-items: center">
        <div class="formula-button">
          <div style="margin-top: 5px">
            <a-button
              v-for="(item, index) in symbolList"
              :key="index"
              :class="buttonIndex === index ? 'buttonClick' : 'button'"
              @click="clickFormulaButton(item, index)"
              >{{ item }}</a-button
            >
          </div>
        </div>
      </div>
      <div class="formula-button-num">
        <a-button
          v-for="item in numList"
          :key="item"
          class="num-button"
          @click="clickFormulaNumButton(item)"
          >{{ item }}</a-button
        >
      </div>
    </div>
    <div slot="editor-modal-footer" class="footer">
      <div
        style="
          width: 325px;
          text-align: left;
          margin-left: 20px;
          margin-top: 5px;
        "
      >
        <div class="error" v-if="isError === 2">
          注意： 不能在公式中添加当前编辑的文本域名称，请插入其他文本域
        </div>
        <div class="error" v-if="isError === 3">
          注意： 公式格式不正确，请更改
        </div>
      </div>
      <div>
        <a-button type="defalut" @click="cancel">取消</a-button>
        <a-button type="primary" @click="submit">确定</a-button>
      </div>
    </div>
  </modal>
</template>

<script>
import modal from "../common/modal.vue";
export default {
  name: "datePicker",
  data() {
    return {
      focusNode: null,
      buttonIndex: 0,
      symbolList: [
        "+",
        "-",
        "×",
        "÷",
        "(",
        ")",
        ">",
        "<",
        "≥",
        "≤",
        "=",
        ":",
        ";",
        "并",
        "或",
      ],
      numList: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, "."],
      isError: 1,
      stayTopFlag: false,
      symbol: "+",
    };
  },
  components: {
    modal,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    editorId: {
      type: String,
      default: "",
    },
    stayTop: {
      type: Boolean,
      default: false,
    },
  },
  mounted() {},
  watch: {
    show: {
      handler(val) {
        if (val) {
          if (this.stayTop) {
            this.$emit("changeStayTop", false);
            this.stayTopFlag = true;
          }
          const formula = this.fieldPropertyField?.formula;
          const that = this;
          if (formula) {
            const regex = /\[(.*?)\]/g; // 匹配方括号及其内部内容
            const parts = [];
            let lastIndex = 0;
            let match;

            while ((match = regex.exec(formula))) {
              const startIndex = match.index;
              const endIndex = regex.lastIndex;
              if (startIndex > lastIndex) {
                parts.push(formula.substring(lastIndex, startIndex));
              }
              parts.push(formula.substring(startIndex, endIndex));
              lastIndex = endIndex;
            }

            if (lastIndex < formula.length - 1) {
              parts.push(formula.substring(lastIndex));
            }

            this.$nextTick(() => {
              for (let i = 0; i < parts.length; i++) {
                const text = parts[i];
                if (text.includes("[")) {
                  const content = text.split(/\[(.*?)\]/).filter(Boolean);
                  that.insertDivChildren(content[0], "button");
                } else {
                  that.insertDivChildren(text, "span");
                }
              }
            });
          }

          this.$nextTick(() => {
            const dom = this.$refs.formulaEdit;
            dom.addEventListener("keydown", function (event) {
              // 检查是否按下了删除键（Backspace 或 Delete）
              if (event.key === "Backspace" || event.key === "Delete") {
                that.verification();
              }
            });
          });
        } else {
          if (this.stayTopFlag) {
            this.$emit("changeStayTop", true);
          }
        }
      },
    },
  },
  methods: {
    submit() {
      this.verification();
      if (this.isError === 1) {
        this.focusNode = null;
        this.editor.editor.formulaMode(false);
        this.$emit("submit");
      }
    },
    //去除空格及检测公式中是否存在][字符
    checkString(string) {
      var pattern = /\]\[/;
      return pattern.test(string);
    },
    verification() {
      let formula = this.$refs.formulaEdit.textContent.replace(/\s/g, "");
      if (formula === "") {
        this.isError = 1;
        return;
      }
      const reg = /\[(.*?)\]/g;
      const result2 = this.checkString(formula);
      let replaceText = this.exchangeFormulaLanguage(formula.replace(reg, 1));
      if (replaceText.indexOf("/0")) {
        replaceText = replaceText.replace("/0", "");
      }
      //验证数学字符
      // const rule1 = /^[-+*/()\d\s.]+$/;
      // const result1 = rule1.test(replaceText);
      if (!result2) {
        try {
          const list = replaceText.split(";");
          for (let i = 0; i < list.length; i++) {
            const formula = list[i].split(":");
            if (formula.length > 2) {
              this.isError = 3;
              return;
            } else if (formula.length === 2) {
              if (!formula[1]) {
                this.isError = 3;
                return;
              }
            }
            this.math("(" + formula[0] + ")");
            this.isError = 1;
          }
        } catch (error) {
          this.isError = 3;
          return;
        }
        const textList = formula.match(reg);
        if (textList && textList.length) {
          for (let i = 0; i < textList.length; i++) {
            const text = textList[i].replace(reg, "$1");
            if (this.fieldPropertyField.name === text) {
              this.isError = 2;
              return;
            }
          }
        }
        this.isError = 1;
      } else {
        this.isError = 3;
      }
    },
    math(text) {
      const Formula = Function;
      return new Formula("return" + text)();
    },
    exchangeFormulaLanguage(formula) {
      const newFormula = formula.replace(/[；]/g, ";");
      const newFormula2 = newFormula.replace(/[：]/g, ":");
      const newFormula3 = newFormula2.replace(/[×]/g, "*");
      const newFormula4 = newFormula3.replace(/[÷]/g, "/");
      const newFormula5 = newFormula4.replace(/[：]/g, ":");
      const newFormula6 = newFormula5.replace(/[≥]/g, ">=");
      const newFormula7 = newFormula6.replace(/[≤]/g, "<=");
      return newFormula7;
    },
    cancel() {
      this.focusNode = null;
      this.isError = 1;
      this.editor.editor.formulaMode(false);
      this.$emit("cancel");
    },
    divBlur() {
      const selection = window.getSelection();
      this.focusNode = selection.focusNode;
      this.focusOffset = selection.focusOffset;
    },
    clickFormulaButton(symbol, i) {
      if (symbol === "并") symbol = "&&";
      if (symbol === "或") symbol = "||";
      if (symbol === "=") symbol = "==";
      this.buttonIndex = i;
      const editableDiv = this.$refs.formulaEdit;
      editableDiv.focus();
      this.insertDivChildren(symbol, "span");
      this.verification();
      this.symbol = symbol;
    },
    clickFormulaNumButton(num) {
      const editableDiv = this.$refs.formulaEdit;
      editableDiv.focus();
      this.insertDivChildren(num, "span");
      this.verification();
    },
    insertFieldFormulaName(name) {
      const editableDiv = this.$refs.formulaEdit;
      editableDiv.focus();

      this.insertDivChildren(name, "button");
    },
    insertText() {
      this.verification();
    },
    clearInput() {
      const editableDiv = this.$refs.formulaEdit;
      editableDiv.innerHTML = "";
      this.isError = 1;
      const range = document.createRange();
      range.selectNodeContents(editableDiv);
      range.collapse(true);
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);
    },
    insertDivChildren(name, type) {
      const editableDiv = this.$refs.formulaEdit;
      const btn = document.createElement(type);
      if (type === "button") {
        btn.innerText = "[" + name + "]";
        btn.contentEditable = false;
      } else {
        btn.innerText = name;
      }
      btn.style.userSelect = "none";

      const range = window.getSelection().getRangeAt(0);

      let spaceMark = "";
      if (isNaN(name)) {
        spaceMark = " ";
      }
      let textNode = document.createTextNode(spaceMark);
      let addSymbol = document.createTextNode(" " + this.symbol);
      let add_symbol = false;
      const textContent = editableDiv.textContent.replace(/\s*/g, "");
      const lastChar = textContent.substr(-1);
      if (lastChar === "]" && type === "button") {
        add_symbol = true;
      }

      if (this.focusNode) {
        range.collapse(true);
        range.selectNodeContents(document.activeElement);
        range.setStart(this.focusNode, this.focusOffset);
        range.setEnd(this.focusNode, this.focusOffset);
        range.insertNode(btn);
        range.insertNode(textNode);
        if (add_symbol) {
          range.insertNode(addSymbol);
        }
      } else {
        editableDiv.appendChild(btn);
        editableDiv.appendChild(textNode);
        if (add_symbol) {
          editableDiv.appendChild(addSymbol);
        }
      }
      range.setStartAfter(btn);
      const selection = window.getSelection();
      // selection.removeAllRanges();
      selection.addRange(range);

      editableDiv.focus();
    },
  },
};
</script>
<style scoped>
.formula-button {
  text-align: center;
  margin-top: 5px;
  margin-bottom: 10px;
  margin-left: 0px;
  margin-right: 0px;
}
.formula-button-num {
  text-align: center;
}
.formula-button-num ::v-deep.ant-btn {
  height: 28px;
  margin-left: 2px;
  margin-right: 2px;
  padding: 10px;
  line-height: 8px;
}
.formula-button ::v-deep.ant-btn {
  height: 28px;
  margin-left: 2px;
  margin-right: 2px;
}
.error {
  color: red;
}
.scrollable-content {
  width: 370px;
  margin-bottom: 5px;
  margin-top: 10px;
  border: 1px solid rgb(217, 217, 217);
  border-radius: 4px;
}
.scrollable-content:focus {
  outline: none; /* 移除默认的焦点边框 */
  box-shadow: 0 0 0 1px #1890ff; /* 自定义聚焦时的阴影效果 */
}
.button {
  height: 30px;
  width: 30px;
  margin-left: 1px;
  margin-right: 1px;
  padding: 0px;
}
.buttonClick {
  height: 30px;
  width: 30px;
  margin-left: 1px;
  margin-right: 1px;
  padding: 0px;
  border: 1px solid rgb(77, 168, 252);
}
.num-button {
  margin-left: 1px;
  margin-right: 1px;
  width: 30px;
  height: 30px;
  padding: 0px;
}
.footer {
  display: flex;
}
</style>
