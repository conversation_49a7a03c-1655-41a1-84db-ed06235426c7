<template>
  <modal :show="show" :width="modalWidth" title="批注编辑" @cancel="cancel">
    <a-button
      class="editor-commentStayTopBtn"
      @click="handleCommentStayTopBtnClick"
      :style="{ backgroundColor: btnColor }"
    >
      <icon-common icon="icon-dingzi1" style="cursor: pointer"></icon-common>
    </a-button>
    <div class="prop">
      <a-textarea
        v-model="value"
        :allowClear="true"
        ref="comment_edit_modal"
        placeholder="请输入批注内容"
        :auto-size="{ minRows: 3, maxRows: 6 }"
      />
    </div>
    <a-button type="link" size="small" @click="handleUseBeforeCommentBtn">{{
      useBeforeCommentMention
    }}</a-button>
    <a-table
      v-if="showUseBeforeComment"
      :columns="columns"
      :data-source="allComments"
      size="small"
      :rowKey="(record) => record.id"
      style="background-color: white"
      :pagination="false"
      :scroll="{ y: 200 }"
    >
      <template slot="date" slot-scope="text">
        <span>{{ formatDate(text) }}</span>
      </template>
      <template slot="operation" slot-scope="text, record">
        <span class="editor-useBeforeComment" @click="handleQuoteClick(record)"
          >关联</span
        >
      </template>
    </a-table>

    <!-- <a-button type="link" size="small">引用已有批注</a-button> -->
    <div slot="editor-modal-footer" class="parent-footer">
      <div>
        <a-button type="default" @click="cancel">取消</a-button>
        <a-button type="primary" @click="submit">确定</a-button>
      </div>
    </div>
  </modal>
</template>

<script>
import modal from "../common/modal.vue";
import iconCommon from "../common/iconCommon.vue";
export default {
  name: "commentEdit",
  components: {
    modal,
    iconCommon,
  },
  data() {
    return {
      modalWidth: 400,
      value: "",
      isEdit: false, // false - 添加批注， true：编辑批注
      showUseBeforeComment: false,
      useBeforeCommentMention: "关联",
      commentStayTop: false,
      btnColor: "#ffffff",
      columns: [
        {
          title: "用户",
          dataIndex: "name",
          key: "name",
          width: "20%",
        },
        {
          title: "时间",
          dataIndex: "date",
          key: "date",
          width: "25%",
          scopedSlots: { customRender: "date" },
          ellipsis: true,
        },
        {
          title: "内容",
          dataIndex: "value",
          key: "value",
          width: "40%",
          ellipsis: true,
        },
        {
          title: "操作",
          dataIndex: "operation",
          width: "15%",
          scopedSlots: { customRender: "operation" },
          key: "operation",
        },
      ],
    };
  },
  mounted() {},
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    comment: {
      type: Object,
      default: () => {},
    },
    allComments: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    show(val) {
      if (val) {
        this.value = this.comment ? this.comment.value : "";
        if (this.value) {
          this.isEdit = true;
        }
        this.$nextTick(() => {
          this.$refs.comment_edit_modal.focus();
        });
      } else {
        this.showUseBeforeComment = false;
        this.useBeforeCommentMention = "关联";
        this.commentStayTop = false;
      }
    },
    commentStayTop(val) {
      let modalMask;
      if (val) {
        this.btnColor = "rgb(230,230,230)";
        this.$nextTick(() => {
          modalMask = document.querySelector(".ant-modal-wrap");
          modalMask.style.pointerEvents = "none";
        });
      } else {
        this.btnColor = "#ffffff";
        this.$nextTick(() => {
          modalMask = document.querySelector(".ant-modal-wrap");
          modalMask.style.pointerEvents = "auto";
        });
      }
    },
  },
  methods: {
    submit() {
      this.$emit("submit", this.value, this.isEdit, this.commentStayTop);
      this.value = "";
      this.isEdit = false;
    },
    cancel() {
      this.$emit("cancel");
      this.isEdit = false;
    },
    handleUseBeforeCommentBtn() {
      this.showUseBeforeComment = !this.showUseBeforeComment;
      if (this.showUseBeforeComment) {
        this.useBeforeCommentMention = "收起";
      } else {
        this.useBeforeCommentMention = "关联";
      }
    },
    handleClick(record) {
      console.log(record);
    },
    formatDate(timestamp) {
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    handleQuoteClick(record) {
      const editor = this.editor.editor;
      const allChars = editor.selection.selected_fields_chars.all_chars;
      if (allChars) {
        if (!allChars.length) {
          return this.$editor.warning("请先选中需要添加批注的文本");
        }
        allChars.forEach((char) => {
          if (
            char.field_position === "normal" &&
            this.editor.TypeJudgment.isCharacter(char)
          ) {
            char.comment_id = record.id;
          }
        });
        if (!this.commentStayTop) {
          this.cancel();
        }
      } else {
        return this.$editor.warning("请先选中需要添加批注的文本！");
      }
    },
    handleCommentStayTopBtnClick() {
      this.commentStayTop = !this.commentStayTop;
    },
  },
};
</script>
<style scoped>
.editor-useBeforeComment {
  color: #1890ff;
}
.editor-useBeforeComment:hover {
  cursor: pointer;
  color: #0f5eff;
}
.editor-commentStayTopBtn {
  border: 0px solid rgb(230, 230, 230);
  position: absolute;
  top: 4px;
  left: 310px;
  transition: none;
}

.editor-commentStayTopBtn:hover {
  border: 1px solid rgb(230, 230, 230);
}
</style>
