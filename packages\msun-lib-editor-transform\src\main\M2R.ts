import newRawInitModel from "../utils/Util";
import Cell from "../../../msun-lib-editor-common/src/editor/Cell";
import Group from "../../../msun-lib-editor-common/src/editor/Groups";
import Paragraph from "../../../msun-lib-editor-common/src/editor/Paragraph";
import Editor from "../../../msun-lib-editor-common/src/editor/Editor";
import Table from "../../../msun-lib-editor-common/src/editor/Table";

/**
 * 原始数据相关处理（模型数据与原始数据相互转换）
 */
export const M2R = {
  // 用于记录font,过滤掉内容中不存在的样式或者重复的样式
  newFontMap: {},
  newFontArray: [],
  newImageMap: new Map(),
  localTest: {},
  /**
   * 初始化扩展方法，主要是由common中导出的方法
   * @param instance
   */
  initExtOptions (instance: any) {
    this.deepClone = instance.utils.deepClone;
    this.uuid = instance.utils.getUUID;
    this.serializeCopy = instance.utils.serializeCopy;
    this.TypeJudgment = instance.TypeJudgment;
    this.localTest = instance.localTest;
    this.removeRepeat = instance.utils.removeRepeat;
    this.fontIsEqual = instance.utils.fontIsEqual;
    this.specialCharHandle = instance.utils.specialCharHandle;
    this.isTibetan = instance.utils.isTibetan;
  },
  /**
     * 处理fontMap数据
     * @param font
     * @return font_id
     */
  handleFontMapData (font: any): string {
    const res_font = this.newFontArray.find((item: any) => this.fontIsEqual(item, font));
    if (res_font) {
      return res_font.id;
    } else {
      if (!font.id) {
        font.id = this.uuid("font");
      }
      this.newFontArray.push(font);
      // @ts-ignore
      this.newFontMap[font.id] = font;
      // @ts-ignore
      return font.id;
    }
  },
  handleImageMapData (src: string, pointId?:string): string {
    const imageId = this.newImageMap.get(src);
    if (imageId) {
      return imageId;
    } else {
      const newImageId = pointId ?? this.uuid("image");
      this.newImageMap.set(src, newImageId);
      return newImageId;
    }
  },
  clearInitData () {
    this.newFontMap = {};
    this.newFontArray = [];
    this.newImageMap.clear();
  },
  /**
     * 将model_data（模型数据）转换为raw_data（原始数据）
     * @param cell_header 页眉
     * @param cell_content 正文
     * @param cell_footer 页脚
     * @param other_info 其他信息，可包含document_meta及其他配置信息
     */
  modelDataToRawData (
    cell_header: Cell,
    cell_content: Cell,
    cell_footer: Cell,
    other_info: any = {}
  ) {
    this.clearInitData();
    const extraInfo = Object.assign({ isFullConvert: true }, other_info.extraInfo);
    const header = this.cellDataToRawData(cell_header);
    const content = this.cellDataToRawData(cell_content, extraInfo);
    const footer = this.cellDataToRawData(cell_footer, extraInfo);
    const groups = this.cellGroupsToRawData(cell_content.groups);
    const fontMap = this.fontMapToRawData(this.newFontMap); // 必须在后边 因为 前边处理文本域样式有处理 FontMap 的逻辑
    const imageSrcObj = this.imageMapToRawData(this.newImageMap);
    const shapes = this.shapesToRawData(other_info.shapes);
    const waterMarks = this.waterMarksToRawData(other_info.waterMarks);
    const { editor } = cell_content;
    const that = this;
    const floatModelRaws = editor.floatModels.map(cell => {
      const obj: any = {};
      obj.originPosition = cell.originPosition;
      obj.width = cell.width;
      obj.height = cell.height;
      obj.data = that.cellDataToRawData(cell, extraInfo);
      return obj;
    });
    let rawData = {
      header,
      footer,
      content,
      groups,
      fontMap,
      imageSrcObj,
      bodyText: "",
      shapes: shapes,
      waterMarks: waterMarks,
      config: {
        page_info: other_info.page_info,
        direction: other_info.page_direction,
        header_horizontal: other_info.header_horizontal,
        footer_horizontal: other_info.footer_horizontal,
        rowLineType: other_info.rowLineType,
        startPageNumber: other_info.startPageNumber
      },
      meta: other_info.document_meta,
      customMeta: other_info.custom_meta,
      newDesignerType: "1", // 用于区分编辑器数据和报表数据
      floatModelRaws
    };
    this.clearInitData();
    rawData = this.deepClone(rawData); // 必须要有 deepClone 方法，因为继续操作有可能会影响到历史堆栈中的数据，撤销重做功能有影响
    return rawData;
  },
  /**
     * 分组数据转换
     * @param groups
     */
  cellGroupsToRawData (groups: Group[] | null): any {
    const raw_groups: any = [];
    if (!groups) {
      return raw_groups;
    }
    for (let i = 0; i < groups.length; i++) {
      const group = groups[i];
      const raw_group = newRawInitModel("group");
      this.objectPropertyAssignmen(group, raw_group);
      raw_groups.push(raw_group);
    }
    return raw_groups;
  },
  /**
     * fontMap转原始数据json
     * @param modelFontMap
     */
  fontMapToRawData (modelFontMap: any) {
    const fontMap = this.serializeCopy(modelFontMap);
    for (const font in fontMap) {
      delete fontMap[font].temp_valid_color;
      delete fontMap[font].temp_word_bgColor;
      delete fontMap[font].temp_word_color;
    }
    return fontMap;
  },
  /**
     * imageMap转原始数据json
     * @param modelImageMap
     */
  imageMapToRawData (modelImageMap: any) {
    const imageMap: any = {};
    for (const [key, value] of modelImageMap) {
      imageMap[value] = key;
    }
    return imageMap;
  },
  /**
     * watermarks转原始数据
     */
  waterMarksToRawData (modelMarks: any) {
    if (!modelMarks) {
      return [];
    }
    const waterMarks = [];

    for (let i = 0; i < modelMarks.length; i++) {
      const modelMark = modelMarks[i];
      const init_mark = this.initParams(modelMark.type);
      for (const mark_pram in init_mark) {
        init_mark[mark_pram] = modelMark[mark_pram];
      }
      waterMarks.push(init_mark);
    }
    return waterMarks;
  },
  /**
     * shapes转原始数据
     */
  shapesToRawData (modelShapes: any) {
    if (!modelShapes || !modelShapes.length) {
      return [];
    }
    const shapes = [];
    for (let i = 0; i < modelShapes.length; i++) {
      const shape = modelShapes[i];
      const init_shape = this.initParams(shape.type);
      for (const shape_pram in init_shape) {
        init_shape[shape_pram] = shape[shape_pram];
      }
      shapes.push(init_shape);
    }
    return shapes;
  },
  /**
     * 初始化的参数
     */
  initParams (type: string) {
    switch (type) {
      case "line":
        return {
          type: type,
          startXY: { x: 0, y: 0 },
          endXY: { x: 0, y: 0 },
          lineWidth: 1,
          color: "000",
          addition: { startXY: null, endXY: null },
          para: {}
        };
      case "cross":
        return {
          type: type,
          startXY: { x: 0, y: 0 },
          endXY: { x: 0, y: 0 },
          color: "000",
          para: {}
        };
      case "rect":
        return {
          type: type,
          startXY: { x: 0, y: 0 },
          endXY: { x: 0, y: 0 },
          color: "000",
          para: {}
        };
      case "circle":
        return {
          type: type,
          startXY: { x: 0, y: 0 },
          endXY: { x: 0, y: 0 },
          color: "000",
          para: {}
        };
      case "fold_line":
        return {
          type: type,
          startXY: { x: 0, y: 0 },
          endXY: { x: 0, y: 0 },
          color: "000",
          para: {},
          addition: { startXY: null, endXY: null },
          foldLine: []
        };
      case "imageMark":
        return {
          type: type,
          mode: "single",
          name: "",
          start: { x: 0, y: 0 },
          width: 100,
          height: 100,
          params: {
            src: ""
          }
        };
      case "textMark":
        return {
          type: type,
          mode: "single",
          start: { x: 0, y: 0 },
          width: 100,
          height: 100,
          params: {
            id: "",
            value: "",
            font_id: ""
          }
        };
    }
  },
  /**
     * 处理cell中的数据为原始数据
     * @param cell
     * @param extraInfo 扩展信息
     */
  cellDataToRawData (cell: Cell, extraInfo: any = {}) {
    const paragraphs = cell.paragraph;
    const raw_data = [];
    let preReturnRawFieldId;
    for (let i = 0; i < paragraphs.length; i++) {
      let container = paragraphs[i];
      if (this.TypeJudgment.isTable(container)) {
        const raw_table = newRawInitModel("table"); // 获取最最基础的原始数据(只是架子里边什么数据都没有)
        // 根据 container 这个 modelData 将属性 赋值给 原始数据 raw_table
        this.objectPropertyAssignmen(container, raw_table);
        for (let j = 0; j < container.children.length; j++) {
          const raw_cell = newRawInitModel("cell"); // 获取最基础的 Cell 原始数据对象
          const ori_cell = container.children[j];

          /*
                     设置原始单元格属性
                     */
          this.objectPropertyAssignmen(ori_cell, raw_cell);
          raw_cell.children = this.cellDataToRawData(ori_cell, extraInfo);
          raw_table.cells.push(raw_cell);
        }
        raw_data.push(raw_table);
      } else {
        container = container as Paragraph;
        const raw_paragraph = newRawInitModel("p");
        // 以下对象放置后需重新初始化，否则会
        const raw_text = newRawInitModel("text");
        extraInfo.preReturnRawFieldId = preReturnRawFieldId;
        preReturnRawFieldId = this.assembleRawParagraph(container, raw_paragraph, raw_text, extraInfo);
        this.objectPropertyAssignmen(container, raw_paragraph);
        // 如果段落中无子元素，则代表空行，此时应该设置换行符样式
        if (!raw_paragraph.children.length) {
          // 如果container没有characters，说明可能是复制了部分文本域背景文本，此时追加一个换行符
          let last_character_font;
          if (container.characters.length) {
            last_character_font = container.characters[container.characters.length - 1].font;
          } else {
            last_character_font = cell.editor.fontMap.add(cell.editor.config.default_font_style);
          }
          raw_text.font_id = this.handleFontMapData(last_character_font);
          raw_text.value = "";
          raw_paragraph.children.push(raw_text);
        }
        // if (raw_paragraph.children.length) {
        //   const children = raw_paragraph.children;
        //   for (let i = children.length - 1; i > 0; i--) {
        //     const child = children[i];
        //     if (child.field_id) {
        //       const parentField = cell.editor.getFieldById(child.field_id);
        //       if (parentField && !parentField.canBeCopied) {
        //         children.splice(i, 1);
        //         raw_paragraph.updateChildren();
        //       }
        //     }
        //   }
        // }

        raw_data.push(raw_paragraph);
      }
    }

    return raw_data;
  },

  /**
     * 装配 rawData 中的段落
     * @param paragraph modelData 中的段落
     * @param rawParagraph 初始化好的 rawData 中的段落
     * @param rawText 初始化好的 段落中的样式一样的字符
     * @param extraInfo 额外的信息
     * @returns
     */
  assembleRawParagraph (paragraph: Paragraph, rawParagraph: any, rawText: any, extraInfo: any) {
    const rawFieldMap: any = {}; // 存储当前段落中的文本域
    // 拿到文本域id
    let allFieldID = paragraph.characters.map((char) => char.field_id);
    // 去重
    allFieldID = this.removeRepeat(allFieldID);
    // 现将当前段落的文本域先缓存起来一会儿用
    for (let i = 0; i < allFieldID.length; i++) {
      const fieldID = allFieldID[i];
      if (fieldID) {
        const rawField = newRawInitModel("field");
        rawField.id = fieldID;
        const fieldInModelData = paragraph.cell.getOrigin().getFieldById(fieldID);
        if (fieldInModelData && fieldInModelData.ext_cell !== null) {
          rawField.ext_cell = this.cellDataToRawData(fieldInModelData.ext_cell);
        }
        this.objectPropertyAssignmen(fieldInModelData, rawField);
        rawFieldMap[fieldID] = {
          raw: rawField,
          model: fieldInModelData
        };
      }
    }

    const characters = paragraph.characters;
    const editor = paragraph.cell.editor;
    let rawField: any;
    for (let j = 0; j < characters.length; j++) {
      const element = characters[j];
      const elementNext = characters[j + 1];
      const elementFieldID = element.field_id;
      // 如果元素有field_id，则代表是一个输入域中的内容
      if (elementFieldID) {
        // 获取对应的文本域模型数据，用于对原始数据赋值(此处一定要使用getOrigin，否则跨页单元格会有问题)
        rawField = rawFieldMap[elementFieldID].raw;
        const fieldInModelData = rawFieldMap[elementFieldID].model;
        if (fieldInModelData) {
          // 这里还必须得加 FontMap.add 不能直接用 this.handleFontMapData(fieldInModelData.style) 否则就会出现调用 reInitRaw 之后打字 再撤销 整体样式变化的 bug
          // TODO xzq 理论上不该放到此处，移至到文本域构造函数中
          // const font = container.cell.editor.fontMap.add(fieldInModelData.style); // TODO 因为这里有 FontMap 的操作 所以在保存的时候处理 FontMap 要在该方法之后
          const font_id = this.handleFontMapData(fieldInModelData.style);
          rawField.font_id = font_id;
          // 当模型数据有父级，则说明原始数据文本域数组raw_fields中肯定存在其父级，根据id获取到其父级对象后将当前对象放入
          if (fieldInModelData.parent) {
            let parent_raw_field = rawFieldMap[fieldInModelData.parent.id]?.raw;
            if (this.localTest && this.localTest.useLocal) {
              // TODO 存在严重问题，在复制粘贴时以及级联文本域触发时,现在增加extraInfo.preReturnRawFieldId的判断，因为数据转换时不一定是全量数据
              if (!parent_raw_field && extraInfo.preReturnRawFieldId && elementFieldID !== extraInfo.preReturnRawFieldId) {
                parent_raw_field = newRawInitModel("field");
                this.objectPropertyAssignmen(fieldInModelData.parent, parent_raw_field);
                rawFieldMap[fieldInModelData.parent.id] = {
                  raw: parent_raw_field,
                  model: fieldInModelData.parent
                };
                rawParagraph.children.push(parent_raw_field);
              }
            }
            // 如果此时parent_raw_field不存在则说明是跨多段，当前是输入域中间位置
            if (parent_raw_field && !parent_raw_field.children.includes(rawField)) {
              parent_raw_field.children.push(rawField);
            }
            if (!parent_raw_field && !rawParagraph.children.includes(rawField)) {
              rawParagraph.children.push(rawField);
            }
          } else {
            // 如果无父节或者原始数据中有父节点，此段落中没有（跨多段），则直接放入
            if (!rawParagraph.children.includes(rawField)) {
              rawParagraph.children.push(rawField);
            }
          }
        }
      } else {
        rawField = null;
      }
      // 非普通字符（背景文本字符、开始边框字符、结束边框字符）不再进行以下操作、换行字符也不需要单独存储
      if (element.field_position !== "normal" ||
                (!rawField && element.value === "\n")) {
        continue;
      }
      if (this.TypeJudgment.isFraction(element)) {
        const raw = newRawInitModel("fraction");
        raw.value += element.value;
        raw.comment_id = element.comment_id; // 批注赋值 ID 能够保存
        raw.cusCommentId = element.cusCommentId; // 批注赋值 ID 能够保存
        element.mark && (raw.mark = element.mark); // 保存文字上的 mark 标记
        rawField ? rawField.children.push(raw) : rawParagraph.children.push(raw);
        raw.font_id = this.handleFontMapData(element.font);
      } else if (this.TypeJudgment.isCharacter(element)) {
        if ((element.value.length > 1 && !this.isTibetan(element.value)) || this.specialCharHandle.specialSymbol.includes(element.value)) {
          const src = this.specialCharHandle.convertImg(element);
          this.handleImageMapData(src, element.value);
        }
        rawText.value += element.value;
        rawText.comment_id = element.comment_id; // 批注赋值 ID 能够保存
        rawText.cusCommentId = element.cusCommentId; // 批注赋值 ID 能够保存
        element.mark && (rawText.mark = element.mark); // 保存文字上的 mark 标记
        if (
          !(this.TypeJudgment.isCharacter(elementNext)) ||
                    !this.fontIsEqual(element.font, elementNext.font) ||
                    element.field_position !== elementNext.field_position ||
                    elementNext.value === "\n" || element.comment_id !== elementNext.comment_id || // 批注 ID 不一样 就生成多个 text
                    element.cusCommentId !== elementNext.cusCommentId ||
                    element.mark !== elementNext.mark || this.TypeJudgment.isFraction(elementNext)
        ) {
          /*
                     设置原始文本段样式
                     */
          rawText.font_id = this.handleFontMapData(element.font);
          rawField
            ? rawField.children.push(rawText)
            : rawParagraph.children.push(rawText);
          editor.event.emit("assembleText", rawText.value, rawField, extraInfo);
          rawText = newRawInitModel("text");
        }
      } else if (this.TypeJudgment.isWidget(element)) {
        const raw_widget = newRawInitModel("widget");
        this.objectPropertyAssignmen(element, raw_widget);
        raw_widget.font_id = this.handleFontMapData(element.font);
        rawField
          ? rawField.children.push(raw_widget)
          : rawParagraph.children.push(raw_widget);
      } else if (this.TypeJudgment.isBox(element)) {
        const raw_box = newRawInitModel("box");
        raw_box.font_id = this.handleFontMapData(element.font);
        this.objectPropertyAssignmen(element, raw_box);
        rawField
          ? rawField.children.push(raw_box)
          : rawParagraph.children.push(raw_box);
      } else if (this.TypeJudgment.isLine(element)) {
        const raw_line = newRawInitModel("line");
        raw_line.font_id = this.handleFontMapData(element.font);
        this.objectPropertyAssignmen(element, raw_line);
        rawField
          ? rawField.children.push(raw_line)
          : rawParagraph.children.push(raw_line);
      } else if (this.TypeJudgment.isButton(element)) {
        const raw_line = newRawInitModel("button");
        raw_line.font_id = this.handleFontMapData(element.font);
        this.objectPropertyAssignmen(element, raw_line);
        rawField
          ? rawField.children.push(raw_line)
          : rawParagraph.children.push(raw_line);
      } else {
        const raw_image = newRawInitModel("image");
        raw_image.font_id = this.handleFontMapData(element.font);
        /*
                 设置原始图片属性
                 */
        this.objectPropertyAssignmen(element, raw_image);
        if (extraInfo.isFullConvert) {
          const imageId = this.handleImageMapData(element.src);
          raw_image.src = imageId;
        } else {
          raw_image.src = element.src;
        }
        rawField
          ? rawField.children.push(raw_image)
          : rawParagraph.children.push(raw_image);
      }
      // 如果该段落最后一个字符仍然有fieldId，则说明是换行符有这个文本域id,则表示这个是一个跨多段的文本域，下一段中一直到结束边框前都是该文本域的子节点
      if (!elementNext) {
        return elementFieldID;
      }
    }
  },
  /**
     * 选区数据转换为原始数据
     * @param editor
     */
  selectionData2RawData (editor: Editor, selectionData: (Paragraph | Table)[] | undefined) {
    if (selectionData) {
      const newRootCell = new Cell(editor, [0, 0], 1, 1, null, "trans");
      newRootCell.paragraph = selectionData;
      const rawData = this.cellDataToRawData(newRootCell);
      this.clearInitData();
      return rawData;
    }
  },
  /**
     * 模型数据对象向原始数据对象的属性赋值
     * @param modelObj modelData
     * @param rawData
     */
  objectPropertyAssignmen (modelObj: any, rawData: any) {
    let propKeyArr;
    if (this.TypeJudgment.isParagraph(modelObj)) {
      propKeyArr = ["id", "align", "vertical_align", "level", "isOrder", "islist", "dispersed_align", "indentation", "before_paragraph_spacing",
        "row_ratio", "page_break", "title_length", "content_padding_left", "restart_list_index", "listNumStyle", "listItemIndent", "itemsWidth"];
    } else if (this.TypeJudgment.isTable(modelObj)) {
      propKeyArr = ["id", "meta", "recordWidth", "recordHeight", "maxTableHeight", "col_size", "row_size", "min_row_size", "notAllowDrawLine", "name", "is_fixed_table_header", "fixed_table_header_num", "editableInFormMode", "rowLineType", "tableFiexedStyle", "newPage", "skipMode", "fullPage"];
      if (this.TypeJudgment.isImageTable(modelObj)) { // 转换 ImageTable 特有的属性，普通的 Table 上就不存了
        rawData.imageList = modelObj.imageList;
        rawData.isImageTable = true;
        rawData.needSerialNum = modelObj.needSerialNum;
      }
    } else if (this.TypeJudgment.isGroup(modelObj)) {
      propKeyArr = ["id", "name", "date", "lock", "is_form", "content_para_id", "header_info", "replace", "page_break", "new_page", "meta"];
    } else if (this.TypeJudgment.isWidget(modelObj)) {
      propKeyArr = ["id", "selected", "height", "disabled", "widgetType", "field_id", "field_position", "border", "selectNum", "params"];
    } else if (this.TypeJudgment.isCell(modelObj)) {
      propKeyArr = ["id", "style", "noWrap", "colspan", "rowspan", "is_show_slash_down", "is_show_slash_up", "rowLineType", "aggregationMode", "is_show_line_top", "is_show_line_right", "is_show_line_bottom", "is_show_line_left", "lock", "padding_left", "padding_right", "padding_top", "padding_bottom", "set_cell_height", "fixedHeight", "meta", "groupKey"];
      // 处理原始数据与实例中属性不一致的情况
      rawData.pos = modelObj.position;
    } else if (this.TypeJudgment.isField(modelObj)) {
      // 这里去掉 style 的赋值 不在 rawData 中保存
      propKeyArr = ["id", "name", "placeholder", "tip", "start_symbol", "end_symbol", "display_type", "show_symbol", "deletable", "canBeCopied", "replaceRule", "readonly", "show_format", "show_field", "cascade_list", "automation_list", "replace_format", "number_format", "source_id",
        "source_list", "meta", "active_type", "multi_select", "formula", "formula_value", "separator", "inputMode", "valid", "valid_content", "forbidden", "box_checked", "required", "box_multi", "max_width", "min_width", "maxHeight", "align", "defaultValue", "position"];
      // 处理原始数据与实例中属性不一致的情况
      rawData.field_type = modelObj.type;
    } else if (this.TypeJudgment.isLine(modelObj)) {
      propKeyArr = ["height", "width", "line_height", "form", "color", "field_id", "field_position"];
    } else if (this.TypeJudgment.isBox(modelObj)) {
      propKeyArr = ["height", "name", "content", "field_id", "field_position", "meta"];
    } else if (this.TypeJudgment.isButton(modelObj)) {
      propKeyArr = ["height", "width", "color", "value", "field_id", "field_position", "meta"];
    } else {
      // 图片 "src",
      propKeyArr = ["width", "height", "ori_width", "ori_height", "meta", "field_id", "field_position", "id", "url"];
    }
    for (let i = 0; i < propKeyArr.length; i++) {
      const key = propKeyArr[i];
      if (modelObj[key] === undefined) {
        continue;
      }
      rawData[key] = modelObj[key];
    }
  }
};
