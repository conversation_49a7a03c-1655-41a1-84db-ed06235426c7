const tableBtnMixIn = {
  data() {
    return {
      showTableChoice: false,
      rowNum: 0,
      colNum: 0,
      position: {
        left: 0,
        top: 0,
      },
      tableBtn: [
        {
          type: "textIcon",
          icon: "icon-biaoge",
          title: "插入表格",
          func: this.handleInsertTableBtnClick,
        },
        {
          type: "textIcon",
          icon: "icon-shanchubiaoge",
          title: "删除表格",
          func: this.deleteTheTable,
        },
        {
          type: "textIcon",
          icon: "icon-shanchuhang1",
          title: "删除行",
          func: this.deleteTheRow,
        },
        {
          type: "textIcon",
          icon: "icon-shanchulie",
          title: "删除列",
          func: this.deleteTheCol,
        },
        {
          type: "textIcon",
          icon: "icon-insertrowabove",
          title: "上方插入行",
          func: () => {
            this.addRowOrColInTbl(this.instance.builtInVariable.Direction.up);
          },
        },
        {
          type: "textIcon",
          icon: "icon-insertrowbelow",
          title: "下方插入行",
          func: () => {
            this.addRowOrColInTbl(this.instance.builtInVariable.Direction.down);
          },
        },
        {
          type: "textIcon",
          icon: "icon-insertrowleft",
          title: "左侧插入列",
          func: () => {
            this.addRowOrColInTbl(this.instance.builtInVariable.Direction.left);
          },
        },
        {
          type: "textIcon",
          icon: "icon-insertrowright",
          title: "右侧插入列",
          func: () => {
            this.addRowOrColInTbl(
              this.instance.builtInVariable.Direction.right
            );
          },
        },
      ],
    };
  },
  created() {},
  methods: {
    // 点击插入表格的按钮
    handleInsertTableBtnClick(e) {
      this.showTableChoice = !this.showTableChoice;
      if (this.showTableChoice) {
        const btn = this.$refs.headerList.$el;
        const btnPosition = btn.getBoundingClientRect();
        this.position.top = btnPosition.top + btnPosition.height + 5 + "px";
        this.position.left = btnPosition.left + btnPosition.width - 50 + "px";
      } else {
        this.rowNum = this.colNum = 0;
      }
    },
    // 删除表格
    deleteTheTable() {
      const { editor } = this.instance;
      editor.deleteTbl();
    },
    // 删除行
    deleteTheRow() {
      const { editor } = this.instance;
      editor.deleteRowFromTbl();
      editor.focus();
    },
    // 删除列
    deleteTheCol() {
      const { editor } = this.instance;
      editor.deleteColFromTbl();
    },
    // 插入行和列
    addRowOrColInTbl(direction) {
      const { editor } = this.instance;
      editor.addRowOrColInTbl(direction);
    },
    // 拆分单元格
    splitCell() {
      const { editor } = this.instance;
      if (this.instance.localTest.useLocal) {
        const element = editor.focusElement;
        if (element.table) {
          this.instance.showSplitCellModal();
        }
      } else {
        if (editor.judgeIsCanSplitCell()) {
          editor.splitCell();
        } else {
          const element = editor.focusElement;
          if (element.table) {
            this.instance.showSplitCellModal();
          }
        }
      }
    },
    // 合并单元格
    mergeCell() {
      const { editor } = this.instance;
      editor.mergeCell();
    },
    // 插入表格
    confirmInsertTable() {
      this.instance.editor.insertTable(this.rowNum, this.colNum);
      this.rowNum = this.colNum = 0;
      this.showTableChoice = false;
    },
    mouseMove(e) {
      const left = this.position.left;
      const top = this.position.top;
      const leftDistance = e.clientX - parseFloat(left) - 20; // 减20 是减去的padding值
      const topDistance = e.clientY - parseFloat(top) - 40; // 减40 也是减去的上边的padding
      const rowNum = topDistance <= 0 ? 0 : parseInt(topDistance / 22) + 1; // 22 是修改背景色的小方块的宽度 加上两边的margin
      const colNum = leftDistance <= 0 ? 0 : parseInt(leftDistance / 22) + 1;
      this.rowNum = rowNum > 10 ? 10 : rowNum; // 定义的是10行10列的表格 所以不能大于10 要跟显示的数量一致
      this.colNum = colNum > 10 ? 10 : colNum;
    },
    isShowBgcClass(i) {
      // 10 是每行的列数
      return (
        parseInt((i - 1) / 10) + 1 <= this.rowNum &&
        i - (parseInt((i - 1) / 10) + 1 - 1) * 10 <= this.colNum
      );
    },
    dealTableLine(data) {
      if (
        data === "inside_inclined_top_line2" ||
        data === "inside_inclined_bottom_line2"
      ) {
        data = data.replace("2", "");
        this.instance.editor.focus();
        this.instance.editor.cotrolTableLine(data, 2);
      } else {
        this.instance.editor.focus();
        this.instance.editor.cotrolTableLine(data);
      }
    },
  },
};
export default tableBtnMixIn;
