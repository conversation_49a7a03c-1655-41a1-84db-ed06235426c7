<template>
  <div
    ref="floatDrag"
    class="editor-shape-position"
    :style="{ left: initInfoUse.x + 'px', top: initInfoUse.y + 'px' }"
  >
    <div
      class="editor-shape-position-drag"
      :class="{ dragging: isDragging }"
      @mousedown.stop.prevent="startDrag"
      @mouseup.stop.prevent="stopDrag"
      @mousemove.stop.prevent="dragging"
    ></div>
    <div class="editor-shape-position-content" ref="shapeContent">
      <div
        v-for="(item, index) in shapeList"
        :key="index"
        :class="
          shapeIndex === index &&
          (shapeIndex === 0 ||
            shapeIndex === 1 ||
            shapeIndex === 2 ||
            shapeIndex === 3 ||
            shapeIndex === 4 ||
            shapeIndex === 5)
            ? 'clickShape'
            : 'shape-border'
        "
        :title="item.name"
        @click="clickShape(index)"
      >
        <icon-common :icon-style="customIconStyle" :icon="item.icon" />
      </div>
    </div>
  </div>
</template>

<script>
import iconCommon from "./common/iconCommon.vue";
export default {
  name: "shapeFloating",
  components: { iconCommon },
  data() {
    return {
      isDragging: false,
      startX: 800,
      startY: 110,
      maxX: window.innerWidth - 35,
      maxY: window.innerHeight - 35,
      shapeIndex: 10000,
      flag: true, // 控制悬浮框是否展开
      box: "", // 悬浮球的dom
      initInfoUse: {},
      customIconStyle: {
        width: "20px",
        height: "15px",
        fill: "rgba(0, 0, 0, 0.8)",
      },
      shapeList: [
        { name: "插入线", icon: "icon-xianduan-copy" },
        { name: "插入椭圆", icon: "icon-ellipse" },
        { name: "插入叉", icon: "icon-close" },
        { name: "插入矩形", icon: "icon-xingzhuang-juxing" },
        { name: "插入折线", icon: "icon-icon-line-graph" },
        { name: "继续绘制", icon: "icon-shibaichujixu-copy-copy" },
        { name: "撤销", icon: "icon-chexiao" },
        { name: "重做", icon: "icon-huifu" },
        { name: "删除", icon: "icon-shanchu" },
        { name: "退出", icon: "icon-tuichu" },
      ],
    };
  },
  props: {
    menuCount: {
      type: Number,
      default: 1,
    },
    initInfo: {
      type: Object,
      default: () => {
        return {
          x: 0,
          y: 0,
        };
      },
    },
  },
  mounted() {
    this.initInfoUse = this.initInfo;
    window.addEventListener("mouseup", this.stopDrag);
    this.box = this.$refs.floatDrag;
  },
  beforeDestroy() {
    window.removeEventListener("mouseup", this.stopDrag);
  },
  methods: {
    clickShape(val) {
      const shapes = this.editor.editor.shapes;
      const shape = shapes.find((shape) => shape.type === "fold_line");
      if (shape) {
        this.$refs.shapeContent.childNodes[5].style.display = "block";
      } else {
        this.$refs.shapeContent.childNodes[5].style.display = "none";
      }
      if (
        (this.shapeIndex === 0 ||
          this.shapeIndex === 1 ||
          this.shapeIndex === 2 ||
          this.shapeIndex === 3 ||
          this.shapeIndex === 4 ||
          this.shapeIndex === 5) &&
        this.shapeIndex === val
      ) {
        this.shapeIndex = 10000;
      } else {
        this.shapeIndex = val;
      }

      switch (this.shapeIndex) {
        case 0:
          this.editor.editor.drawShape("line");
          break;

        case 1:
          this.editor.editor.drawShape("circle");
          break;
        case 2:
          this.editor.editor.drawShape("cross");
          break;
        case 3:
          this.editor.editor.drawShape("rect");
          break;
        case 4:
          this.editor.editor.drawShape("fold_line");
          break;
        case 5:
          this.editor.editor.drawShape("continue_line");
          break;
        case 6:
          this.editor.editor.drawShape("close");
          this.editor.history.undo();
          break;
        case 7:
          this.editor.editor.drawShape("close");
          this.editor.history.redo();
          break;
        case 8:
          this.editor.editor.drawShape("close");
          this.deleteShape();
          break;
        case 9:
          this.$emit("shapeMode");

          break;
        case 10000:
          this.editor.editor.drawShape("close");

          break;
        default:
          break;
      }
    },
    startDrag(event) {
      this.isDragging = true;
      try {
        this.startX = event.clientX || event.touches[0].clientX;
        this.startY = event.clientY || event.touches[0].clientY;
        document.addEventListener("mousemove", this.dragging);
        this.$emit("startDrag", event, this.initInfoUse);
        // eslint-disable-next-line no-empty
      } catch (e) {}
    },
    deleteShape() {
      const shape = this.editor.editor.internal.focus_shape;
      if (shape.is_editor) {
        this.editor.editor.delete_shape();
        shape.is_editor = false;
      }
    },
    dragging(event) {
      if (this.isDragging) {
        event.preventDefault();
        try {
          const currentX = event.clientX || event.touches[0].clientX;
          const currentY = event.clientY || event.touches[0].clientY;
          const diffX = currentX - this.startX;
          const diffY = currentY - this.startY;
          const newX = this.initInfo.x + diffX;
          const newY = this.initInfo.y + diffY;
          this.initInfoUse.x = Math.max(20, Math.min(newX, this.maxX));
          this.initInfoUse.y = Math.max(20, Math.min(newY, this.maxY));

          // if (this.initInfo) {
          //   this.initInfo.x = Math.max(20, Math.min(newX, this.maxX));
          //   this.initInfo.y = Math.max(20, Math.min(newY, this.maxY));
          // }
          // this.initInfo = {
          //   x: Math.max(20, Math.min(newX, this.maxX)),
          //   y: Math.max(20, Math.min(newY, this.maxY)),
          // };
          this.startX = currentX;
          this.startY = currentY;
          // eslint-disable-next-line no-empty
        } catch (e) {}
      }
    },
    stopDrag() {
      this.isDragging = false;
      document.removeEventListener("mousemove", this.dragging);
    },

    // 获取要改变得样式属性
    getStyleAttr(obj, attr) {
      if (obj.currentStyle) {
        // IE 和 opera
        return obj.currentStyle[attr];
      } else {
        return window.getComputedStyle(obj, null)[attr];
      }
    },
  },
};
</script>
<style lang="less" scoped>
.editor-shape-position {
  position: fixed;
  z-index: 10003 !important;
  justify-content: center;
  left: 10%;
  top: 20%;
  width: 35px;
  border-radius: 32px;
  overflow: hidden;
  user-select: none;
  font-size: 12px;
  display: block;
  background: #9b9b9b;
  margin: 0;
  opacity: 1;
  color: rgb(108, 108, 108);
  box-shadow: 0 0 0 3px rgba(52, 52, 52, 0.1);
  transform: translateZ(0) rotateX(10deg);
  background: white;

  .editor-shape-position-drag {
    height: 16px;
    cursor: grab;
    text-align: center;
    line-height: 15px;
    border-bottom: 1px solid #fff;
  }

  .editor-shape-position-drag:hover,
  .editor-shape-position-drag:focus {
    outline: none;
  }

  .editor-shape-position-drag:active {
    cursor: grabbing;
    background-color: rgb(236, 236, 236);
  }

  .editor-shape-position-content {
    height: 100%;
    margin-bottom: 15px;
    text-align: center;
    cursor: pointer;
  }

  .shape-border {
    border: 2px solid rgb(236, 236, 236);
    background-color: white;
    margin: 0 auto;
    height: 30px;
    padding-top: 4px;
    cursor: pointer;
  }
  .shape-border:hover {
    border: 2px solid rgb(168, 168, 168);
    background-color: rgb(236, 236, 236);
    margin: 0 auto;
    padding-top: 4px;
    cursor: pointer;
  }

  .clickShape {
    border: 2px solid rgb(168, 168, 168);
    background-color: rgb(236, 236, 236);
    height: 30px;
    padding-top: 4px;
    cursor: pointer;
  }

  .close {
    width: 20px;
    height: 15px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    background: #9b9b9b;
    position: absolute;
    right: -10px;
    top: -12px;
    cursor: pointer;
  }

  .cart {
    border-radius: 50%;
    width: 5em;
    height: 5em;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .header-notice {
    display: inline-block;
    transition: all 0.3s;

    span {
      vertical-align: initial;
    }

    .notice-badge {
      color: inherit;

      .header-notice-icon {
        font-size: 16px;
        padding: 4px;
      }
    }
  }

  &.active {
    background-color: #9b9b9b !important;

    .active-des {
      color: #1a1818 !important;
      font-weight: bold !important;
    }
  }

  .drag-ball {
    .drag-content {
      overflow-wrap: break-word;
      font-size: 14px;
      color: #fff;
      letter-spacing: 2px;
    }
  }
}

.editor-popover-class .ant-popover-inner {
  opacity: 0.9;
  border-radius: 10px;
  background: #ececec;
}

.editor-popover-class .ant-popover-inner-content {
  padding: 5px 2px 2px 5px;
}

.editor-popover-content {
  text-align: center;

  .inner-title {
    font-size: 13px;
    font-weight: 600;
    user-select: none;
  }

  .inner-icon {
    background: white;
    width: 80px;

    .ant-divider {
      margin: 0;
    }

    .ant-divider-inner-text {
      font-size: 10px;
      padding: 0;
      user-select: none;
    }

    .icon-menu-item {
      cursor: pointer;
      padding: 2px 5px 2px 5px;
      font-size: 16px;
      width: 30px;
      height: 30px;
    }

    .icon-menu-item:hover {
      background-color: #eeeeee;
      border-radius: 10px;
    }

    .icon-menu-item:active {
      background-color: #e2e2e2;
      border-radius: 10px;
    }
  }

  .list-item {
    padding: 2px 5px;
    border-radius: 5px;
    background: white;
    white-space: pre-wrap;
    text-align: left;
    white-space: pre-wrap;
  }

  .list-item-title {
    font-size: 12px;
    font-weight: bold;
    color: #333;
    text-align: left;
    user-select: none;
  }

  .list-item-content {
    font-size: 12px;
    line-height: 1.5;
    color: #666;
  }

  .menu {
    background: white;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
  }

  .menu-item {
    display: inline-flex;
    align-items: center;
    padding: 3px 5px;
    color: #333;
    text-decoration: none;
    user-select: none;

    .menu-icon {
      margin: 3px 2px 0px 0px;
      font-size: 12px;
    }
  }

  .menu-item:hover {
    background-color: #eeeeee;
    border-radius: 10px;
  }

  .menu-item:active {
    background-color: #e2e2e2;
    border-radius: 10px;
  }
}

/*.editor-popover-class .ant-popover-arrow {*/
/*  border-top-color: #f0f0f0 !important;*/
/*  border-right-color: #f0f0f0 !important;*/
/*  border-bottom-color: #f0f0f0 !important;*/
/*  border-left-color: #f0f0f0 !important;*/
/*}*/
</style>
