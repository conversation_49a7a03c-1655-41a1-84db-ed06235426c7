import { deleteUndefinedProperties } from "../assets/js/utils";

const MsunEditor_WR = {
  WRZipSource: ['":"', '","', '"},{"', '":{"', '},{"', '":', ',"', "false"],
  WRZipDest: "!$%&@^`~",
  WRAsciiMap_R:
    "r<~VH6SAZ?yiO %1#Ek>]`+|F_t/$RgQnX=YbqL)7uU9,oaB5}-'ev4;JD.2WsP@3zhl\"cfC^(K0mx!&Td{*GM:[pIj8\\Nw",
  propNameMap: {
    AlternatingBackColor: "G",
    BackColor: "Q",
    BarcodeType: "b",
    BorderCustom: "i",
    CharSpacing: "x",
    DBFieldName: "WA",
    FreeCell: "AB",
    GroupLabel: "OB",
    GroupTitle: "PB",
    Height: "ZB",
    IsCrossTab: "jB",
    LeftMargin: "tB",
    LnSpacing: "JC",
    Name: "YC",
    Oriention: "eC",
    RightMargin: "qD",
    SeriesLabel: "HE",
    Type: "LF",
    Value: "UF",
    Width: "eF",
  },
  propNameMap_R: {
    A: "AdjustRowHeight",
    B: "AlignColumn",
    C: "AlignColumnEx",
    D: "AlignColumnSide",
    E: "AlignToGrid",
    F: "Alignment",
    G: "AlternatingBackColor",
    H: "AlwaysShowHScrollBar",
    I: "AlwaysShowVScrollBar",
    J: "Anchor",
    K: "AppendBlankCol",
    L: "AppendBlankColWidth",
    M: "AppendBlankRow",
    N: "AppendBlankRowAtLast",
    O: "AppendBlankRowExclude",
    P: "Author",
    Q: "BackColor",
    R: "BackImage",
    S: "BackImageFile",
    T: "BackImagePreview",
    U: "BackImagePrint",
    V: "BackStyle",
    W: "BackgroundColor",
    X: "BarRatio",
    Y: "BarSingleSeriesColor",
    Z: "BarWidth",
    a: "BarWidthPercent",
    b: "BarcodeType",
    c: "BeforePostRecordScript",
    d: "BeforeSortScript",
    e: "BeginDateParameter",
    f: "Bold",
    g: "BookmarkText",
    h: "BorderColor",
    i: "BorderCustom",
    j: "BorderPrintType",
    k: "BorderStyles",
    l: "BorderWidth",
    m: "BottomMargin",
    n: "BubbleScalePerCm",
    o: "ByFields",
    p: "ByY2Axis",
    q: "CanGrow",
    r: "CanShrink",
    s: "CaptionAlignment",
    t: "CaptionPosition",
    u: "Center",
    v: "CenterView",
    w: "CenterWithDetailGrid",
    x: "CharSpacing",
    y: "CharacterSpacing",
    z: "Charset",
    AA: "Chart3D",
    BA: "ChartType",
    CA: "CheckSum",
    DA: "CodePage",
    EA: "ColLineColor",
    FA: "ColLineWidth",
    GA: "ColSpan",
    HA: "Color",
    IA: "Column",
    JA: "ColumnCount",
    KA: "ColumnMove",
    LA: "ColumnResize",
    MA: "ConditionScript",
    NA: "ConnectionString",
    OA: "ContinuePrint",
    PA: "CoordLineColor",
    QA: "CoordLineVisible",
    RA: "CornerDx",
    SA: "CornerDy",
    TA: "Cursor",
    UA: "CustomDraw",
    VA: "CustomDrawScript",
    WA: "DBFieldName",
    XA: "DataField",
    YA: "DataName",
    ZA: "DataType",
    aA: "Description",
    bA: "Direction",
    cA: "DisabledSumFields",
    dA: "DisplayField",
    eA: "Dock",
    fA: "DtmxEncoding",
    gA: "DtmxMatrixSize",
    hA: "DtmxModuleSize",
    iA: "Editable",
    jA: "EndDateParameter",
    kA: "EndEllipsis",
    lA: "ExportBeginScript",
    mA: "ExportEndScript",
    nA: "FalseText",
    oA: "FetchRecordScript",
    pA: "FillColor",
    qA: "FillColorAuto",
    rA: "FillStyle",
    sA: "FirstCharIndent",
    tA: "FixCols",
    uA: "FixedWidth",
    vA: "FlowTo",
    wA: "FontWidthRatio",
    xA: "ForeColor",
    yA: "Format",
    zA: "FormatScript",
    AB: "FreeCell",
    BB: "GetDisplayTextScript",
    CB: "GlobalScript",
    DB: "GridColsPerUnit",
    EB: "GridLinePrintType",
    FB: "GridRowsPerUnit",
    GB: "GroupAuto",
    HB: "GroupAutoSum",
    IB: "GroupBeginScript",
    JB: "GroupCount",
    KB: "GroupEndScript",
    LB: "GroupField",
    MB: "GroupIndex",
    NB: "GroupKeepTogether",
    OB: "GroupLabel",
    PB: "GroupTitle",
    QB: "GrowToBottom",
    RB: "GrowToNextRow",
    SB: "HCrossFields",
    TB: "HCrossPeriodType",
    UB: "HNewPageFixed",
    VB: "HPercentColumns",
    WB: "HResort",
    XB: "HSortAsc",
    YB: "HTotalAtFirst",
    ZB: "Height",
    aB: "HideOnRecordsetEmpty",
    bB: "HtmlTags",
    cB: "Image",
    dB: "ImageFile",
    eB: "ImageIndex",
    fB: "IncludeFooter",
    gB: "InitializeScript",
    hB: "InnerIndent",
    iB: "InnerStyles",
    jB: "IsCrossTab",
    kB: "Italic",
    lB: "KeepTogether",
    mB: "L2R",
    nB: "Label",
    oB: "LabelAsGroup",
    pB: "LabelInBar",
    qB: "LabelText",
    rB: "LabelTextAngle",
    sB: "Left",
    tB: "LeftMargin",
    uB: "LegendAtBottom",
    vB: "LegendColumnCount",
    wB: "LegendCursor",
    xB: "LegendShowSum",
    yB: "LegendSumLabel",
    zB: "LegendValueVisible",
    AC: "LegendVisible",
    BC: "Length",
    CC: "LimitsPerPage",
    DC: "LineColor",
    EC: "LineSpacing",
    FC: "LineType",
    GC: "LineVisible",
    HC: "LineWeight",
    IC: "ListCols",
    JC: "LnSpacing",
    KC: "Lock",
    LC: "Locked",
    MC: "MarginBeginWeight",
    NC: "MarginEndWeight",
    OC: "MarginGap",
    PC: "MarkerColor",
    QC: "MarkerColorAuto",
    RC: "MarkerLegendShow",
    SC: "MarkerSize",
    TC: "MarkerStyle",
    UC: "Max",
    VC: "Min",
    WC: "MirrorMargins",
    XC: "MonoPrint",
    YC: "Name",
    ZC: "NegativeAsZero",
    aC: "NewPage",
    bC: "NewPageColumn",
    cC: "OccupiedColumns",
    dC: "OccupyColumn",
    eC: "Oriention",
    fC: "PDF417Columns",
    gC: "PDF417ErrorLevel",
    hC: "PDF417Rows",
    iC: "PDF417Simple",
    jC: "PaddingBottom",
    kC: "PaddingLeft",
    lC: "PaddingRight",
    mC: "PaddingTop",
    nC: "PageColumnCount",
    oC: "PageColumnDirection",
    pC: "PageColumnGroupNA",
    qC: "PageColumnSpacing",
    rC: "PageDivideCount",
    sC: "PageDivideLine",
    tC: "PageDivideSpacing",
    uC: "PageEndScript",
    vC: "PageGroup",
    wC: "PageProcessRecordScript",
    xC: "PageStartScript",
    yC: "ParagraphSpacing",
    zC: "Parameter",
    AD: "ParentPageSettings",
    BD: "PenColor",
    CD: "PenStyle",
    DD: "PenWidth",
    ED: "PercentFormat",
    FD: "Picture",
    GD: "Pitch",
    HD: "PointWeight",
    ID: "PrintAdaptFitText",
    JD: "PrintAdaptMethod",
    KD: "PrintAdaptRFCStep",
    LD: "PrintAdaptRepeat",
    MD: "PrintAdaptTryToOnePage",
    ND: "PrintAsDesignPaper",
    OD: "PrintAtBottom",
    PD: "PrintBeginScript",
    QD: "PrintEndScript",
    RD: "PrintGridBorder",
    SD: "PrintOffsetSaveToLocal",
    TD: "PrintOffsetX",
    UD: "PrintOffsetY",
    VD: "PrintPageScript",
    WD: "PrintRotation",
    XD: "PrintToStretch",
    YD: "PrintType",
    ZD: "PrinterName",
    aD: "ProcessBeginScript",
    bD: "ProcessEndScript",
    cD: "ProcessRecordScript",
    dD: "QRCodeErrorLevel",
    eD: "QRCodeMask",
    fD: "QRCodeVersion",
    gD: "QuerySQL",
    hD: "RTF",
    iD: "RTrimBlankChars",
    jD: "RankNo",
    kD: "RegisterNo",
    lD: "RelationFields",
    mD: "RepeatOnPage",
    nD: "RepeatStyle",
    oD: "ReportFile",
    pD: "ResetPageNumber",
    qD: "RightMargin",
    rD: "RotateMode",
    sD: "RowCount",
    tD: "RowLineColor",
    uD: "RowLineWidth",
    vD: "RowSelection",
    wD: "RowSpan",
    xD: "RowsIncludeGroup",
    yD: "RowsPerPage",
    zD: "SameAsColumn",
    AE: "ScriptType",
    BE: "SelectionBackColor",
    CE: "SelectionForeColor",
    DE: "SeriesAuto",
    EE: "SeriesCount",
    FE: "SeriesCursor",
    GE: "SeriesField",
    HE: "SeriesLabel",
    IE: "SeriesName",
    JE: "Shadow",
    KE: "ShadowColor",
    LE: "ShadowWidth",
    ME: "ShapeType",
    NE: "SharePrintSetupOptions",
    OE: "ShiftMode",
    PE: "ShowColLine",
    QE: "ShowGrid",
    RE: "ShowMoneyDigit",
    SE: "ShowMoneyLabel",
    TE: "ShowMoneyLine",
    UE: "ShowMoneyLineColor",
    VE: "ShowMoneySepLineColor",
    WE: "ShowMoneyWidth",
    XE: "ShowPreviewWndScript",
    YE: "ShowRowLine",
    ZE: "ShrinkFontToFit",
    aE: "SingleSeriesColored",
    bE: "Size",
    cE: "SizeMode",
    dE: "SkipQuery",
    eE: "SortAsc",
    fE: "SortCaseSensitive",
    gE: "SortFields",
    hE: "SortSummaryBox",
    iE: "Space",
    jE: "SpanToNewPage",
    kE: "Strikethrough",
    lE: "Style",
    mE: "Styles",
    nE: "SubtotalCols",
    oE: "SummaryFun",
    pE: "SystemVar",
    qE: "Tag",
    rE: "Text",
    sE: "TextAlign",
    tE: "TextAngle",
    uE: "TextFormat",
    vE: "TextOrientation",
    wE: "TextVisible",
    xE: "Title",
    yE: "TitleRepeat",
    zE: "TitleRows",
    AF: "ToNewExcelSheet",
    BF: "TooltipText",
    CF: "Top",
    DF: "TopMargin",
    EF: "TotalCols",
    FF: "TotalExcludeColumns",
    GF: "TotalHPercentColumns",
    HF: "TotalVPercentColumns",
    IF: "Transparent",
    JF: "TransparentMode",
    KF: "TrueText",
    LF: "Type",
    MF: "U2D",
    NF: "Underline",
    OF: "Unit",
    PF: "VAlign",
    QF: "VCrossFields",
    RF: "VPercentColumns",
    SF: "VResort",
    TF: "VSortAsc",
    UF: "Value",
    VF: "ValueAsPercent",
    WF: "ValueFormat",
    XF: "ValueVisible",
    YF: "Version",
    ZF: "Visible",
    aF: "Watermark",
    bF: "WatermarkAlignment",
    cF: "WatermarkSizeMode",
    dF: "Weight",
    eF: "Width",
    fF: "WordWrap",
    gF: "XAxisLabel",
    hF: "XAxisMaximum",
    iF: "XAxisMinimum",
    jF: "XAxisSpace",
    kF: "XAxisTextAngle",
    lF: "XAxisTextFormat",
    mF: "XAxisTextVisible",
    nF: "XAxisVisible",
    oF: "XValueField",
    pF: "XmlTableName",
    qF: "YAxisLabel",
    rF: "YAxisMaximum",
    sF: "YAxisMinimum",
    tF: "YAxisSpace",
    uF: "YAxisTextFormat",
    vF: "YAxisTextVisible",
    wF: "YAxisVisible",
    xF: "YValueField",
    yF: "ZValueField",
    zF: "ZValueFormat",
  },
  wrPropNameEncode: function (e) {
    var t = MsunEditor_WR.propNameMap[e];
    return t ? t : e;
  },
  wrPropNameDecode: function (e) {
    var t = MsunEditor_WR.propNameMap_R[e];
    return t ? t : e;
  },
  decodeWR: function (e) {
    function t(e) {
      return e >= " " && "~" >= e && (e = d[e.charCodeAt(0) - 32]), e;
    }
    for (
      var r,
        o,
        n,
        i,
        a,
        l = "",
        s = 4,
        c = e.length,
        u = MsunEditor_WR,
        d = u.WRAsciiMap_R,
        h = u.WRZipSource,
        f = u.WRZipDest,
        p = [];
      c > s;

    )
      l += t(e[s++]);
    for (s = 0, c = 0, i = l[s]; "[" == i; ) {
      for (
        a = {}, r = ++s;
        "," != i && ((i = l[++s]), !("0" > i || i > "9"));

      );
      if ("," != i) break;
      for (
        a.f = +l.substring(r, s), r = ++s;
        "]" != i && ((i = l[++s]), !("0" > i || i > "9"));

      );
      if ("]" != i) break;
      (a.s = +l.substring(r, s++)), p.push(a), (c = s), (i = l[s]);
    }
    for (e = l.substr(c).split(""), a = p.length; a-- > 0; )
      (o = p[a].f), (n = p[a].s), (i = e[o]), (e[o] = e[n]), (e[n] = i);
    for (c = e.length, l = "", s = 0; c > s; s++)
      (i = e[s]),
        (a = f.indexOf(i)),
        a >= 0 ? (l += h[a]) : ("#" == i && (i = e[++s]), (l += i));
    return l;
  },
};

/** 压缩后的grf转为json */
function unzipGrf(grf) {
  return MsunEditor_WR.decodeWR(grf);
}

/** 恢复json中混淆的属性名 */
function restorePropName(grfObj) {
  if (Array.isArray(grfObj)) {
    return grfObj.map(restorePropName);
  }

  let newObj = {};
  for (let key in grfObj) {
    let newKey = MsunEditor_WR.wrPropNameDecode(key);
    newObj[newKey] = grfObj[key];
    if (newObj[newKey] && typeof newObj[newKey] === "object") {
      newObj[newKey] = restorePropName(grfObj[key]);
    }
  }
  return newObj;
}

const trans = {
  data() {
    return {
      tolerance: 7, //分行时的精度值，默认7
      documentInfo: {},
      defaultFontStyle: {
        family: "宋体",
        fontSize: 10.5,
      },
      calcCoefficient: 37.875,
    };
  },
  methods: {
    initParam() {
      this.documentInfo = {};
    },
    decodeGrf(grf) {
      if (grf.startsWith("_WR_")) {
        const unzipedGrf = unzipGrf(grf);
        const grfObj = JSON.parse(unzipedGrf);
        const restoredGrfObj = restorePropName(grfObj);
        return restoredGrfObj;
      } else {
        return JSON.parse(grf);
      }
    },
    parseGrfJson(grfJson) {
      this.editor.config.history_limit = 0;
      this.editor.clearDocument();
      this.initParam();
      // 重点的结构  页眉、正文、页脚
      const {
        ReportHeader,
        DetailGrid,
        PageHeader,
        Font,
        Printer,
        ReportFooter,
      } = grfJson;
      if (Font) {
        if (Font.Name) {
          this.editor.config.default_font_style.family = Font.Name;
        }
        if (Font.Size) {
          this.editor.config.default_font_style.height =
            this.handleFontSize2Height(Font.Size);
        }
      }

      this.handlePageConfig(Printer);
      this.handleControl(PageHeader);
      this.handleControl(ReportHeader);
      if (DetailGrid) {
        const { Recordset, ColumnContent, ColumnTitle, Group } = DetailGrid;
        const { BeforePostRecordScript } = Recordset;
        const { ColumnContentCell, FormatScript } = ColumnContent;
        const { ColumnTitleCell } = ColumnTitle;
        // 脚本信息
        // console.log(BeforePostRecordScript);
        if (BeforePostRecordScript) {
          const funStr = this.convertToJsFunction(BeforePostRecordScript);
          this.editor.document_meta.BeforePostRecordScript = funStr;
          // const convertCode = new Function(funStr + "\nreturn convertCode;")();
          // console.log(
          //   convertCode({
          //     leftOperRoomGoTo: "1,2,3",
          //     beforeAnesPatInfo: "0",
          //   })
          // );
        }
        if (FormatScript) {
          const funStr = this.convertToJsFunction(FormatScript);
          this.editor.document_meta.FormatScript = funStr;
        }
        if (Group) {
          for (let i = 0; i < Group.length; i++) {
            this.handleControl(Group[i].GroupHeader);
            if (i === 0) {
              this.handleControl(ColumnTitleCell);
              this.handleControl(ColumnContentCell);
            }
            this.handleControl(Group[i].GroupFooter);
          }
        } else {
          this.handleControl(ColumnTitleCell);
          this.handleControl(ColumnContentCell);
        }
      }

      this.handleControl(ReportFooter);

      this.editor.config.history_limit = 50;
    },
    handleControl(container) {
      if (container) {
        if (!Array.isArray(container)) {
          container = [].concat(container);
        }
        for (let i = 0; i < container.length; i++) {
          const { Control } = container[i];
          this.handleItems(Control);
        }
      }
    },
    handlePageConfig(Printer) {
      let { LeftMargin, TopMargin, RightMargin, BottomMargin, Oriention } =
        Printer;
      const editor = this.instance.editor;
      const config = editor.config;
      if (LeftMargin) {
        config.page_padding_left = LeftMargin * this.calcCoefficient;
      }
      if (RightMargin) {
        config.page_padding_right = RightMargin * this.calcCoefficient;
      }
      if (TopMargin) {
        config.page_padding_top = 0;
        config.header_margin_top = TopMargin * this.calcCoefficient;
      }
      if (BottomMargin) {
        config.page_padding_bottom = 0;
        config.footer_margin_bottom = BottomMargin * this.calcCoefficient;
      }
      if (Oriention === "Landscape") {
        config.page_direction = "horizontal";
      } else {
        editor.config.page_size_type = "custom";
        editor.config.customPageSize = {
          width: 793 + 100,
          height: 1121 + 300,
        };
      }
      editor.reInitConfig(config);
    },
    convertToJsFunction(script) {
      const lines = script.split("\n");
      const jsLines = [
        "function convertCode(fieldValuePairs) {",
        "    const result = {};",
      ];
      for (const line of lines) {
        let trimmedLine = line.trim();
        if (trimmedLine.startsWith("//")) {
          continue; // 跳过注释
        }
        trimmedLine = trimmedLine.replace(
          /Report\.FieldByName\("(\w+)"\)\.AsString/g,
          'String(fieldValuePairs["$1"])'
        );
        if (
          trimmedLine.includes("ControlByName") &&
          trimmedLine.includes("ImageIndex")
        ) {
          const match = trimmedLine.match(
            /ControlByName\("(\w+)"\)\.ImageIndex\s*=\s*(-?\d+)/
          );
          if (match) {
            const controlName = match[1];
            const imageIndex = match[2];
            trimmedLine = `result["${controlName}"] = { ImageIndex: ${imageIndex} };`;
          }
        }
        if (trimmedLine.includes("ControlByName")) {
          const controlName = trimmedLine.match(/ControlByName\("(\w+)"\)/)[1];
          if (trimmedLine.includes("Visible")) {
            const visibility = trimmedLine.includes("=1") ? "true" : "false";
            trimmedLine = `result["${controlName}"] = { Visible: ${visibility} };`;
          }
        }
        if (trimmedLine.startsWith("for")) {
          trimmedLine = trimmedLine.replace(
            /for\s*\(\s*var\s*(\w+)\s*=\s*0\s*;\s*\1\s*<\s*(\w+)\s*;\s*\1\+\+\s*\)/,
            "for (let $1 = 0; $1 < $2; $1++)"
          );
        }
        if (trimmedLine !== "") {
          jsLines.push(`    ${trimmedLine}`);
        }
      }
      jsLines.push("    return result;");
      jsLines.push("}");
      return jsLines.join("\n");
    },
    handleFontSize2Height(fontSize) {
      let target = this.defaultFontStyle.fontSize;
      if (fontSize) {
        target = fontSize / 10000;
      }
      const config = this.instance.config.getConfig();
      const dict = config.fontSizeConfig;
      const res = dict.find((item) => {
        return item.option === String(target);
      });
      if (res) {
        return res.value;
      }
      target = target * 1.25;
      const arr = config.fontHeightAsc;
      let closest = arr[0]; // 假设第一个元素是最接近的
      let minDiff = Math.abs(arr[0] - target); // 计算第一个元素与目标的差的绝对值
      for (let i = 1; i < arr.length; i++) {
        let diff = Math.abs(arr[i] - target); // 计算当前元素与目标的差的绝对值
        if (diff < minDiff) {
          // 如果当前差的绝对值更小
          minDiff = diff; // 更新最小差的绝对值
          closest = arr[i]; // 更新最接近的值
        }
      }
      return closest;
    },
    handleItems(items) {
      if (!items) return;
      const rows = this.groupByRows(items);
      const { editor } = this.instance;
      for (let i = 0; i < rows.length; i++) {
        const row = rows[i].filter((ele) => {
          const type = ele["Type"];
          if (type === "PictureBox") {
            return !!ele["ImageIndex"];
          } else {
            return type !== "Line";
          }
        });
        row.sort((a, b) => {
          if (!a.Left) {
            a.Left = 0;
          }
          if (!b.Left) {
            b.Left = 0;
          }
          return a.Left - b.Left;
        });
        if (!row.length) {
          continue;
        }
        // 插入表格
        const table = editor.insertTable(1, row.length + 1, {
          opacityOfLines: 0,
        });
        let colWidthArr = [];
        const cells = table.children;
        for (let j = 0; j < row.length; j++) {
          const cell = cells[j + 1];
          // cell.noWrap = true;
          const f = editor.selection.focus;
          editor.selection.setCursorPosition([f[0], j + 1, 0, 0]);
          const item = row[j];
          switch (item["Type"]) {
            case "StaticBox":
              this.handleStaticBox(item);
              break;
            case "MemoBox":
              this.handleMemoBox(item);
              break;
            case "FieldBox":
              this.handleFieldBox(item);
              break;
            case "PictureBox":
              this.handlePictureBox(item);
              break;
            case "FreeGrid":
              this.handleFreeGrid(item);
              break;
            default:
          }
          let sumWidth = colWidthArr.reduce((acc, curr) => acc + curr, 0);
          let colWidth = 3;
          if (item["Left"]) {
            colWidth = Math.ceil(
              item["Left"] * this.calcCoefficient - sumWidth
            );
          }
          colWidthArr.push(colWidth);
        }
        if (colWidthArr.length) {
          colWidthArr.forEach((ele, index) => {
            const cell = cells[index];
            if (index !== 0) {
              if (cell.fields[0].min_width) {
                colWidthArr[index] += 5;
              } else {
                colWidthArr[index] += 10;
              }
            }
          });
          table.setColWidth(colWidthArr);
        }

        editor.selection.setCursorPosition([editor.selection.focus[0] + 1, 0]);
        editor.insertField({ type: "anchor" });
        editor.refreshDocument(true);
      }
    },
    groupByRows(jsonArray) {
      // 首先按Top值对对象进行排序
      jsonArray.sort((a, b) => {
        if (!a.Top) {
          a.Top = 0;
        }
        if (!b.Top) {
          b.Top = 0;
        }
        return a.Top - b.Top;
      });

      // 用来存放结果
      const rows = [];

      // 设置一个阈值，用于判断两个对象是否在同一行（可以根据实际调整）
      const tolerance = this.tolerance;

      // 当前行初始化
      let currentRow = [jsonArray[0]];
      let ccT = this.calcCoefficient;
      // 遍历排序后的数组
      for (let i = 1; i < jsonArray.length; i++) {
        const currentItem = jsonArray[i];
        const previousItem = jsonArray[i - 1];

        // 如果当前元素的Top值与前一个元素接近，认为它们属于同一行
        if (
          Math.abs(currentItem.Top * ccT - previousItem.Top * ccT) <= tolerance
        ) {
          currentRow.push(currentItem);
        } else {
          // 如果当前元素的Top值与前一个元素不接近，开始新的一行
          rows.push(currentRow);
          currentRow = [currentItem];
        }
      }

      // 把最后一行加入到结果中
      if (currentRow.length > 0) {
        rows.push(currentRow);
      }

      return rows;
    },
    handleFreeGrid(freeGrid) {
      const { FreeGridCell, Border, FreeGridColumn, ColumnCount, RowCount } =
        freeGrid;
      const editor = this.editor;
      // 如果是网格，则先删除原表格
      editor.deleteTbl();
      // 根据行列重新插入表格
      const table = editor.insertTable(RowCount, ColumnCount);
      const colWidthArr = FreeGridColumn.map((ele) => {
        return ele.Width * this.calcCoefficient;
      });
      table.setColWidth(colWidthArr);
      for (let i = 0; i < FreeGridCell.length; i++) {
        const item = FreeGridCell[i];
        const { TextAlign, Text, Font } = item;
        const f = editor.selection.focus;
        editor.selection.setCursorPosition([f[0], i, 0, 0]);
        this.handleCellAlign(TextAlign);
        this.splitMutilFieldByText(Text, Font);
      }
    },
    splitMutilFieldByText(Text, Font) {
      const editor = this.editor;
      if (Text) {
        let style = {};
        if (Font) {
          style = {
            bold: Font.Bold,
            family: Font.Name,
            height: this.handleFontSize2Height(Font.Size),
          };
        }
        const result = Text.split(/(\[#.*?#\])/);
        for (let j = 0; j < result.length; j++) {
          if (!result[j]) continue;
          const res = result[j].match(/\[#(.*?)#\]/);
          if (res) {
            const prop = {
              name: res[1],
              placeholder: res[1],
              style,
            };
            this.handleFieldProp(prop);
            editor.insertField(prop);
          } else {
            const field = editor.insertField({ style });
            field.setNewText(result[j]);
            editor.updateFieldText({ fields: [field] });
          }
        }
      }
    },
    handleStaticBox(item) {
      const { editor } = this.instance;
      const { Text, Name, Width, Font, TextAlign } = item;
      let fixedWidth = Math.floor(Width * this.calcCoefficient);
      const prop = {
        type: "normal",
        name: Name,
        // min_width: fixedWidth,
        // max_width: fixedWidth,
      };
      if (Font) {
        prop.style = {
          bold: Font.Bold,
          family: Font.Name,
          height: this.handleFontSize2Height(Font.Size),
        };
      }
      this.handleFieldAlign(TextAlign, prop);
      this.handleCellAlign(TextAlign);
      this.handleFieldProp(prop, fixedWidth);
      const field = editor.insertField(prop);
      field.setNewText(Text);
      editor.updateFieldText({ fields: [field] });
      return field;
    },
    handleFieldAlign(TextAlign, prop) {
      if (TextAlign && TextAlign.indexOf("Center") > -1) {
        prop.align = "center";
      }
      if (TextAlign && TextAlign.indexOf("Right") > -1) {
        prop.align = "right";
      }
    },
    handleCellAlign(TextAlign) {
      const { editor } = this.instance;
      if (TextAlign) {
        if (TextAlign.indexOf("Center") > -1) {
          editor.changeContentAlign("center"); // 修改水平对齐方式
        }
        if (TextAlign.indexOf("Right") > -1) {
          editor.changeContentAlign("right"); // 修改水平对齐方式
        }
        if (TextAlign.indexOf("Middle") > -1) {
          editor.setVerticalAlign("center"); // 修改垂
        }
        if (TextAlign.indexOf("Bottom") > -1) {
          editor.setVerticalAlign("bottom"); // 修改垂
        }
      }
    },
    handleMemoBox(item) {
      // console.log("MemoBox", JSON.stringify(item));
      const { editor } = this.instance;
      const { Text, Name, Width, Border, Font, TextAlign } = item;
      let fixedWidth = Math.floor(Width * this.calcCoefficient);
      const prop = { type: "normal", name: Name };
      if (Font) {
        prop.style = {
          bold: Font.Bold,
          family: Font.Name,
          height: this.handleFontSize2Height(Font.Size),
        };
      }
      this.handleFieldAlign(TextAlign, prop);
      this.handleCellAlign(TextAlign);
      if (Text) {
        const fields = this.parseInput(Text);
        if (fields.length) {
          for (let i = 0; i < fields.length; i++) {
            const item = fields[i];
            const { format, field } = item;
            prop.name = field;
            prop.placeholder = field;
            if (format === "0.00") {
              prop.type = "number";
              prop.number_format = 3;
            }
            prop.max_width = fixedWidth;
            // 文本域类型，根据format再次识别一下格式
            if (Border && Border.Styles === "[DrawBottom]") {
              prop.display_type = "line";
            }
            this.handleFieldProp(prop, fixedWidth);
            editor.insertField(prop);
          }
        } else {
          this.splitMutilFieldByText(Text, Font);
        }
      }
    },
    handleFieldBox(item) {
      const { editor } = this.instance;
      const { DataField, Border, Width, TextAlign, Font, Name } = item;
      // console.log("FieldBox", JSON.stringify(item));
      const fixedWidth = Math.floor(Width * this.calcCoefficient);
      const prop = {
        type: "normal",
        name: DataField ?? Name,
        placeholder: DataField ?? Name,
        max_width: fixedWidth,
      };
      if (Font) {
        prop.style = {
          bold: Font.Bold,
          family: Font.Name,
          height: this.handleFontSize2Height(Font.Size),
        };
      }
      this.handleFieldAlign(TextAlign, prop);
      if (Border && Border.Styles === "[DrawBottom]") {
        prop.display_type = "line";
      }
      this.handleFieldProp(prop, fixedWidth);

      editor.insertField(prop);
    },
    handlePictureBox(item) {
      const { editor } = this.instance;
      const { DataField, Border, Width, TextAlign, Name, ImageIndex } = item;
      if (ImageIndex === -3 || ImageIndex === -4) {
        const items = [
          {
            name: Name + "_Item",
            checked: ImageIndex === -3 ? true : false,
            value: "",
            formula_value: -3,
          },
        ];
        const prop = {
          isGroup: true,
          border: "solid",
          hideBorder: true,
          groupName: Name,
          isMulti: false,
          widgetType: "checkbox",
          deletable: 1,
          items,
        };
        const res = editor.insertWidget(prop);
        if (Border && Border.Styles === "[DrawBottom]") {
          res.display_type = "line";
        }
        return res;
      }
    },
    parseInput(input) {
      const matches = input.match(/\[#\{([^}]+)\}(?::([^}]+))?#\]/g);
      if (!matches) return [];
      return matches.map((match) => {
        const [, field, format] = match.match(/\[#\{([^}]+)\}(?::([^}]+))?#\]/);
        return { field, format: format || null };
      });
    },
    handleFieldProp(field, fixedWidth) {
      field.start_symbol = "";
      field.end_symbol = "";
      field.tip = field.placeholder;
      // field.placeholder = "#";
      if (field.placeholder && field.placeholder.length > 6) {
        field.placeholder =
          field.placeholder.slice(0, 3) +
          "…" +
          field.placeholder.slice(field.placeholder.length - 2);
      }
      if (
        field.align === "center" ||
        field.align === "right" ||
        field.display_type === "line"
      ) {
        if (fixedWidth) {
          field.min_width = fixedWidth;
        }
      }
      deleteUndefinedProperties(field);
    },
  },
};
export default trans;
