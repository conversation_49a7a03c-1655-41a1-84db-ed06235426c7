import { Config } from "./Config";
import Editor from "./Editor";
import Image from "./Image";
import Renderer from "./Renderer";
import { Path } from "./Path";
import Shape from "./shape";
import Button from "./Button";
import { is<PERSON><PERSON>cter, isN<PERSON>ber, isPointInRectangle } from "./Utils";
import { isBox, isButton, isImage, isLine, isWidget } from "./Helper";
import { ShapeMode, TypeInImageMeta } from "./Constant";
import EditorLocalTest from "../../localtest";
import EditorHelper from "./EditorHelper";
let focusImage: Image | null = null;
let focusButton: Button | null = null;
let img_para_path: number[] = [];
let image_shadow_show: boolean = false;
const image_shadow_size: { width: number; height: number } = {
  width: 0,
  height: 0
};
let mark_shadow_size: { x: number, y: number, width: number; height: number } = {
  x: 0,
  y: 0,
  width: 0,
  height: 0
};
export function setMarkShadowSize(shadow_size: any) {
  mark_shadow_size = shadow_size;
}
export function getImageParaPath() {
  return img_para_path;
}
export function setImageParaPath(para_path: Path) {
  img_para_path = para_path;
}
export function setShadowChange(change: boolean) {
  image_shadow_show = change;
}
export function getShadowChange() {
  return image_shadow_show;
}
export function setFocusImage(image: Image | null) {
  if (image) {
    image.show = true;
    focusImage = image;
  } else if (focusImage) {
    focusImage.show = false;
    focusImage = null;
  }
}
export function getFocusImage() {
  return focusImage;
}
/**
 * 图片拖动时绘制虚影，计算倍率
 * @param x // 测试发现是相对于画布原点,没看传入的地方
 * @param y
 * @param editor
 * @returns
 */
export function fictitiousShadow(x: number, y: number, editor: Editor, type: string = "image") {
  y += editor.scroll_top;
  let absolute_xy = { x: 0, y: 0 };
  let width = focusImage?.width;
  let height = focusImage?.height;
  // 点到了哪个小方块
  let area = editor.internal.pointer_down_state.area;
  if (type === "image") {
    // 通过路径获取元素绝对位置
    const view_path = editor.modelPath2viewPath(editor.selection.anchor);

    // 获取元素左上角距离 canvas 画布原点的 x y 值
    absolute_xy = editor.getElementAbsolutePositionByViewPath(view_path);
  } else if (type === "imageMark") {
    if (!editor.internal.focusMark) return
    if (editor.internal.focusMark.mode === "repeat") {
      y = editor.getSpacingPageTopByY(y);
    }
    area = editor.internal.is_edit_mark;
    width = editor.internal.focusMark.width;
    height = editor.internal.focusMark.height;
    let markLeft = editor.internal.focusMark.start.x + editor.page_left
    let markTop = editor.internal.focusMark.start.y
    if (editor.internal.focusMark.params.padding) {
      markLeft = editor.internal.focusMark.start.x + editor.page_left + editor.config.page_padding_left
      markTop = editor.internal.focusMark.start.y + editor.config.page_padding_top
    }
    absolute_xy = { x: markLeft, y: markTop };
  }

  if (!(width && height)) return;
  let start_xy = absolute_xy;
  // 计算元素拉伸后宽高与原来的比例
  let width_ratio = Math.abs(x - absolute_xy.x) / width;
  let height_ratio = Math.abs(y - absolute_xy.y) / height;
  switch (area) {
    // 左上拉
    case 0:
      image_shadow_show = true;
      width_ratio = Math.abs(x - absolute_xy.x - width) / width;
      height_ratio = Math.abs(y - absolute_xy.y - height) / height;
      if (width_ratio < height_ratio) {
        width_ratio = (width * height_ratio) / width;
      } else {
        height_ratio =
          (height * width_ratio) / height;
      }
      start_xy = {
        x: (absolute_xy.x + width) - width * width_ratio,
        y: (absolute_xy.y + height) - height * height_ratio
      };
      break;
    // 向上拉
    case 1:
      image_shadow_show = true;
      height_ratio = Math.abs(y - absolute_xy.y - height) / height;
      width_ratio = 1;
      start_xy = {
        x: (absolute_xy.x + width) - width * width_ratio,
        y: (absolute_xy.y + height) - height * height_ratio
      };
      break;
    // 右上拉
    case 2:
      image_shadow_show = true;
      width_ratio = Math.abs(x - absolute_xy.x) / width;
      height_ratio = Math.abs(y - absolute_xy.y - height) / height;
      if (width_ratio < height_ratio) {
        width_ratio = (width * height_ratio) / width;
      } else {
        height_ratio =
          (height * width_ratio) / height;
      }
      start_xy = {
        x: absolute_xy.x,
        y: (absolute_xy.y + height) - height * height_ratio
      };

      break;
    // 左拉
    case 3:
      image_shadow_show = true;
      width_ratio = Math.abs(x - absolute_xy.x - width) / width;
      height_ratio = 1;
      start_xy = {
        x: (absolute_xy.x + width) - width * width_ratio,
        y: absolute_xy.y
      };
      break;
    // 横向向右拉
    case 4:
      image_shadow_show = true;
      height_ratio = 1;
      break;
    // 左下拉
    case 5:
      image_shadow_show = true;
      width_ratio = Math.abs(x - absolute_xy.x - width) / width;
      height_ratio = Math.abs(y - absolute_xy.y) / height;
      if (width_ratio < height_ratio) {
        width_ratio = (width * height_ratio) / width;
      } else {
        height_ratio =
          (height * width_ratio) / height;
      }
      start_xy = {
        x: (absolute_xy.x + width) - width * width_ratio,
        y: absolute_xy.y
      };
      break;
    // 竖向向下拉
    case 6:
      image_shadow_show = true;
      width_ratio = 1;
      break;
    // 右下拉
    case 7:
      image_shadow_show = true;

      if (width_ratio < height_ratio) {
        width_ratio = (width * height_ratio) / width;
      } else {
        height_ratio =
          (height * width_ratio) / height;
      }
      break;
  }
  if (type === "image") {
    createShadowRect(width_ratio, height_ratio, start_xy, editor);
  } else {
    createMarkShadowRect(width_ratio, height_ratio, start_xy, editor);
  }
}

export function createMarkShadowRect(
  width_ratio: number,
  height_ratio: number,
  absolute_xy: any,
  editor: Editor
) {
  const image_map = editor.imageMap.get();
  // 放大缩小的偏移量
  const offset = editor.internal.view_scale_offset;
  if (!editor.internal.focusMark) return
  Renderer.save();
  const ctx = Renderer.get()
  ctx.globalAlpha = 0.8;
  const page = editor.getPageByRealY(editor.internal.focusMark.params.real_y)
  const x = (absolute_xy.x + offset) * editor.config.devicePixelRatio * editor.viewScale
  let y = (absolute_xy.y - editor.scroll_top) * editor.config.devicePixelRatio * editor.viewScale;
  if (editor.internal.focusMark.mode === "repeat") {
    y = (absolute_xy.y + page * (editor.page_size.height + editor.config.page_margin_bottom) - editor.scroll_top + editor.config.editor_padding_top);
  }
  const width = editor.internal.focusMark.width * width_ratio * editor.config.devicePixelRatio * editor.viewScale
  const height = editor.internal.focusMark.height * height_ratio * editor.config.devicePixelRatio * editor.viewScale
  ctx.drawImage(
    image_map.get(editor.internal.focusMark.params.src).data,
    x,
    y,
    width,
    height
  );
  ctx.strokeStyle = 'red';
  ctx.font = '20px STheiti, SimHei';
  ctx.fillStyle = 'red';
  // 绘制文本
  const top = 22
  ctx.strokeText(String(Math.floor(width)), x + 5, y + top);
  const measure = Math.floor(ctx.measureText(String(Math.floor(width))).width);
  ctx.fillText("x", x + measure + 10, y + top);
  ctx.strokeText(String(Math.floor(height)), x + measure + 25, y + top);
  mark_shadow_size = {
    x: absolute_xy.x,
    y: absolute_xy.y,
    width: editor.internal.focusMark.width * width_ratio,
    height: editor.internal.focusMark.height * height_ratio
  };
  Renderer.restore();
}
/**
 * 绘制虚影
 * @param width_ratio //虚影宽与原来的倍率
 * @param height_ratio //虚影高与原来的倍率
 * @param absolute_xy //图片左上角绝对定位
 * @param editor
 */
export function createShadowRect(
  width_ratio: number,
  height_ratio: number,
  absolute_xy: any,
  editor: Editor
) {
  const image_map = editor.imageMap.get();
  // 放大缩小的偏移量
  const offset = editor.internal.view_scale_offset;
  const ctx = Renderer.get()
  ctx.globalAlpha = 0.8;
  // 没有经过render（）的绘制都需要乘以editor.config.devicePixelRatio 和 editor.viewScale
  const x = (absolute_xy.x + offset) * editor.config.devicePixelRatio * editor.viewScale
  const y = (absolute_xy.y - editor.scroll_top) * editor.config.devicePixelRatio * editor.viewScale
  const width = focusImage!.width * width_ratio * editor.config.devicePixelRatio * editor.viewScale
  const height = focusImage!.height * height_ratio * editor.config.devicePixelRatio * editor.viewScale
  ctx.drawImage(
    image_map.get(focusImage!.src).data,
    x,
    y,
    width,
    height
  );
  ctx.strokeStyle = 'red';
  ctx.font = '20px STheiti, SimHei';
  ctx.fillStyle = 'red';
  // 绘制文本
  const top = 22
  ctx.strokeText(String(Math.floor(width)), x + 5, y + top);
  const measure = Math.floor(ctx.measureText(String(Math.floor(width))).width);
  ctx.fillText("x", x + measure + 10, y + top);
  ctx.strokeText(String(Math.floor(height)), x + measure + 25, y + top);
  ctx.globalAlpha = 1;
  const focus_row = editor.selection.getFocusRow();
  const page = editor.pages[0];
  const header_height = page.header.header_outer_bottom;
  const footer_height = page.height - page.footer.footer_outer_top;
  if (
    Config.rect_width * 2 <= focusImage!.width * width_ratio &&
    focusImage!.width * width_ratio <=
    focus_row.width - focus_row.padding_left &&
    Config.rect_width * 2 <= focusImage!.height * height_ratio &&
    focusImage!.height * height_ratio <=
    page.height - header_height - footer_height - Config.img_margin * 2 - 150
  ) {
    image_shadow_size.width = focusImage!.width * width_ratio;
    image_shadow_size.height = focusImage!.height * height_ratio;
  }
}
/**
 * 鼠标抬起图片大小改变
 * @param editor
 * @returns
 */
export function pointerUpMarkChange(editor: Editor) {
  if (!editor.internal.focusMark) return
  editor.internal.focusMark.start = {
    x: mark_shadow_size.x - editor.page_left,
    y: mark_shadow_size.y
  };
  editor.internal.focusMark.params.real_y = mark_shadow_size.y + editor.config.editor_padding_top;
  if (editor.internal.focusMark.params.padding) {
    editor.internal.focusMark.start = {
      x: mark_shadow_size.x - editor.page_left - editor.config.page_padding_left,
      y: mark_shadow_size.y - editor.config.page_padding_top
    };
    editor.internal.focusMark.params.real_y = mark_shadow_size.y + editor.config.editor_padding_top - editor.config.page_padding_top
  }
  editor.internal.focusMark.width = mark_shadow_size.width;
  editor.internal.focusMark.height = mark_shadow_size.height;
  editor.render();
}
/**
 * 鼠标抬起图片大小改变
 * @param editor
 * @returns
 */
export function pointerUpImageChange(editor: Editor): boolean {
  const focus_para = editor.selection.getFocusParagraph();

  if (focus_para) {
    const character = focus_para.characters[editor.selection.para_start[editor.selection.para_start.length - 1]];
    if (isImage(character)) {
      const para_path = [...editor.selection.para_start];
      // 获取element的真实坐标后一位的坐标，设置光标位置
      para_path[para_path.length - 1] =
        focus_para.characters.indexOf(character) + 1;
      setFocusImage(character);
      editor.changeImageSize(image_shadow_size.width, image_shadow_size.height);
      editor.selection.setSelectionByPath(editor.selection.para_start, para_path);
      editor.imageMap.refreshImage(character.src);
    }
  }
  return true;
}
/**
 * 光标在 不同位置的样式改变
 * @param x
 * @param y
 * @param editor
 * @param is_drag //是否拖拽
 */
export function cursorType(x: number, y: number, editor: Editor, page_left: number, is_drag: string = "") {
  editor.internal.isOnReplicableIdentifiers = false;
  y += editor.scroll_top;

  const getCanvasDom = Renderer.getCanvasDom();
  if (getCanvasDom) {
    if (is_drag !== "") {
      editor.internal.cursor_state.type = getCanvasDom.style.cursor = is_drag;
    } else if (editor.print_continue || editor.area_print) {
      editor.internal.cursor_state.type = getCanvasDom.style.cursor = "default";
    } else if (editor.is_shape_mode) {
      const shape_x = x - page_left;
      const shape = Shape.isInShape(shape_x, y, editor);
      if (shape) {
        // 如果是画线画叉画圈画线模式下而且不在编辑状态则光标形状不改变
        if (editor.internal.draw_shape && !shape.is_editor) return;
        const result = shape.isInShapeEditing(shape_x, y, editor);
        // 如果是绘制折线则光标显示小手
        if (shape.type === "fold_line" && result) {
          editor.internal.cursor_state.type = getCanvasDom.style.cursor = "pointer";
          return;
        }
        // 如果是number类型则根据number的大小确定光标的类型
        if (isNumber(result)) {
          switch (result) {
            case 1:
              editor.internal.cursor_state.type = getCanvasDom.style.cursor = "nw-resize";
              break;
            case 2:
              editor.internal.cursor_state.type = getCanvasDom.style.cursor = "sw-resize";
              break;
            case 3:
              editor.internal.cursor_state.type = getCanvasDom.style.cursor = "nesw-resize";
              break;
            case 4:
              editor.internal.cursor_state.type = getCanvasDom.style.cursor = "nwse-resize";
              break;
            default:
              break;
          }
        } else {
          // 计算线倾斜的斜率，改变光标的类型
          const slope = (shape.startXY.y - shape.endXY.y) / (shape.startXY.x - shape.endXY.x);
          if (result && slope >= 0) {
            editor.internal.cursor_state.type = getCanvasDom.style.cursor = "se-resize";
          } else if (result && slope < 0) {
            editor.internal.cursor_state.type = getCanvasDom.style.cursor = "ne-resize";
          } else {
            editor.internal.cursor_state.type = getCanvasDom.style.cursor = "move";
          }
        }
        if ((editor.internal.draw_shape === ShapeMode.Line || editor.internal.draw_shape === ShapeMode.Circle || editor.internal.draw_shape === ShapeMode.Cross || editor.internal.draw_shape === ShapeMode.Rect)) {
          editor.internal.cursor_state.type = getCanvasDom.style.cursor = "default";
        }
      } else {
        editor.internal.cursor_state.type = getCanvasDom.style.cursor = "default";
      }
    } else if (editor.waterMark) {
      const mark = editor.isInMark(x, y);
      if (mark) {
        if (mark.is_edit) {
          let re_y = y;
          const rep_y = editor.getSpacingPageTopByY(y);
          if (mark.mode === "repeat") {
            re_y = rep_y;
          }
          let markLeft = mark.start.x + editor.page_left
          let markTop = mark.start.y
          if (mark.params.padding) {
            markLeft = mark.start.x + editor.page_left + editor.config.page_padding_left
            markTop = mark.start.y + editor.config.page_padding_top
          }
          const result = isInImageRect(x, re_y, markLeft, markTop, mark.width, mark.height, "mark");
          if (isNumber(result)) {
            switch (result) {
              case 0:
                editor.internal.cursor_state.type = getCanvasDom.style.cursor = "nw-resize";
                break;
              case 1:
                editor.internal.cursor_state.type = getCanvasDom.style.cursor = "s-resize";
                break;
              case 2:
                editor.internal.cursor_state.type = getCanvasDom.style.cursor = "ne-resize";
                break;
              case 3:
                editor.internal.cursor_state.type = getCanvasDom.style.cursor = "ew-resize";
                break;
              case 4:
                editor.internal.cursor_state.type = getCanvasDom.style.cursor = "ew-resize";
                break;
              case 5:
                editor.internal.cursor_state.type = getCanvasDom.style.cursor = "ne-resize";
                break;
              case 6:
                editor.internal.cursor_state.type = getCanvasDom.style.cursor = "s-resize";
                break;
              case 7:
                editor.internal.cursor_state.type = getCanvasDom.style.cursor = "se-resize";
                break;
              default:
                break;
            }
          } else {
            editor.internal.cursor_state.type = getCanvasDom.style.cursor = "move";
          }
        } else {
          editor.internal.cursor_state.type = getCanvasDom.style.cursor = "move";
        }
      } else {
        editor.internal.cursor_state.type = getCanvasDom.style.cursor = "default";
      }
    } else if (editor.is_comment_mode && (editor.internal.IsInCommentRange)) {
      // const firstComment = editor.commentBox[0];
      // if (firstComment) {
      //   if (x > firstComment.left && x < firstComment.left + firstComment.width) {
      //     const commentBox = editor.commentBox;
      //     const commentY: any = [];
      //     let flag = false;
      //     commentBox.forEach((comment: any) => {
      //       const y = [comment.top, comment.top + comment.height];
      //       commentY.push(y);
      //     });
      //     commentY.forEach((commentY: any) => {
      //       if (commentY[0] < y && y < commentY[1]) {
      //         flag = true;
      //       }
      //     });
      //     if (flag) {
      //       editor.internal.cursor_state.type = getCanvasDom.style.cursor = "pointer";
      //     } else {
      //       editor.internal.cursor_state.type = getCanvasDom.style.cursor = "default";
      //     }
      //   } else {
      //     editor.internal.cursor_state.type = getCanvasDom.style.cursor = "default";
      //   }
      // }
      // const originTop = editor.scroll_top ? editor.scroll_top : editor.pages[0].top;
      // const left = editor.page_left + editor.page_size.width + 16;
      // const scrollTop = editor.scroll_top;
      // if (x > left + 230 && x < left + 240) {
      //   if (y > originTop - scrollTop + 15 && y - scrollTop < originTop + 25) {
      //     if (!editor.internal.hideCommentOperation.hideCloseButton) {
      //       editor.internal.cursor_state.type = getCanvasDom.style.cursor = "pointer";
      //     }
      //   }
      // }
    }else if(editor.format_brush){
      const generateBrushCursor=()=> {
        const canvas = document.createElement('canvas');
        canvas.width = 32;
        canvas.height = 32;
        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        // 绘制顶部的矩形：白色填充，黑色边框
        ctx.fillStyle = '#fff'; // 白色背景
        ctx.fillRect(10.5, 4.5, 12.5, 8.5); // 填充矩形 (x, y, width, height)

        ctx.strokeStyle = '#000'; // 黑色边框
        ctx.lineWidth = 0.5; // 设置边框宽度
        ctx.strokeRect(10.5, 4.5, 12.5, 8.5); // 画边框

        // 添加竖着的黑色线，位于左侧三分之一处
        ctx.beginPath();
        ctx.moveTo(14.5, 4.5); // 从 (14, 4) 开始
        ctx.lineTo(14.5, 10.5); // 画到 (14, 12)
        ctx.strokeStyle = '#000'; // 黑色线
        ctx.stroke(); // 画线

        ctx.beginPath();
        ctx.moveTo(18.5, 4.5); // 从 (18, 4) 开始
        ctx.lineTo(18.5, 8.5); // 画到 (18, 8)
        ctx.strokeStyle = '#000'; // 黑色线
        ctx.stroke(); // 画线

        ctx.beginPath();
        ctx.moveTo(10.5, 10.5); // 从 (10, 10) 开始
        ctx.lineTo(22.5, 10.5); // 画到 (22, 10)
        ctx.strokeStyle = '#000'; // 黑色线
        ctx.stroke(); // 画线

        // 绘制底部的小正方形：黑色背景
        ctx.fillStyle = '#000'; // 黑色背景
        ctx.fillRect(14.5, 12.5, 4.5, 7.5); // 正方形 (x, y, width, height)

        // 绘制左侧的工字型图案
        // 上水平线
        ctx.beginPath();
        ctx.moveTo(2.5, 4.5); // 从 (4, 4) 开始
        ctx.lineTo(8.5, 4.5); // 画到 (8, 4)
        ctx.strokeStyle = '#000'; // 黑色线
        ctx.stroke(); // 画线

        // 下水平线
        ctx.beginPath();
        ctx.moveTo(2.5, 18.5); // 从 (4, 10) 开始
        ctx.lineTo(8.5, 18.5); // 画到 (8, 10)
        ctx.strokeStyle = '#000'; // 黑色线
        ctx.stroke(); // 画线

        // 垂直线
        ctx.beginPath();
        ctx.moveTo(5.5, 4.5); // 从 (6, 4) 开始
        ctx.lineTo(5.5, 18.5); // 画到 (6, 10)
        ctx.strokeStyle = '#000'; // 黑色线
        ctx.stroke(); // 画线

        // 转换成 Data URL
        return canvas.toDataURL('image/png');
      }
      const brushCursor = generateBrushCursor();
      getCanvasDom.style.cursor = `url(${brushCursor}), auto`;
    } else {
      const container_info = editor.getContainerInfoByPoint(x, y);
      if (container_info) {
        // let path = editor.viewPath2ModelPath(container_info.view_path);
        // const info=  editor.getElementByModelPath(path,editor)
        const element = container_info.element;
        const cell = container_info.row.parent;
        const total_height = cell.children[cell.children.length - 1].bottom;
        // 单元格固定高度滚动逻辑
        if (cell.parent && cell.set_cell_height.type === "scroll" && total_height >= cell.height) {
          editor.internal.cell_is_scroll = true;
          // 单元格固定高度滚动需要的cell
          editor.internal.scroll_cell_info = cell;
          if (cell.children.length) {
            const bottom = cell.children[cell.children.length - 1].bottom;
            // 判断是否会出现滚动条 
            if (bottom > cell.height) {
              const right = container_info.row.parent.realLeft + container_info.row.parent.width;
              // 判断是否在滚动条内
              if (x >= right - 5 && x <= right - 1) {
                const height = cell.height * (cell.height / bottom);
                const scroll_top = cell.scroll_cell_top / (bottom - cell.height) * (cell.height - height - 3);
                if (scroll_top + cell.realTop <= y && y <= scroll_top + height + cell.realTop) {
                  editor.internal.cursor_state.type = getCanvasDom.style.cursor = "pointer";
                  editor.internal.move_in_cell_bar = true;
                  return;
                }
              }
            }
          }
        } else {
          editor.internal.cell_is_scroll = false;
          editor.internal.scroll_cell_info = null;
        }
        editor.internal.move_in_cell_bar = false;
        if (focusButton && focusButton.pointer_in && focusButton !== element) {
          focusButton.pointer_in = false;
          editor.render();
        }
        if (isImage(element)) {
          imageCursorType(x, y, editor, getCanvasDom, container_info, element);
        } else if (isWidget(element)) {
          editor.internal.cursor_state.type = getCanvasDom.style.cursor = "pointer";
        } else if (isCharacter(element)) {
          editor.internal.cursor_state.type = getCanvasDom.style.cursor = "text";
          if (element.field_id) {
            const field = editor.getFieldById(element.field_id)
            if (field) {
              const xy = EditorHelper.getAbsoluteXYByParaPath(
                field.end_para_path,
                editor
              );
              if (field.showPoint) {
                const height = field.rows[0]?.height
                const arc_x = xy.x + field.end_sym_char.width
                const arcResult1 = isPointInRectangle(arc_x - 8, xy.y, arc_x, xy.y + height / 2, x, y)
                const arcResult2 = isPointInRectangle(arc_x - 8, xy.y + height / 2, arc_x, xy.y + height, x, y)
                if (arcResult1) {
                  editor.internal.cursor_state.type = getCanvasDom.style.cursor = "ew-resize";

                } else if (arcResult2) {
                  editor.internal.cursor_state.type = getCanvasDom.style.cursor = "ew-resize";

                }

              } else if (field.meta.changed) {
                const arc_x = xy.x + field.end_sym_char.width - 5
                const arc_y = xy.y + field.end_sym_char.height + 8
                const arcResult = isInArc(x, y, arc_x, arc_y, 4)
                if (arcResult) {
                  editor.internal.cursor_state.type = getCanvasDom.style.cursor = "pointer";
                  editor.internal.is_in_link = true
                } else {
                  editor.internal.is_in_link = false
                }
              }
            }

          }
        } else if (isLine(element)) {
          editor.internal.cursor_state.type = getCanvasDom.style.cursor = "text";
        } else if (isBox(element)) {
          editor.internal.cursor_state.type = getCanvasDom.style.cursor = "pointer";
        } else if (isButton(element)) {
          if (!element.pointer_in) {
            element.pointer_in = true;
            editor.render();
          }
          focusButton = element;
          editor.internal.cursor_state.type = getCanvasDom.style.cursor = "pointer";
        } else {
          editor.internal.cursor_state.type = getCanvasDom.style.cursor = "default";
        }
        if (editor.internal.mouseOverFiledId) {
          let [ox, oy] = editor.internal.position;
          ox = ox ?? -11;
          oy = oy ?? -11;
          // FIXME 缩放的 150 的时候 就不会变小手了 后边再研究
          if (x > ox && x < ox + 10 && (y > oy && y < oy + 10)) {
            editor.internal.cursor_state.type = getCanvasDom.style.cursor = "pointer";
            editor.internal.isOnReplicableIdentifiers = true;
            editor.render();
          }
        }
        return container_info;
      } else {
        editor.internal.cell_is_scroll = false;
        editor.internal.scroll_cell_info = null;
        editor.internal.cursor_state.type = getCanvasDom.style.cursor = "default";
        if (focusButton) {
          focusButton.pointer_in = false;
          editor.render();
        }
      }
    }
  }
}
export function imageCursorType(x: number, y: number, editor: Editor, getCanvasDom: HTMLCanvasElement, container_info: any, element: Image) {

  if (element?.meta?.imageType === TypeInImageMeta.HUMAN_BODY_DIAGRAM_TYPE) {
    editor.internal.cursor_state.type = getCanvasDom.style.cursor = "default";
  } else {
    editor.internal.cursor_state.type = getCanvasDom.style.cursor = "move";
  }

  const { view_path, row } = container_info;

  // 获取element的真实坐标
  view_path[view_path.length - 1] = container_info.row.children.indexOf(
    element
  );
  if (element.show) {
    // element的左上角的绝对坐标
    const element_p = editor.getElementAbsolutePositionByViewPath(
      view_path
    );
    const start_x = Math.round(element_p.x * 10) / 10;
    const start_y = Math.round((element_p.y + (row.height - element.height)) * 10) / 10;
    const width = element.width;
    const height = element.height;
    const result = isInImageRect(x, y, start_x, start_y, width, height);
    const non_editable = getFocusImage()?.meta.non_editable;
    let type = "default";
    if (result === 0) {
      type = getCanvasDom.style.cursor = "se-resize";
    } else if (result === 1 && !non_editable) {
      type = getCanvasDom.style.cursor = "n-resize";
    } else if (result === 2 && !non_editable) {
      type = getCanvasDom.style.cursor = "sw-resize";
    } else if (result === 3 && !non_editable) {
      type = getCanvasDom.style.cursor = "w-resize";
    } else if (result === 4 && !non_editable) {
      type = getCanvasDom.style.cursor = "w-resize";
    } else if (result === 5 && !non_editable) {
      type = getCanvasDom.style.cursor = "sw-resize";
    } else if (result === 6 && !non_editable) {
      type = getCanvasDom.style.cursor = "n-resize";
    } else if (result === 7 && !non_editable) {
      type = getCanvasDom.style.cursor = "se-resize";
    } else {
      type = getCanvasDom.style.cursor = "default";
    }
    editor.internal.cursor_state = {
      type: type,
      element: element,
      area: result
    };
  }
}

export function isInImageRect(x: number, y: number, start_x: number, start_y: number, width: number, height: number, type: string = "rect") {
  let imageRectList = [
    { x: [start_x, start_x + 6], y: [start_y, start_y + 6] },
    {
      x: [
        start_x + width / 2 - 3,
        start_x + width / 2 + 3
      ],
      y: [start_y, start_y + 6]
    },
    { x: [start_x + width - 6, start_x + width], y: [start_y, start_y + 6] },
    {
      x: [start_x - 3, start_x + 3],
      y: [
        start_y + height / 2 - 3,
        start_y + height / 2 + 3
      ]
    },
    {
      x: [start_x + width - 6, start_x + width],
      y: [
        start_y + height / 2 - 3,
        start_y + height / 2 + 3
      ]
    },
    { x: [start_x, start_x + 6], y: [start_y + height - 6, start_y + height] },
    {
      x: [
        start_x + width / 2 - 3,
        start_x + width / 2 + 3
      ],
      y: [start_y + height - 6, start_y + height]
    },
    { x: [start_x + width - 6, start_x + width], y: [start_y + height - 6, start_y + height] }
  ];
  if (type === "mark") {
    imageRectList = [
      { x: [start_x - 5, start_x + 5], y: [start_y - 5, start_y + 5] },
      {
        x: [
          start_x + width / 2 - 5,
          start_x + width / 2 + 5
        ],
        y: [start_y - 5, start_y + 5]
      },
      { x: [start_x + width - 5, start_x + width + 5], y: [start_y - 5, start_y + 5] },
      {
        x: [start_x - 5, start_x + 5],
        y: [
          start_y + height / 2 - 5,
          start_y + height / 2 + 5
        ]
      },
      {
        x: [start_x + width - 5, start_x + width + 5],
        y: [
          start_y + height / 2 - 5,
          start_y + height / 2 + 5
        ]
      },
      { x: [start_x - 5, start_x + 5], y: [start_y + height - 5, start_y + height + 5] },
      {
        x: [
          start_x + width / 2 - 5,
          start_x + width / 2 + 5
        ],
        y: [start_y + height - 5, start_y + height + 5]
      },
      { x: [start_x + width - 5, start_x + width + 5], y: [start_y + height - 5, start_y + height + 5] }
    ];
  }

  for (let i = 0; i < imageRectList.length; i++) {
    const limit = imageRectList[i];
    if (
      limit.x[0] <= x &&
      x <= limit.x[1] &&
      limit.y[0] <= y &&
      y <= limit.y[1]
    ) {
      return i;
    }
  }
}
export function isInArc(x: number, y: number, arc_x: number, arc_y: number, radius: number) {
  const dx = x - arc_x;
  const dy = y - arc_y;
  const distanceSquared = dx * dx + dy * dy;
  const radiusSquared = radius * radius;
  return distanceSquared < radiusSquared;
}
