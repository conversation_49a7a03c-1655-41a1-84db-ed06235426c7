//日期

export function parseDate(string: any) {
  if (string instanceof Date) {
    return string
  }
  if (typeof string === 'string') {
    const result = new Date(string)

    if (!isNaN(result.getTime())) {
      return result
    }
  }
  return false
}
/**
 * 取年
 * @param date
 * @returns
 */
export function YEAR(date: any) {
  let result = parseDate(date);
  if (result) {
    return result.getFullYear()
  }
  return false
}
/**
 * 取月
 * @param date
 * @returns
 */
export function MONTH(date: any) {
  let result = parseDate(date);
  if (result) {
    return result.getMonth() + 1
  }
  return false
}
/**
 * 取日
 * @param date
 * @returns
 */
export function DAY(date: any) {
  let result = parseDate(date);
  if (result) {
    return result.getDate()
  }
  return false
}
/**
 * 取时
 * @param date
 * @returns
 */
export function HOUR(date: any) {
  let result = parseDate(date);
  if (result) {
    return result.getHours()
  }
  return false
}
/**
 * 取分
 * @param date
 * @returns
 */
export function MINUTE(date: any) {
  let result = parseDate(date);

  if (result) {
    return result.getMinutes()
  }

  return false
}
/**
   * 取秒
   * @param date
   * @returns
   */
export function SECOND(date: any) {
  let result = parseDate(date)
  if (result) {
    return result.getSeconds()
  }
  return false
}

/**
 * 取当前系统时间
 */
export function NOW() {
  let date = new Date();

  return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}`
}
/**
 * 计算两个日期之间的差值
 * @param unit - 返回的时间单位
 */
export function DATEDIF(_startDate: any, _endDate: any, unit: 'Y' | 'M' | 'D' | 'H' | 'MI' | 'S' | 'MS' = 'D') {
  if (arguments.length < 2) {
    return false
  }
  const startDate = parseDate(_startDate)
  const endDate = parseDate(_endDate)
  if (!(startDate && endDate)) return false
  const diff = endDate.getTime() - startDate.getTime()

  switch (unit) {
    case 'Y':
      return endDate.getFullYear() - startDate.getFullYear()
    case 'M':
      return (endDate.getFullYear() - startDate.getFullYear()) * 12 + (endDate.getMonth() - startDate.getMonth())
    case 'D':
      return diff / 1000 / 60 / 60 / 24
    case 'H':
      return diff / 1000 / 60 / 60
    case 'MI':
      return diff / 1000 / 60
    case 'S':
      return diff / 1000
    case 'MS':
      return diff
    default:
      return false
  }

}

/**
 * 获取两个时间的间隔时间，返回差值日期的字符串， 默认格式 mm月dd天
 * @param _startDate 
 * @param _endDate
 * @param unit 返回的间隔时间格式，关键字需要用{} 包裹。关键信息如下：
 * y 代表年份， yyyy 代表四位的年份
 * m 代表月份   mm表示不足两位的补零
 * d代表日      dd表示不足两位的补零
 * h代表小时    hh表示不足两位的补零
 * i代表分钟    ii表示不足两位的补零
 * s 代表秒     ss表示不足两位的补零
 * 除了{}包裹的变量外，还可以组合汉字和其他特殊字符等生成结果。例如：{yyyy}年{m}月{dd}日——{hh}H
 */
export function FINDINTERVALDATE(_startDate: any, _endDate: any, unit: string = "mm月dd日") {
  if (arguments.length < 2) {
    return false
  }

  const startDate = parseDate(_startDate)
  const endDate = parseDate(_endDate)

  if (!(startDate && endDate)) return false

  return formatDifTime(endDate.getTime() - startDate.getTime(), unit)
}

/**
 * 返回时间戳转换成年月日时分秒的值，支持定制返回格式
 * @param time 传入的毫秒数，注意是毫秒数
 * @param cFormat
 * @returns
 */
export function formatDifTime(time: number, cFormat: string = "{m}个月{d}天") {
  time = Math.round(time / 1000)
  const formatObj: any = {
    y: 0,
    m: 0,
    d: 0,
    h: 0,
    i: 0,
    s: 0
  };

  const allForm = ['{y}', '{m}', '{d}', '{h}', '{i}', '{s}']
  let deltaTimes = [60, 60, 24, 365]
  while (time > 0 && allForm.length) {
    let str = allForm.shift() as string
    if (str === '{m}') { deltaTimes[3] = 30 }
    if (!cFormat.includes(str)) { continue }

    let length = allForm.length > 4 ? 4 : allForm.length
    let deltaTime = 1
    for (let i = 0; i < length; i++) { deltaTime *= deltaTimes[i] }
    const roundTime = Math.floor(time / deltaTime)
    formatObj[str!.replace(/[{}]/g, '')] = roundTime
    time = time - roundTime * deltaTime
  }

  const time_str = cFormat.replace(/{([ymdhis])}/g, (result, key) => {
    const value = formatObj[key];
    return value.toString()
  });
  return time_str;
}

//

export function parseNumber(string: any) {
  if (typeof string === "boolean") {
    string = Number(string);
  }
  if (!isNaN(string)) {
    return Number(string);
  }
  return false;
}

/**
 * 绝对值
 * @param number
 * @returns
 */
export function ABS(number: any) {
  number = parseNumber(number)

  if (!number) return false

  return Math.abs(number)
}

/**
 * #### 特殊函数, 运行到了再对内部求值
 * 所有值都为true时返回true，有一个为false返回false
 */
export function AND(astParser: any, ...args: ChildNode[]) {

  for (let i = 0; i < args.length; i++) {
    const val = astParser.evalNode(args[i])
    if (val.type === 'ERROR') {
      return val
    }
    if (!!val.value === false) {
      return { type: 'BOOLEAN', value: false } as any
    }
  }
  return { type: 'BOOLEAN', value: true } as any
}

/**
 * #### 特殊函数, 运行到了再对内部求值
 * 所有值都为false时返回false，有一个为true返回true
 */
export function OR(astParser: any, ...args: ChildNode[]) {

  for (let i = 0; i < args.length; i++) {
    const val = astParser.evalNode(args[i])
    if (val.type === 'ERROR') {
      return val
    }
    if (!!val.value === true) {
      return { type: 'BOOLEAN', value: true } as any
    }
  }

  return { type: 'BOOLEAN', value: false } as any
}

/**
 * 判断是否为null值
 * @param value 
 * @returns 
 */
export function ISNULL(value: any) {
  return value == null || value === 'null'
}

/**
 * #### 特殊函数, 运行到了再对内部求值
 * 判断是否满足某个条件
 * @param logical_test
 * @param value_if_true
 * @param value_if_false
 * @returns
 */
export function IF(astParser: any, logical_test: ChildNode, value_if_true?: ChildNode, value_if_false?: ChildNode) {
  if (!logical_test) {
    return false
  }

  const condition = astParser.evalNode(logical_test)
  if (condition.type === 'ERROR') {
    return condition
  }

  // 没有则返回默认值 true/false
  return condition.value
    ? value_if_true
      ? astParser.evalNode(value_if_true)
      : { type: 'BOOLEAN', value: true } as any
    : value_if_false
      ? astParser.evalNode(value_if_false)
      : { type: 'BOOLEAN', value: false } as any

}


/**
 * #### 特殊函数, 运行到了再对内部求值
 * 
 */
export function IFERROR(astParser: any, expected: ChildNode, whenError: ChildNode) {

  if (!expected || !whenError) {
    return {
      type: "ERROR",
      msg: "IFERROR 函数需要两个参数",
      value: "ERROR",
    } 
  }

  return false

  // 这块cactch暂时没用到，因为没有throw
  // } catch (e: any) {
  //   return astParser.evalNode(whenError)
  // }
}

export function isDefined(arg: any) {
  return arg !== undefined && arg !== null;
}
export function numbers(a: any) {
  const possibleNumbers = flatten(...a);

  return possibleNumbers.filter((el) => typeof el === "number");
}
/**
 * 平均数
 */
export function AVERAGE(a: any) {
  const flatArguments = flatten(a)
  const flatArgumentsDefined = flatArguments.filter(isDefined)
  const range = numbers(flatArgumentsDefined)
  const n = range.length
  let sum = 0
  let count = 0
  let result

  for (let i = 0; i < n; i++) {
    sum += range[i]
    count += 1
  }

  result = sum / count

  if (isNaN(result)) {
    return false
  }
  return result
}
export function add(...args: number[]): number {
  const factor = correctionFactor(...args);

  return args.reduce((result, number) => {
    return Math.round(number * factor) + result
  }, 0) / factor;
}

/**
 * 求和
 */
export function SUM(...a: any[]) {
  let result: number 

  // a 一定是数组
  a = flatten(a)

  for (let i = 0; i < a.length; i++) {
    if (a[i] === '' || a[i] === null || a[i] === undefined) a[i] = 0 // 空单元格值默认为0

    const pNum = parseNumber(a[i])
    if (!pNum ) {
      return false
    }
  }
  result = add(...a)
  if (isNaN(result)) {
    return false
  }
  return result
}
/**
 * 转为浮点数
 */
export function FLOAT(value: any) {
  return parseNumber(value)
}

/**
 * 转为整数
 */
export function INT(value: any) {
  let result = parseNumber(value)
  if (!result) return false
  return parseInt(result.toFixed())
}

/**
 * 获取圆周率
 */
export function PI() {
  return Math.PI
}

/**
 * v1的v2次幂
 */
export function POWER(v1: any, v2: any) {
  let t1 = parseNumber(v1)
  let t2 = parseNumber(v2)
  if (!t1) {
    return false
  }
  if (!t2) {
    return false
  }
  return Math.pow(t1, t2)
}

/**
 * x保留y位小数,四舍五入
 */
export function ROUND(x: any, y: any) {
  let t1 = parseNumber(x)
  let t2 = parseNumber(y)
  if (!t1) {
    return false
  }
  if (!t2) {
    return false
  }
  const t3 = Math.pow(10, t2 || 0)
  return Math.round(t1 * t3) / t3
}

/**
 * x保留y位小数,直接取整，不四舍五入
 */
export function TRUNC(x: any, y: any) {
  let t1 = parseNumber(x)
  let t2 = parseNumber(y)
  if (!t1) {
    return false
  }
  if (!t2) {
    return false
  }
  const arr = String(t1).split('.')
  return Number(`${arr[0]}.${arr[1] ? arr[1].substr(0, t2) : '0'.repeat(t2)}`)
}

/**
 * x向上取整
 */
export function ROUNDUP(x: any) {
  let t = parseNumber(x)
  if (!t) {
    return false
  }
  return Math.ceil(t)
}

/**
 * x向下取整
 */
export function ROUNDDOWN(x: any) {
  let t = parseNumber(x)
  if (!t) {
    return false
  }
  return Math.floor(t)
}


/**
 * 只要参数不是string类型，均抛出错误，此方法只能用于解析公式参数
 * @param string
 * @returns
 */
export function parseString(string: any) {
  if (string === undefined || string === null) {
    return false;
  }

  if (typeof string === "boolean" || typeof string === "number") {
    string = string.toString()
  }

  if (typeof string !== "string") {
    return false;
  }

  return string;
}

/**
 * 返回文本字符串中的字符个数
 * @param text
 * @returns
 */
export function LEN(text: any) {
  if (arguments.length === 0) {
    return false;
  }
  const textAsString = parseString(text);
  if (!textAsString) return false;

  return textAsString.length;
}

/**
 * 删除字符串中多余的空格，但会在英文字符串中保留一个作为词与词之间分隔的空格
 * @param text
 * @returns
 */
export function TRIM(text: any) {
  text = parseString(text)

  if (!text) return false

  return text.replace(/\s+/g, ' ').trim()
}
/**
 * 
 * @param text 
 * @param deleteStr 
 * @param insertStr 
 */
export function REPLACE(text: string, deleteStr: string | RegExp,insertStr: string = '') {
  if (typeof deleteStr === 'object') {
    return text.replace(new RegExp(deleteStr), insertStr)
  }
  return text.replace(deleteStr, insertStr)
}

/**
 * 
 * @param text 
 * @param deleteStr 
 * @param insertStr 
 */
export function REPLACEALL(text: string, deleteStr: string | RegExp,insertStr: string = '') {
  if (typeof deleteStr === 'object') {
    return text.replace(new RegExp(deleteStr), insertStr)
  }
  //@ts-ignore
  return text.replaceAll(deleteStr, insertStr)
}
/**
 * 去除左侧空格
 */
export function LTRIM(text: any) {
  let t = parseString(text)
  if (!t) {
    return false
  }
  //@ts-ignore
  return t.trimStart()
}

/**
 * 去除右侧空格
 */
export function RTRIM(text: any) {
  let t = parseString(text)
  if (!t) {
    return false
  }
  //@ts-ignore
  return t.trimEnd()
}

/**
 * 获取参数的小数位数并乘10
 * @param x
 * @returns
 */
function multiplier(x: number) {
  const parts = x.toString().split('.');

  return parts.length < 2 ? 1 : Math.pow(10, parts[1].length);
}

export function SPLIT(text: string, splitKey: string, index: number) {
  index--
  return text?.split(splitKey)[index] || ''
}
/**
 * 获取校正计算因子
 */
function correctionFactor(...args: number[]): number {
  return args.reduce((result, current) => {

    let currentFactor = multiplier(current)

    return currentFactor > result ? currentFactor : result
  }, 1)
}

export function multiply(...args: number[]): number {
  const factor = correctionFactor(...args);

  const length = args.length;

  return args.reduce((result, number) => {
    return Math.round(number * factor) * result
  }, 1) / Math.pow(factor, length);
}

/**
 * 求积
 */
export function PRODUCT(...a: any[]) {
  let result: number

  // a 一定是数组
  a = flatten(a)

  for (let i = 0; i < a.length; i++) {
    //if(a[i] === undefined) a[i] = 0;
    const pNum = parseNumber(a[i])
    if (!pNum) {
      return false
    }
  }
  result = multiply(...a)

  if (isNaN(result)) {
    return false
  }
  return result
}
/**
 * 将二维或者二维与一维混合数组转为一维数组
 * @param args
 * @returns
 */
export function flatten(...args: any[]) {
  while (!isFlat(args)) {
    args = flattenShallow(args);
  }

  return args;
}
/**
 * 是否是一维数组
 * @param array
 * @returns
 */
export function isFlat(array: any[]) {
  if (!array) {
    return false;
  }

  for (let i = 0; i < array.length; ++i) {
    if (Array.isArray(array[i])) {
      return false;
    }
  }

  return true;
}

export function flattenShallow(array: any[]) {
  if (!array || !array.reduce) {
    return array;
  }

  return array.reduce((a, b) => {
    const aIsArray = Array.isArray(a);
    const bIsArray = Array.isArray(b);

    if (aIsArray && bIsArray) {
      return a.concat(b);
    }

    if (aIsArray) {
      a.push(b);

      return a;
    }

    if (bIsArray) {
      return [a].concat(b);
    }

    return [a, b];
  });
}

// 取模
export function MOD(x: number, y: number) {
  let t1 = parseNumber(x)
  let t2 = parseNumber(y)
  if (!t1) {
    return false
  }
  if (!t2) {
    return false
  }

  return t1 % t2;
}

/**
 * 判断x是否包含y
 */
export function CONTAINS(x: any, y: any) {
  let t1 = parseString(x)
  let t2 = parseString(y)
  if (!t1) {
    return t1
  }
  if (!t2) {
    return t2
  }
  return t1.includes(t2)
}

/**
 * 传如多个字符串，x都包含则返回true，否则返回false
 * @param x 
 * @param strs 
 */
export function INCLUDESALL(x: any, ...strs: any) {
  let t1 = parseString(x)
  let t2 = strs.map((s: any) => parseString(s))
  if (!t1) {
    return false
  }
  if (t2.some((s: any) => !s)) {
    return false
  }
  return t2.every((s: any) => (t1 as string).includes(s as string))
}

/**
 * 传如多个字符串，x包含其中一个则返回true，都不包含返回false
 * @param x 
 * @param strs 
 */
export function INCLUDESONE(x: any, ...strs: any) {
  let t1 = parseString(x)
  let t2 = strs.map((s: any) => parseString(s))
  if (!t1) {
    return t1
  }
  if (t2.some((s: any) => !s)) {
    return false
  }
  return t2.some((s: any) => (t1 as string).includes(s as string))
}

/**
 * 将一个文本字符串的第一个字符开始返回指定个数的字符
 * @param text
 * @param number
 * @returns
 */
export function LEFT(text: any, number: any) {
  text = parseString(text);

  if (!text) return false;

  number = number === undefined ? 1 : number;
  number = parseNumber(number);

  if (!number) return false;

  return text.substring(0, number);
}

/**
 * 从一个本文字符串的最后一个字符开始返回指定个数的字符
 * @param text
 * @number
 * @returns
 */
export function RIGHT(text: any, number: any) {
  text = parseString(text);

  if (!text) return false;

  number = number === undefined ? 1 : number;
  number = parseNumber(number);

  if (!number) return false;

  return text.substring(text.length - number);
}