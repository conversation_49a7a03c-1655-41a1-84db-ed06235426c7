import Editor from "./Editor";
import XField from "./XField";
import <PERSON>Field from "./BoxField";
import { system_variables } from "./Config";
import createImagePlaceholder, {
  formatDate,
  getBarcodeOrQrCodeSrc,
  isBase64Image,
  isField,
  isNotNullAndUndefined,
  isRow,
  isTable, serializeCopy, uuid
} from "./Utils";
import Cell from "./Cell";
import Table from "./Table";
import { SkipMode, VerticalAlign } from "./Constant";
import Widget from "./Widget";
import Font from "./Font";
import Paragraph from "./Paragraph";
import { isBoxField, isImage } from "./Helper";
import Group from "./Groups";
import EditorHelper from "./EditorHelper";
import formulaList from "./formula";
import { deepClone } from "./Utils";
import * as echarts from 'echarts';
import Image from "./Image";
export default class AutoFill {
  appendRowTable: any = {}
  expression: string = "";
  token: any[] = [];
  needUpdateFields: XField[] = [];
  index: number = 0;
  BINARY_OPERATORS = ["&", "<", ">", "=", "<=", ">=", "<>", "+", "-", "*", "/", "%"];
  resetInitParam() {
    this.appendRowTable = {}
    this.needUpdateFields = []
  }
  // 根据传入的数据替换文本 这里边的 jsonData 就是 design 上的 oriDataSet
  fillContentByJson(editor: Editor, rawData: any, jsonData: any, jsonType: string, isClear: boolean) {
    this.resetInitParam();
    if (rawData) {
      editor.reInitRawByConfig(rawData);
      editor.refreshDocument();
    }
    // 因为跨页单元格不能合并，所以此处使用临时办法先将纸张尺寸修改，然后再进行还原处理
    const oriPageInfo = {
      page_size_type: editor.config.page_size_type,
      customPageSize: {
        width: editor.config.customPageSize.width,
        height: editor.config.customPageSize.height
      }
    };
    editor.config.page_size_type = "custom";
    editor.config.customPageSize = {
      width: 1000,
      height: 20000
    }
    const copiedDataObj = serializeCopy(jsonData)
    if (!isClear && jsonType === "dataSet") {
      // 处理数据集时先拷贝，其中有处理原数据数据的地方
      jsonData = serializeCopy(jsonData)
      // 先清空为空的自定义字段，后面会添加
      jsonData['自定义字段'] = {}
      this.handleScript(jsonData, editor.document_meta.BeforePostRecordScript);
      this.handleScript(jsonData, editor.document_meta.FormatScript);
      this.handleGroupVsDataSet(editor, jsonData)
      this.dynamicInsertDict(editor, jsonData)
    }
    if (jsonType === "dataSet") {
      jsonData = this.handleDataSet2FillJson(jsonData);
    }
    if (typeof jsonData === "string") {
      jsonData = JSON.parse(jsonData);
    }

    // 处理自定义字段合并到数据集中
    this.updateCustomFields(jsonData,editor)

    // 因为肯定要用到文档中的所有文本域，所以此时我们将所有的文本域先全部拿出来，因为使用时是通过name匹配，所以我们按name进行归类
    let allFields = editor.getAllFields();
    const nameVsFields: any = {}
    const beCascadeFields: any = []
    while (allFields.length) {
      // 顺序不能错
      const field = allFields.shift()!;
      if (field.ext_cell) {
        allFields = allFields.concat(field.ext_cell.fields)
      }
      if (!nameVsFields[field.name]) {
        nameVsFields[field.name] = [];
      }
      nameVsFields[field.name].push(field)
      if (field.cascade_list && field.cascade_list.length) {
        beCascadeFields.push(...field.cascade_list.map((f: any) => f.show_field_names).flat())
      }
    }
    const fields: XField[] = [];
    const allTables: any = editor.getAllTables();
    const alreadyHandleField: any = {}
    const dataSetNameVsValue: any = {}
    for (let i = 0; i < jsonData.length; i++) {
      const item = jsonData[i];
      if (item.type === "table") {
        this.insertTableFillContent(editor, allTables, item);
      } else {
        if (item.value === undefined || item.value === null) {
          continue;
        }
        dataSetNameVsValue[item.name] = item;
        let ffs = nameVsFields[item.name];
        const splitName = item.name.split(">")[1];
        if (splitName) {
          const mhFfs = nameVsFields[splitName];
          if (mhFfs) {
            if (!ffs) {
              ffs = mhFfs
            } else {
              ffs = ffs.concat(mhFfs)
            }
          }
        }
        if (!ffs || !ffs.length) {
          continue;
        }
        alreadyHandleField[item.name] = true;
        // 如果值是数组说明是一个自动扩展表，此时进入表格自动扩展逻辑
        if (Array.isArray(item.value)) {
          this.handleAutoFillTableField(editor, ffs, item, isClear, jsonData);
          continue;
        }
        for (let i = 0; i < ffs.length; i++) {
          const f = ffs[i];
          this.fillFieldCore(editor, fields, f, item, isClear, jsonData)
          if (f.meta && f.meta.fillLimit === "haltSubsequent") {
            break;
          }
          
        }
      }
    }
    
    const chartFields = [];
    const newAllfields = editor.getAllFields();
    for (const key in copiedDataObj) {
      for (const f of newAllfields) {
        if (f.name === key && f.meta.name === "chart") {
          f.meta.chartData = copiedDataObj[key];
          chartFields.push(f);
        }
      }
    }
    fields.push(...chartFields);
    this.updateFieldTextAop(editor,fields)
    // 以上完成基本填充逻辑。下面则根据级联规则显示隐藏部分文本域
    editor.getAllFields().forEach((field: any) => {
      editor.showOrHideField(field)
    })
    // 下面再填充级联规则显示出来的文本域
    const fields2: any = []
    beCascadeFields.forEach((name: any) => {
      const item = dataSetNameVsValue[name];
      if (!alreadyHandleField[name] && item) {
        const ffs = editor.getFieldsByName(name)
        if (ffs && ffs.length) {
          ffs.forEach((f: XField | BoxField) => {
            this.fillFieldCore(editor, fields2, f, item, isClear, jsonData)
          });
        }
      }
    })
    this.updateFieldTextAop(editor,fields2)

    const needUpdateFields = editor.getAllFields()
    // 先将所有带有文本域公式的文本域全部获取到，然后将其公式拼接成一个超大字符串用于后面触发公式自动计算逻辑
    let allFormulaStr = ""
    needUpdateFields.forEach((f) => {
      if (f.formula) {
        allFormulaStr += f.formula
      }
    })
    for (let i = 0, len = needUpdateFields.length; i < len; i++) {
      const field = needUpdateFields[i]
      // 还要处理自动化设置过的文本域
      if ((field.automation_list && field.automation_list.length)
        || allFormulaStr.indexOf("[" + field.name + "]") > -1) {
        XField.updateFieldAdvancedFunctions(field, editor,{ clear:false })
      }
    }
    // 临时处理
    editor.config.page_size_type = oriPageInfo.page_size_type;
    editor.config.customPageSize = oriPageInfo.customPageSize;
    this.handleFieldsCharacterSize(editor)
    editor.refreshDocument(true)
    // 因为涉及到分页，在此时才能知道
    this.handleSystemVar(editor, isClear);
  }
  // 动态插入字典（将对应字典名称的多选框或下拉框删除后重新插入）
  dynamicInsertDict(editor: Editor, dataSet: any) {
    // 首先将字典数据集搞出来，
    // 然后全局搜索对应的文本域进行删除
    // 删除后紧接着重新插入（删除前将对应文本域的一些属性应该记录，重新赋值给新插入的文本域）
    const vsFieldName: any = {}
    for (let key in dataSet) {
      if (key.endsWith("-dict")) {
        const dict = dataSet[key];
        key = key.split("-dict")[0]
        for (let i = 0, len = dict.length; i < len; i++) {
          const item = dict[i];
          const name = key + ">" + item.name;
          if (!vsFieldName[name]) {
            vsFieldName[name] = []
          }
          vsFieldName[name].push(item)
        }
      }
    }
    // 先处理页眉页脚
    EditorHelper.enterEditHeaderAndFooterMode(editor, "header")
    this.updateDictVsField(editor, vsFieldName);
    EditorHelper.enterEditHeaderAndFooterMode(editor, "footer")
    this.updateDictVsField(editor, vsFieldName);
    EditorHelper.quitEditHeaderAndFooterMode(editor)
    this.updateDictVsField(editor, vsFieldName);

  }

  handleScript(jsonData: any, scriptStr: any) {
    if (scriptStr) {
      try {
        const scriptDataSet: any = {}
        const convertScript = new Function(scriptStr + "\nreturn convertCode;")();
        for (const key in jsonData) {
          const resDataSet = convertScript(jsonData[key])
          Object.assign(scriptDataSet, resDataSet)
        }
        const resultScriptDataSet: any = {}
        for (const key in scriptDataSet) {
          if (scriptDataSet[key].Visible !== undefined) {
            resultScriptDataSet[key] = scriptDataSet[key].Visible
          }
          if (scriptDataSet[key].ImageIndex !== undefined) {
            resultScriptDataSet[key] = scriptDataSet[key].ImageIndex
          }
        }
        // console.log(resultScriptDataSet)
        // 此处不应该直接覆盖，需合并
        if (!jsonData["scriptDataSet"]) {
          jsonData["scriptDataSet"] = {}
        }
        Object.assign(jsonData["scriptDataSet"], resultScriptDataSet)
      } catch (e) {
        console.error("脚本执行异常》》》", e)
      }
    }
  }

  updateDictVsField(editor: Editor, vsFieldName: any) {
    for (const name in vsFieldName) {
      // // 只处理文本域中dynamic属性为true的,  先处理正文中的
      const fields = editor.getFieldsByName(name, editor.current_cell).filter((f) => f.meta.dynamic)
      for (let i = 0, len = fields.length; i < len; i++) {
        const field = fields[i];
        editor.removeFields([field])
        // @ts-ignore
        const { type, placeholder, start_symbol, end_symbol, multi_select, name, tip, box_multi, required, border } = field;
        if (type === "select") {
          const sourceList = vsFieldName[name].map((ele: any) => {
            return {
              text: ele.label,
              value: ele.value,
            };
          });
          const newField = editor.insertField({ type, placeholder, start_symbol, end_symbol, multi_select, source_list: sourceList, name, tip });
          if (isField(newField)) {
            newField.meta = field.meta;
          }
        }
        if (type === "box") {
          const sourceList = vsFieldName[name].map((node: any) => {
            return {
              key: uuid("choice"),
              value: node.label,
              formula_value: node.value,
              disabled: 0,
            };
          });
          const newField = editor.insertWidget({
            isGroup: true,
            groupName: name,
            isMulti: !!box_multi,
            required: required,
            widgetType: "checkbox",
            deletable: 1,
            hideBorder: 1,
            border,
            items: sourceList,
          });
          if (isBoxField(newField)) {
            newField.meta = field.meta;
          }
        }
      }
    }
  }
  //更新创建字段的值
  updateCustomFields(dataList: any, editor: Editor) {
    if (!(editor.document_meta.customField && editor.document_meta.customField.length)) return
    const customFields = editor.document_meta.customField
    for (let i = 0; i < customFields.length; i++) {
      try {
        const customField = customFields[i];
        const name = customField.fieldName;
        // 将缓存的变量清空
        this.token = []
        this.index = 0;
        this.expression = ""
        const composeRule=  this.parse(customField.formula)
        const res = this.parseComposeField(dataList,composeRule)
        if (res.type === "ERROR") {
          editor.event.emit("message", {
            type: "error",
            msg: "自定义字段【" +  name + "】" + (res.msg  ||  "出错了")
          })
        }
        const value = res.value;
        if (value === undefined) continue
        dataList.push({ name:"自定义字段>" + name,value })
      } catch (e:any) {
        console.error(e)
        editor.event.emit("message", {
          type: 'error',
          msg: "自定义字段异常："+e.message
        })
      }
    }
  }
  parseComposeField(data: any,rule:any) {
    if (!(this.token && this.token.length)) return
    let composeRule = deepClone(rule);
    if ("children" in composeRule) {
      this.replaceField(composeRule, data);
    } else if (composeRule.type === "VARIABLE") {
      const info = data.find((item: any) => item.name === composeRule.value)
      if (info) {
        composeRule = {
          type: "VARIABLE",
          value: info.value
        }
      }
    }

    return this.parseTree(composeRule)
  }

  replaceField(childNodes: any, data: any) {
    for (let index = 0; index < childNodes.children.length; index++) {
      const child = childNodes.children[index]!;
      if ("children" in child) {
        this.replaceField(child, data)
      } else {
        const findField =  data.find((item: any) => item.name === child.value)
        if (child.type === "VARIABLE" && typeof (child.value) === "string") {
          childNodes.children[index] = {
            type: 'VARIABLE',
            value: findField ? findField.value : ""
          }
        }
      }
    }
  }

  parseTree(tokenTree: any) {
    const cloneToken = deepClone(tokenTree)
    return this.evalTree(cloneToken);
  }
  hasChildren(tokenNode: any) {
    return "children" in tokenNode;
  }
  evalTree(tokenTree: any) {
    if (this.hasChildren(tokenTree)) {
      return this.evalNode(tokenTree);
    }
    return tokenTree;
  }
  /**
 * 只要参数不是string类型，均抛出错误，此方法只能用于解析公式参数
 */
  parseString2(string: any) {
    if (string === undefined || string === null) {
      return false;
    }

    if (typeof string === "boolean" || typeof string === "number") {
      string = string.toString()
    }

    if (typeof string !== "string") {
      return false;
    }

    return string;
  }
  parseNumber2(string: any) {
    if (typeof string === "boolean") {
      string = Number(string);
    }

    if (Array.isArray(string)) {
      string = string.reduce((p, c) => p + Number(), 0);
    }

    if (!isNaN(string)) {
      return Number(string);
    }

    return false;
  }



  EvalNumber = {
    multiplier(x: number) {
      const parts = x.toString().split('.');

      return parts.length < 2 ? 1 : Math.pow(10, parts[1].length);
    },
    /**
 * 获取校正计算因子
 */
    correctionFactor(...args: number[]): number {
      return args.reduce((result, current) => {
        let currentFactor = this.multiplier(current)
        return currentFactor > result ? currentFactor : result
      }, 1)
    },

    add(...args: number[]): number {
      const factor = this.correctionFactor(...args);

      return args.reduce((result, number) => {
        return Math.round(number * factor) + result
      }, 0) / factor;
    },

    subtract(...args: number[]): number {
      const factor = this.correctionFactor(...args);

      return args.reduce((result, number) => {
        return Math.round(result * factor) - Math.round(number * factor)
      }) / factor;
    },

    multiply(...args: number[]): number {
      const factor = this.correctionFactor(...args);

      const length = args.length;

      return args.reduce((result, number) => {
        return Math.round(number * factor) * result
      }, 1) / Math.pow(factor, length);
    },

    divide(...args: number[]): number {
      const factor = this.correctionFactor(...args);

      return args.reduce((result, number) => {
        return Math.round(result * factor) / Math.round(number * factor)
      });
    },
    mod(...args: number[]): number {
      const factor = this.correctionFactor(...args);

      return args.reduce((result, number) => {
        return Math.round(result * factor) % Math.round(number * factor)
      });
    },
  }
  parseSimpleExpr(operator: string, left: any, right: any) {
    // operator  & = <> > < >= <= + - * /
    switch (operator) {
      case "&": {
        const l = this.parseString2(left);
        const r = this.parseString2(right);

        if (!isNotNullAndUndefined(l) || !isNotNullAndUndefined(r)) {
          return false
        }

        return {
          type: "STRING",
          value: l.toString().concat(r.toString())
        };
      }
      case "=":
        return {
          type: "BOOLEAN",
          // eslint-disable-next-line eqeqeq
          value: left == right
        };

      case "<>":
        return {
          type: "BOOLEAN",
          // eslint-disable-next-line eqeqeq
          value: left != right
        };
    }

    // 以下为数值运算，需要将值转换成数字。
    let l = this.parseNumber2(left);
    const r = this.parseNumber2(right);

    if (!isNotNullAndUndefined(l) || !isNotNullAndUndefined(r)) {
      return false
    }

    switch (operator) {
      case "+":
        return {
          type: "NUMBER",
          value: this.EvalNumber.add(l, r)
        };
      case "-":
        return {
          type: "NUMBER",
          value: this.EvalNumber.subtract(l, r)
        };
      case "*":
        return {
          type: "NUMBER",
          value: this.EvalNumber.multiply(l, r)
        };
      case "/":
        if (r === 0) {
          return false
        }
        return {
          type: "NUMBER",
          value: this.EvalNumber.divide(l, r)
        };
      case "%":
        if (r === 0) {
          return false
        }
        return {
          type: "NUMBER",
          value: this.EvalNumber.mod(l, r)
        };
      case ">":
        return {
          type: "BOOLEAN",
          value: l > r
        };
      case ">=":
        return {
          type: "BOOLEAN",
          value: l >= r
        };
      case "<":
        return {
          type: "BOOLEAN",
          value: l < r
        };
      case "<=":
        return {
          type: "BOOLEAN",
          value: l <= r
        };
    }

    return false
  }
  LAZY_EVAL_FUNCTIONS = new Set(['IF', 'IFERROR', 'AND', 'OR'])
  /**
   * 将节点解析求值为valueToken.
   * 若在解析过程中出现Error则直接返回ErrorToken
   */
  evalNode(tokenNode: any) {
    if ('value' in tokenNode) {
      return tokenNode
    }
    if (tokenNode.node.type === "OPERATOR") {
      const parsedChildren = tokenNode.children.map((item: any) => {
        if (this.hasChildren(item)) {
          return this.evalNode(item!);
        }
        return item;
      });

      const errorNode = parsedChildren.find((item: any) => (item).type === "ERROR")
      if (errorNode) {
        return errorNode
      }

      const leftNode = parsedChildren[0];
      const rightNode = parsedChildren[1];

      const result = this.parseSimpleExpr(
        tokenNode.node.value,
        leftNode.value,
        rightNode.value
      );
      return !result
        ? {
          type: "ERROR",
          msg: "自定义字段异常:请检查含有【" + tokenNode.node.value + "】操作符的表达式"
        }
        : result;
    } else {
      /** 已转为大写的函数名 */
      const functionName = tokenNode.node.value.toUpperCase();

      const children = tokenNode.children;

      const functionItem = formulaList.find((item: any) => item.name === functionName)
      if (!functionItem) {
        return {
          type: "ERROR",
          msg: `自定义字段异常:未知函数名${functionName}`,
        };
      }

      // 部分函数特殊处理, 直接将作为参数的ast node传入后进行延迟求值。
      // 这些函数直接返回ValueToken、 ErrorToken
      // TODO 后期可以考虑全部改为延迟求值
      if (this.LAZY_EVAL_FUNCTIONS.has(functionName)) {
        // TODO 因为evalNode方法是在this里面的，
        //      所以这里把this传进去. 后面如果从类里面提出来了可以去掉this

        const result = functionItem.handler(this, ...children)
        return result
      }

      // 非延迟求值, 直接求出全部参数值
      const parsedChildren = children.map((item: any) => {
        if (this.hasChildren(item!)) {
          return this.evalNode(item!);
        }
        return item;
      });

      const errorNode = parsedChildren.find((item: any) => item.type === "ERROR")
      if (errorNode) {
        return errorNode
      }

      const params = parsedChildren.map((item: any) => item.value)

      const result = functionItem.handler(...params)

      return !result
        ? {
          type: "ERROR",
          msg: "自定义字段异常:函数【" + functionName + "】出错了"
        }
        : {
          type: functionItem.returnType,
          value: result,
        };
    }
  }

  //创建字段解析公式
  parse(expression: any) {
    if (typeof expression !== 'string') {
      throw new Error(`formula expression should be a string parameter, but get [${typeof expression}]`)
    }
    this.expression = expression.startsWith('=') ? expression.slice(1) : expression
    try {
      while (this.index < this.expression.length) {
        this.parseToken()
      }
      return this.multipleOperator(this.token )
    } catch (error:any) {
      return {
        type: "ERROR",
        msg: `${error.message}`,
      };
    }
  }

  charAt(index = this.index) {
    return this.expression.charAt(index);
  }
  /** 处理回车和换行符, 考虑 \n 和 \n\r 还有制表符 \t */
  parseSpaces() {
    let char = this.charAt();

    while (char === " " || char === "\n" || char === "\r" || char === "\t") {
      char = this.charAt(++this.index);
    }
  }
  charCodeAt(index = this.index) {
    return this.expression.charCodeAt(index);
  }
  get currentExpr() {
    return this.expression.substring(this.index)
  }
  get lastToken() {
    return this.token[this.token.length - 1]
  }
  isBooleanTrue() {
    return this.currentExpr.match(/^TRUE/i) !== null;
  }
  isBooleanFalse() {
    return this.currentExpr.match(/^FALSE/i) !== null;
  }
  isDigit(charCode: number) {
    return charCode >= 48 && charCode <= 57; // 0...9
  }
  parseNumber() {
    let number = "";
    // 数字开头
    while (this.isDigit(this.charCodeAt())) {
      number += this.charAt(this.index++);
    }
    // '.'开头
    if (this.charCodeAt() === 46) {
      number += this.charAt(this.index++);
      while (this.isDigit(this.charCodeAt())) {
        number += this.charAt(this.index++);
      }
    }
    // 科学计数法
    let char = this.charAt();
    if (char === "e" || char === "E") {
      number += this.charAt(this.index++);
      char = this.charAt();
      if (char === "+" || char === "-") {
        number += this.charAt(this.index++);
      }
      while (this.isDigit(this.charCodeAt())) {
        number += this.charAt(this.index++);
      }
      // 如果e + - 后无数字，报错
      if (!this.isDigit(this.charCodeAt(this.index - 1))) {
        return {
          type: "ERROR",
          msg: `自定义字段异常:非法数字(${number}${this.charAt()})，缺少指数`,
        };
      }
    }

    return {
      type: "NUMBER",
      value: parseFloat(number)
    };
  }
  isLetter(charCode: number) {
    return (charCode >= 65 && charCode <= 90) || // A...Z
      (charCode >= 97 && charCode <= 122); // a...z
  }

  parseFunctionName() {
    const matchFunctionName = this.currentExpr.match(/^[a-z][a-z0-9]*([\._][a-z][a-z0-9]*)*\s*\(/i);

    if (matchFunctionName === null) {
      let str = this.currentExpr.match(/^[a-z][a-z0-9]*/i)![0]
      this.index += str.length
      return {
        type: "ERROR",
        msg: `自定义字段异常:未能识别独立的标识符【${str}】，请确认是否添加函数()标识`,
      }
    }
    let identifier = matchFunctionName[0];

    this.index += identifier.length;

    identifier = identifier.substring(0, identifier.length - 1)

    return {
      type: "FUNCTION",
      value: identifier
    };
  }

  includes<T extends U, U>(arr: ReadonlyArray<T>, item: U): item is T {
    return (arr as any).includes(item as T);
  }
  UNARY_OPERATORS = ["+", "-"] as const;
  /**
   * 当前token栈顶的token是运算式的一部分
   * @returns
   */
  isGroupBoundary() {
    return this.lastToken && "type" in this.lastToken && (["COMMA", "FUNCTION", "BRACKET"] as any).includes(this.lastToken.type);
  }
  isOperatorToken(token: any) {
    return token && ('type' in token) && token.type === 'OPERATOR'
  }

  isUnaryOperatorToken(token: any) {
    return token && ("type" in token) && token.type === "UNARY_OPERATOR"
  }
  getOperatorPrecedence(operator: any) {
    return this.PRECEDENCE[operator] || 0;
  }
  PRECEDENCE: any = {
    "&": 3,
    "<": 5,
    ">": 5,
    "=": 5,
    "<=": 5,
    ">=": 5,
    "<>": 5,
    "+": 7,
    "-": 7,
    "*": 9,
    "/": 9,
    "%": 9
  };
  isBracketToken(token: any) {
    return token && ('type' in token) && token.type === 'BRACKET'
  }
  isFunctionToken(token: any) {
    return token && ('type' in token) && token.type === 'FUNCTION'
  }
  /**
  * 处理一步运算式，要么三项（形如 3 + 1），要么两项（形如 -1）
  * @param stack
  */
  singleOperation(stack: any[]) {
    if (stack.length === 3) {
      const operatorToken = stack[1];
      const leftNode = stack[0];
      const rightNode = stack[2];

      if (this.isOperatorToken(operatorToken)) {

        if (
          this.isBracketToken(leftNode) ||
          this.isFunctionToken(leftNode) ||
          this.isBracketToken(rightNode) ||
          this.isFunctionToken(rightNode)
        ) {
          // 操作符两边是操作符或者函数名或者逗号
          return {
            type: 'ERROR',
            msg: `自定义字段异常:语法错误`,
          }
        }

        return {
          node: operatorToken,
          children: [leftNode as ChildNode, rightNode as ChildNode]
        }
      }

      return {
        type: "ERROR",
        msg: `无法识别的运算符："${'value' in operatorToken ? operatorToken.value : operatorToken.node.value}"`,
      };
    } else { // stack.length === 2
      console.warn('现在应该走到这里吗?')

      if (this.isOperatorToken(stack[0])) {
        if (stack[0].value !== "+" && stack[0].value !== "-") {
          return {
            type: "ERROR",
            msg: `自定义字段异常:未能将 ${stack[0].value} 识别为一元运算符`,
          };
        }

        if (this.isOperatorToken(stack[1])) {
          return {
            type: "ERROR",
            msg: `自定义字段异常:无法解析标识符【 ${stack[0].value + stack[1].value} 】`,
          };
        }
      } else {
        return {
          type: "ERROR",
          msg: `自定义字段异常:标识符【 ${"type" in stack[0] ? stack[0].value : stack[0].node.value} 】期望是 “+”或者“-”`,
        }
      }
      const node: any = stack[0];
      if (node.value === "-") {
        return {
          node,
          children: [
            {
              type: 'NUMBER',
              value: 0
            },
            stack[1] as ChildNode
          ]
        }
      }
      return stack[1] as ChildNode
    }
  }
  isErrToken(token: any) {
    return token && ('type' in token) && token.type === 'ERROR'
  }
  createNodeTree(node: any, leftNode: ChildNode, rightNode: ChildNode) {
    return {
      node,
      children: [leftNode, rightNode]
    }
  }
  createOperatorToken(operator: any) {
    return {
      type: 'OPERATOR',
      value: operator
    }
  }
  parseOperation(operator: any) {
    // 先尝试解析一元运算符
    if (this.includes(this.UNARY_OPERATORS, operator)) {
      /* 下列情况下，+ - 被解析为一元运算符

* 前面是左括号
* 前面是逗号
* 前面是任意运算符
* 前面没有任何值
*/
      if (
        !this.lastToken ||
        this.isGroupBoundary() ||
        this.isOperatorToken(this.lastToken) ||
        this.isUnaryOperatorToken(this.lastToken)
      ) {
        const unaryOpNode: any = {
          type: "UNARY_OPERATOR",
          value: operator
        };
        this.token.push(unaryOpNode)
        return
      }
    }

    const precedence = this.getOperatorPrecedence(operator);
    if (this.token.length > 1) {
      const stack: any[] = [];

      while (this.token.length && !this.isGroupBoundary()) {
        stack.unshift(this.token.pop()!);
      }
      // 一元表达式 +1， -2
      if (stack.length === 2) {
        const result = this.singleOperation(stack.splice(0));

        if (this.isErrToken(result)) {
          this.token.push(result);

          return;
        }
        stack.push(result);
      }
      if (stack.length > 2) {
        const preOperator = stack[stack.length - 2];

        if (this.isOperatorToken(preOperator)) {
          // 前一个运算符的优先级大于等于当前运算符，可先将前面的3个token转换为node
          if (this.getOperatorPrecedence(preOperator.value) >= precedence) {
            const innerStack: any[] = [];
            for (let i = 0; i < 3; i++) {
              innerStack.unshift(stack.pop()!);
            }

            while (stack.length) {
              this.token.push(stack.shift()!);
            }

            this.token.push(this.createNodeTree(innerStack[1], innerStack[0] as ChildNode, innerStack[2] as ChildNode));
          }
        } else {
          this.token.push({
            type: "ERROR",
            msg: `自定义字段异常:未识别运算符 "${('value' in preOperator) ? preOperator.value : preOperator.node.value}"`,
          });
          return;
        }

      }
      this.token = this.token.concat(stack);
    }
    this.token.push(this.createOperatorToken(operator));
  }
  SINGLE_QUOTE_CODE = 39; // single quote
  DOUBLE_QUOTE_CODE = 34; // double quotes
  parseString() {
    let str = "";
    const quote = this.charAt(this.index++);
    let closed = false;
    while (this.index < this.expression.length) {
      let char = this.charAt(this.index++);
      if (char === quote) {
        closed = true;
        break;
      } else if (char === "\\") {
        // Check for all of the common escape codes
        char = this.charAt(this.index++);
        switch (char) {
          case "n":
            str += "\n";
            break;
          case "r":
            str += "\r";
            break;
          case "t":
            str += "\t";
            break;
          case "b":
            str += "\b";
            break;
          case "f":
            str += "\f";
            break;
          case "v":
            str += "\x0B";
            break;
          default:
            str += char;
        }
      } else {
        str += char;
      }
    }

    if (!closed) {
      return {
        type: "ERROR",
        msg: `自定义字段异常:字符"${str}"缺少闭合括号`,
      };
    }

    return {
      type: "STRING",
      value: str
    };
  }
  OPEN_BRACKET_CODE = 40; // (
  CLOSE_BRACKET_CODE = 41; // )
  createBracketToken() {
    return {
      type: "BRACKET",
      value: "("
    }
  }
  /**
 * 计算stack栈的表达式结果
 * @param stack
 * @returns
 */
  multipleOperator(stack: any[]) {
    if (stack.length === 1) {
      const token = stack[0];

      if (this.isBracketToken(token) || this.isFunctionToken(token)) { // 错误：表达式或者组表达式只存在一个token，且token为 “（”，或者 函数名
        return {
          type: "ERROR",
          msg: `自定义字段异常:无法解析字符串${token.value}`,
        };
      }

      return token;
    }

    while (stack.length > 1) { // TODO 是否有等于2的情况
      const spliceLength =
        stack.length > 2
          ? 3 : stack.length === 2
            ? 2 : 0;

      const result = this.singleOperation(stack.splice(0 - spliceLength, spliceLength));

      if (this.isErrToken(result)) {
        return result;
      }

      stack.push(result);
    }

    return stack[0] as ChildNode;
  }
  isCommaToken(token: any) {
    return token && ("type" in token) && token.type === "COMMA"
  }
  createFunctionNode(node: any, ...rest: ChildNode[]) {
    return {
      node,
      children: rest
    }
  }
  parseCloseBracket() {
    let stack: any[] = []
    // stack 存储了前括号到后括号之前的所有token
    while (this.token.length && (!this.isBracketToken(this.lastToken) && !this.isFunctionToken(this.lastToken))) {
      stack.unshift(this.token.pop()!)
    }

    if (this.isBracketToken(this.lastToken) && (stack.length === 0 || this.token.length === 0)) {
      this.token.push({
        type: 'ERROR',
        msg: '未闭合括号',
      })
      return
    }

    if (this.isBracketToken(this.lastToken)) { // 闭合括号属于普通括号
      this.token.pop() // 删掉前括号 “（”
      this.token.push(this.multipleOperator(stack))
    } else { // 闭合括号属于函数
      if (stack.length) {
        let currentToken = stack[stack.length - 1]
        const tempStack: any[] = []

        // 注意如果括号属于函数，且存在多个参数值，只需要处理最后一个参数token，因为之前的参数在遇到逗号时均已处理完毕
        while (stack.length && !this.isCommaToken(currentToken)) {
          tempStack.unshift(stack.pop()!)

          currentToken = stack[stack.length - 1]
        }
        const result = this.multipleOperator(tempStack)
        // 组装functionTreeNode,注意组合过程中删除 CommaToken
        stack = stack.filter(item => !this.isCommaToken(item))
        stack.push(result)
      }
      const functionToken = this.token.pop();
      this.token.push(this.createFunctionNode(functionToken, ...(stack as ChildNode[])))
    }
  }
  COMMA_CODE = 44; // ,
  parseComma() {
    const stack: any[] = [];

    if (!this.lastToken) {
      this.token.push({
        type: "ERROR",
        msg: "自定义字段异常:不能以 “，” 作为公式起点",
      });
      return;
    }
    while (!this.isFunctionToken(this.lastToken) && !this.isCommaToken(this.lastToken)) {
      stack.unshift(this.token.pop()!);
    }
    if (!stack.length) { // 错误情景：函数名后面紧跟逗号或者两个逗号紧邻
      this.token.push({
        type: "ERROR",
        msg: "自定义字段异常:无法解析“,”之前的元素",
      });
      return;
    } else {
      this.token.push(this.multipleOperator(stack));
    }

    // 只有上一步的最后一个token不是错误token时，才允许追加逗号token
    if (!this.isErrToken(this.lastToken)) {
      this.token.push({
        type: "COMMA",
        value: ","
      });
    }

    this.index += 1
  }
  getVariable() { // 遇到[]了，或者[]和中间的变量名，返回: [variable]
    const end = this.currentExpr.indexOf(']') + 1
    const str = this.currentExpr.substring(0, end)
    return str
  }
  /**
* 读取未加引号的字符串，当走到parseToken最后一步时，认为是未加引号的string
*/
  readString() {
    const literal = "+-*/<>,";
    let value = "";
    while (!literal.includes(this.charAt(this.index)) && this.currentExpr.length) {
      value += this.charAt(this.index);
      this.index += 1;
    }
    return {
      type: "STRING",
      value
    };
  }
  isFunctionNode(token: any) {
    return token && ("node" in token) && token.node.type === "FUNCTION"
  }
  isValueToken(token: any) {
    return token && ("type" in token) && (["ARRAY", "NUMBER", "STRING", "BOOLEAN", "VARIABLE"] as any).includes(token.type)
  }
  isOperatorNode(token: any) {
    return token && ("node" in token) && token.node.type === "OPERATOR"
  }
  isChildNode(token: any): token is ChildNode {
    return token && (this.isOperatorNode(token) || this.isFunctionNode(token) || this.isValueToken(token))
  }
  parseToken() {
    //TODO 为保证一元运算符的正确性，不能提前return
    this.parseSpaces();
    const char = this.charAt();
    const charCode = this.charCodeAt();
    if (this.isBooleanTrue()) {
      this.index += 4;
      this.token.push({
        type: "BOOLEAN",
        value: true
      });
    } else if (this.isBooleanFalse()) {
      this.index += 5;
      this.token.push({
        type: "BOOLEAN",
        value: false
      });
    } else if (this.isDigit(charCode) || charCode === 46) {
      // 数字
      const numToken: any = this.parseNumber()
      this.token.push(numToken);
      // console.log("number token", ...this.token);
    } else if (this.isLetter(charCode) || char === "$") {
      let matchSheetStr: RegExpMatchArray | null | string = this.currentExpr.match(/^\$?[a-z0-9_]+!/i);

      if (matchSheetStr) {
        let sheetName = matchSheetStr[0].substring(0, matchSheetStr[0].length - 1)
        this.index += sheetName.length + 1;
      } else {
        matchSheetStr = ''
      }
      this.token.push(this.parseFunctionName());
      // 需要在处理 "(" 前调用
      // console.log("function token", ...this.token);
    } else if ((this.BINARY_OPERATORS as any).includes(char)) {
      // 二元 / 一元运算符
      this.index++;

      let current_op = char;
      if (this.currentExpr.startsWith("=") || this.currentExpr.startsWith(">")) { // >= <= <>
        current_op = `${char + this.currentExpr[0]}`;
        this.index++;
      }

      if (!(this.BINARY_OPERATORS as any).includes(current_op)) {
        return {
          type: "ERROR",
          msg: `自定义字段异常:无法识别运算符 ${current_op}`,
        };
      }
      this.parseOperation(current_op);
      // console.log("operation token", ...this.token);
    } else if (charCode === this.SINGLE_QUOTE_CODE || charCode === this.DOUBLE_QUOTE_CODE) {
      // 字符串
      this.token.push(this.parseString());
      // console.log("string token", ...this.token);
    } else if (charCode === this.OPEN_BRACKET_CODE) {
      // 括号 (
      this.index += 1;
      this.token.push(this.createBracketToken());
    } else if (charCode === this.CLOSE_BRACKET_CODE) {
      // 括号 )，需要将函数或在括号中的表达式求值
      this.parseCloseBracket();
      this.index++;
    } else if (charCode === this.COMMA_CODE) {
      // 逗号，遇到逗号需要回溯到token中的FUNCTION，与COMMA，将其中的运算算出值
      this.parseComma();
    } else if (char === '[') {
      // 中括号加[]中间的字母认为是变量
      const str = this.getVariable()
      this.index += str.length;
      this.token.push({
        type: "VARIABLE",
        value: str.replace(/[\[\]]/g, '')
      });
    } else { // 认为是未加引号的字符串
      this.token.push(this.readString());
    }
    //====================  处理一维运算符 ====================
    //! 需要保证上面没有return
    // 只有childToken才可以跟一维运算符折叠
    if (!this.isChildNode(this.lastToken)) {
      return
    }

    // 倒数第二个token是一元运算符, 则开始折叠
    const secondLastToken = this.token[this.token.length - 2];
    if (secondLastToken &&
      this.isUnaryOperatorToken(secondLastToken)
    ) {
      // 开始折叠
      let token = this.token.pop() as ChildNode
      while (this.lastToken && this.isUnaryOperatorToken(this.lastToken)) {
        let unaryOpToken = this.token.pop();

        token = {
          node: {
            type: "FUNCTION",
            value: unaryOpToken.value
          },
          children: [token]
        } as any
      }
      this.token.push(token)
    }
  }



  // 获取当前文档中的分组，然后匹配数据集，如果能匹配到再进行下一步处理
  handleGroupVsDataSet(editor: Editor, dataSet: any) {
    const groups = editor.getAllGroup();
    if (!groups.length) {
      return
    }
    // 统计当前文档中不同name的分组数量
    const nameVsGroup: any = {}
    const needDelGroup: any = []
    for (let i = 0, len = groups.length; i < len; i++) {
      const item = groups[i];
      const key = item.name;
      // 如果有分组，分组有名称，但是分组未对应任何数据集则将分组删除
      if (key && this.isEmpty(dataSet[key]) && this.isEmpty(dataSet[key + "_print"])) {
        needDelGroup.push(item)
        continue;
      }
      if (!nameVsGroup[key]) {
        nameVsGroup[key] = []
      }
      if (item.meta.repeat !== false) {
        nameVsGroup[key].push(item)
      }
    }
    editor.deleteGroup(needDelGroup);
    this.insertGroupAndSet(editor, dataSet, nameVsGroup)

  }
  isEmpty(value: any) {
    if (value === null || value === undefined) return true; // 检查是否为null或undefined
    if (Array.isArray(value)) return value.length === 0; // 检查是否为空数组
    if (typeof value === 'object') return Object.keys(value).length === 0; // 检查是否为空对象
    return false; // 其他情况
  }
  insertGroupAndSet(editor: Editor, dataSet: any, nameVsGroup: any) {
    for (let name in nameVsGroup) {
      if (!nameVsGroup[name].length) {
        continue;
      }
      let normalCount = 0;
      let printCount = 0;
      if (Array.isArray(dataSet[name])) {
        normalCount = dataSet[name].length;
      }
      if (Array.isArray(dataSet[name + "_print"])) {
        printCount = dataSet[name + "_print"].length;
      }
      // 如果都是数组
      if (normalCount && printCount) {
        // 将打印的分组数据合并到不带后缀的数据集中
        dataSet[name] = dataSet[name].concat(dataSet[name + "_print"])
      } else if (!normalCount && printCount) {
        // 如果只有打印是数组， 非打印续写的是null空对象
        dataSet[name] = [dataSet[name]].concat(dataSet[name + "_print"])
      }
      dataSet[name + "_print"] = []
      if (normalCount + printCount > nameVsGroup[name].length) {
        let addCount = normalCount + printCount - nameVsGroup[name].length;
        // 将光标定位到最后一个分组，然后copy其中的数据，然后在其下方插入一个新分组
        const lastGroup = nameVsGroup[name][nameVsGroup[name].length - 1];
        editor.locationToGroupById(lastGroup.id);
        const rawData = Group.getRawData(editor, lastGroup)
        for (let i = 0; i < addCount; i++) {
          const newGroup = editor.insertGroup();
          if (newGroup) {
            newGroup.new_page = lastGroup.new_page
            newGroup.name = name;
            editor.insertTemplateData(rawData)
          }
        }
      }
      if (printCount) {
        const groups = editor.selection.getGroupsByName(name)
        groups.slice(groups.length - printCount).forEach((g) => {
          g.meta.print = true;
        })
      }
    }

  }

  // 处理系统变量
  handleSystemVar(editor: Editor, isClear: boolean) {
    const systemVal: any = system_variables;
    const pageCount = String(editor.pages.length);
    const systemTime = formatDate(new Date())
    const needUpdateFields: any = []
    for (const key in systemVal) {
      // 序号要单独处理
      if (key === "serial_number") {
        continue;
      }
      const name = systemVal[key]
      const ffs = editor.getFieldsByName(name);
      for (let i = 0, len = ffs.length; i < len; i++) {
        const f = ffs[i];
        // 根据f要知道在第几页， 并且页脚中的内容
        if (key === "page_number") {
          let needContainer: any
          if (!f.cell.parent && isRow(f.rows[0])) {
            needContainer = f.rows[0];
          }
          if (f.cell.parent) {
            needContainer = f.cell.parent;
          }
          if (!needContainer) {
            continue
          }
          f.setNewText(String(needContainer.page_number))
        } else if (key === "page_count") {
          f.setNewText(pageCount)
        } else if (key === "system_time") {
          f.setNewText(systemTime)
        }
        if (isClear) {
          f.setNewText("")
        }
        needUpdateFields.push(f)
      }
    }

    const serialNumName = systemVal["serial_number"];
    let serialNumFields = editor.getFieldsByName(serialNumName);
    let lastSf = null;
    const tableFieldsMap: any = {}
    for (let i = 0, len = serialNumFields.length; i < len; i++) {
      const sf = serialNumFields[i];
      const tableId = sf.cell.parent ? sf.cell.parent.id : ""
      if (!tableFieldsMap[tableId]) {
        tableFieldsMap[tableId] = [];
      }
      tableFieldsMap[tableId].push(sf)
      if (!sf.cell.parent) {
        continue
      }
      if (lastSf !== sf.cell.parent) {
        editor.locatePathInField(sf)
        let newField = this.swapCell(editor, sf.cell, sf.cell.parent, serialNumName)
        while (newField && newField.cell.parent) {
          tableFieldsMap[tableId].push(newField)
          newField = this.swapCell(editor, newField.cell, newField.cell.parent, serialNumName)
        }
      }
      lastSf = sf.cell.parent;
    }
    for (const tableId in tableFieldsMap) {
      const fields = tableFieldsMap[tableId]
      for (let i = 0, len = fields.length; i < len; i++) {
        const field = fields[i];
        field.setNewText(String(i + 1))
        needUpdateFields.push(field)
      }
    }
    this.updateFieldTextAop(editor,needUpdateFields)

  }
  // 用于对更新前和更新后处理
  updateFieldTextAop(editor:Editor,fields:XField[]){
    const fields1 = [];
    const fields2 = [];
    for (const field of fields) {
      if (field.meta?.name === "chart") {
        fields2.push(field);
      } else {
        fields1.push(field);
      }
    }
    editor.updateFieldText({ fields: fields1 })
    if (fields2.length) {
      for (const f of fields2) {
        const image = f.children[0] as Image;
        // 这里能调用 editor.updateFieldText 就能调用 replaceFieldsImage 我在这里利用 echarts 获取一个图表的 url 就可以了
        const editorEchartsContainer = document.createElement("div");
        const width = image?.width || 200;
        const height = image?.height || 150;
        editorEchartsContainer.style.width =  900 + "px";
        editorEchartsContainer.style.height = 600 + "px";
        const myChart = echarts.init(editorEchartsContainer);
        
        const xAxisData = image.meta.xAxisData || "";
        const yAxisData = image.meta.yAxisData || [];
        const series = yAxisData.map((key: any) => ({
          type: image.meta.chartType || "bar",
          name: key,
          encode: { 
            x: xAxisData, 
            y: key // y 轴使用选中的字段
          }
        }));
        myChart.setOption({
          animation: false,
          dataset: {
            source: f.meta.chartData || []
          },
          legend: {
            top: 'bottom' // 图例位置
          },
          xAxis: {
            type: 'category',
            axisLabel: { interval: 0 }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value}' // 根据实际单位调整
            }
          },
          series
        })
        const url = myChart.getDataURL({
          type: 'png', // 图片类型，可以是 'png', 'jpeg' 等
          backgroundColor: '#fff' // 图片背景色，默认为 '#fff'
        });
        editor.replaceFieldsImage([f], { src: url, width, height });
        f.meta.chartData = undefined;
      }
    }
    


    // 收集下更新过的field
    this.needUpdateFields = this.needUpdateFields.concat(fields)
  }

  // 因为开始修改了纸张尺寸，所以该逻辑要放在最后，不然无法计算换行
  handleFieldsCharacterSize(editor:Editor){
    const fields = this.needUpdateFields;
    for (let i = 0 ,len = fields.length; i < len; i++) {
      const f = fields[i];
      editor.handleFieldCharacterSize(f)
    }
    this.needUpdateFields = []
  }

  fillFieldCore(editor: Editor, waitingForReplaceFields: XField[], f: XField, item: any, isClear: boolean, jsonData: any) {
    if (item.value === undefined || item.value === null) {
      return
    }
    if (f.type === "box") {
      if (isClear) {
        (f as BoxField).parent_box.clearBoxChecked();
      } else {
        (f as BoxField).updateGroupCheckedByValue(item.value);
      }
      return
    }
    if (typeof item.value === "string" && (item.value.startsWith("http") || isBase64Image(item.value)) || f.meta.qrCodeType || (f.meta.placeholder && isBase64Image(item.value))) {
      // 首先获取当前文本域中是否存在图片，如果存在则按照图片宽高进行设置
      const image = f.getAllElements().find(ele => isImage(ele));
      let needWidth, needHeight;
      if (image) {
        needWidth = image.width;
        needHeight = image.height;
      } else {
        if (f.meta.placeImageWh) {
          needWidth = f.meta.placeImageWh[0];
          needHeight = f.meta.placeImageWh[1];
        }
      }
      let src = item.value;
      if (isClear) {
        const showTextMap: any = {
          "qrcode": "二维码",
          "barcode": "条形码",
        };
        let showText = "图片位"
        const showType = f.meta.qrCodeType;
        if (showType) {
          if (showTextMap[showType]) {
            showText = showTextMap[showType]
          }
        }
        src = createImagePlaceholder(showText);
      } else if (f.meta.qrCodeType) {
        let meta = f.meta;
        if (isImage(image)) {
          meta = image.meta;
        }
        const infoStr = meta.qrCodeInfo;
        const infoStrSplit: any = this.splitFiledStringArr(infoStr);
        let codeText = "";
        for (let j = 0; j < infoStrSplit?.length; j++) {
          const fieldStr = infoStrSplit[j];
          if (fieldStr.startsWith("[") && fieldStr.endsWith("]")) {
            const fStrName = fieldStr.replace("[", "").replace("]", "")
            const selItem = jsonData.find((it: any) => it.name === fStrName)
            if (selItem) {
              codeText += selItem.value ?? ""
            }
          } else {
            codeText += fieldStr
          }
        }
        if (codeText) {
          src = getBarcodeOrQrCodeSrc(codeText, {
            type: meta.qrCodeType, options: {
              format: meta.barcodeFormat,
              displayValue: meta.displayValue === "show" ? true : false
            }
          })
        }
      }
      if (item && src) {
        const meta = (isImage(image) && f.meta.placeholder) ? image.meta : f.meta
        isClear ? meta.placeholder = true : meta.placeholder = false;
        editor.replaceFieldsImage([f], {
          src,
          width: needWidth ? needWidth : item.width ?? f.meta.ori_width,
          height: needHeight ? needHeight : item.height ?? f.meta.ori_height,
          meta
        });
      } else {
        f.setNewText(" ");
        f.meta.ori_width = needWidth ? needWidth : item.width ?? f.meta.ori_width
        f.meta.ori_height = needHeight ? needHeight : item.height ?? f.meta.ori_height
        f.meta = isImage(image) ? image.meta : {};
        waitingForReplaceFields.push(f)
      }
    } else {
      if (isClear) {
        f.setNewText("");
        waitingForReplaceFields.push(f);
      } else {
        if (item.value !== "") {
          let value = this.handleFieldValue(f, item);
          value = this.handleValue(value);
          f.setNewText(value);
          waitingForReplaceFields.push(f);
        }
      }

    }
    return waitingForReplaceFields;
  }
  swapCell(editor: Editor, focusCell: Cell, table: Table, name: string) {
    let belowCell = table.children.find((cell) => {
      return cell.top === focusCell.bottom && cell.left === focusCell.left && cell.right === focusCell.right
    })
    if (belowCell && belowCell.getStr() === "\n") {
      const copyCell = focusCell.copy(table);
      editor.swapCells(belowCell, copyCell);
      // 此时将focusCell内容拷贝到belowCell中，并且将光标置于belowCell中
      const cellFields = editor.getFieldsByName(name, belowCell);
      if (!cellFields[0]) {
        return false;
      }
      editor.locatePathInField(cellFields[0]);
      return cellFields[0];
    } else {
      return false;
    }
  }

  handleAutoFillTableField(editor: Editor, fields: any, item: any, isClear: boolean, jsonData: any) {
    const { name, value } = item;
    const normalFields = [];
    let tableFields: any = [];

    for (let i = 0, len = fields.length; i < len; i++) {
      const field = fields[i];
      if (field.cell.parent) {
        tableFields.push(field);
      } else {
        normalFields.push(field);
      }
    }
    let wfFields: XField[] = []
    for (let i = 0, len = normalFields.length; i < len; i++) {
      const f = normalFields[i];
      this.fillFieldCore(editor, wfFields, f, { name: item.name, value: item.value[i] }, isClear, jsonData)
      if (f.meta && f.meta.fillLimit === "haltSubsequent" && item.value.length === 1) {
        break;
      }
    }
    
    this.updateFieldTextAop(editor,wfFields)
    // 如果表格中的文本域只有一个则判定为自动扩展，如果大于一个则按数量填充，此时将光标定位到表格的最后一个文本域中开始追加行数，追加的行数为两者之差
    let isAppendRow = false;
    let table = null;
    const ori_insert_row_carry_data = editor.config.insert_row_carry_data;
    editor.config.insert_row_carry_data = 0;

    if (tableFields.length === 1 && value.length && tableFields[0].meta.extend !== false) {
      editor.locatePathInField(tableFields[0]);
      for (let i = 0, len = value.length - tableFields.length; i < len; i++) {
        let focusCell = editor.selection.getFocusCell()!
        table = focusCell.parent!;
        const res = this.swapCell(editor, focusCell, table, name)
        if (!res && !this.appendRowTable[table.id]) {
          editor.locatePathInField(tableFields[0]);
          focusCell = editor.selection.getFocusCell()!
          editor.addRowOrColInTbl(3);
          this.swapCell(editor, focusCell, table, name)
          isAppendRow = true;
        }
      }
      editor.config.insert_row_carry_data = ori_insert_row_carry_data;
      if (isAppendRow && table) {
        this.appendRowTable[table.id] = isAppendRow;
      }
      // 此时再重新获取表格内文本域进行内容替换
      tableFields = editor.getFieldsByName(name).filter(f => f.cell.parent)
    }
    let wfTableFields: XField[] = []
    const alreadyHandleCells: any = [];
    for (let i = 0, len = tableFields.length; i < len; i++) {
      const f = tableFields[i];
      const cell = f.cell;
      this.fillFieldCore(editor, wfTableFields, f, { name: item.name, value: item.value[i] }, isClear, jsonData)
      if (alreadyHandleCells.some((val: Cell) => val === cell)) {
        continue;
      }
      // 因为是按顺序自上而下填充， 此处我们先判断这个文本域是否存在合并属性，
      // 如果存在再判断下方单元格内容与当前单元格内容是否完全一致，
      // 完全一致则列入需合并单元格组
      // 此时将下方单元格存储（主要用于合并前清空用）
      const pushNeedHandleCell = (curField: any, curCell: Cell) => {
        if (curField.meta.merge) {
          const downCell = curCell.getAdjacentCell("down");
          if (downCell) {
            const downField = downCell.fields.find((val: any) => { return val.meta.merge && val.name === curField.name })
            if (downField) {
              if (!alreadyHandleCells.some((val: Cell) => val === curCell)) {
                alreadyHandleCells.push(curCell);
              }
              alreadyHandleCells.push(downCell);
              pushNeedHandleCell(downField, downCell)
            }
          }
        }
      }
      pushNeedHandleCell(f, cell)
    }
    this.updateFieldTextAop(editor,wfTableFields)

    // 一下为处理表格内字段值相同时合并单元格的逻辑
    let groupHandleCells: any[] = [];

    for (let i = 0, len = alreadyHandleCells.length; i < len; i++) {
      const cell = alreadyHandleCells[i];
      let nextCell = alreadyHandleCells[i + 1];

      // 开始新的分组
      groupHandleCells.push(cell);

      // 检查后续单元格是否与当前单元格内容一致，并加入分组
      while (nextCell && nextCell.getStr() === cell.getStr()) {
        groupHandleCells.push(nextCell);
        i++;  // 移动到下一个单元格
        nextCell = alreadyHandleCells[i + 1];  // 更新 nextCell
      }

      // 处理分组：清空除了第一个单元格外的内容，并合并单元格
      if (groupHandleCells.length > 1) {
        for (let j = 1; j < groupHandleCells.length; j++) {
          groupHandleCells[j].clear();
        }
        Table.mergeCells(editor, groupHandleCells);
      }

      // 重置当前分组
      groupHandleCells = [];
    }

  }
  splitFiledStringArr(inputString: string) {
    if (!inputString) {
      return [""]
    }
    // 使用正则表达式匹配字符串中的模式
    const pattern = /\[[^\]]*\]|[^[\]]+/g;
    const result = inputString.match(pattern);
    return result;
  }
  handleFieldValue(f: XField, item: any) {
    let value = item.value;
    if (value === undefined || value === null) {
      value = ""
    }
    // 增加非法base64校验，防止编辑器卡死
    const valLen = String(value).length
    if (String(value).startsWith("data") && valLen > 1000) {
      return "格式错误"
    }
    if (valLen > 3000) {
      return "字符串超长"
    }
    let dict = f.source_list;
    if (dict.length || typeof value === "object") {
      if (f.multi_select === 1) {
        const separatorArray = ["，", "；", "、", "%"];
        let val = item.value;
        // 字符串中如果存在英文逗号，则也处理成数组
        if (typeof item.value === "string" && item.value.indexOf(",") > -1) {
          val = item.value.split(",");
        }
        if (!Array.isArray(val)) {
          val = [String(val)];
        }
        const vals = dict.filter((ele: any) => val.includes(String(ele.value))).map(e => e.text);
        value = vals.join(separatorArray[f.separator]);
      } else {
        const fItem = dict.find((ele: any) => String(ele.value) === String(value));
        if (fItem) {
          value = fItem.text;
        } else {
          const anyTextItem = dict.find((ele: any) => ele.value === "*")
          if (anyTextItem) {
            value = anyTextItem.text;
          }
        }
      }
    }
    return typeof value === "object" ? value.value : value;
  }
  initTableMustParams(item: any) {
    if (!Array.isArray(item.repeatColCells)) {
      item.repeatColCells = [];
    }
    if (!Array.isArray(item.repeatRowCells)) {
      item.repeatRowCells = [];
    }
  }
  insertTableFillContent(editor: Editor, allTables: Table[], item: any) {
    let table;
    if (item.name) {
      table = editor.getTablesByName(item.name)[0];
    } else {
      table = allTables.shift();
    }
    if (!table) return;
    this.initTableMustParams(item);
    editor.deleteTbl(table);
    let cols = 1;
    let rows = 1;
    let allTableCount = 1;
    let lastTableCols;
    let lastTableRows;
    // fillType 为 1时，则是按列填充, 首先进行矩阵行列变换
    const contentCells = item.contentCells;
    if (item.fillType === 1) {
      const repeatCols = item.repeatColCells.length
        ? item.repeatColCells[0].length
        : 0;
      const contentMaxCols = item.maxCols - repeatCols;
      const allContentColCount = contentCells.length; // 共多少列
      allTableCount = Math.ceil(allContentColCount / contentMaxCols);
      // 查看重复列， 如果存在 则可判断共需要拆成几个表格
      if (item.repeatColCells.length) {
        rows = item.repeatColCells.length + item.repeatRowCells.length;
      } else {
        // 不存在重复列的情况下，行按照内容最多的行计算
        contentCells.forEach((c: any) => {
          rows = Math.max(c.length, rows);
        });
      }
      lastTableRows = rows;
      if (allContentColCount >= contentMaxCols) {
        cols = lastTableCols = contentMaxCols;
        allTableCount = Math.max(
          allTableCount,
          Math.ceil(allContentColCount % contentMaxCols)
        );
        if (allTableCount > 1 && allContentColCount % cols) {
          lastTableCols = allContentColCount % cols;
        }
        cols += repeatCols;
        lastTableCols += repeatCols;
      } else {
        cols = lastTableCols = allContentColCount + repeatCols;
      }
    } else {
      const repeatCols = item.repeatColCells.length
        ? item.repeatColCells[0].length
        : 0;
      contentCells.forEach((c: any) => {
        cols = Math.max(c.length, cols);
      });
      if (item.maxCols) {
        // 当指定最大列数，剩余内容丢弃
        cols = Math.min(cols + repeatCols, item.maxCols);
      } else {
        cols += repeatCols;
        rows = lastTableRows =
          item.repeatRowCells.length + item.contentCells.length;
      }
      lastTableCols = cols;
    }
    let cycCells = [...item.contentCells];
    for (let i = 0; i < allTableCount; i++) {
      if (i === allTableCount - 1) {
        cols = lastTableCols;
        rows = lastTableRows;
      }
      let newTable;
      if (i === 0) {
        newTable = editor.insertTable(rows, cols, { name: "AutoTable" });
      } else {
        newTable = editor.insertTable(rows, cols, {
          name: "AutoTable",
          newPage: true,
          skipMode: SkipMode.ROW
        });
      }
      if (!isTable(newTable)) continue;
      if (item.fixedHeaderNum) {
        newTable.fixed_table_header_num = item.fixedHeaderNum;
      }
      this.handleTableColSize(newTable, item);
      cycCells = this.fillTableCellContent(editor, newTable, item, cycCells);
      // 因为合并后
      this.handleMergeCell(editor, newTable, item);
      // 插入表格后将光标置于表格下方段落开头
      editor.selection.setCursorPosition([newTable.cell_index + 1, 0]);
      editor.refreshDocument();
    }
  }
  fillTableCellContent(editor: Editor, table: Table, item: any, cycCells: any) {
    if (!table) return;
    if (item.repeatRowCells) {
      for (let i = 0; i < item.repeatRowCells.length; i++) {
        const cells = table.getCellsByRowIndex(i);
        const rawCells = item.repeatRowCells[i];
        rawCells.forEach((c: any, index: number) => {
          if (cells[index]) {
            const modelCell = cells[index];
            this.fillCell(editor, modelCell, c, table);
          }
        });
      }
    }
    if (item.repeatColCells) {
      const offset = item.repeatRowCells.length;
      for (let i = 0; i < item.repeatColCells.length; i++) {
        const cells = table.getCellsByRowIndex(i + offset);
        const rawCells = item.repeatColCells[i];
        rawCells.forEach((c: any, index: number) => {
          if (cells[index]) {
            const modelCell = cells[index];
            this.fillCell(editor, modelCell, c, table);
          }
        });
      }
    }
    if (item.fillType === 1) {
      if (cycCells) {
        const rowOffset = item.repeatRowCells.length;
        const colOffset = item.repeatColCells.length
          ? item.repeatColCells[0].length
          : 0;
        for (let i = 0; i < table.col_size.length; i++) {
          const cells = table.getCellsByColIndex(i + colOffset);
          cells.forEach((c, index) => {
            if (index >= rowOffset) {
              const rawCell = cycCells[i][index - rowOffset];
              if (rawCell) {
                this.fillCell(editor, c, rawCell, table);
              }
            }
          });
        }
        return cycCells.slice(table.col_size.length - 1, cycCells.length);
      }
    } else {
      if (cycCells) {
        const rowOffset = item.repeatRowCells.length;
        const colOffset = item.repeatColCells.length
          ? item.repeatColCells[0].length
          : 0;
        for (let i = 0; i < table.row_size.length; i++) {
          const cells = table.getCellsByRowIndex(i + rowOffset);
          cells.forEach((c, index) => {
            if (index >= colOffset) {
              const rawCell = cycCells[i][index - rowOffset];
              if (rawCell) {
                this.fillCell(editor, c, rawCell, table);
              }
            }
          });
        }
        return cycCells.slice(table.row_size.length - 1, cycCells.length);
      }
    }
  }
  fillCell(editor: Editor, modelCell: Cell, rawCell: any, table: Table) {
    this.handleCellStyleSet(modelCell, rawCell.style);
    if (rawCell.content) {
      editor.selection.setCursorPosition([
        table.cell_index,
        modelCell.index,
        0,
        0
      ]);
      this.toggleCellBorder(editor, modelCell, rawCell, table);
      rawCell.content.forEach((cc: any) => {
        editor.contextState.resetFontState();
        if (cc.type === "text") {
          this.handleTextStyle(editor, cc.style);
          editor.insertText(cc.value);
        } else if (cc.type === "checkbox" || cc.type === "radio") {
          const widget = editor.insertSimpleWidget(cc.type) as Widget;
          widget.selected = !!cc.value;
        } else if (cc.type === "raw") {
          editor.insertTemplateData(cc.value);
        } else if (cc.type === "field") {
          const newField = editor.insertField({ name: cc.name })
          if (isField(newField)) {
            if (isBase64Image(cc.value)) {
              editor.replaceFieldsImage([newField], {
                src: cc.value,
                width: cc.width,
                height: cc.height,
                meta: cc.meta
              })
              editor.caret_move("right");
            } else if (cc.value === 0 || cc.value) {
              newField.setNewText(this.handleValue(cc.value));
              this.updateFieldTextAop(editor,[newField] )
              editor.caret_move("right");
            }
          }
        }
      });
    }
  }
  handleValue(val: any) {
    if (val === "" || val === null || val === undefined) {
      return " "
    } else {
      return val;
    }
  }
  handleTextStyle(editor: Editor, style: any) {
    if (!style) return;
    const newFont: Font = editor.createElement("font", style) as Font;
    editor.contextState.setFontState(newFont);
  }
  handleCellStyleSet(modelCell: Cell, style: any) {
    if (!style) return;
    const firstPara = modelCell.paragraph[0] as Paragraph;
    if (String(style.align).indexOf("center") > -1) {
      firstPara.align = "center";
    }
    if (String(style.align).indexOf("dispersed") > -1) {
      firstPara.align = "dispersed";
    }
    if (String(style.align).indexOf("left") > -1) {
      firstPara.align = "left";
    }
    if (String(style.align).indexOf("right") > -1) {
      firstPara.align = "right";
    }
    if (String(style.align).indexOf("middle") > -1) {
      modelCell.vertical_align = VerticalAlign.CENTER;
    }
    if (String(style.align).indexOf("top") > -1) {
      modelCell.vertical_align = VerticalAlign.TOP;
    }
    if (String(style.align).indexOf("bottom") > -1) {
      modelCell.vertical_align = VerticalAlign.BOTTOM;
    }
  }
  toggleCellBorder(editor: Editor, modelCell: Cell, rawCell: any, table: Table) {
    if (!rawCell.toggleBorder) return;
    const { left, right, top, bottom } = rawCell.toggleBorder;
    const setBorderToggle = () => {
      if (left) {
        editor.cotrolTableLine("left_line");
      }
      if (right) {
        editor.cotrolTableLine("right_line");
      }
      if (top) {
        editor.cotrolTableLine("top_line");
      }
      if (bottom) {
        editor.cotrolTableLine("under_line");
      }
    };
    const oriFocus = [...editor.selection.focus];
    setBorderToggle();
    if (rawCell.rowSpan) {
      // 从当前单元格往下走
      for (let i = 1; i < rawCell.rowSpan; i++) {
        const cell = table.getCellByPosition(
          modelCell.position[0] + i,
          modelCell.position[1]
        );
        if (!cell) {
          continue;
        }
        editor.selection.setCursorPosition([
          table.cell_index,
          cell.index,
          0,
          0
        ]);
        setBorderToggle();
      }
    }
    if (rawCell.colSpan) {
      // 从当前单元格往右走
      for (let i = 1; i < rawCell.colSpan; i++) {
        const cell = table.getCellByPosition(
          modelCell.position[0],
          modelCell.position[1] + i
        );
        if (!cell) {
          continue;
        }
        editor.selection.setCursorPosition([
          table.cell_index,
          cell.index,
          0,
          0
        ]);
        setBorderToggle();
      }
    }
    editor.selection.setCursorPosition(oriFocus);
  }
  handleMergeCell(editor: Editor, table: Table, item: any) {
    if (item.repeatRowCells) {
      for (let i = 0; i < item.repeatRowCells.length; i++) {
        const rawCells = item.repeatRowCells[i];
        rawCells.forEach((rawCell: any) => {
          this.mergeCellByRaw(editor, item, table, rawCell, i);
        });
      }
    }
    if (item.repeatColCells) {
      for (let i = 0; i < item.repeatColCells.length; i++) {
        const rawCells = item.repeatColCells[i];
        rawCells.forEach((rawCell: any, index: number) => {
          this.mergeCellByRaw(editor, item, table, rawCell, index);
        });
      }
    }
  }
  mergeCellByRaw(editor: Editor, item: any, table: Table, rawCell: any, i: number) {
    const rowOffset = item.repeatRowCells.length;
    const selectedCellsPath = [];
    if (rawCell.colSpan) {
      for (let j = 0; j < rawCell.colSpan; j++) {
        const cell = table.getCellByPosition(i, j);
        if (!cell) {
          continue;
        }
        selectedCellsPath.push([table.cell_index, cell.index, 0, 0]);
      }
      editor.selection.selected_cells_path = selectedCellsPath;
      editor.mergeCell();
      editor.selection.clearSelectedInfo();
    }
    if (rawCell.rowSpan) {
      for (let j = 0; j < rawCell.rowSpan; j++) {
        const cell = table.getCellByPosition(j + rowOffset, i);
        if (!cell) {
          continue;
        }
        selectedCellsPath.push([table.cell_index, cell.index, 0, 0]);
      }
      editor.selection.selected_cells_path = selectedCellsPath;
      editor.mergeCell();
      editor.selection.clearSelectedInfo();
    }
  }
  handleTableColSize(table: Table, item: any) {
    if (!item.colSize || !item.colSize.length) {
      return;
    }
    const oriColSize = [...table.col_size];
    const totalWidth = oriColSize.reduce((total, current) => total + current);
    const newColSize: number[] = [];
    oriColSize.forEach((num, index) => {
      if (item.colSize && item.colSize[index]) {
        newColSize.push(Math.round((item.colSize[index] * totalWidth) / 100));
      } else {
        newColSize.push(
          (totalWidth -
            newColSize.reduce((total, current) => total + current)) /
          (oriColSize.length - newColSize.length)
        );
      }
    });
    table.col_size = newColSize;
  }
  // 处理数据集成能够正常填充的json数据
  handleDataSet2FillJson(dataSet: any) {
    const fillJson: any = [];
    const tableJson: any = [];
    for (const dataSetId in dataSet) {
      if (Array.isArray(dataSet[dataSetId]) && dataSet[dataSetId].length > 1) {
        this.handleTableDataSet(tableJson, dataSet[dataSetId], dataSetId);
      } else {
        if(typeof dataSet[dataSetId] === "object"){
          // 第一层一定是数据集 , 只处理是数据和json对象的情况
          this.handCycleDataSet(fillJson, dataSet[dataSetId], dataSetId);
        }
      }
    }
    // 将tableJson中的数据进行排序，然后将排序后的数组合并至fillJson中
    tableJson.sort((a: any, b: any) => b.value.length - a.value.length)
    fillJson.push(...tableJson)
    return fillJson;
  }
  transformJsonArray(jsonArray: any) {
    const obj = jsonArray.reduce((result: any, item: any) => {
      if (!item) {
        return result
      }
      Object.keys(item).forEach(key => {
        if (!result[key]) {
          result[key] = [];
        }
        result[key].push(item[key]);
      });
      return result;
    }, {});
    // 如果数组末尾是空字符串或者null或者undefined则去掉末尾项
    Object.keys(obj).forEach(key => {
      while (obj[key].length > 0 && (obj[key][obj[key].length - 1] === "" || obj[key][obj[key].length - 1] === null || obj[key][obj[key].length - 1] === undefined)) {
        obj[key].pop();
      }
    });
    return obj;
  }

  handleTableDataSet(fillJson: any, dataSet: any, pCode: string) {
    if (!dataSet) {
      return
    }
    const resJson = this.transformJsonArray(dataSet);
    for (const key in resJson) {
      fillJson.push({
        name: pCode + ">" + key,
        value: resJson[key]
      })
    }
  }

  handCycleDataSet(fillJson: any, dataSet: any, pCode: string) {
    if (Array.isArray(dataSet)) {
      dataSet = dataSet[0];
    }
    if (dataSet) {
      for (const key in dataSet) {
        if (dataSet[key] === undefined || dataSet[key] === null) {
          continue;
        }
        if (
          (key !== "children" && Array.isArray(dataSet[key])) ||
          typeof dataSet[key] !== "object"
        ) {
          fillJson.push({
            name: pCode ? pCode + ">" + key : key,
            value: dataSet[key]
          });
        } else {
          const newPCode = pCode ? pCode + ">" + key : key;
          if (Array.isArray(dataSet[key].children)) {
            if (dataSet[key].value !== null && dataSet[key].value !== undefined) {
              fillJson.push({
                name: newPCode,
                value: dataSet[key].value,
                dict: dataSet[key].children
              });
            }
          }
        }
      }
    }
  }
}
