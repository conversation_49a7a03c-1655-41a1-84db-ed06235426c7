import Editor from "./Editor";
import XField from "./XField";
import <PERSON>Field from "./BoxField";
import { system_variables } from "./Config";
import createImagePlaceholder , {
  formatDate ,
  getBarcodeOrQrCodeSrc ,
  isBase64Image ,
  isField ,
  isRow ,
  isTable , serializeCopy , uuid
} from "./Utils";
import Cell from "./Cell";
import Table from "./Table";
import { SkipMode , VerticalAlign } from "./Constant";
import Widget from "./Widget";
import Font from "./Font";
import Paragraph from "./Paragraph";
import { isBoxField , isImage } from "./Helper";
import Group from "./Groups";
import EditorHelper from "./EditorHelper";

export default class AutoFill {
        appendRowTable:any =  {}
        resetInitParam(){
          this.appendRowTable = {}
        }
        // 根据传入的数据替换文本
        fillContentByJson(editor: Editor, rawData: any, jsonData: any, jsonType: string, isClear: boolean) {
          this.resetInitParam();
          if (rawData) {
            editor.reInitRawByConfig(rawData);
            editor.refreshDocument();
          }
          // 因为跨页单元格不能合并，所以此处使用临时办法先将纸张尺寸修改，然后再进行还原处理
          const oriPageInfo = {
            page_size_type:editor.config.page_size_type,
            customPageSize:{
              width:editor.config.customPageSize.width,
              height:editor.config.customPageSize.height
            }
          };
          editor.config.page_size_type = "custom";
          editor.config.customPageSize = {
            width:1000,
            height:20000
          }
          if(!isClear && jsonType === "dataSet"){
            // 处理数据集时先拷贝，其中有处理原数据数据的地方
            jsonData = serializeCopy(jsonData)
            this.handleScript(jsonData,editor.document_meta.BeforePostRecordScript);
            this.handleScript(jsonData,editor.document_meta.FormatScript);
            this.handleGroupVsDataSet(editor,jsonData)
            this.dynamicInsertDict(editor,jsonData)
          }
          if (jsonType === "dataSet") {
            jsonData = this.handleDataSet2FillJson(jsonData);
          }
          if (typeof jsonData === "string") {
            jsonData = JSON.parse(jsonData);
          }
          // 因为肯定要用到文档中的所有文本域，所以此时我们将所有的文本域先全部拿出来，因为使用时是通过name匹配，所以我们按name进行归类
          let allFields = editor.getAllFields();
          const nameVsFields:any = {}
          const beCascadeFields:any = []
          while (allFields.length){
            // 顺序不能错
            const field = allFields.shift()!;
            if(field.ext_cell){
              allFields = allFields.concat(field.ext_cell.fields)
            }
            if(!nameVsFields[field.name]){
              nameVsFields[field.name] = [];
            }
            nameVsFields[field.name].push(field)
            if(field.cascade_list && field.cascade_list.lenth){
              beCascadeFields.push(...field.cascade_list.map((f:any)=>f.show_field_names).flat())
            }
          }

          const fields: XField[] = [];
          const allTables:any = editor.getAllTables();
          const alreadyHandleField:any = {}
          const dataSetNameVsValue:any = {}
          for (let i = 0; i < jsonData.length; i++) {
            const item = jsonData[i];
            if (item.type === "table") {
              this.insertTableFillContent(editor, allTables, item);
            } else {
              if (item.value === undefined || item.value === null) {
                continue;
              }
              dataSetNameVsValue[item.name] = item;
              let ffs = nameVsFields[item.name];
              const splitName = item.name.split(">")[1];
              if(splitName){
                const mhFfs = nameVsFields[splitName];
                if(mhFfs){
                  if(!ffs){
                    ffs = mhFfs
                  }else {
                    ffs = ffs.concat(mhFfs)
                  }
                }
              }
              if (!ffs || !ffs.length) {
                continue;
              }
              alreadyHandleField[item.name] = true;
              // 如果值是数组说明是一个自动扩展表，此时进入表格自动扩展逻辑
              if(Array.isArray(item.value)){
                this.handleAutoFillTableField(editor,ffs,item,isClear,jsonData);
                continue;
              }
              ffs.forEach((f: XField | BoxField) => {
                this.fillFieldCore(editor,fields,f,item,isClear,jsonData)
              });
            }
          }
          editor.updateFieldText({ fields });
          // 以上完成基本填充逻辑。下面则根据级联规则显示隐藏部分文本域
          allFields.forEach((field:any)=>{
            editor.showOrHideField(field)
          })
          // 下面再填充级联规则显示出来的文本域
          const fields2:any = []
          beCascadeFields.forEach((name:any)=>{
            const item =  dataSetNameVsValue[name];
            if(!alreadyHandleField[name] && item){
              const ffs = editor.getFieldsByName(name)
              if(ffs && ffs.length){
                ffs.forEach((f: XField | BoxField) => {
                  this.fillFieldCore(editor,fields2,f,item,isClear,jsonData)
                });
              }
            }
          })
          editor.updateFieldText({ fields:fields2 });

          const needUpdateFields = editor.getAllFields()
          // 先将所有带有文本域公式的文本域全部获取到，然后将其公式拼接成一个超大字符串用于后面触发公式自动计算逻辑
          let allFormulaStr = ""
          needUpdateFields.forEach((f)=>{
            if(f.formula){
              allFormulaStr += f.formula
            }
          })
          for (let i = 0 ,len = needUpdateFields.length; i <  len; i++) {
            const field = needUpdateFields[i]
            // 还要处理自动化设置过的文本域
            if ((field.automation_list && field.automation_list.length)
                || allFormulaStr.indexOf("["+field.name+"]")>-1) {
              XField.updateFieldAdvancedFunctions(field, editor)
            }
          }
          // 临时处理
          editor.config.page_size_type = oriPageInfo.page_size_type;
          editor.config.customPageSize = oriPageInfo.customPageSize;
          editor.refreshDocument(true)
          // 因为涉及到分页，在此时才能知道
          this.handleSystemVar(editor,isClear);
        }
        // 动态插入字典（将对应字典名称的多选框或下拉框删除后重新插入）
        dynamicInsertDict(editor:Editor,dataSet:any){
          // 首先将字典数据集搞出来，
          // 然后全局搜索对应的文本域进行删除
          // 删除后紧接着重新插入（删除前将对应文本域的一些属性应该记录，重新赋值给新插入的文本域）
          const vsFieldName:any = {}
          for (let key in dataSet) {
            if(key.endsWith("-dict")){
              const dict = dataSet[key];
              key = key.split("-dict")[0]
              for (let i = 0 ,len = dict.length; i < len ; i++) {
                const item = dict[i];
                const name = key+">"+item.name;
                if(!vsFieldName[name]){
                  vsFieldName[name] = []
                }
                vsFieldName[name].push(item)
              }
            }
          }
          // 先处理页眉页脚
          EditorHelper.enterEditHeaderAndFooterMode(editor, "header")
          this.updateDictVsField(editor,vsFieldName);
          EditorHelper.enterEditHeaderAndFooterMode(editor, "footer")
          this.updateDictVsField(editor,vsFieldName);
          EditorHelper.quitEditHeaderAndFooterMode(editor)
          this.updateDictVsField(editor,vsFieldName);

        }

        handleScript(jsonData:any,scriptStr:any){
          if(scriptStr){
            try{
              const scriptDataSet:any = {}
              const convertScript = new Function(scriptStr + "\nreturn convertCode;")();
              for (const key in jsonData) {
                const resDataSet = convertScript(jsonData[key])
                Object.assign(scriptDataSet,resDataSet)
              }
              const resultScriptDataSet:any = {}
              for (const key in scriptDataSet) {
                if(scriptDataSet[key].Visible !== undefined){
                  resultScriptDataSet[key] =scriptDataSet[key].Visible
                }
                if(scriptDataSet[key].ImageIndex !== undefined){
                  resultScriptDataSet[key] =scriptDataSet[key].ImageIndex
                }
              }
              // console.log(resultScriptDataSet)
              jsonData["scriptDataSet"] = resultScriptDataSet
            }catch(e){
              console.error("脚本执行异常》》》",e)
            }
          }
        }

        updateDictVsField(editor:Editor,vsFieldName:any){
          for (const name in vsFieldName) {
            // // 只处理文本域中dynamic属性为true的,  先处理正文中的
            const fields = editor.getFieldsByName(name,editor.current_cell).filter((f)=>f.meta.dynamic)
            for (let i = 0,len = fields.length ; i < len ; i++) {
              const field = fields[i];
              editor.removeFields([field])
              // @ts-ignore
              const { type, placeholder, start_symbol, end_symbol, multi_select, name, tip, box_multi, required, border } = field;
              if(type === "select"){
                const sourceList = vsFieldName[name].map((ele:any) => {
                  return {
                    text: ele.label,
                    value: ele.value,
                  };
                });
                const newField = editor.insertField({ type, placeholder, start_symbol, end_symbol, multi_select, source_list: sourceList, name, tip  });
                if(isField(newField)){
                  newField.meta = field.meta;
                }
              }
              if(type === "box"){
                const sourceList = vsFieldName[name].map((node:any) => {
                  return {
                    key: uuid("choice"),
                    value: node.label,
                    formula_value: node.value,
                    disabled: 0,
                  };
                });
                const newField = editor.insertWidget({
                  isGroup: true,
                  groupName:name,
                  isMulti: !!box_multi,
                  required: required,
                  widgetType:"checkbox",
                  deletable: 1,
                  hideBorder: 1,
                  border,
                  items: sourceList,
                });
                if(isBoxField(newField)){
                  newField.meta = field.meta;
                }
              }
            }
          }
        }

        // 获取当前文档中的分组，然后匹配数据集，如果能匹配到再进行下一步处理
        handleGroupVsDataSet(editor:Editor,dataSet:any){
          const groups = editor.getAllGroup();
          if(!groups.length){
            return
          }
          // 统计当前文档中不同name的分组数量
          const nameVsGroup:any = {}
          const needDelGroup:any = []
          for (let i = 0,len = groups.length ; i <  len; i++) {
            const item = groups[i];
            const key = item.name;
            // 如果有分组，分组有名称，但是分组未对应任何数据集则将分组删除
            if(key && this.isEmpty(dataSet[key]) && this.isEmpty(dataSet[key+"_print"])){
              needDelGroup.push(item)
              continue;
            }
            if(!nameVsGroup[key]){
              nameVsGroup[key] = []
            }
            if(item.meta.repeat !== false){
              nameVsGroup[key].push(item)
            }
          }
          editor.deleteGroup(needDelGroup);
          this.insertGroupAndSet(editor,dataSet,nameVsGroup)

        }
        isEmpty(value:any) {
          if (value === null || value === undefined) return true; // 检查是否为null或undefined
          if (Array.isArray(value)) return value.length === 0; // 检查是否为空数组
          if (typeof value === 'object') return Object.keys(value).length === 0; // 检查是否为空对象
          return false; // 其他情况
        }
        insertGroupAndSet(editor:Editor,dataSet:any,nameVsGroup:any){
          for (let name in nameVsGroup) {
            if(!nameVsGroup[name].length){
              continue;
            }
            let normalCount = 0;
            let printCount = 0;
            if(Array.isArray(dataSet[name])){
              normalCount = dataSet[name].length;
            }
            if(Array.isArray(dataSet[name+"_print"])){
              printCount = dataSet[name+"_print"].length;
            }
            // 如果都是数组
            if(normalCount && printCount){
              // 将打印的分组数据合并到不带后缀的数据集中
              dataSet[name] = dataSet[name].concat(dataSet[name+"_print"])
            }else if(!normalCount && printCount){
              // 如果只有打印是数组， 非打印续写的是null空对象
              dataSet[name] = [dataSet[name]].concat(dataSet[name+"_print"])
            }
            dataSet[name+"_print"] = []
            if(normalCount + printCount > nameVsGroup[name].length){
              let addCount = normalCount + printCount - nameVsGroup[name].length;
              // 将光标定位到最后一个分组，然后copy其中的数据，然后在其下方插入一个新分组
              const lastGroup = nameVsGroup[name][nameVsGroup[name].length - 1];
              editor.locationToGroupById(lastGroup.id);
              const rawData = Group.getRawData(editor,lastGroup)
              for (let i = 0 ; i <  addCount; i++) {
                const newGroup = editor.insertGroup();
                if(newGroup){
                  newGroup.new_page = lastGroup.new_page
                  newGroup.name = name;
                  editor.insertTemplateData(rawData)
                }
              }
            }
            if(printCount){
              const groups = editor.selection.getGroupsByName(name)
              groups.slice(groups.length - printCount).forEach((g)=>{
                g.meta.print = true;
              })
            }
          }

        }

        // 处理系统变量
        handleSystemVar(editor:Editor,isClear:boolean){
          const systemVal:any = system_variables;
          const pageCount = String(editor.pages.length);
          const systemTime = formatDate(new Date())
          const needUpdateFields:any = []
          for (const key in systemVal) {
            // 序号要单独处理
            if(key === "serial_number"){
              continue;
            }
            const name =  systemVal[key]
            const ffs = editor.getFieldsByName(name);
            for (let i = 0 ,len = ffs.length; i <  len; i++) {
              const f = ffs[i];
              // 根据f要知道在第几页， 并且页脚中的内容
              if(key === "page_number"){
                let needContainer:any
                if(!f.cell.parent && isRow(f.rows[0])){
                  needContainer = f.rows[0];
                }
                if(f.cell.parent){
                  needContainer = f.cell.parent;
                }
                if(!needContainer){
                  continue
                }
                f.setNewText(String(needContainer.page_number))
              }else if(key === "page_count"){
                f.setNewText(pageCount)
              }else if(key === "system_time"){
                f.setNewText(systemTime)
              }
              if(isClear){
                f.setNewText("")
              }
              needUpdateFields.push(f)
            }
          }

          const serialNumName = systemVal["serial_number"];
          let serialNumFields = editor.getFieldsByName(serialNumName);
          let lastSf = null;
          const tableFieldsMap:any = {}
          for (let i = 0,len = serialNumFields.length ; i <  len; i++) {
            const sf = serialNumFields[i];
            const tableId = sf.cell.parent ? sf.cell.parent.id : ""
            if(!tableFieldsMap[tableId]){
              tableFieldsMap[tableId] = [];
            }
            tableFieldsMap[tableId].push(sf)
            if(!sf.cell.parent){
              continue
            }
            if(lastSf !== sf.cell.parent){
              editor.locatePathInField(sf)
              let newField = this.swapCell(editor,sf.cell,sf.cell.parent,serialNumName)
              while (newField && newField.cell.parent){
                tableFieldsMap[tableId].push(newField)
                newField = this.swapCell(editor,newField.cell,newField.cell.parent,serialNumName)
              }
            }
            lastSf = sf.cell.parent;
          }
          for (const tableId in tableFieldsMap) {
            const fields = tableFieldsMap[tableId]
            for (let i = 0,len =  fields.length; i <  len; i++) {
              const field = fields[i];
              field.setNewText(String(i+1))
              needUpdateFields.push(field)
            }
          }
          editor.updateFieldText({ fields:needUpdateFields })

        }
        fillFieldCore(editor:Editor,waitingForReplaceFields:XField[],f:XField,item:any,isClear:boolean,jsonData:any){
          if (item.value === undefined || item.value === null) {
            return
          }
          if (f.type === "box") {
            if (isClear) {
              (f as BoxField).parent_box.clearBoxChecked();
            } else {
              (f as BoxField).updateGroupCheckedByValue(item.value);
            }
            return
          }
          if (typeof item.value === "string" && (item.value.startsWith("http") || isBase64Image(item.value)) || f.meta.qrCodeType || (f.meta.placeholder && isBase64Image(item.value))) {
            // 首先获取当前文本域中是否存在图片，如果存在则按照图片宽高进行设置
            const image = f.getAllElements().find(ele => isImage(ele));
            let needWidth,needHeight;
            if(image){
              needWidth = image.width;
              needHeight = image.height;
            }else{
              if(f.meta.placeImageWh){
                needWidth = f.meta.placeImageWh[0];
                needHeight = f.meta.placeImageWh[1];
              }
            }
            let src = item.value;
            if (isClear) {
              const showTextMap: any = {
                "qrcode": "二维码",
                "barcode": "条形码",
              };
              let showText = "图片位"
              const showType = f.meta.qrCodeType;
              if (showType) {
                if (showTextMap[showType]) {
                  showText = showTextMap[showType]
                }
              }
              src = createImagePlaceholder(showText);
            } else if (f.meta.qrCodeType) {
              let meta = f.meta;
              if (isImage(image)) {
                meta = image.meta;
              }
              const infoStr = meta.qrCodeInfo;
              const infoStrSplit:any = this.splitFiledStringArr(infoStr);
              let codeText = "";
              for (let j = 0; j < infoStrSplit?.length; j++) {
                const fieldStr = infoStrSplit[j];
                if (fieldStr.startsWith("[") && fieldStr.endsWith("]")) {
                  const fStrName = fieldStr.replace("[", "").replace("]", "")
                  const selItem = jsonData.find((it: any) => it.name === fStrName)
                  if (selItem) {
                    codeText += selItem.value ?? ""
                  }
                } else {
                  codeText += fieldStr
                }
              }
              if(codeText){
                src = getBarcodeOrQrCodeSrc(codeText, {
                  type: meta.qrCodeType, options: {
                    format: meta.barcodeFormat,
                    displayValue: meta.displayValue === "show" ? true : false
                  }
                })
              }
            }
            if (item && src) {
              const meta = (isImage(image) && f.meta.placeholder) ? image.meta : f.meta
              isClear ? meta.placeholder = true : meta.placeholder = false;
              editor.replaceFieldsImage([f], {
                src,
                width: needWidth ? needWidth : item.width ?? f.meta.ori_width,
                height: needHeight ? needHeight : item.height ?? f.meta.ori_height,
                meta
              });
            }else{
              f.setNewText(" ");
              f.meta.ori_width =  needWidth ? needWidth  : item.width ?? f.meta.ori_width
              f.meta.ori_height =  needHeight ? needHeight : item.height ?? f.meta.ori_height
              f.meta = isImage(image)?image.meta:{};
              waitingForReplaceFields.push(f)
            }
          } else {
            if (isClear) {
              f.setNewText("");
              waitingForReplaceFields.push(f);
            } else {
              if(item.value!==""){
                let value = this.handleFieldValue(f, item);
                value = this.handleValue(value);
                f.setNewText(value);
                waitingForReplaceFields.push(f);
              }
            }

          }
          return waitingForReplaceFields;
        }
        swapCell(editor:Editor,focusCell:Cell,table:Table,name:string){
          let belowCell =  table.children.find((cell)=>{
            return  cell.top === focusCell.bottom && cell.left === focusCell.left && cell.right === focusCell.right
          })
          if(belowCell && belowCell.getStr() === "\n"){
            const copyCell = focusCell.copy(table);
            editor.swapCells(belowCell,copyCell);
            // 此时将focusCell内容拷贝到belowCell中，并且将光标置于belowCell中
            const cellFields = editor.getFieldsByName(name,belowCell);
            editor.locatePathInField(cellFields[0]);
            return cellFields[0];
          }else{
            return false;
          }
        }

        handleAutoFillTableField(editor:Editor,fields:any,item:any,isClear:boolean,jsonData:any){
          const { name,value } = item;
          const normalFields = [];
          let tableFields:any = [];

          for (let i = 0 ,len = fields.length; i < len; i++) {
            const field = fields[i];
            if(field.cell.parent){
              tableFields.push(field);
            }else{
              normalFields.push(field);
            }
          }
          let wfFields:XField[] = []
          for (let i = 0,len = normalFields.length ; i <  len; i++) {
            const f = normalFields[i];
            this.fillFieldCore(editor,wfFields,f, { name:item.name,value:item.value[i] },isClear,jsonData)
          }
          editor.updateFieldText({ fields:wfFields });
          // 如果表格中的文本域只有一个则判定为自动扩展，如果大于一个则按数量填充，此时将光标定位到表格的最后一个文本域中开始追加行数，追加的行数为两者之差
          let isAppendRow = false;
          let table = null;
          const ori_insert_row_carry_data = editor.config.insert_row_carry_data;
          editor.config.insert_row_carry_data = 0;

          if(tableFields.length === 1 && value.length){
            editor.locatePathInField(tableFields[0]);
            for (let i = 0 ,len = value.length - tableFields.length; i < len; i++) {
              let focusCell = editor.selection.getFocusCell()!
              table = focusCell.parent!;
              const res = this.swapCell(editor,focusCell,table,name)
              if(!res && !this.appendRowTable[table.id]){
                editor.locatePathInField(tableFields[0]);
                focusCell = editor.selection.getFocusCell()!
                editor.addRowOrColInTbl(3);
                this.swapCell(editor,focusCell,table,name)
                isAppendRow = true;
              }
            }
            editor.config.insert_row_carry_data = ori_insert_row_carry_data;
            if(isAppendRow && table){
              this.appendRowTable[table.id] = isAppendRow;
            }
            // 此时再重新获取表格内文本域进行内容替换
            tableFields = editor.getFieldsByName(name).filter(f=>f.cell.parent)
          }
          let wfTableFields:XField[] = []
          const alreadyHandleCells:any = [];
          for (let i = 0 ,len = tableFields.length; i <  len; i++) {
            const f = tableFields[i];
            const cell = f.cell;
            this.fillFieldCore(editor,wfTableFields,f, { name:item.name,value:item.value[i] },isClear,jsonData)
            if(alreadyHandleCells.some((val:Cell)=> val === cell)){
              continue;
            }
            // 因为是按顺序自上而下填充， 此处我们先判断这个文本域是否存在合并属性，
            // 如果存在再判断下方单元格内容与当前单元格内容是否完全一致，
            // 完全一致则列入需合并单元格组
            // 此时将下方单元格存储（主要用于合并前清空用）
            const pushNeedHandleCell = (curField:any,curCell:Cell) => {
              if(curField.meta.merge){
                const downCell = curCell.getAdjacentCell("down");
                if(downCell){
                  const downField = downCell.fields.find((val:any)=>{return val.meta.merge && val.name === curField.name})
                  if(downField){
                    if(!alreadyHandleCells.some((val:Cell)=> val === curCell)){
                      alreadyHandleCells.push(curCell);
                    }
                    alreadyHandleCells.push(downCell);
                    pushNeedHandleCell(downField,downCell)
                  }
                }
              }
            }
            pushNeedHandleCell(f,cell)
          }
          editor.updateFieldText({ fields:wfTableFields });


          // 一下为处理表格内字段值相同时合并单元格的逻辑
          let groupHandleCells: any[] = [];

          for (let i = 0, len = alreadyHandleCells.length; i < len; i++) {
            const cell = alreadyHandleCells[i];
            let nextCell = alreadyHandleCells[i + 1];

            // 开始新的分组
            groupHandleCells.push(cell);

            // 检查后续单元格是否与当前单元格内容一致，并加入分组
            while (nextCell && nextCell.getStr() === cell.getStr()) {
              groupHandleCells.push(nextCell);
              i++;  // 移动到下一个单元格
              nextCell = alreadyHandleCells[i + 1];  // 更新 nextCell
            }

            // 处理分组：清空除了第一个单元格外的内容，并合并单元格
            if (groupHandleCells.length > 1) {
              for (let j = 1; j < groupHandleCells.length; j++) {
                groupHandleCells[j].clear();
              }
              Table.mergeCells(editor, groupHandleCells);
            }

            // 重置当前分组
            groupHandleCells = [];
          }

        }
        splitFiledStringArr(inputString: string) {
          if (!inputString) {
            return [""]
          }
          // 使用正则表达式匹配字符串中的模式
          const pattern = /\[[^\]]*\]|[^[\]]+/g;
          const result = inputString.match(pattern);
          return result;
        }
        handleFieldValue(f: XField, item: any) {
          let value = item.value;
          if(value === undefined || value === null){
            value = ""
          }
          // 增加非法base64校验，防止编辑器卡死
          const valLen = String(value).length
          if(String(value).startsWith("data") && valLen > 1000){
            return "格式错误"
          }
          if(valLen > 3000){
            return "字符串超长"
          }
          let dict = f.source_list;
          if (dict.length || typeof value === "object") {
            if (f.multi_select === 1) {
              const separatorArray = ["，", "；", "、", "%"];
              let val = item.value;
              // 字符串中如果存在英文逗号，则也处理成数组
              if (typeof item.value === "string" && item.value.indexOf(",") > -1) {
                val = item.value.split(",");
              }
              if (!Array.isArray(val)) {
                val = [String(val)];
              }
              const vals = dict.filter((ele: any) => val.includes(String(ele.value))).map(e =>  e.text);
              value = vals.join(separatorArray[f.separator]);
            } else {
              const fItem = dict.find((ele: any) => String(ele.value) === String(value));
              if (fItem) {
                value = fItem.text;
              }else{
                const anyTextItem = dict.find((ele: any) => ele.value === "*")
                if(anyTextItem){
                  value = anyTextItem.text;
                }
              }
            }
          }
          return typeof value === "object" ? value.value : value;
        }
        initTableMustParams(item: any) {
          if (!Array.isArray(item.repeatColCells)) {
            item.repeatColCells = [];
          }
          if (!Array.isArray(item.repeatRowCells)) {
            item.repeatRowCells = [];
          }
        }
        insertTableFillContent(editor: Editor, allTables: Table[], item: any) {
          let table;
          if (item.name) {
            table = editor.getTablesByName(item.name)[0];
          } else {
            table = allTables.shift();
          }
          if (!table) return;
          this.initTableMustParams(item);
          editor.deleteTbl(table);
          let cols = 1;
          let rows = 1;
          let allTableCount = 1;
          let lastTableCols;
          let lastTableRows;
          // fillType 为 1时，则是按列填充, 首先进行矩阵行列变换
          const contentCells = item.contentCells;
          if (item.fillType === 1) {
            const repeatCols = item.repeatColCells.length
              ? item.repeatColCells[0].length
              : 0;
            const contentMaxCols = item.maxCols - repeatCols;
            const allContentColCount = contentCells.length; // 共多少列
            allTableCount = Math.ceil(allContentColCount / contentMaxCols);
            // 查看重复列， 如果存在 则可判断共需要拆成几个表格
            if (item.repeatColCells.length) {
              rows = item.repeatColCells.length + item.repeatRowCells.length;
            } else {
              // 不存在重复列的情况下，行按照内容最多的行计算
              contentCells.forEach((c: any) => {
                rows = Math.max(c.length, rows);
              });
            }
            lastTableRows = rows;
            if (allContentColCount >= contentMaxCols) {
              cols = lastTableCols = contentMaxCols;
              allTableCount = Math.max(
                allTableCount,
                Math.ceil(allContentColCount % contentMaxCols)
              );
              if (allTableCount > 1 && allContentColCount % cols) {
                lastTableCols = allContentColCount % cols;
              }
              cols += repeatCols;
              lastTableCols += repeatCols;
            } else {
              cols = lastTableCols = allContentColCount + repeatCols;
            }
          } else {
            const repeatCols = item.repeatColCells.length
              ? item.repeatColCells[0].length
              : 0;
            contentCells.forEach((c: any) => {
              cols = Math.max(c.length, cols);
            });
            if (item.maxCols) {
              // 当指定最大列数，剩余内容丢弃
              cols = Math.min(cols + repeatCols, item.maxCols);
            } else {
              cols += repeatCols;
              rows = lastTableRows =
                        item.repeatRowCells.length + item.contentCells.length;
            }
            lastTableCols = cols;
          }
          let cycCells = [...item.contentCells];
          for (let i = 0; i < allTableCount; i++) {
            if (i === allTableCount - 1) {
              cols = lastTableCols;
              rows = lastTableRows;
            }
            let newTable;
            if (i === 0) {
              newTable = editor.insertTable(rows, cols, { name: "AutoTable" });
            } else {
              newTable = editor.insertTable(rows, cols, {
                name: "AutoTable",
                newPage: true,
                skipMode:SkipMode.ROW
              });
            }
            if (!isTable(newTable)) continue;
            if (item.fixedHeaderNum) {
              newTable.fixed_table_header_num = item.fixedHeaderNum;
            }
            this.handleTableColSize(newTable, item);
            cycCells = this.fillTableCellContent(editor, newTable, item, cycCells);
            // 因为合并后
            this.handleMergeCell(editor, newTable, item);
            // 插入表格后将光标置于表格下方段落开头
            editor.selection.setCursorPosition([newTable.cell_index + 1, 0]);
            editor.refreshDocument();
          }
        }
        fillTableCellContent(editor: Editor, table: Table, item: any, cycCells: any) {
          if (!table) return;
          if (item.repeatRowCells) {
            for (let i = 0; i < item.repeatRowCells.length; i++) {
              const cells = table.getCellsByRowIndex(i);
              const rawCells = item.repeatRowCells[i];
              rawCells.forEach((c: any, index: number) => {
                if (cells[index]) {
                  const modelCell = cells[index];
                  this.fillCell(editor, modelCell, c, table);
                }
              });
            }
          }
          if (item.repeatColCells) {
            const offset = item.repeatRowCells.length;
            for (let i = 0; i < item.repeatColCells.length; i++) {
              const cells = table.getCellsByRowIndex(i + offset);
              const rawCells = item.repeatColCells[i];
              rawCells.forEach((c: any, index: number) => {
                if (cells[index]) {
                  const modelCell = cells[index];
                  this.fillCell(editor, modelCell, c, table);
                }
              });
            }
          }
          if (item.fillType === 1) {
            if (cycCells) {
              const rowOffset = item.repeatRowCells.length;
              const colOffset = item.repeatColCells.length
                ? item.repeatColCells[0].length
                : 0;
              for (let i = 0; i < table.col_size.length; i++) {
                const cells = table.getCellsByColIndex(i + colOffset);
                cells.forEach((c, index) => {
                  if (index >= rowOffset) {
                    const rawCell = cycCells[i][index - rowOffset];
                    if (rawCell) {
                      this.fillCell(editor, c, rawCell, table);
                    }
                  }
                });
              }
              return cycCells.slice(table.col_size.length - 1, cycCells.length);
            }
          } else {
            if (cycCells) {
              const rowOffset = item.repeatRowCells.length;
              const colOffset = item.repeatColCells.length
                ? item.repeatColCells[0].length
                : 0;
              for (let i = 0; i < table.row_size.length; i++) {
                const cells = table.getCellsByRowIndex(i + rowOffset);
                cells.forEach((c, index) => {
                  if (index >= colOffset) {
                    const rawCell = cycCells[i][index - rowOffset];
                    if (rawCell) {
                      this.fillCell(editor, c, rawCell, table);
                    }
                  }
                });
              }
              return cycCells.slice(table.row_size.length - 1, cycCells.length);
            }
          }
        }
        fillCell(editor: Editor, modelCell: Cell, rawCell: any, table: Table) {
          this.handleCellStyleSet(modelCell, rawCell.style);
          if (rawCell.content) {
            editor.selection.setCursorPosition([
              table.cell_index,
              modelCell.index,
              0,
              0
            ]);
            this.toggleCellBorder(editor, modelCell, rawCell, table);
            rawCell.content.forEach((cc: any) => {
              editor.contextState.resetFontState();
              if (cc.type === "text") {
                this.handleTextStyle(editor, cc.style);
                editor.insertText(cc.value);
              } else if (cc.type === "checkbox" || cc.type === "radio") {
                const widget = editor.insertSimpleWidget(cc.type) as Widget;
                widget.selected = !!cc.value;
              } else if (cc.type === "raw") {
                editor.insertTemplateData(cc.value);
              } else if(cc.type === "field"){
                const newField = editor.insertField({ name:cc.name })
                if(isField(newField)){
                  if(isBase64Image(cc.value)){
                    editor.replaceFieldsImage([newField],{
                      src:cc.value,
                      width:cc.width,
                      height:cc.height,
                      meta:cc.meta
                    })
                    editor.caret_move("right");
                  }else if(cc.value === 0 || cc.value){
                    newField.setNewText(this.handleValue(cc.value));
                    editor.updateFieldText({ fields:[newField] })
                    editor.caret_move("right");
                  }
                }
              }
            });
          }
        }
        handleValue(val:any){
          if(val === "" || val === null || val === undefined){
            return " "
          }else{
            return val;
          }
        }
        handleTextStyle(editor: Editor, style: any) {
          if (!style) return;
          const newFont: Font = editor.createElement("font", style) as Font;
          editor.contextState.setFontState(newFont);
        }
        handleCellStyleSet(modelCell: Cell, style: any) {
          if (!style) return;
          const firstPara = modelCell.paragraph[0] as Paragraph;
          if (String(style.align).indexOf("center") > -1) {
            firstPara.align = "center";
          }
          if (String(style.align).indexOf("dispersed") > -1) {
            firstPara.align = "dispersed";
          }
          if (String(style.align).indexOf("left") > -1) {
            firstPara.align = "left";
          }
          if (String(style.align).indexOf("right") > -1) {
            firstPara.align = "right";
          }
          if (String(style.align).indexOf("middle") > -1) {
            modelCell.vertical_align = VerticalAlign.CENTER;
          }
          if (String(style.align).indexOf("top") > -1) {
            modelCell.vertical_align = VerticalAlign.TOP;
          }
          if (String(style.align).indexOf("bottom") > -1) {
            modelCell.vertical_align = VerticalAlign.BOTTOM;
          }
        }
        toggleCellBorder(editor: Editor, modelCell: Cell, rawCell: any, table: Table) {
          if (!rawCell.toggleBorder) return;
          const { left, right, top, bottom } = rawCell.toggleBorder;
          const setBorderToggle = () => {
            if (left) {
              editor.cotrolTableLine("left_line");
            }
            if (right) {
              editor.cotrolTableLine("right_line");
            }
            if (top) {
              editor.cotrolTableLine("top_line");
            }
            if (bottom) {
              editor.cotrolTableLine("under_line");
            }
          };
          const oriFocus = [...editor.selection.focus];
          setBorderToggle();
          if (rawCell.rowSpan) {
            // 从当前单元格往下走
            for (let i = 1; i < rawCell.rowSpan; i++) {
              const cell = table.getCellByPosition(
                modelCell.position[0] + i,
                modelCell.position[1]
              );
              if (!cell) {
                continue;
              }
              editor.selection.setCursorPosition([
                table.cell_index,
                cell.index,
                0,
                0
              ]);
              setBorderToggle();
            }
          }
          if (rawCell.colSpan) {
            // 从当前单元格往右走
            for (let i = 1; i < rawCell.colSpan; i++) {
              const cell = table.getCellByPosition(
                modelCell.position[0],
                modelCell.position[1] + i
              );
              if (!cell) {
                continue;
              }
              editor.selection.setCursorPosition([
                table.cell_index,
                cell.index,
                0,
                0
              ]);
              setBorderToggle();
            }
          }
          editor.selection.setCursorPosition(oriFocus);
        }
        handleMergeCell(editor: Editor, table: Table, item: any) {
          if (item.repeatRowCells) {
            for (let i = 0; i < item.repeatRowCells.length; i++) {
              const rawCells = item.repeatRowCells[i];
              rawCells.forEach((rawCell: any) => {
                this.mergeCellByRaw(editor, item, table, rawCell, i);
              });
            }
          }
          if (item.repeatColCells) {
            for (let i = 0; i < item.repeatColCells.length; i++) {
              const rawCells = item.repeatColCells[i];
              rawCells.forEach((rawCell: any, index: number) => {
                this.mergeCellByRaw(editor, item, table, rawCell, index);
              });
            }
          }
        }
        mergeCellByRaw(editor: Editor, item: any, table: Table, rawCell: any, i: number) {
          const rowOffset = item.repeatRowCells.length;
          const selectedCellsPath = [];
          if (rawCell.colSpan) {
            for (let j = 0; j < rawCell.colSpan; j++) {
              const cell = table.getCellByPosition(i, j);
              if (!cell) {
                continue;
              }
              selectedCellsPath.push([table.cell_index, cell.index, 0, 0]);
            }
            editor.selection.selected_cells_path = selectedCellsPath;
            editor.mergeCell();
            editor.selection.clearSelectedInfo();
          }
          if (rawCell.rowSpan) {
            for (let j = 0; j < rawCell.rowSpan; j++) {
              const cell = table.getCellByPosition(j + rowOffset, i);
              if (!cell) {
                continue;
              }
              selectedCellsPath.push([table.cell_index, cell.index, 0, 0]);
            }
            editor.selection.selected_cells_path = selectedCellsPath;
            editor.mergeCell();
            editor.selection.clearSelectedInfo();
          }
        }
        handleTableColSize(table: Table, item: any) {
          if (!item.colSize || !item.colSize.length) {
            return;
          }
          const oriColSize = [...table.col_size];
          const totalWidth = oriColSize.reduce((total, current) => total + current);
          const newColSize: number[] = [];
          oriColSize.forEach((num, index) => {
            if (item.colSize && item.colSize[index]) {
              newColSize.push(Math.round((item.colSize[index] * totalWidth) / 100));
            } else {
              newColSize.push(
                (totalWidth -
                            newColSize.reduce((total, current) => total + current)) /
                        (oriColSize.length - newColSize.length)
              );
            }
          });
          table.col_size = newColSize;
        }
        // 处理数据集成能够正常填充的json数据
        handleDataSet2FillJson(dataSet: any) {
          const fillJson: any = [];
          const tableJson :any = [];
          for (const dataSetId in dataSet) {
            if (Array.isArray(dataSet[dataSetId]) && dataSet[dataSetId].length > 1) {
              this.handleTableDataSet(tableJson,dataSet[dataSetId],dataSetId);
            }else{
              // 第一层一定是数据集
              this.handCycleDataSet(fillJson, dataSet[dataSetId], dataSetId);
            }
          }
          // 将tableJson中的数据进行排序，然后将排序后的数组合并至fillJson中
          tableJson.sort((a:any,b:any)=>b.value.length - a.value.length)
          fillJson.push(...tableJson)
          return fillJson;
        }
        transformJsonArray(jsonArray:any) {
          const obj = jsonArray.reduce((result:any, item:any) => {
            if(!item){
              return result
            }
            Object.keys(item).forEach(key => {
              if (!result[key]) {
                result[key] = [];
              }
              result[key].push(item[key]);
            });
            return result;
          }, {});
            // 如果数组末尾是空字符串或者null或者undefined则去掉末尾项
          Object.keys(obj).forEach(key => {
            while (obj[key].length > 0 && (obj[key][obj[key].length - 1] === "" || obj[key][obj[key].length - 1] === null || obj[key][obj[key].length - 1] === undefined)) {
              obj[key].pop();
            }
          });
          return obj;
        }

        handleTableDataSet(fillJson: any, dataSet: any, pCode: string){
          if(!dataSet){
            return
          }
          const resJson = this.transformJsonArray(dataSet);
          for (const key in resJson) {
            fillJson.push({
              name:pCode + ">" + key,
              value : resJson[key]
            })
          }
        }

        handCycleDataSet(fillJson: any, dataSet: any, pCode: string) {
          if (Array.isArray(dataSet)) {
            dataSet = dataSet[0];
          }
          if (dataSet) {
            for (const key in dataSet) {
              if (dataSet[key] === undefined || dataSet[key]=== null) {
                continue;
              }
              if (
                (key !== "children" && Array.isArray(dataSet[key])) ||
                        typeof dataSet[key] !== "object"
              ) {
                fillJson.push({
                  name: pCode ? pCode + ">" + key : key,
                  value: dataSet[key]
                });
              } else {
                const newPCode = pCode ? pCode + ">" + key : key;
                if (Array.isArray(dataSet[key].children)) {
                  if (dataSet[key].value !== null && dataSet[key].value !== undefined) {
                    fillJson.push({
                      name: newPCode,
                      value: dataSet[key].value,
                      dict: dataSet[key].children
                    });
                  }
                }
              }
            }
          }
        }
}
