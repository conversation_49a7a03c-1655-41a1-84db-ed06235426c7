/* eslint-disable no-labels */
import Editor from "../../../msun-lib-editor-common/src/editor/Editor";
import Page from "../../../msun-lib-editor-common/src/editor/Page";
import Shape from "../../../msun-lib-editor-common/src/editor/shape";
import <PERSON>Field from "../../../msun-lib-editor-common/src/editor/XField";
import Cell from "../../../msun-lib-editor-common/src/editor/Cell";
import Renderer from "../../../msun-lib-editor-common/src/editor/Renderer";
import Character from "../../../msun-lib-editor-common/src/editor/Character";
import Font from "../../../msun-lib-editor-common/src/editor/Font";
import Table from "../../../msun-lib-editor-common/src/editor/Table";
import Row from "../../../msun-lib-editor-common/src/editor/Row";
import Paragraph from "../../../msun-lib-editor-common/src/editor/Paragraph";
import { isRow, isTable, uuid, arrDeduplication, sort, keepDecimal, isCharacter } from "../../../msun-lib-editor-common/src/editor/Utils";
import { Config } from "../../../msun-lib-editor-common/src/editor/Config";
import { SkipMode } from "../../../msun-lib-editor-common/src/editor/Constant";
import { isUseCurrentLogic } from "../../../msun-lib-editor-common/src/editor/Helper";
import EditorLocalTest from "../../../msun-lib-editor-common/localtest";

enum RowLineType {
    VOID = 0,
    SOLID = 1,
  }
enum BreakType {
    soft,
    hard
  }
export const M2V = {
  initExtOptions (instance:any) {
    this.systemVariables = instance.sysVariables;
  },

  update (editor: Editor, cell_index: number = 0, page_number: number = 1, page_index: number = 0) {
    if (editor.editFloatModelMode) return;
    // TODO rendering 属性应该可以干掉 没啥用
    editor.internal.rendering = true;

    let commentArr = [];
    const commentIdMap = new Map();
    if (editor.is_comment_mode) {
      commentArr = editor.getCommentInfo();
    }

    // 编辑页眉页脚时 所有页面都要更新
    if (editor.is_edit_hf_mode) {
      cell_index = 0;
      page_number = 1;
      page_index = 0;
      editor.pages = [];
    }

    // page_number 最小值为 1 所以无论如何 editor.pages 都会留一个 page 的，
    // 所以在更改视口大小调用 updateCanvasSize 的时候，第一页的 page.left 没有变化
    // 如果一切都从 0 开始 就应该清空 editor.pages 所有页面重新 new 出来 宽 高 left 就都是对的了
    if (cell_index === 0 && page_number === 1 && page_index === 0) {
      editor.pages.length = 0;
    }

    // 处理 view_data pages
    if (editor.pages.length) {
      // 删除掉 page_number(从1开始) 页后边的所有页数据重新生成
      editor.pages.splice(page_number, editor.pages.length);

      // 删除 page_number 页内 page_index(从0开始) 处以及后边的所有行重新生成
      const last_page = editor.pages[editor.pages.length - 1];
      last_page.children.splice(page_index, last_page.children.length);
    }

    // 获取 cell_index 之后的元素(包含 cell_index 处的元素)
    let elements = editor.root_cell.children.slice(cell_index); // Row 和 Table 的集合
    // 处理分组页眉信息，保证页眉高度一直，必须放到处理正文数据之前
    this.handleGroupsHeaderInfo(editor, editor.header_cell);

    while (elements.length) { // 每进一次循环 是一页
      let page = editor.pages[page_number - 1];

      // 当前页 顶部边界 距离canvas最顶部边界的距离
      editor.internal.page_position =
            editor.config.editor_padding_top +
            (page_number - 1) * (editor.page_size.height + editor.config.page_margin_bottom);

      // 当前页为空的时候 需要new一个新的页面出来
      if (!page) { // 初始化的时候 page 有可能为 undefined
        page = new Page(
          editor.page_left,
          editor.internal.page_position, // 该页距离canvas最顶部边界的距离
          editor.page_size.width, // 该页的宽
          editor.page_size.height, // 该页的高
          editor
        ); // 该操作 保存了该页 距离 canvas 上右下左边界的距离 和 页面内 上右下左的内边距的距离 这些基本位置信息 同时还new出来了页眉页脚
        // 页数
        page.number = page_number;
        // 页数组
        editor.pages.push(page);
      }
      // 表格的 split_parts 里边的数量不对 这个需要修复 2024/05/31
      elements = this.add(page, elements, page_index); // 将数据放到页面上 并且减少对应的elements

      // 处理批注 先在这里循环一遍 page 里边的所有元素 因为都写在这里后边好处理 牺牲一点新能 写到 add 什么的 里边会很分散 ↓
      if (editor.is_comment_mode && editor.useNewVersionCommentList) {
        const comments = [];
        if (editor.is_comment_mode && commentArr.length) {
          OUT_FOR: for (let i = 0; i < page.children.length; i++) {
            const content = page.children[i];
            if (isRow(content)) {
              for (let j = 0; j < commentArr.length; j++) {
                if (commentArr[j].paraId === content.paragraph.id) {
                  const box = commentArr.splice(j, 1)[0];
                  box.position = content.top + page.top;
                  comments.push(box);
                  if (!commentArr.length) {
                    // eslint-disable-next-line no-labels
                    break OUT_FOR;
                  }
                }
              }
              for (const c of content.children) {
                if (isCharacter(c) && c.comment_id) {
                  if (!commentIdMap.has(c.comment_id)) {
                    commentIdMap.set(c.comment_id, true);
                    for (let j = 0; j < commentArr.length; j++) {
                      if (commentArr[j].id === c.comment_id) {
                        const box = commentArr.splice(j, 1)[0];
                        box.position = content.top + page.top;
                        comments.push(box);
                        if (!commentArr.length) {
                          // eslint-disable-next-line no-labels
                          break OUT_FOR;
                        }
                        break;
                      }
                    }
                  }
                }
              }
            } else if (isTable(content)) {
              for (const cell of content.children) {
                for (const row of cell.children) {
                  for (let j = 0; j < commentArr.length; j++) {
                    if (commentArr[j].paraId === (row as Row).paragraph.id) {
                      const box = commentArr.splice(j, 1)[0];
                      box.position = content.top + cell.top + row.top + page.top;
                      comments.push(box);
                      if (!commentArr.length) {
                        // eslint-disable-next-line no-labels
                        break OUT_FOR;
                      }
                      break;
                    }
                  }
                  for (const c of row.children) {
                    if (isCharacter(c) && c.comment_id) {
                      if (!commentIdMap.has(c.comment_id)) {
                        commentIdMap.set(c.comment_id, true);
                        for (let j = 0; j < commentArr.length; j++) {
                          if (commentArr[j].id === c.comment_id) {
                            const box = commentArr.splice(j, 1)[0];
                            box.position = content.top + cell.top + row.top + page.top;
                            comments.push(box);
                            if (!commentArr.length) {
                              // eslint-disable-next-line no-labels
                              break OUT_FOR;
                            }
                            break;
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
        let commentTotal = 0;
        if (editor.document_meta?.commentsIDSet) {
          for (const key in editor.document_meta.commentsIDSet) {
            const arr = editor.document_meta.commentsIDSet[key];
            for (const o of arr) {
              if (!o.abolished) {
                commentTotal++;
              }
            }
          }
        }
        editor.commentTotal = commentTotal;
        page.comments = comments;
        page.setCommentBox();
      }

      if (elements.length) { // 说明没有将元素全部放到页面上去 还需要再创建下一个页面 页码要增加 页面内更新位置要归零
        page_number += 1;
        page_index = 0;
      }

      // 打印模式
      if (editor.print_mode) {
        page.show_corner_line = false;
      }
    }
    if (editor.shapes.length) {
      Shape.updateShape(editor);
    }

    this.updateHeaderFooterInfo(editor);
    XField.updateFieldsAsterisk(editor);
    editor.updateCaret(); // 更新光标

    if (editor.config.rowLineType === RowLineType.SOLID) {
      editor.internal.rowHeight = editor.internal.getCountOfRowHeight();
    }
  },
  handleGroupsHeaderInfo (editor: Editor, header_cell: Cell) {
    if (!editor.root_cell.groups.length) {
      return;
    }
    const lengthInfo = this.getHeaderFieldMaxLength(editor.root_cell);
    if (!Object.keys(lengthInfo).length) {
      return;
    }
    // 处理原始数据中的内容
    const fields = editor.getAllFields(header_cell);
    for (let i = 0; i < fields.length; i++) {
      const field = fields[i];
      // 此处只刷新最内层文本域，否则会出问题
      if (field.getFieldInChildren().length > 1) {
        continue;
      }
      // 如果内容中包含换行也不进行替换操作
      if (field.children.findIndex((char) =>
        (char instanceof Character && char.value === "\n")) > -1) {
        continue;
      }
      let field_key = field.id;
      let info = lengthInfo[field_key];
      if (!info) {
        field_key = field.name;
        info = lengthInfo[field_key];
      }
      if (!info) {
        continue;
      }
      const text = field.text.trim();
      const { width } = Renderer.measure(new Font(field.style), text, editor);
      if (width < info.width) {
        const chars = field.textToFieldCharacter(text);
        field.clear();
        chars.push(this.createCustomWidthCharToField(field, text, info.value));
        field.children = chars;
        field.updateChildren();
      } else if (width > info.width) { // 经过一通分析 width 是不可能大于 info.width 的，肯定是要用分组里边的数据，所以这里用分组里最长的数据更新下，方便计算正确的页眉高度，正确的内容应该在后边会更新的
        const chars = field.textToFieldCharacter(info.value);
        field.clear();
        field.children = chars;
        field.updateChildren();
      }
    }
  },
  updateHeaderFooterInfo (editor: Editor) {
    // 逻辑优化， 首先判断有无分组，如果没有分组，则getHeaderFieldMaxLength与replaceGroupDiffInfo函数不需要执行
    const hasGroup = !!editor.root_cell.groups.length;
    const headerFieldsInfo = hasGroup ? this.getHeaderFieldMaxLength(editor.root_cell) : false;
    const exeReplaceGroup = hasGroup && Object.keys(headerFieldsInfo).length;
    const headerHasPageNum = !!editor.getFieldsByName(this.systemVariables.page_number, editor.header_cell).length;
    const footerHasPageNum = !!editor.getFieldsByName(this.systemVariables.page_number, editor.footer_cell).length;
    editor.header_diff_info = {};
    const pages = editor.pages;
    for (let i = 0; i < pages.length; i++) {
      const page = pages[i];
      if (exeReplaceGroup) {
        page.header.replaceGroupDiffInfo(headerFieldsInfo);
      }
      if (headerHasPageNum) {
        page.header.initCellData();
        page.header.replacePageInfo();
      }
      if (footerHasPageNum) {
        page.footer.initCellData();
        page.footer.replacePageInfo();
      }
    }
    if (!headerHasPageNum) {
      pages[0].header.replacePageInfo();
    }
    if (!footerHasPageNum) {
      pages[0].footer.replacePageInfo();
    }
  },
  getHeaderFieldMaxLength (root_cell: Cell) {
    const lengthInfo: any = {};
    // 处理分组中包含的页眉数据信息
    const groups = root_cell.groups;
    const editor = root_cell.editor;
    for (let i = 0; i < groups.length; i++) {
      const header_info = groups[i].header_info;
      if (!header_info) {
        continue;
      }
      for (const key in header_info) {
        if (!lengthInfo[key]) {
          lengthInfo[key] = {};
          lengthInfo[key].width = -1;
          lengthInfo[key].value = "";
        }
        let field = editor.getFieldById(key, editor.header_cell);
        if (!field) {
          field = editor.getFieldsByName(key, editor.header_cell)[0];
        }
        if (!field) {
          continue;
        }
        let totalWidth = 0;
        const font = new Font(field.style);
        for (const text of String(header_info[key])) {
          const { width } = Renderer.measure(font, text, editor);
          totalWidth += width;
        }
        if (lengthInfo[key].width < totalWidth) {
          lengthInfo[key].width = totalWidth;
          lengthInfo[key].value = header_info[key];
        }
      }
    }
    return lengthInfo;
  },
  createCustomWidthCharToField (field: XField, ori_str: string, max_str: string) {
    const cur_width = field.textToFieldCharacter(ori_str).reduce(function (total, currentValue) {
      return total + currentValue.width;
    }, 0);
    const max_width = field.textToFieldCharacter(max_str).reduce(function (total, currentValue) {
      return total + currentValue.width;
    }, 0);
    const width = max_width - cur_width;
    const char = new Character(new Font(field.style), " ", width > 0 ? width : 0);
    char.field_id = field.id;
    return char;
  },
  // 表格拆分逻辑
  add (page:Page, elements: (Table | Row)[], page_index: number): (Table | Row)[] {
    // page_index 的赋值在各个操作的时候，首次渲染的时候全部选择 即为0
    page.children.splice(page_index, page.children.length);

    // 光标位置 如果不在第一页 没有子元素 就在刚开始的位置 如果有子元素 就在元素末尾
    // 如果是第一行的话 就是该行顶部边界 距离 当前页顶部边界的距离
    // 如果不是第一行的话 就是上一行的bottom(还是距离当前页顶部边界的距离)
    if (page_index === 0) {
      // 如果从开头更新
      // 那么第一行的 top 值就应该是 页眉的底部边界距离该页顶部边界的距离 加上 配置的 该页的内容距离页眉底部边界的距离
      page.cursor_position = page.header.header_outer_bottom + page.editor.config.content_margin_header;
    } else {
      if (page.children.length) {
        // 如果不是从第 0 行开始 并且还得有内容 那么 更新处的 元素 top 值 就应该是 上一行的 bottom 的值
        page.cursor_position = page.children[page_index - 1].bottom;
      } else {
        page.cursor_position = 0;
      }
    }
    // elements 的各个子元素的属性赋值 elements数组的值来自 root_cell.children, root_node
    // element 是row | table
    for (let i = 0; i < elements.length; i++) {
      const element = elements[i];

      element.top = page.cursor_position; // 所以每个row或者table的 top值都是距离 所在页 顶部边界的距离

      // 段落前间距
      if (isRow(element) && element.row_index_in_para === 0) {
        element.top =
          element.top +
          element.paragraph.before_paragraph_spacing * element.height;
      }
      // TODO 图片拖至最大高度时页眉页脚添加row会挤压图片导致页面崩溃，此方法可以避免此种情况，但仍需验证
      // if (isRow(element)) {
      //   element.children.forEach(e => {
      //     if (isImage(e)) {
      //       const content_height = this.footer.footer_outer_top - this.header.header_outer_bottom - Config.content_margin_footer - Config.content_margin_header;
      //       if (e.height > content_height) {
      //         e.height = content_height;
      //         element.height = content_height;
      //       }
      //     };
      //   });
      // }
      if (isTable(element) && element.fullPage && element.editor.isUseCurrentLogic()) {
        element.refreshTableRowSize();
      }
      // element.top 是在这儿赋的值 但是 elemtn.bottom 都是计算属性 用的 top + height
      // 元素底部高度是否超出有效页面的高度
      // 避免 10.000002 > 10 的这种情况
      let contidions = false;
      if (isUseCurrentLogic(page.editor)) {
        contidions = keepDecimal(element.bottom, 3) > keepDecimal(page.footer.footer_outer_top - page.editor.config.content_margin_footer, 3);
      } else {
        contidions = element.bottom >
        page.footer.footer_outer_top - page.editor.config.content_margin_footer;
      }

      if (contidions) {
        if (isRow(element)) {
          // 页面刚开始渲染,内容超出页面会调用这个方法，有返回值的时候会进入递归循环里 => editor => update
          return elements.slice(i, elements.length); // 超出了 当前页放不下 返回剩余元素并将剩余元素放在下一页
        }
        if (isTable(element)) {
          // if (EditorLocalTest.transUse && element.origin && element.origin.split_parts.length > 0) {
          //   const nextElement = elements[i + 1];

          //   element.page_number = this.number;
          //   element.page_index = page_index;
          //   page_index += 1;
          //   this.cursor_position = element.bottom;
          //   this.children.push(element);
          //   const result = elements.slice(i + 1, elements.length);
          //   if (isTable(nextElement) && nextElement.origin === element.origin) {
          //     return result;
          //   } else {
          //     continue;
          //   }
          // }
          // 记录表格优化的想法 ↓
          // 1. 改成一边计算单元格的 row_size 一边判断应该放到上一页还是拆分该单元格
          // 记录表格优化的想法 ↑

          // 分割线高度 应该是理论上的分割线位置处 距离表格该表格顶部边界的距离 跟下行注释比 这是真实的理论位置
          // 不能用 this.height - this.padding_bottom - element.top; 这样计算 因为页脚可能内容很多 这个算法没有考虑页脚内容和设置的 footer_top 等情况
          const split_line =
            page.footer.footer_outer_top -
            page.editor.config.content_margin_footer -
            element.top;
          const tables = this.split(element, split_line, page.editor.root_cell);
          // if (!EditorLocalTest.transUse) {
          if (element.page_break) {
            // 此element已经被分割过了
            element.getOrigin().split_parts.splice(-1, 1, ...tables);
          } else {
            element.getOrigin().split_parts = tables; // 没分割过 将操作后的tables数组赋值给对应的element对象
          }
          // }

          if (tables.length === 1) return elements.slice(i, elements.length); // 整个表格移到下一页

          element.page_number = page.number;

          element.page_index = page_index;

          // 分割后的两个列表
          const pre_table = tables[0];

          const nextTables = tables.slice(1);

          pre_table.page_number = page.number;

          page.children.push(pre_table);

          pre_table.page_index = page.children.indexOf(pre_table);

          const result = elements.slice(i + 1, elements.length);

          result.unshift(...nextTables);

          return result;
        }
      } else if (isTable(element)) {
        // 此时是没有分割的
        if (element.fullPage && page.editor.isUseCurrentLogic()) {
          let preHeight = 0;
          for (let i = 0; i < element.row_size.length - 1; i++) {
            preHeight += element.row_size[i];
          }
          // const height = page.footer.footer_outer_top - (preHeight + element.top + page.editor.config.content_margin_header);
          const contentHeight = page.footer.footer_outer_top - page.header.header_outer_bottom - page.editor.config.content_margin_footer - page.editor.config.content_margin_header;
          const height = contentHeight - preHeight;

          element.row_size[element.row_size.length - 1] = height;
        }
      }

      if (isTable(element)) {
        // if (EditorLocalTest.transUse && element.origin && element.origin.split_parts.length > 0) {
        //   const nextElement = elements[i + 1];

        //   element.page_number = this.number;
        //   element.page_index = page_index;
        //   page_index += 1;

        //   this.cursor_position = element.bottom;

        //   this.children.push(element);
        //   const result = elements.slice(i + 1, elements.length);
        //   if (isTable(nextElement) && nextElement.origin === element.origin) {
        //     return result;
        //   } else {
        //     continue;
        //   }
        // } else {
        // 未被分割，重置split属性
        this.resetSplitAttr(element);
        // }
      }

      element.page_number = page.number;

      page.children.push(element);

      elements[i].page_index = page_index;

      page_index += 1;

      page.cursor_position = element.bottom;

      // TODO 普通文档的分页和下方分组分页的逻辑可能出现冲突，同时使用的时候请注意
      if (isRow(element) && element.page_break) {
        return elements.slice(i + 1, elements.length);
      }

      const nextElement = elements[i + 1];

      // 处理表格的分页
      if (isTable(nextElement) && nextElement.newPage) {
        return elements.slice(i + 1, elements.length);
      }

      if ((isTable(element)) && element.group_id) {
        // 所在分组
        const group = page.editor.selection.getGroupByGroupId(element.group_id);
        // 分组换页 分组最后一个块级元素 当前元素为表格
        if (group!.page_break && element.id === group!.content_para_id[group!.content_para_id.length - 1]) {
          return elements.slice(i + 1, elements.length);
        }
      } else if (isRow(element) && element.paragraph.group_id) {
        // 所在分组
        const group = page.editor.selection.getGroupByGroupId(element.paragraph.group_id);
        if (group!.page_break) {
          const groupPara = group!.paragraph;
          // 分组最后一段
          const last_para = (groupPara[groupPara.length - 1] as Paragraph);
          // 分组换页 当前元素为分组最后一行
          if (element.id === last_para.id || element.id === last_para.lastRow?.id) {
            return elements.slice(i + 1, elements.length);
          }
        }
      }

      // 处理分组的分页
      if ((isTable(nextElement)) && nextElement.group_id) {
        const group = page.editor.selection.getGroupByGroupId(nextElement.group_id)!;
        if (group.new_page && nextElement.id === group.content_para_id[0]) {
          return elements.slice(i + 1, elements.length);
        }
      } else if ((isRow(nextElement)) && nextElement.paragraph.group_id) {
        const group = page.editor.selection.getGroupByGroupId(nextElement.paragraph.group_id)!;
        if (group.new_page) {
          const firstParagraph = group.paragraph[0] as Paragraph;
          if (nextElement.id === firstParagraph.firstRow?.id) {
            return elements.slice(i + 1, elements.length);
          }
        }
      }
    }

    return [];
  },
  split (table:Table, split_line: number, parent: Cell): Table[] {
    if (table.editor.isUseCurrentLogic()) {
      return this.newVersionTableSplit(table, split_line, parent);
    }
    const table1 = new Table(
      table.editor,
      table.id,
      table.group_id,
      table.col_size,
      table.row_size,
      table.min_row_size,
      parent,
      parent.left + parent.padding_left,
      parent.right - parent.padding_right,
      table.top,
      false,
      SkipMode.ROW
    );
    const table2 = new Table(
      table.editor,
      table.id,
      table.group_id,
      table.col_size,
      table.row_size,
      table.min_row_size,
      parent,
      parent.left + parent.padding_left,
      parent.right - parent.padding_right,
      table.editor.config.page_padding_top, // 第二个表格 一定在这一页的开头 所以top值等于这个
      false,
      SkipMode.ROW
    );

    // 原始表格时所有的分割属性重新初始化
    if (!table.origin) {
      this.resetSplitAttr(table);
    }
    this.recordSplitLine(table, split_line);
    table1.origin = table2.origin = table.getOrigin();

    let split_cell_row_index: number = 0;
    let page_break_line: number; // 功能跟 split_line 一样 只不过在硬分割的时候值要改变一下为 cell.top 软分割的时候 不用更改 就是 split_line

    // 遍历一遍，查找是否存在硬分割
    for (let i = 0; i < table.children.length; i++) {
      const cell = table.children[i];

      let hard_split_flag = false;

      // 不用考虑 cell.bottom === split_line 的情况，此情况肯定table至少有两行，且存在某个cell top = split_line 必存在一个cell bottom = split_line
      if (cell.top === split_line) {
        // 该单元格的top值 应该就是上边单元格的bottom值 所以只找top值相等的就可以了
        hard_split_flag = true;
      }

      if (cell.top < split_line && cell.bottom > split_line) {
        const cell_split_line = split_line - cell.top; // 分割线距离当前单元格顶部边界的距离

        hard_split_flag = this.ifHardSplit(cell, cell_split_line); // 分割线如果正好穿过单元格内第一行内容的话 不能将字给拆成上下两部分 所以要将那种情况看做硬分割 所以这里要做一个判断 仅仅靠那个 if 是不够的
      }
      // 该单元格是硬分割
      if (hard_split_flag) {
        // cell 在第一行，且分割线移到 cell 头部，整个表格下移
        if (cell.position[0] === 0) {
          return [table];
        }
        // 分割线在cell边界处
        table1.page_break_type = table2.page_break_type = BreakType.hard;

        page_break_line = cell.top;

        split_cell_row_index = cell.position[0];

        break;
      }
    }

    // TODO ？？先查找了一遍硬分割 就是说 优先 硬分割 为什么？ 就没有 先找到了 硬分割 然后软分割 分不了的情况吗？
    if (table1.page_break_type === BreakType.hard) {
      // 硬分割情况
      table.assembleSplitTable(
        // 装配拆分后的表格 就是拆分row_size, min_row_size, 还有单元格(单元格位置也是经过修改的)
        table1,
        table2,
        page_break_line!,
        split_cell_row_index,
        BreakType.hard
      );
      // 处理表格线的透明度 - 硬分割 ↓
      // 硬分割 下方的表格会多出一条横线  所以changeOpacityRow 横坐标要多出一组改变横坐标的  changeOpacityCol 也要多出一组 改变的是横坐标
      const origin_talbe =
      table.getOrigin().split_parts[table.getOrigin().split_parts.length - 1] ||
      table.getOrigin();
      // 根表格 所有修改透明度的 横线和竖线 编号
      const all_opacity_rows = JSON.parse(
        JSON.stringify(origin_talbe.notAllowDrawLine.changeOpacityRow)
      );
      const all_opacity_cols = JSON.parse(
        JSON.stringify(origin_talbe.notAllowDrawLine.changeOpacityCol)
      );
      // 1. 计算table1总共 有多少行
      const table1_row_size = table1.row_size.length;
      const table1_opacity_rows = []; // 计算出上个表格改透明度的下标
      for (let i = 0; i < all_opacity_rows.length;) {
        const horizontal_line_number = all_opacity_rows[i][0]; // 横坐标
        if (horizontal_line_number <= table1_row_size) {
          // 横坐标 小于 等于 row_size 就是属于table1的
          const numbers = all_opacity_rows.splice(i, 1); // 删除掉 给table1
          table1_opacity_rows.push([...numbers[0]]);
        } else {
          // 否则就是属于 table2的
          all_opacity_rows[i][0] -= table1_row_size;
          i++;
        }
      }
      // 经过上个循环 table2会少一条横线 所以要 循环一遍table1的横线编号 如果最后一行有 那么table2第一行就得有 加上
      for (let i = 0; i < table1_opacity_rows.length; i++) {
        if (table1_opacity_rows[i][0] === table1_row_size) {
          all_opacity_rows.push([0, table1_opacity_rows[i][1]]);
        }
      }
      table1.notAllowDrawLine.changeOpacityRow = table1_opacity_rows;
      table2.notAllowDrawLine.changeOpacityRow = all_opacity_rows;

      const table1_opacity_cols = []; // 计算出上个表格的透明度数组
      for (let i = 0; i < all_opacity_cols.length;) {
        const vertical_line_number = all_opacity_cols[i][1];
        if (vertical_line_number <= table1_row_size - 1) {
          const numbers = all_opacity_cols.splice(i, 1);
          table1_opacity_cols.push([...numbers[0]]);
        } else {
          all_opacity_cols[i][1] -= table1_row_size;
          i++;
        }
      }
      table1.notAllowDrawLine.changeOpacityCol = table1_opacity_cols;
      table2.notAllowDrawLine.changeOpacityCol = all_opacity_cols;
      // 处理表格线的透明度 ↑
    } else {
      // TODO ？？没有一个单元格能够硬分割 才会进这里进行软分割
      // 进入此行，说明是软分割,
      let split_row_top = 0; // 其实就是row_size相加值 就是单元格上方每行的高度和 是个过渡变量 没什么实际用途
      let split_row_index = 0; // 被分割线分割的虚拟单元格的索引

      for (let i = 0; i < table.row_size.length; i++) {
        if (split_row_top + table.row_size[i] > split_line) {
          // TODO ?? split_line 是距离当前页的表格顶部边界的距离 this 也不是原始表格 拆分多页的表格 this 也只是当前要被拆分的表格
          split_row_index = i;
          break;
        }
        split_row_top += table.row_size[i];
      }
      this.assembleSplitTable(
        table,
        table1,
        table2,
        split_line,
        split_row_index, // 软分割的时候 说明 这个 row_size 应该被拆分
        BreakType.soft
      );
      // 软分割 - 处理表格线的透明度 代码必须写在这个else的分支 最后 table1才有东西 ↓
      // 就一个单元格来说 - 借鉴硬分割：是table2多画一条横线(行坐标为0)  那么软分割：是table1多画了一条横线(行坐标为table1.row_size.length) table2多画了一条横线(行坐标为0)和两条竖线(行坐标为0)
      // table1最后一个条横线和table2第一条横线就不管了,因为他们肯定是要画出来的, 然后就是根据table1的两条竖线画talbe2的两条竖线(table2多画的两条竖线依赖于table1行坐标为table1.row_size.length - 1的竖线)  只管竖线
      const origin_talbe =
      table.getOrigin().split_parts[table.getOrigin().split_parts.length - 1] ||
      table.getOrigin(); // 这个获取的是：新拆分之前的上一页的表格
      // 根表格 所有修改透明度的 横线和竖线 编号
      const all_opacity_rows = JSON.parse(
        JSON.stringify(origin_talbe.notAllowDrawLine.changeOpacityRow)
      );
      const backup_rows = JSON.parse(
        JSON.stringify(origin_talbe.notAllowDrawLine.changeOpacityRow)
      ); // 备用 因为all_opacity_rows会被修改
      const all_opacity_cols = JSON.parse(
        JSON.stringify(origin_talbe.notAllowDrawLine.changeOpacityCol)
      );
      // 1. 计算table1总共 有多少行
      const table1_row_size = table1.row_size.length;
      const table1_opacity_rows = []; // 计算出上个表格改透明度的下标
      for (let i = 0; i < all_opacity_rows.length;) {
        const horizontal_line_number = all_opacity_rows[i][0]; // 横坐标
        // 如果原表格的最后一条横线 是透明的 那么第二个表格的最后一条横线也是透明的
        if (horizontal_line_number < table1_row_size - 1) {
          // 横坐标 小于 row_size 就是属于table1的
          const numbers = all_opacity_rows.splice(i, 1); // 删除掉
          table1_opacity_rows.push([...numbers[0]]); // 给table1
        } else if (horizontal_line_number === table1_row_size - 1) {
          table1_opacity_rows.push([all_opacity_rows[i][0], all_opacity_rows[i][1]]); // 给table1
          all_opacity_rows[i] = [0, all_opacity_rows[i][1]]; // 因为是软分割 所以必然要留下一个给 table2 行值就是 0
          i++;
        } else {
          // 否则就是属于 table2 的
          all_opacity_rows[i][0] -= table1_row_size - 1;
          i++;
        }
      }
      // 循环备用的那个数组
      for (let i = 0; i < backup_rows.length; i++) {
        const horizontal_line_number = backup_rows[i][0]; // 横坐标
        if (horizontal_line_number === origin_talbe.row_size.length) {
          table1_opacity_rows.push([table1.row_size.length, backup_rows[i][1]]);
        }
        if (horizontal_line_number === 0 && split_row_index === 0) {
          all_opacity_rows.push([0, backup_rows[i][1]]);
        }
      }
      table1.notAllowDrawLine.changeOpacityRow = table1_opacity_rows;
      table2.notAllowDrawLine.changeOpacityRow = all_opacity_rows;

      const table1_opacity_cols = []; // 计算出table1的透明度数组
      for (let i = 0; i < all_opacity_cols.length;) {
        const vertical_line_number = all_opacity_cols[i][1]; // 行坐标
        if (vertical_line_number < table1_row_size) {
          // 行坐标小于 table1_row_size的 是属于table1的
          const numbers = all_opacity_cols.splice(i, 1);
          table1_opacity_cols.push([...numbers[0]]);
        } else {
          // 否则就是属于talbe2的
          all_opacity_cols[i][1] -= table1_row_size - 1;
          i++;
        }
      }
      // 经过上方的循环 table2 应该是少两条竖线 和 一条横线的 (横线先不管  竖线得画上)
      for (let i = 0; i < table1_opacity_cols.length; i++) {
        if (table1_opacity_cols[i][1] === table1_row_size - 1) {
          // 就看最后一行的
          all_opacity_cols.push([table1_opacity_cols[i][0], 0]);
        }
      }
      table1.notAllowDrawLine.changeOpacityCol = table1_opacity_cols;
      table2.notAllowDrawLine.changeOpacityCol = all_opacity_cols;
      // 软分割 - 处理表格线的透明度 ↑
    }

    // 处理表格线  ↓
    const table1_cells = table1.children;
    const table2_cells = table2.children;
    for (const cell of table1_cells) {
      const res = cell.getInsideLineNumber();
      table1.notAllowDrawLine.row.push(...res.res_rows);
      table1.notAllowDrawLine.col.push(...res.res_cols);
    }
    for (const cell of table2_cells) {
      // 这个不用挪了，有别的地方在用
      const res = cell.getInsideLineNumber();
      table2.notAllowDrawLine.row.push(...res.res_rows);
      table2.notAllowDrawLine.col.push(...res.res_cols);
    }
    // 处理表格线  ↑

    // 目标：固定表头
    // 思考逻辑：处理时机就是在这个方法里边，就是在 modelData 转为 viewData 的时候，将第一页表格中表头的所有单元格都放到下一页中的开头去，然后再修改 row_size 和 min_row_size 等各种属性
    // 表头是不能够被拆分的 甚至说表头 下方必须有一行 表头也不能在第一页的最后 如果出现那些情况就让固定表头的功能失效
    // 固定表头实现 ↓
    const originTable = table.getOrigin();
    const rowNum = originTable.fixed_table_header_num; // 表头总共四行 从1开始数的
    const cancel_fixed = table1.children.every(
      (cell) => cell.position[0] < rowNum
    ); // 如果第一页的单元格 全部都是表头中的 那么就应该 取消固定表的功能
    if (table.fixed_table_header_num && !cancel_fixed) {
      const headerCells = originTable.children.filter(
        (cell:any) => cell.position[0] < rowNum
      );

      const firstRowHeight = originTable.row_size.slice(0, rowNum);
      // 所有单元格的位置 行数 暂时都加1
      table2.children.forEach((cell) => {
        cell.position[0] += rowNum;
      });
      table2.children.unshift(...headerCells);
      table2.row_size.unshift(...firstRowHeight);
      table2.min_row_size.unshift(...table.min_row_size.slice(0, rowNum));

      const notDrawRow: number[][] = [];
      originTable.notAllowDrawLine.row.forEach((rowLine) => {
        if (rowLine[0] < rowNum) {
          notDrawRow.push(rowLine);
        }
      });
      table2.notAllowDrawLine.row.forEach((position) => {
        position[0] += rowNum;
      });
      table2.notAllowDrawLine.row.unshift(...notDrawRow);

      const opacityRow: number[][] = [];
      originTable.notAllowDrawLine.changeOpacityRow.forEach((rowLine) => {
        if (rowLine[0] < rowNum) {
          opacityRow.push(rowLine);
        }
      });
      table2.notAllowDrawLine.changeOpacityRow.forEach((position) => {
        position[0] += rowNum;
      });
      table2.notAllowDrawLine.changeOpacityRow.unshift(...opacityRow);

      const notDrawCol: number[][] = [];
      originTable.notAllowDrawLine.col.forEach((colLine) => {
        if (colLine[1] < rowNum) {
          notDrawCol.push(colLine);
        }
      });
      table2.notAllowDrawLine.col.forEach((position) => {
        position[1] += rowNum;
      });
      table2.notAllowDrawLine.col.unshift(...notDrawCol);

      const opacityCol: number[][] = [];
      originTable.notAllowDrawLine.changeOpacityCol.forEach((colLine) => {
        if (colLine[1] < rowNum) {
          opacityCol.push(colLine);
        }
      });
      table2.notAllowDrawLine.changeOpacityCol.forEach((position) => {
        position[1] += rowNum;
      });
      table2.notAllowDrawLine.changeOpacityCol.unshift(...opacityCol);
      table1.fixed_table_header_num = table2.fixed_table_header_num =
        originTable.fixed_table_header_num;
    }
    // 固定表头实现 ↑

    // 有的医院要求 表格线要撑满整页
    // 软分割也会有字体字号的问题
    if (table.fullPage && table.editor.isUseCurrentLogic()) {
      let preHeight = 0;
      for (let i = 0; i < table1.row_size.length - 1; i++) {
        preHeight += table1.row_size[i];
      }
      const page = table.editor.pages[0];
      // const height = page.footer.footer_outer_top - (preHeight + table.top + table.editor.config.content_margin_header + table.editor.config.content_margin_footer);
      const contentHeight = page.footer.footer_outer_top - page.header.header_outer_bottom - page.editor.config.content_margin_footer - page.editor.config.content_margin_header;
      const height = contentHeight - preHeight;

      table1.row_size[table1.row_size.length - 1] = height;
    }
    table1.tableFiexedStyle = table2.tableFiexedStyle = table.tableFiexedStyle;
    table1.name = table2.name = table.name;
    table1.skipMode = table2.skipMode = table.skipMode;
    table1.editableInFormMode = table2.editableInFormMode = table.editableInFormMode;
    table1.newPage = table2.newPage = table.newPage;
    return [table1, table2];
  },
  resetSplitAttr (table:Table) {
    table.split_line_arr = [];
    table.split_parts = [];
    table.page_break = false;
    const cells = table.children;
    for (let i = 0; i < cells.length; i++) {
      // cells[i].resetSplitAttr();
      cells[i].page_break = false;
      cells[i].split_parts = [];
      cells[i].split_row_indexes = [];
    }
  },
  recordSplitLine (table:Table, split_line: number) {
    if (table.page_break) {
      // 此表格已经被分割过
      const origin = table.getOrigin();
      const split_line_arr = origin.split_line_arr;
      const previous_split_line = split_line_arr[split_line_arr.length - 1];
      split_line_arr.push(previous_split_line + split_line);
    } else {
      table.split_line_arr = [split_line];
    }
  },
  ifHardSplit (cell:Cell, split_line: number) {
    const first_child = cell.children[0];
    if (!first_child) return false;

    const top = 0;
    let bottom = first_child.bottom;
    // 对于上下居中的单元格，则要按照不居中时来判断是否需要硬分割
    if (first_child.top !== 0) {
      bottom = first_child.bottom - first_child.top;
    }

    return (
      isRow(first_child) &&
      top < split_line &&
      bottom > split_line
    );
  },
  assembleSplitTable (
    table:Table,
    table1: Table,
    table2: Table,
    split_line: number,
    split_row_index: number,
    break_type: BreakType
  ) {
    // 因为有过渡变量 table1_split_row_index 和 截取的时候 两个表格 用的变量不一样 所以硬分割的时候 正好分割 row_size 和 min_row_size 在软分割的时候 应该被拆分的 row_size 和 min_row_size 被分给了上下两个表格(两个表格都有了这个值)
    const table1_split_row_index =
      break_type === BreakType.hard ? split_row_index : split_row_index + 1;
    // 分配 row_size 和 min_row_size 给两个表格
    table1.row_size = table.row_size.slice(0, table1_split_row_index);
    table1.min_row_size = table.min_row_size.slice(0, table1_split_row_index);
    table2.row_size = table.row_size.slice(split_row_index);
    table2.min_row_size = table.min_row_size.slice(split_row_index);
    table1.fullPage = table2.fullPage = table.fullPage || false;
    // 软分割时要设置被分割后 table1 末尾行与 table2 首行的 min_row_size，然后在后面调用 refreshTableRowSize 中依照 min_row_size 与内容高度进行重置两个 table 的 row_size
    if (break_type === BreakType.soft) {
      const sumTable1RowSize = table.row_size // 第一个表格 所有的 row_size 的集合
        .slice(0, table1_split_row_index)
        .reduce((num1: number, num2: number) => {
          return num1 + num2;
        });
      const ori_table1_end_row_size = table.row_size[table1.row_size.length - 1]; // ？？ 在原始表格中 要拆分的这一行的 row_size 大小

      table1.min_row_size[table1.row_size.length - 1] = // table1 最后一个 min_row_size 的 min_row_size
        ori_table1_end_row_size - (sumTable1RowSize - split_line);
      // table2.min_row_size[0] = sumTable1RowSize - split_line;
      const calc_row_size =
      table.min_row_size[split_row_index] -
        table1.min_row_size[table1.row_size.length - 1];
      table2.min_row_size[0] =
        calc_row_size < Config.min_row_size
          ? Config.min_row_size
          : calc_row_size;
    }

    const cellTopMap: any = {};
    table.row_size.forEach((rs:any, index, array) => {
      if (index === 0) {
        cellTopMap[index] = 0;
      } else {
        cellTopMap[index] = array
          .slice(0, index)
          .reduce((total, current) => total + current);
      }
    });

    // 分配 cell 给表格 this 指的是从当前页要拆分的表格(不包括已经拆分好的 上一页的表格数据了 就是要将 this 拆成两个表格)
    for (let i = 0; i < table.children.length; i++) {
      const cell = table.children[i];
      const top = cellTopMap[cell.position[0]];
      const bottom = top + cell.height;
      if (top < split_line && bottom > split_line) {
        const cells = this.cellSplit(
          cell,
          // 之前先找硬分割 只是计算 split_line ？？ 这才是真正的 拆分表格，本质上就是拆分单元格
          split_line,
          split_row_index,
          table1,
          table2,
          break_type
        );
        table1.children.push(cells[0]);
        table1.cellMap.set(cells[0].id, cells[0]);
        cells[1].position[0] = 0;
        table2.children.push(cells[1]);
        table2.cellMap.set(cells[1].id, cells[1]);
        // cell.setModelCellWhenSplitTbl(); // TODO  这里也不需要进行 重置 cell 的 parent 吧
      } else if (bottom <= split_line) {
        // 应该放到 上一个表格中
        this.pushCellCopy2Table(table1, cell);
      } else if (top >= split_line) {
        // 该单元格 应该属于 下一页的表格2
        // 原来是进行了 copy 因为改变这个 cell modelData 上的 cell 也会改变 他们是同一个东西 最起码位置信息就要改变 但是还不能影响到 modelData
        const new_cell = new Cell(
          cell.editor,
          [cell.position[0] - split_row_index, cell.position[1]],
          cell.colspan,
          cell.rowspan,
          table2,
          cell.id
        );
        Cell.attrJudgeUndefinedAssign(new_cell, cell);
        new_cell.children = cell.children;
        new_cell.paragraph = cell.paragraph;
        // judgeUndefinedAssign(new_cell, cell, ["padding_bottom", "style", "padding_top", "padding_right", "padding_left", "vertical_align", "is_show_slash_up", "is_show_slash_down", "hf_part", "children", "paragraph"]);
        // 因为当前表格如果为已经分割过的表格，此时所有的又重新copy,将会与原始单元格中记录的单元格不一致，所以此处记录id用户路径转换时作对比
        new_cell.origin = cell.getOrigin();

        // 清空split单元格属性，table1不需要清除 否则跨页时会将原数据中内容清掉

        // cell.getOrigin().resetSplitAttr();
        table2.children.push(new_cell);
        table2.cellMap.set(new_cell.id, new_cell);
      }
      this.resetSplitRowIndexs(cell);
    }
    table1.refreshTableRowSize();
    const editor = table.editor;
    const page = editor.pages[0];
    if (table1.bottom > page.footer.footer_outer_top && editor.isUseCurrentLogic()) {
      table1.row_size[table1.row_size.length - 1] -= (table1.bottom - page.footer.footer_outer_top + editor.config.content_margin_footer);
      table1.min_row_size[table1.min_row_size.length - 1] -= (table1.bottom - page.footer.footer_outer_top + editor.config.content_margin_footer);
    }
    // 如果不调用该函数，则分割后的表格高度计算不正确，会有死循环的情况
    table2.refreshTableRowSize();

    // table1.row_size[table1.row_size.length - 1] = table1.min_row_size[table1.min_row_size.length - 1];
    // table2.row_size[0] = table2.min_row_size[0];
    // table2.row_size[table2.row_size.length - 1] = table2.min_row_size[table2.min_row_size.length - 1];
    table1.page_break = table2.page_break = true; // 记录下来 说明该表格 是被拆分过的表格
  },
  pushCellCopy2Table (table: Table, cell: Cell): Cell {
    const new_cell = new Cell(
      cell.editor,
      [...cell.position],
      cell.colspan,
      cell.rowspan,
      table,
      cell.id
    );
    // 这个不用挪，用的地方有点多
    Cell.attrJudgeUndefinedAssign(new_cell, cell);
    new_cell.children = cell.children;
    new_cell.paragraph = cell.paragraph;
    // judgeUndefinedAssign(new_cell, cell, ["padding_bottom", "style", "padding_top", "padding_right", "padding_left", "vertical_align", "is_show_slash_up", "is_show_slash_down", "hf_part", "children", "paragraph"]);
    new_cell.origin = cell.getOrigin();
    table.children.push(new_cell);
    table.cellMap.set(new_cell.id, new_cell);
    return new_cell;
  },
  resetSplitRowIndexs (cell:Cell) {
    const originCell = cell.getOrigin();
    originCell.split_row_indexes = [];
    if (originCell.split_parts.length) {
      let last_length = 0;
      for (let i = 0; i < originCell.split_parts.length - 1; i++) {
        const childrenLength = originCell.split_parts[i].children.length;
        last_length += childrenLength;
        originCell.split_row_indexes.push(last_length);
      }
    }
  },
  cellSplit (
    cell:Cell,
    split_line: number,
    split_row_index: number,
    parent1: Table,
    parent2: Table,
    type = BreakType.soft,
    unassignedRowSize?: number[]

  ): Cell[] {
    let top;
    if (unassignedRowSize) {
      top = cell.position[0] === 0
        ? 0
        : unassignedRowSize
          .slice(0, cell.position[0])
          .reduce((total, current) => total + current);
    } else {
      top = cell.top;
    }
    const cell_split_line = split_line - top;

    const cell1 = new Cell(
      cell.editor,
      cell.position,
      cell.colspan,
      cell.rowspan,
      parent1
    );
    const cell2 = new Cell(
      cell.editor,
      cell.position,
      cell.colspan,
      cell.rowspan,
      parent2
    );
    // 拆分的单元格保持id一致， 目前是解决鼠标移入查看痕迹修改人用
    // TODO 加上判断临时解决，需再排查根本原因
    if (cell.editor.view_mode === "person") {
      cell1.id = cell2.id = cell.id;
    }
    if (cell.editor.document_meta.handleTableRowSize) {
      cell1.padding_top = cell.padding_top;
      cell2.padding_bottom = cell.padding_bottom;
    }

    cell1.is_show_slash_up = cell2.is_show_slash_up = cell.is_show_slash_up;
    cell1.is_show_slash_down = cell2.is_show_slash_down = cell.is_show_slash_down;

    cell1.colspan = cell2.colspan = cell.colspan;
    cell2.rowspan = cell2.rowspan = cell.rowspan;
    cell1.style = cell2.style = cell.style;

    const origin = cell.getOrigin();

    cell1.position = [...cell.position];
    cell2.position = [0, cell.position[1]];

    if (type === BreakType.soft) {
      cell1.rowspan = split_row_index - cell.position[0] + 1;
      cell2.rowspan = cell.rowspan + 1 - cell1.rowspan;
      // 硬分割时，分界线在split_row_index上方
    } else {
      cell1.rowspan = split_row_index - cell.position[0];
      cell2.rowspan = cell.rowspan - cell1.rowspan;
    }
    for (let i = 0; i < cell.children.length; i++) {
      const child = cell.children[i] as Row; // 暂时视为row

      if (child.bottom <= cell_split_line) {
        const new_row = child.copy(cell1);
        new_row.id = child.id;
        cell1.children.push(new_row);
      }
      // 当child 的top值与cell_split_line相等时，也需要设置split_row_indexes值，否则路径转换出错
      if ((child.top <= cell_split_line && child.bottom > cell_split_line) ||
        child.top > cell_split_line) {
        const new_row = child.copy(cell2);
        new_row.id = child.id;
        cell2.children.push(new_row);
      }
    }
    this.resetSplitCell(cell2);

    if (cell.page_break) {
      if (cell.editor.isUseCurrentLogic()) {
        origin.split_parts.push(cell1, cell2); // 因为 新版表格拆分 外边 pop 了 所以这里都要 push 进去
      } else {
        origin.split_parts.splice(-1, 1, cell1, cell2);
      }
    } else {
      origin.split_parts = [cell1, cell2];
    }

    this.generateParagraphByRow(cell1);
    this.generateParagraphByRow(cell2);
    cell1.origin = cell2.origin = origin;
    cell1.page_break = cell2.page_break = true;
    return [cell1, cell2];
  },
  resetSplitCell (cell: Cell) {
    let cursor_position = 0;

    for (let j = 0; j < cell.children.length; j++) {
      const child = cell.children[j]; // TODO 暂时不考虑嵌套表格

      child.top = cursor_position;

      cursor_position += child.height;
    }
  },
  generateParagraphByRow (cell:Cell) {
    const rows = cell.children;
    const paragraphs: Paragraph[] = [];
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i] as Row;
      const rowPara = row.paragraph;
      if (i === 0 || (rows[i - 1] as Row).linebreak) {
        const para = new Paragraph(uuid("para"), cell, rowPara.group_id);
        para.setParagraphAttr(rowPara);
        paragraphs.push(para);
      }
      if (row.children.length) {
        paragraphs[paragraphs.length - 1].characters.push(...row.children);
        paragraphs[paragraphs.length - 1].children.push(row);
      }
      if (row.linebreak) {
        paragraphs[paragraphs.length - 1].characters.push(row.linebreak);
      }
    }
    cell.paragraph = paragraphs;
  },

  // 新版表格拆分获取单元格的 top 值和 bottom 值
  getCellTopAndBottom (cell: Cell, unRowSize: number[]): {top: number, bottom: number} {
    const top = keepDecimal(cell.getTop(unRowSize), 3);
    const bottom = keepDecimal(top + this.getHeight(cell, unRowSize), 3);
    return {
      top,
      bottom
    };
  },

  // 获取分割的类型 是软分割还是硬分割
  getSplitType (cellsTraversedBySplitLine: Cell[], unRowSize: number[], splitLine: number) {
    let splitRowSizeIndex = 0; // 应该被拆分的 row_size 的下标
    let cellSplitLine = 0; // 拆分单元格使用 默认为 0 跟 splitLine 用法一样
    let hasHardSplit = false;
    for (const spliteCell of cellsTraversedBySplitLine) {
      const cellTop = spliteCell.getTop(unRowSize);
      const distanceToCellTopBorder = splitLine - cellTop;

      if ((this.ifHardSplit(spliteCell, distanceToCellTopBorder) || cellTop === splitLine)) {
        if (cellSplitLine === 0 || cellTop < cellSplitLine) {
          // 得加上 cellTop < cellsplitLine 硬分割只能往上 不能往下 所以得按小的来吧
          splitRowSizeIndex = Math.max(spliteCell.position[0], splitRowSizeIndex);
          cellSplitLine = cellTop;
          hasHardSplit = true;
        }
      }
    }
    cellSplitLine = keepDecimal(cellSplitLine, 3);
    return {
      splitRowSizeIndex,
      cellSplitLine,
      hasHardSplit
    };
  },

  getSplitType2 (cellsTraversedBySplitLine: Cell[], unRowSize: number[], splitLine: number, table: Table) {
    let splitRowSizeIndex = 0;
    let cellSplitLine = 0;

    let hasHardSplit = false;
    for (const cell of cellsTraversedBySplitLine) {
      const cellTop = cell.getTop(unRowSize);
      const distanceToCellTopBorder = splitLine - cellTop;

      if (this.ifHardSplit(cell, distanceToCellTopBorder) || cellTop === splitLine) {
        if (cell.position[0] === 0) {
          table.split_parts.pop();
          return {
            returnTable: [table]
          };
        };
        splitRowSizeIndex = Math.max(cell.position[0], splitRowSizeIndex);
        cellSplitLine = cellTop;
        hasHardSplit = true;
      }
    }
    cellSplitLine = keepDecimal(cellSplitLine, 3);
    return {
      splitRowSizeIndex,
      cellSplitLine,
      hasHardSplit
    };
  },

  handleTempPreTableChildrenHardSplit (preTable: Table, nextTable: Table, tempPreTableChildren: Cell[], unRowSize: number[], cellSplitLine: number, splitRowSizeIndex: number) {
    for (let i = 0; i < tempPreTableChildren.length; i++) {
      const temCell = tempPreTableChildren[i];
      const { top, bottom } = this.getCellTopAndBottom(temCell, unRowSize);
      const originTable = nextTable.origin;
      if (bottom <= cellSplitLine) {
        if (temCell.shouldPushSplitParts) {
          preTable.cellMap.set(temCell.id, temCell);
          preTable.children.push(temCell);
          temCell.getOrigin().split_parts.push(temCell);
          this.addLinesThatAreNotDrawn(preTable, temCell);
        } else {
          preTable.children.push(temCell);
          preTable.cellMap.set(temCell.id, temCell);
          this.addLinesThatAreNotDrawn(preTable, temCell);
        }
      } else {
        // 因为此时的分割线是重置的 所以能到这里的单元格必然是被分割线穿过的 必然是要进行软分割的 而软分割后的单元格必然是 c1 放到上一页 c2 放到下一页
        // c2 不可能再进行分割了 也没有必要进行计算 c2 的高度 因为这是分割线重置之后才软分割的 否则按照原来的分割线 该单元格是毫无疑问要放到上一页中去的
        // 那么有没有可能需要计算高度呢？应该是没有的
        // shouldPushSplitParts 这里也不用考虑 拆分完 全部都要放到 split_parts 里边去 cellSplit 里边正好做了
        if (top < cellSplitLine && bottom > cellSplitLine) {
          const [c1, c2] = this.cellSplit(
            temCell,
            cellSplitLine,
            splitRowSizeIndex, // 被拆分的 row_size 的下标
            preTable,
            nextTable,
            BreakType.hard,
            unRowSize
          );
          if (originTable) {
            const optRow = originTable?.notAllowDrawLine.changeOpacityRow.find(p => p[0] === temCell.end_row_index + 1 && p[1] === temCell.start_col_index);
            if (optRow) {
              for (let i = 0; i < c1.colspan; i++) {
                preTable.notAllowDrawLine.changeOpacityRow.push([c1.end_row_index + 1, c1.start_col_index + i]);
                nextTable.notAllowDrawLine.changeOpacityRow.push([c2.start_row_index, c2.start_col_index + i]);
              }
            }
          }

          preTable.children.push(c1);
          preTable.cellMap.set(c1.id, c1);
          this.addLinesThatAreNotDrawn(preTable, c1);

          nextTable.children.push(c2);
          nextTable.cellMap.set(c2.id, c2);
          this.addLinesThatAreNotDrawn(nextTable, c2);
        } else {
          // 这个 else 是原来的逻辑 应该不会走 但是留着吧 以防万一
          temCell.position[0] -= splitRowSizeIndex;
          if (temCell.shouldPushSplitParts) {
            nextTable.cellMap.set(temCell.id, temCell);
            nextTable.children.push(temCell);
            temCell.getOrigin().split_parts.push(temCell);
            this.resetSplitRowIndexs(temCell);
            this.addLinesThatAreNotDrawn(nextTable, temCell);
          } else {
            temCell.parent = nextTable; // 在一进来 new Cell 的时候 传进去的 parent 是 preTable 是不对的 所以这里更正
            nextTable.children.push(temCell);
            nextTable.cellMap.set(temCell.id, temCell);
            this.addLinesThatAreNotDrawn(nextTable, temCell);
          }
        }
      }
      this.resetSplitRowIndexs(temCell);
    }
    tempPreTableChildren.length = 0;
  },

  handleTempPreTableChildrenSoftSplit (preTable: Table, tempPreTableChildren: Cell[]) {
    // 软分割的时候 这些可以直接放到 preTable 里边去 不会有放到 nextTable 里边的情况
    for (let i = 0; i < tempPreTableChildren.length; i++) {
      const temCell = tempPreTableChildren[i];
      if (temCell.shouldPushSplitParts) {
        preTable.cellMap.set(temCell.id, temCell);
        preTable.children.push(temCell);
        temCell.getOrigin().split_parts.push(temCell);
        this.resetSplitRowIndexs(temCell);
        this.addLinesThatAreNotDrawn(preTable, temCell);
      } else {
        preTable.children.push(temCell);
        preTable.cellMap.set(temCell.id, temCell);
        this.addLinesThatAreNotDrawn(preTable, temCell);
      }
    }
    tempPreTableChildren.length = 0;
  },

  handleCellsTraversedBySplitLineHardSplit1 (preTable: Table, nextTable: Table, cellsTraversedBySplitLine: Cell[], unRowSize: number[], cellSplitLine: number, splitRowSizeIndex: number, tempPreTableChildren: Cell[]) {
    let nextTableFirstRowSize = 0;
    const cells = [];
    const shouldBeReassignedCells: Cell[] = [];
    const preTableNeedHandleLineCells = [];
    const originTable = nextTable.origin;
    let flag = true; // 加个标记 判断被线穿过的单元格 应该分配到下一页中去的单元格的 position 要不要赋值成 0 如果出现了那种情况是不应该的 没有那种情况就应该是 0 默认先都为 0
    for (const cell of tempPreTableChildren) {
      const { bottom } = this.getCellTopAndBottom(cell, unRowSize);
      if (bottom > cellSplitLine) {
        flag = false;
        break;
      }
    }

    for (const traversedCell of cellsTraversedBySplitLine) {
      const { top, bottom } = this.getCellTopAndBottom(traversedCell, unRowSize);
      if (top < cellSplitLine && bottom > cellSplitLine) {
        const [c1, c2] = this.cellSplit(
          traversedCell,
          cellSplitLine,
          splitRowSizeIndex,
          preTable,
          nextTable,
          BreakType.hard,
          unRowSize
        );

        if (originTable) {
          const optRow = originTable?.notAllowDrawLine.changeOpacityRow.find(p => p[0] === traversedCell.end_row_index + 1 && p[1] === traversedCell.start_col_index);
          if (optRow) {
            for (let i = 0; i < c1.colspan; i++) {
              preTable.notAllowDrawLine.changeOpacityRow.push([c1.end_row_index + 1, c1.start_col_index + i]);
              nextTable.notAllowDrawLine.changeOpacityRow.push([c2.start_row_index, c2.start_col_index + i]);
            }
          }
        }

        const splitedCell = traversedCell.getOrigin().split_parts.pop()!;
        splitedCell.shouldPushSplitParts = true; // 这个如果再进软分割的时候就继续 pop 出来，但是在最后直接放到上一页表格中去的时候就要往 origin.split_parts 里边放

        preTable.children.push(c1);
        preTable.cellMap.set(c1.id, c1);
        preTableNeedHandleLineCells.push(c1);

        const c2RealContentHeight = this.getRealContentHeight(c2);
        if (c2.rowspan === 1) {
          nextTableFirstRowSize = Math.max(c2RealContentHeight, nextTableFirstRowSize);
        } else {
          cells.push({
            rowspan: c2.rowspan,
            maxHeight: c2RealContentHeight
          });
        }

        shouldBeReassignedCells.push(c2);

        this.resetSplitRowIndexs(traversedCell);
        c2.position[0] = 0;
      } else {
        const newCell = new Cell(
          traversedCell.editor,
          [...traversedCell.position],
          traversedCell.colspan,
          traversedCell.rowspan,
          nextTable,
          traversedCell.id
        );
        Cell.attrJudgeUndefinedAssign(newCell, traversedCell);
        newCell.children = traversedCell.children;
        newCell.paragraph = traversedCell.paragraph;
        newCell.origin = traversedCell.getOrigin();
        // console.log(newCell.getStr(), "打印");
        // 按说直接是第 0 行 但是有那个特殊情况 所以直接写 0 是不对的 是不是跟新的分割线的位置有很大的关系 就是跟 splitRowSizeIndex 有关系 但是不能直接减呢？
        // 不能再这里减等于 因为 while 循环的时候 会再次减等于 就跟这里重复了
        // 目前来说应该是干掉这行 然后再看下一个 bug
        // 所以现在的问题是 我啥时候直接赋值为 0 啥时候不管 让 while 循环里边减去 splitRowSizeIndex
        // 主要还是得看分割线吧 看分割线的位置有没有调整 此时的分割线应该是调整后的分割线
        // newCell.position[0] -= splitRowSizeIndex;
        if (flag) {
          newCell.position[0] = 0; // 正常情况下应该是 0
        }
        // 因为在调用该方法之前 就已经分配好 tempPreTableChildren 了 所以可以根据 preTable.children 里边的单元格来进行判断
        shouldBeReassignedCells.push(newCell);
      }
    }

    preTableNeedHandleLineCells.forEach(c => {
      this.addLinesThatAreNotDrawn(preTable, c);
    });
    cells.sort((a, b) => a.rowspan - b.rowspan);
    return {
      nextTableFirstRowSize,
      shouldBeReassignedCells,
      cells: EditorLocalTest.useLocal ? cells : undefined
    };
  },

  handleCellsTraversedBySplitLineHardSplit2 (preTable: Table, nextTable: Table, cellsTraversedBySplitLine: Cell[], unRowSize: number[], cellSplitLine: number, splitRowSizeIndex: number) {
    let nextTableFirstRowSize = 0;
    const shouldBeReassignedCells: Cell[] = [];
    const preTableNeedHandleLineCells = [];
    const originTable = nextTable.origin;
    // 分配被分割线穿过的单元格
    for (const cell of cellsTraversedBySplitLine) {
      const { top, bottom } = this.getCellTopAndBottom(cell, unRowSize);

      if (top < cellSplitLine && bottom > cellSplitLine) {
        const [c1, c2] = this.cellSplit(
          cell,
          cellSplitLine,
          splitRowSizeIndex, // 被拆分的 row_size 的下标
          preTable,
          nextTable,
          BreakType.hard,
          unRowSize
        );

        if (originTable) {
          const optRow = originTable?.notAllowDrawLine.changeOpacityRow.find(p => p[0] === cell.end_row_index + 1 && p[1] === cell.start_col_index);
          if (optRow) {
            for (let i = 0; i < c1.colspan; i++) {
              preTable.notAllowDrawLine.changeOpacityRow.push([c1.end_row_index + 1, c1.start_col_index + i]);
              nextTable.notAllowDrawLine.changeOpacityRow.push([c2.start_row_index, c2.start_col_index + i]);
            }
          }
        }

        const splitedCell = cell.getOrigin().split_parts.pop()!;
        splitedCell.shouldPushSplitParts = true; // 这个如果再进软分割的时候就继续 pop 出来，但是在最后直接放到上一页表格中去的时候就要往 origin.split_parts 里边放

        preTable.children.push(c1);
        preTable.cellMap.set(c1.id, c1);
        preTableNeedHandleLineCells.push(c1);
        shouldBeReassignedCells.push(c2);

        const c2RealContentHeight = this.getRealContentHeight(c2);
        if (c2.rowspan === 1) {
          nextTableFirstRowSize = Math.max(c2RealContentHeight, nextTableFirstRowSize);
        }

        this.resetSplitRowIndexs(cell); // 此时要重新更新 之前更新的不一定对了
      } else {
        const newCell = new Cell(
          cell.editor,
          [0, cell.position[1]],
          cell.colspan,
          cell.rowspan,
          nextTable,
          cell.id
        );
        Cell.attrJudgeUndefinedAssign(newCell, cell);
        newCell.children = cell.children;
        newCell.paragraph = cell.paragraph;
        newCell.origin = cell.getOrigin();
        shouldBeReassignedCells.push(newCell);
      }
    }
    cellsTraversedBySplitLine.length = 0;
    preTableNeedHandleLineCells.forEach(c => {
      this.addLinesThatAreNotDrawn(preTable, c);
    });

    return {
      nextTableFirstRowSize,
      shouldBeReassignedCells
    };
  },

  handleCellsTraversedBySplitLineSoftSplit1 (preTable: Table, nextTable: Table, cellsTraversedBySplitLine: Cell[], unRowSize: number[], cellSplitLine: number, splitRowSizeIndex: number) {
    // 应该被重新分配的单元格集合
    const preTableNeedHandleLineCells = [];
    let nextTableFirstRowSize = 0; // 表格拆分 下一页表格 第一个 row_size 的应该的高度
    const shouldBeReassignedCells: Cell[] = []; // 为了保证顺序 所以用这个数组过渡 不能直接循环里边用 unCells.unshift(c2) 添加
    const originTable = nextTable.origin;
    for (const cellTranversed of cellsTraversedBySplitLine) {
      const { top, bottom } = this.getCellTopAndBottom(cellTranversed, unRowSize);
      if (top < cellSplitLine && bottom > cellSplitLine) {
        const [c1, c2] = this.cellSplit(
          cellTranversed,
          cellSplitLine,
          splitRowSizeIndex,
          preTable,
          nextTable,
          BreakType.soft,
          unRowSize
        );

        if (originTable) {
          const optRow = originTable?.notAllowDrawLine.changeOpacityRow.find(p => p[0] === cellTranversed.end_row_index + 1 && p[1] === cellTranversed.start_col_index);
          if (optRow) {
            for (let i = 0; i < c1.colspan; i++) {
              preTable.notAllowDrawLine.changeOpacityRow.push([c1.end_row_index + 1, c1.start_col_index + i]);
              nextTable.notAllowDrawLine.changeOpacityRow.push([c2.start_row_index, c2.start_col_index + i]);
            }
          }
        }

        // 因为虽然是被拆分成了两个单元格 但是下一个单元格就又往 unCells 里边放了 下一轮又可能是硬分割直接放在上一页表格 也有可能继续进行软分割
        // 所以下一次还是软分割的时候就要把原来放到 split_parts 里边的最后一个删除掉,因为又被拆分了 但是要做个标记 要是下次被硬分割直接放到上一页中去的时候
        // split_parts 里边就少一个了 所以要做标记循环的时候别忘了放进 split_parts 里边去
        const splitedCell = cellTranversed.getOrigin().split_parts.pop()!;
        splitedCell.shouldPushSplitParts = true; // 这个如果再进软分割的时候就继续 pop 出来，但是在最后直接放到上一页表格中去的时候就要往 origin.split_parts 里边放

        preTable.children.push(c1);
        preTable.cellMap.set(c1.id, c1);
        preTableNeedHandleLineCells.push(c1);
        c2.position[0] = 0;
        shouldBeReassignedCells.push(c2); // 为了保证再进训话时候的顺序是对的，提供一个变量先收集一下 直接用 unCell.unshift(c2) 会导致顺序不对 因为会 unCells.unshift(...shouldBeReassignedCells)

        const c2RealContentHeight = this.getRealContentHeight(c2);
        if (c2.rowspan === 1) {
          nextTableFirstRowSize = Math.max(c2RealContentHeight, nextTableFirstRowSize);
        }

        this.resetSplitRowIndexs(cellTranversed);
      } else {
        // 这些是直接放到下一页表格中去的
        const newCell = new Cell(
          cellTranversed.editor,
          [0, cellTranversed.position[1]],
          cellTranversed.colspan,
          cellTranversed.rowspan,
          nextTable,
          cellTranversed.id
        );
        Cell.attrJudgeUndefinedAssign(newCell, cellTranversed);
        newCell.children = cellTranversed.children;
        newCell.paragraph = cellTranversed.paragraph;
        newCell.origin = cellTranversed.getOrigin();
        if (newCell.rowspan > 1 || newCell.colspan > 1) {
          for (let i = 0; i < newCell.rowspan - 1; i++) {
            for (let j = 0; j < newCell.colspan; j++) {
              nextTable.notAllowDrawLine.row.push([
                newCell.position[0] + 1 + i,
                newCell.position[1] + j
              ]);
            }
          }
        }
        shouldBeReassignedCells.push(newCell);
      }
    }
    preTableNeedHandleLineCells.forEach(c => {
      this.addLinesThatAreNotDrawn(preTable, c);
    });

    return {
      nextTableFirstRowSize,
      shouldBeReassignedCells
    };
  },

  handleCellsTraversedBySplitLineSoftSplit2 (preTable: Table, nextTable: Table, cellsTraversedBySplitLine: Cell[], unRowSize: number[], splitLine: number, splitRowSizeIndex: number) {
    const preTableNeedHandleLineCells = [];
    let nextTableFirstRowSize = 0;
    const rowspanArr: number[] = [];
    let nextTableRowSize = 0;
    // 应该被重新分配的单元格集合
    const shouldBeReassignedCells: Cell[] = []; // 为了保证顺序 所以用这个数组过渡 不能直接循环里边用 unCells.unshift(c2) 添加
    const originTable = nextTable.origin;
    // 这一块应该是每次软分割执行 preTable = nextTable 的时候都要执行的 可以考虑抽离出来 ↑
    for (const traversedCell of cellsTraversedBySplitLine) {
      const { top, bottom } = this.getCellTopAndBottom(traversedCell, unRowSize);
      if (top < splitLine && bottom > splitLine) {
        const [c1, c2] = this.cellSplit(
          traversedCell,
          splitLine,
          splitRowSizeIndex,
          preTable,
          nextTable,
          BreakType.soft,
          unRowSize
        );

        if (originTable) {
          const optRow = originTable?.notAllowDrawLine.changeOpacityRow.find(p => p[0] === traversedCell.end_row_index + 1 && p[1] === traversedCell.start_col_index);
          if (optRow) {
            for (let i = 0; i < c1.colspan; i++) {
              preTable.notAllowDrawLine.changeOpacityRow.push([c1.end_row_index + 1, c1.start_col_index + i]);
              nextTable.notAllowDrawLine.changeOpacityRow.push([c2.start_row_index, c2.start_col_index + i]);
            }
          }
        }

        // 因为虽然是被拆分成了两个单元格 但是下一个单元格就又往 unCells 里边放了 下一轮又可能是硬分割直接放在上一页表格 也有可能继续进行软分割
        // 所以下一次还是软分割的时候就要把原来放到 split_parts 里边的最后一个删除掉,因为又被拆分了 但是要做个标记 要是下次被硬分割直接放到上一页中去的时候
        // split_parts 里边就少一个了 所以要做标记循环的时候别忘了放进 split_parts 里边去
        const splitedCell = traversedCell.getOrigin().split_parts.pop()!;
        splitedCell.shouldPushSplitParts = true; // 这个如果再进软分割的时候就继续 pop 出来，但是在最后直接放到上一页表格中去的时候就要往 origin.split_parts 里边放
        // 这个 splitedCell 就是 c2

        preTable.children.push(c1);
        preTable.cellMap.set(c1.id, c1);
        preTableNeedHandleLineCells.push(c1);
        c2.position[0] = 0;
        shouldBeReassignedCells.push(c2); // 为了保证再进训话时候的顺序是对的，提供一个变量先收集一下 直接用 unCell.unshift(c2) 会导致顺序不对 因为会 unCells.unshift(...shouldBeReassignedCells)
        // cell.getOrigin().split_parts.push(c1, c2); // 不用在这儿 push cell.split 的时候就已经放进去了

        const c2RealContentHeight = this.getRealContentHeight(c2);
        if (c2.rowspan === 1) {
          nextTableFirstRowSize = Math.max(c2RealContentHeight, nextTableFirstRowSize);
        }
        if (rowspanArr.length === 0) {
          rowspanArr.push(c2.rowspan);
        } else if (c2.rowspan === rowspanArr[0]) {
          rowspanArr.push(c2.rowspan);
        }
        nextTableRowSize = Math.max(c2RealContentHeight);
        this.resetSplitRowIndexs(traversedCell);
      } else {
      // 这些是直接放到下一页表格中去的
        const newCell = new Cell(
          traversedCell.editor,
          [0, traversedCell.position[1]],
          traversedCell.colspan,
          traversedCell.rowspan,
          nextTable,
          traversedCell.id
        );
        Cell.attrJudgeUndefinedAssign(newCell, traversedCell);
        newCell.children = traversedCell.children;
        newCell.paragraph = traversedCell.paragraph;
        newCell.origin = traversedCell.getOrigin();
        if (newCell.rowspan > 1 || newCell.colspan > 1) {
          for (let i = 0; i < newCell.rowspan - 1; i++) {
            for (let j = 0; j < newCell.colspan; j++) {
              nextTable.notAllowDrawLine.row.push([
                newCell.position[0] + 1 + i,
                newCell.position[1] + j
              ]);
            }
          }
        }
        shouldBeReassignedCells.push(newCell);
      }
    }
    preTableNeedHandleLineCells.forEach(c => {
      this.addLinesThatAreNotDrawn(preTable, c);
    });

    return {
      nextTableFirstRowSize,
      nextTableRowSize,
      shouldBeReassignedCells,
      rowspanArr
    };
  },

  // TODO 拆分表格的 page_break 赋值需要重新整理  暂时是先在 最后循环一遍全部 page_break = true 24/06/03
  newVersionTableSplit (table:Table, initSplitLine: number, parent: Cell): Table[] {
    if (table.origin && table.origin.split_parts.length > 0) return [table];
    const fixedRowNum = table.fixed_table_header_num;
    const fixedRowSize = [...table.row_size.slice(0, fixedRowNum)];
    const fixedMinRowSize = [...table.min_row_size.slice(0, fixedRowNum)];
    const cancelFixed = fixedRowSize.reduce((t, c) => t + c, 0) >= initSplitLine; // TODO 这个判断好像不绝对
    const headerCells = [];
    if (!cancelFixed) {
      for (const cell of table.children) {
        if (cell.position[0] < fixedRowNum) {
          cell.isHeaderCell = true;
          headerCells.push(cell);
        } else {
          break;
        }
      }
    }
    const splitTables: Table[] = [];
    this.resetSplitAttr(table);
    let pageNumber = table.page_number;
    let splitLine = initSplitLine;
    const editor = table.editor;
    const page = editor.pages[0];
    const fixedSplitLine = page.contentHeight;
    const unRowSize = [...table.row_size];
    const unMinRowSize = [...table.min_row_size];
    const unOpacityHorizontal = arrDeduplication(table.notAllowDrawLine.changeOpacityRow); // 数组去重 并且进行了克隆
    sort(unOpacityHorizontal);
    const unOpacityVertical = arrDeduplication(table.notAllowDrawLine.changeOpacityCol);
    sort(unOpacityVertical);
    let splitedRowSizeNum = 0; // 已经被拆分过的(已经放到上方表格中去了) row_size 的数量

    let preTable = new Table(editor, table.id, table.group_id, table.col_size, [], [], parent, parent.left + parent.padding_left, parent.right - parent.padding_right, table.top, false, SkipMode.ROW);
    preTable.page_number = pageNumber++;
    preTable.page_index = table.page_index;
    preTable.origin = table;
    splitTables.push(preTable);
    table.split_parts.push(preTable);

    const unshiftCells: Cell[] = [];

    const cellsTraversedBySplitLine = []; // 被分割线穿过的单元格 如果这里边没有单元格就只能是硬分割 如果这里有单元格就可能是硬分割也可能是软分割

    const tempPreTableChildren: Cell[] = [];
    let index = 0;
    while (index < table.children.length || unshiftCells.length) {
      const cell = unshiftCells.length > 0 ? unshiftCells.shift()! : table.children[index++]!;
      const copiedCell = cell.simpleCopy(preTable);
      copiedCell.position[0] !== 0 && (copiedCell.position[0] -= splitedRowSizeNum); // 因为等于 0 的是在单元格拆分的时候就直接修改过位置的是正确的 所以不需要减
      if (headerCells.length > 0 && preTable.page_number > table.page_number && !cell.isHeaderCell) {
        copiedCell.position[0] += fixedRowNum;
      }
      const { top, bottom } = this.getCellTopAndBottom(copiedCell, unRowSize);
      if (bottom <= splitLine) {
        if (cell.shouldPushSplitParts) {
          if (headerCells.length > 0 && preTable.page_number > table.page_number && !cell.isHeaderCell) {
            cell.position[0] += fixedRowNum;
          }
          tempPreTableChildren.push(cell);
        } else {
          tempPreTableChildren.push(copiedCell);
        }
      }

      // 如果没进这个判断就会是硬分割 进了这个判断也有可能是硬分割
      if (top <= splitLine && bottom > splitLine) {
        cellsTraversedBySplitLine.push(copiedCell);
      }

      if (top > splitLine) {
        unshiftCells.unshift(cell); // 因为处理的都是 copiedCell 所以遇到这种就直接再放回到 unCells 里边去 也没有问题
        // 此时不一定就应该有两个表格了 会有整个表格被拽下来的情况 比如三行的表格 第一行有特别大的字
        const nextTable = new Table(editor, table.id, table.group_id, table.col_size, [], [], parent, parent.left + parent.padding_left, parent.right - parent.padding_right, page.header.header_outer_bottom, false, SkipMode.ROW); // 第二个表格一定在这一页的开头 所以 top 值等于这个
        table.split_line_arr.push((table.split_line_arr[table.split_line_arr.length - 1] || 0) + splitLine);
        nextTable.page_number = pageNumber++;
        nextTable.page_index = 0;
        nextTable.origin = table;

        let { splitRowSizeIndex, cellSplitLine, hasHardSplit } = this.getSplitType(cellsTraversedBySplitLine, unRowSize, splitLine);

        if (hasHardSplit) {
          if (table.row_size.length === unRowSize.length && splitRowSizeIndex === 0) { // 情况之一: 三行的表格 第一行内容很高 整体都会被拽下去就会走这儿了
            table.split_parts.pop();
            return [table];
          };

          nextTable.page_break_type = BreakType.hard;

          const { nextTableFirstRowSize, shouldBeReassignedCells, cells } = this.handleCellsTraversedBySplitLineHardSplit1(preTable, nextTable, cellsTraversedBySplitLine, unRowSize, cellSplitLine, splitRowSizeIndex, tempPreTableChildren);
          this.handleTempPreTableChildrenHardSplit(preTable, nextTable, tempPreTableChildren, unRowSize, cellSplitLine, splitRowSizeIndex);

          preTable.row_size = unRowSize.slice(0, splitRowSizeIndex);
          unRowSize.splice(0, splitRowSizeIndex);
          unRowSize[0] = Math.max(unRowSize[0], nextTableFirstRowSize);
          if (cells && cells.length) {
            // 先管放不下的情况 然后修改 下一个单元格就要在修改后的 row_size 的基础上判断是否能够装得下 如果可以的话就不用管了
            for (const cell of cells) {
              const rowspan = cell.rowspan;
              let total = 0;
              for (let i = 0; i < rowspan; i++) {
                total += unRowSize[i];
              }
              if (cell.maxHeight > total) {
                unRowSize[rowspan - 1] += keepDecimal(cell.maxHeight - total, 3);
              }
            }
          }

          preTable.min_row_size = unMinRowSize.slice(0, splitRowSizeIndex);
          unMinRowSize.splice(0, splitRowSizeIndex);
          if (headerCells.length > 0) {
            unRowSize.unshift(...fixedRowSize);
            unMinRowSize.unshift(...fixedMinRowSize);
            unshiftCells.unshift(...headerCells, ...shouldBeReassignedCells);
          } else {
            unshiftCells.unshift(...shouldBeReassignedCells);
          }

          splitedRowSizeNum += splitRowSizeIndex;

          if (headerCells.length > 0 && preTable.page_number > table.page_number) {
            splitedRowSizeNum -= fixedRowNum;
          }
          // 处理线 ↓
          for (let i = 0; i < unOpacityHorizontal.length;) {
            const coordinate = unOpacityHorizontal[i];
            const horizontalNum = coordinate[0];
            if (horizontalNum < splitRowSizeIndex) {
              preTable.notAllowDrawLine.changeOpacityRow.push([...coordinate]);
              unOpacityHorizontal.splice(i, 1); // 因为删除了 所以 i 在这里不能加 1 否则就会跳过一个编号
            } else if (horizontalNum === splitRowSizeIndex) {
              preTable.notAllowDrawLine.changeOpacityRow.push([...coordinate]);
              unOpacityHorizontal.splice(i, 1);
              unOpacityHorizontal.unshift([0, coordinate[1]]); // 不能用 push 否则就会再次进入循环进行判断 就又放到 preTable 里边去了
              i++;
            } else {
              coordinate[0] -= splitRowSizeIndex;
              i++;
            }
          }
          for (let i = 0; i < unOpacityVertical.length;) {
            const coordinate = unOpacityVertical[i];
            const horizontalNum = coordinate[1];
            if (horizontalNum < splitRowSizeIndex - 1) {
              preTable.notAllowDrawLine.changeOpacityCol.push([...coordinate]);
              unOpacityVertical.splice(i, 1);
            } else if (horizontalNum === splitRowSizeIndex - 1) {
              preTable.notAllowDrawLine.changeOpacityCol.push([...coordinate]);
              unOpacityVertical.splice(i, 1);
              unOpacityVertical.unshift([coordinate[1], 0]);
              i++;
            } else {
              coordinate[1] -= splitRowSizeIndex;
              i++;
            }
          }
        // 处理线 ↑
        } else {
          nextTable.page_break_type = BreakType.soft;

          // 因为是软分割 要重新计算 被拆分的 row_size 先计算到 splitLine 之前的 row_size 的总高度 不能放到最下方  cell.split 要用 splitRowSizeIndex
          let totalRowSizeHeight = 0;
          for (let i = 0; i < unRowSize.length; i++) {
            if (totalRowSizeHeight + unRowSize[i] > splitLine) {
              splitRowSizeIndex = i;
              break;
            }
            totalRowSizeHeight += unRowSize[i];
          }

          this.handleTempPreTableChildrenSoftSplit(preTable, tempPreTableChildren);

          const { nextTableFirstRowSize, shouldBeReassignedCells } = this.handleCellsTraversedBySplitLineSoftSplit1(preTable, nextTable, cellsTraversedBySplitLine, unRowSize, splitLine, splitRowSizeIndex);

          unshiftCells.unshift(...shouldBeReassignedCells);

          splitedRowSizeNum += splitRowSizeIndex; // TODO 还得仔细研究一下这里 如果有固定一行表头的话 += 是不对的
          if (headerCells.length > 0 && preTable.page_number > table.page_number) {
            splitedRowSizeNum -= fixedRowNum;
          }
          preTable.row_size = unRowSize.slice(0, splitRowSizeIndex + 1);
          unRowSize.splice(0, splitRowSizeIndex + 1);
          preTable.min_row_size = unMinRowSize.slice(0, splitRowSizeIndex + 1);
          unMinRowSize.splice(0, splitRowSizeIndex + 1);

          // 上边只是分配 row_size 接下来是要分割 row_size
          // 分割 row_size
          // 1. 计算 preTable 的 row_size 高度和(包括了应该被拆分还没被拆分的 row_size)
          const preTableSumRowSizeHasToBeSplit = preTable.row_size.reduce((total, current) => total + current, 0);
          // 将要被拆分的 row_size 的高度
          const rowSizeToBeSplit = preTable.row_size[preTable.row_size.length - 1];
          // 将要被拆分的 row_size 分配给 preTalbe 之后的剩余高度
          const restHeightOfToBeSplit = preTableSumRowSizeHasToBeSplit - splitLine;
          // 将要被拆分的 row_size 的高度减去应该分配给 preTable 的 row_size 之后的剩余高度，就是 preTable 最后一个 row_size 的高度
          preTable.min_row_size[preTable.min_row_size.length - 1] = preTable.row_size[preTable.row_size.length - 1] = rowSizeToBeSplit - restHeightOfToBeSplit;

          // 应该分配给 nextTable 的 row_size 的高度，就是被拆分的 row_size 的高度，减去分配给 preTable 的 row_size 的高度
          let rowSizeAssignedToNextTable = rowSizeToBeSplit - preTable.row_size[preTable.row_size.length - 1];
          rowSizeAssignedToNextTable = rowSizeAssignedToNextTable > nextTableFirstRowSize ? rowSizeAssignedToNextTable : nextTableFirstRowSize;
          unRowSize.unshift(rowSizeAssignedToNextTable);
          const minRowSizeAssignedToNextTable = rowSizeAssignedToNextTable < Config.min_row_size ? Config.min_row_size : rowSizeAssignedToNextTable;
          unMinRowSize.unshift(minRowSizeAssignedToNextTable);
          if (headerCells.length > 0) {
            unRowSize.unshift(...fixedRowSize);
            unMinRowSize.unshift(...fixedMinRowSize);
            unshiftCells.unshift(...headerCells);
          }
          preTable.page_break_type = BreakType.soft;
          // 处理线 ↓
          for (let i = 0; i < unOpacityHorizontal.length;) {
            const coordinate = unOpacityHorizontal[i];
            const horizontalNum = coordinate[0];
            if (horizontalNum <= splitRowSizeIndex) {
              preTable.notAllowDrawLine.changeOpacityRow.push([...coordinate]);
              unOpacityHorizontal.splice(i, 1);
            } else if (horizontalNum === splitRowSizeIndex + 1) {
              preTable.notAllowDrawLine.changeOpacityRow.push([...coordinate]);
              unOpacityHorizontal.splice(i, 1);
              unOpacityHorizontal.unshift([0, coordinate[1]]); // 不能用 push 否则就会再次进入循环进行判断 就又放到 preTable 里边去了
              unOpacityHorizontal.unshift([1, coordinate[1]]); // 不能用 push 否则就会再次进入循环进行判断 就又放到 preTable 里边去了
              i += 2;
            } else {
              coordinate[0] -= splitRowSizeIndex;
              i++;
            }
          }
          for (let i = 0; i < unOpacityVertical.length;) {
            const coordinate = unOpacityVertical[i];
            const horizontalNum = coordinate[1];
            if (horizontalNum <= splitRowSizeIndex - 1) {
              preTable.notAllowDrawLine.changeOpacityCol.push([...coordinate]);
              unOpacityVertical.splice(i, 1);
            } else if (horizontalNum === splitRowSizeIndex) {
              preTable.notAllowDrawLine.changeOpacityCol.push([...coordinate]);
              unOpacityVertical.splice(i, 1);
              unOpacityVertical.unshift([coordinate[0], 0]);
              unOpacityVertical.unshift([coordinate[0], 1]);
              i += 2;
            } else {
              coordinate[1] -= splitRowSizeIndex;
              i++;
            }
          }
        // 处理线 ↑
        }
        cellsTraversedBySplitLine.length = 0;
        splitLine = fixedSplitLine;
        preTable.page_break = true;

        table.split_parts.push(nextTable);
        splitTables.push(nextTable);

        preTable = nextTable;
      } else if (index >= table.children.length && cellsTraversedBySplitLine.length && unshiftCells.length === 0) { // 比如一个单元格无限软分割会走这里 或者 正好有硬分割 但是没有 top > splitLine 的单元格的时候就会走这个
      // 增加 unshiftCells.length === 0 因为如果有多列的表格,在最后一列进行无限软分割的时候 unshiftCells 只拿出来一个单元格的时候就会进这个判断结果就不对了 应该是要把 unshiftCells 都走完了确定应该进行拆分的时候才进这里进行拆分
        let { splitRowSizeIndex, cellSplitLine, hasHardSplit, returnTable } = this.getSplitType2(cellsTraversedBySplitLine, unRowSize, splitLine, table);

        if (returnTable) {
          return returnTable;
        }

        if (hasHardSplit) {
          const nextTable = new Table(editor, table.id, table.group_id, table.col_size, [], [], parent, parent.left + parent.padding_left, parent.right - parent.padding_right, page.header.header_outer_bottom, false, SkipMode.ROW); // 第二个表格一定在这一页的开头 所以 top 值等于这个
          table.split_line_arr.push((table.split_line_arr[table.split_line_arr.length - 1] || 0) + splitLine);

          nextTable.page_number = pageNumber++;
          nextTable.page_index = 0;
          nextTable.origin = table;
          nextTable.page_break_type = BreakType.hard;

          this.handleTempPreTableChildrenHardSplit(preTable, nextTable, tempPreTableChildren, unRowSize, cellSplitLine, splitRowSizeIndex);

          const { nextTableFirstRowSize, shouldBeReassignedCells } = this.handleCellsTraversedBySplitLineHardSplit2(preTable, nextTable, cellsTraversedBySplitLine, unRowSize, cellSplitLine, splitRowSizeIndex);

          splitLine = fixedSplitLine;
          preTable.row_size = unRowSize.slice(0, splitRowSizeIndex);
          unRowSize.splice(0, splitRowSizeIndex);
          unRowSize[0] = Math.max(unRowSize[0], nextTableFirstRowSize);
          preTable.min_row_size = unMinRowSize.slice(0, splitRowSizeIndex);
          unMinRowSize.splice(0, splitRowSizeIndex);
          splitedRowSizeNum += splitRowSizeIndex;
          if (headerCells.length > 0 && preTable.page_number > table.page_number) {
            splitedRowSizeNum -= fixedRowNum;
          }

          table.split_parts.push(nextTable);
          splitTables.push(nextTable);

          unshiftCells.unshift(...shouldBeReassignedCells);
          if (headerCells.length > 0) {
            unRowSize.unshift(...fixedRowSize);
            unMinRowSize.unshift(...fixedMinRowSize);
            unshiftCells.unshift(...headerCells);
          }
          // 处理线 ↓
          for (let i = 0; i < unOpacityHorizontal.length;) {
            const coordinate = unOpacityHorizontal[i];
            const horizontalNum = coordinate[0];
            if (horizontalNum < splitRowSizeIndex) {
              preTable.notAllowDrawLine.changeOpacityRow.push([...coordinate]);
              unOpacityHorizontal.splice(i, 1);
            } else if (horizontalNum === splitRowSizeIndex) {
              preTable.notAllowDrawLine.changeOpacityRow.push([...coordinate]);
              unOpacityHorizontal.splice(i, 1);
              unOpacityHorizontal.unshift([0, coordinate[1]]); // 不能用 push 否则就会再次进入循环进行判断 就又放到 preTable 里边去了
              i++;
            } else {
              coordinate[0] -= splitRowSizeIndex;
              i++;
            }
          }
          for (let i = 0; i < unOpacityVertical.length;) {
            const coordinate = unOpacityVertical[i];
            const horizontalNum = coordinate[1];
            if (horizontalNum < splitRowSizeIndex - 1) {
              preTable.notAllowDrawLine.changeOpacityCol.push([...coordinate]);
              unOpacityVertical.splice(i, 1);
            } else if (horizontalNum === splitRowSizeIndex - 1) {
              preTable.notAllowDrawLine.changeOpacityCol.push([...coordinate]);
              unOpacityVertical.splice(i, 1);
              unOpacityVertical.unshift([coordinate[1], 0]);
              i++;
            } else {
              coordinate[1] -= splitRowSizeIndex;
              i++;
            }
          }
          // 处理线 ↑
          preTable = nextTable;
        } else {
        // 因为如果有 return [this] 的情况就已经 return 掉了 到这儿 肯定是要创建 nextTable 的,一旦创建 nextTable 就要往 split_parts 和 splitTalbes 里边放 并且修改好 nextTable 的各个属性值
          const nextTable = new Table(editor, table.id, table.group_id, table.col_size, [], [], parent, parent.left + parent.padding_left, parent.right - parent.padding_right, page.header.header_outer_bottom, false, SkipMode.ROW); // 第二个表格一定在这一页的开头 所以 top 值等于这个
          table.split_line_arr.push((table.split_line_arr[table.split_line_arr.length - 1] || 0) + splitLine); // 每次增加一个表格的时候 this.split_line_arr 就要追加一个线的高度

          nextTable.page_number = pageNumber++;
          nextTable.page_index = 0;
          nextTable.origin = table;
          nextTable.page_break_type = BreakType.soft;

          // 因为是软分割 要重新计算 被拆分的 row_size 先计算到 splitLine 之前的 row_size 的总高度 不能放到最下方  cell.split 要用 splitRowSizeIndex
          let totalRowSizeHeight = 0;
          for (let i = 0; i < unRowSize.length; i++) {
            if (totalRowSizeHeight + unRowSize[i] > splitLine) {
              splitRowSizeIndex = i;
              break;
            }
            totalRowSizeHeight += unRowSize[i];
          }

          // 这一块应该是每次软分割执行 preTable = nextTable 的时候都要执行的（或者每次处理 cellsTraversedBySplitLine 里边的单元格的时候） 可以考虑抽离出来 ↓

          this.handleTempPreTableChildrenSoftSplit(preTable, tempPreTableChildren);

          const { nextTableFirstRowSize, nextTableRowSize, rowspanArr, shouldBeReassignedCells } = this.handleCellsTraversedBySplitLineSoftSplit2(preTable, nextTable, cellsTraversedBySplitLine, unRowSize, splitLine, splitRowSizeIndex);

          unshiftCells.unshift(...shouldBeReassignedCells);

          splitedRowSizeNum += splitRowSizeIndex;
          if (headerCells.length > 0 && preTable.page_number > table.page_number) {
            splitedRowSizeNum -= fixedRowNum;
          }
          preTable.row_size = unRowSize.slice(0, splitRowSizeIndex + 1);
          unRowSize.splice(0, splitRowSizeIndex + 1);
          preTable.min_row_size = unMinRowSize.slice(0, splitRowSizeIndex + 1);
          unMinRowSize.splice(0, splitRowSizeIndex + 1);

          // 上边只是分配 row_size 接下来是要分割 row_size
          // 分割 row_size
          // 1. 计算 preTable 的 row_size 高度和(包括了应该被拆分还没被拆分的 row_size)
          const preTableSumRowSizeHasToBeSplit = preTable.row_size.reduce((total, current) => total + current, 0);
          // 将要被拆分的 row_size 的高度
          const rowSizeToBeSplit = preTable.row_size[preTable.row_size.length - 1];
          // 将要被拆分的 row_size 分配给 preTalbe 之后的剩余高度
          const restHeightOfToBeSplit = preTableSumRowSizeHasToBeSplit - splitLine;
          // 将要被拆分的 row_size 的高度减去应该分配给 preTable 的 row_size 之后的剩余高度，就是 preTable 最后一个 row_size 的高度
          preTable.min_row_size[preTable.min_row_size.length - 1] = preTable.row_size[preTable.row_size.length - 1] = rowSizeToBeSplit - restHeightOfToBeSplit;

          // 应该分配给 nextTable 的 row_size 的高度，就是被拆分的 row_size 的高度，减去分配给 preTable 的 row_size 的高度
          let rowSizeAssignedToNextTable = rowSizeToBeSplit - preTable.row_size[preTable.row_size.length - 1];

          const minRowSizeAssignedToNextTable = rowSizeAssignedToNextTable < Config.min_row_size ? Config.min_row_size : rowSizeAssignedToNextTable;
          unMinRowSize.unshift(minRowSizeAssignedToNextTable);
          rowSizeAssignedToNextTable = rowSizeAssignedToNextTable > nextTableFirstRowSize ? rowSizeAssignedToNextTable : nextTableFirstRowSize;
          unRowSize.unshift(Math.max(rowSizeAssignedToNextTable, minRowSizeAssignedToNextTable));
          if (rowspanArr.length === cellsTraversedBySplitLine.length && rowspanArr[0] !== 1) {
            const rowspan = rowspanArr[0];
            const rowsizeArr = unRowSize.slice(0, rowspan);
            const rowsizeTotal = rowsizeArr.reduce((t, c) => t + c, 0);
            if (rowsizeTotal !== nextTableRowSize) {
              const difference = rowsizeTotal - nextTableRowSize;
              rowsizeArr[rowspan - 1] -= difference;
              unRowSize.splice(0, rowsizeArr.length, ...rowsizeArr);
            }
          }
          preTable.page_break_type = BreakType.soft;

          splitLine = fixedSplitLine;
          cellsTraversedBySplitLine.length = 0;
          table.split_parts.push(nextTable);
          splitTables.push(nextTable);
          if (headerCells.length > 0) {
            unRowSize.unshift(...fixedRowSize);
            unMinRowSize.unshift(...fixedMinRowSize);
            unshiftCells.unshift(...headerCells);
          }
          // 处理线 ↓
          for (let i = 0; i < unOpacityHorizontal.length;) {
            const coordinate = unOpacityHorizontal[i];
            const horizontalNum = coordinate[0];
            if (horizontalNum <= splitRowSizeIndex) {
              preTable.notAllowDrawLine.changeOpacityRow.push([...coordinate]);
              unOpacityHorizontal.splice(i, 1);
            } else if (horizontalNum === splitRowSizeIndex + 1) {
              preTable.notAllowDrawLine.changeOpacityRow.push([...coordinate]);
              unOpacityHorizontal.splice(i, 1);
              unOpacityHorizontal.unshift([0, coordinate[1]]); // 不能用 push 否则就会再次进入循环进行判断 就又放到 preTable 里边去了
              unOpacityHorizontal.unshift([1, coordinate[1]]); // 不能用 push 否则就会再次进入循环进行判断 就又放到 preTable 里边去了
              i += 2;
            } else {
              coordinate[0] -= splitRowSizeIndex;
              i++;
            }
          }
          for (let i = 0; i < unOpacityVertical.length;) {
            const coordinate = unOpacityVertical[i];
            const horizontalNum = coordinate[1];
            if (horizontalNum <= splitRowSizeIndex - 1) {
              preTable.notAllowDrawLine.changeOpacityCol.push([...coordinate]);
              unOpacityVertical.splice(i, 1);
            } else if (horizontalNum === splitRowSizeIndex) {
              preTable.notAllowDrawLine.changeOpacityCol.push([...coordinate]);
              unOpacityVertical.splice(i, 1);
              unOpacityVertical.unshift([coordinate[0], 1]);
              unOpacityVertical.unshift([coordinate[0], 0]);
              i += 2;
            } else {
              coordinate[1] -= splitRowSizeIndex;
              i++;
            }
          }
          // 处理线 ↑
          preTable = nextTable;
        }
      }
    }

    if (!cellsTraversedBySplitLine.length) {
      if (tempPreTableChildren.length > 0) {
        this.handleTempPreTableChildrenSoftSplit(preTable, tempPreTableChildren);
      }
      preTable.row_size = [...unRowSize];
      preTable.min_row_size = [...unMinRowSize];
      preTable.notAllowDrawLine.changeOpacityRow.push(...unOpacityHorizontal);
      preTable.notAllowDrawLine.changeOpacityCol.push(...unOpacityVertical);
    }

    // TODO 先暂时用这个,没问题了再解决顺序问题,可能就几个有软分割的单元格才会有顺序问题,到时候可以把那几个专门拿出来进行排序,不用所有表格都进行排序
    splitTables.forEach(table => {
      table.sortingCells();
      table.page_break = true;
      // table.refreshTableRowSize(); // 不能通过调用这个接口解决 row_size 不对的问题 会造成死循环
    });
    return splitTables;
  },
  addLinesThatAreNotDrawn (preTable:Table, cell: Cell) {
    if (cell.rowspan > 1 || cell.colspan > 1) {
      for (let i = 0; i < cell.rowspan - 1; i++) {
        for (let j = 0; j < cell.colspan; j++) {
          preTable.notAllowDrawLine.row.push([
            cell.position[0] + 1 + i,
            cell.position[1] + j
          ]);
        }
      }
      for (let i = 0; i < cell.colspan - 1; i++) {
        for (let j = 0; j < cell.rowspan; j++) {
          preTable.notAllowDrawLine.col.push([
            cell.position[1] + 1 + i,
            cell.position[0] + j
          ]);
        }
      }
    }
  },
  getRealContentHeight (cell:Cell) {
    const childrenLength = cell.children.length;
    if (childrenLength) {
      if (cell.editor.document_meta.handleTableRowSize) {
        return cell.children.reduce((prev:any, row:any) => prev + row.height, 0) + cell.padding_top + cell.padding_bottom;
      }
      return cell.children.reduce((prev:any, row:any) => prev + row.height, 0);
    }
    if (cell.editor.document_meta.handleTableRowSize) {
      return cell.padding_top;
    } else {
      return 0;
    }
  },
  getHeight (cell:Cell, rowSize?: number[]) {
    rowSize = rowSize || cell.row_size;

    let height = 0;
    for (let i = 0; i < cell.rowspan; i++) {
      height += rowSize[cell.position[0] + i];
    }
    return height;
  }

};
