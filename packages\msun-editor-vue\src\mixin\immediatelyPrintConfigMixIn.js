const immediatelyPrintConfigMixIn = {
  data() {
    return {
      showImmediatelyPrintConfig: false,
    };
  },
  methods: {
    immediatePrintSetting(pageSetting) {
      const editor = this.instance.editor;
      editor.document_meta ?? {};
      editor.document_meta.immediatePrintSetting = pageSetting;
      this.showImmediatelyPrintConfig = false;
    },
    closeImmediatePrintPageSettingModal() {
      this.showImmediatelyPrintConfig = false;
    },
  },
};
export default immediatelyPrintConfigMixIn;
