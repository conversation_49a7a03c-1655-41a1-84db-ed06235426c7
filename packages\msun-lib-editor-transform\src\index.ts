// @ts-ignore
import initEditorCommonBase from "msun-lib-editor-common";
import { D2R } from "./main/D2R";
import { M2H } from "./main/M2H";
import { M2R } from "./main/M2R";
import { V2P } from "./main/V2P";
import { M2V } from "./main/M2V";
import Editor from "../../msun-lib-editor-common/src/editor/Editor";
import { R2M } from "./main/R2M";
import Cell from "../../msun-lib-editor-common/src/editor/Cell";
import Group from "../../msun-lib-editor-common/src/editor/Groups";
import { H2R } from "./main/H2R";
export default function (
  dom: string | HTMLElement,
  config: any = {},
  source: string
) {
  const instance = initEditorCommonBase(dom, config, source);
  const { editor } = instance;

  const bindingEditorEvent = (editor:Editor) => {
    const newInstance = { ...instance };
    newInstance.editor = editor;

    // R2M ↓
    R2M.initExtOptions(instance);
    editor.event.on("transReInitRaw", ({ rawData, isClearHistory }: {rawData: any, isClearHistory: boolean}) => {
      return R2M.reInitRaw({ editor, rawData, isClearHistory });
    });
    editor.event.on("transCellInsertRaw", ({
      cell,
      node,
      nextNode,
      groups,
      nextCell
    }: {
      cell: Cell,
      node: any,
      nextNode: any,
      groups?: Group[],
      nextCell?: Cell
    }) => {
      return R2M.cellInsertRaw(cell, node, nextNode, groups, nextCell);
    });
    // R2M ↑
    editor.event.on("dcXml2RawData", (xml:any) => {
      return D2R.startEntrance(xml, instance);
    });
    editor.event.on("modelData2Html", (paras:any) => {
      if (!paras) paras = editor.root_cell.paragraph;
      return M2H.modelData2Html(instance, paras);
    });
    H2R.initExtOptions(instance);
    editor.event.on("html2RawData", (htmlStr:string) => {
      return H2R.html2RawData(htmlStr);
    });
    V2P.initExtOptions(instance);
    editor.event.on("assemblePageJson", (extraInfo:any = {}, selectedPages:any) => {
      return V2P.assemblePageJson(editor, extraInfo, selectedPages);
    });
    M2V.initExtOptions(instance);
    editor.event.on("update", (editor: Editor, cell_index: number = 0, page_number: number = 1, page_index: number = 0) => {
      return M2V.update(editor, cell_index, page_number, page_index);
    });
    M2R.initExtOptions(instance);
    editor.event.on("modelData2RawData", (
      cell_header: any,
      cell_content: any,
      cell_footer: any,
      other_info: any) => {
      return M2R.modelDataToRawData(cell_header, cell_content, cell_footer, other_info);
    });
    editor.event.on("selectionData2RawData", (selectionData:any) => {
      return M2R.selectionData2RawData(editor, selectionData);
    });
    // TODO 先这样写，等稳定后再研究是否能够去除
    editor.event.on("clearDataTransTempInfo", () => {
      M2R.clearInitData();
    });
    editor.event.on("initNewEditor", (newEditor:Editor) => {
      bindingEditorEvent(newEditor);
    });
  };

  bindingEditorEvent(editor);
  return {
    ...instance
  };
};
