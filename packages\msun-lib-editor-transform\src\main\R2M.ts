import Editor from "msun-lib-editor-common/src/editor/Editor";
import Cell from "msun-lib-editor-common/src/editor/Cell";
import { RowLineType } from "../../../msun-lib-editor-common/src/editor/Definition";
import { handleCompatibilityProblemByVersion, recordVersionInfo } from "../Helper";
import { PACS_TABLE_NAME } from "../constant";
import Paragraph from "../../../msun-lib-editor-common/src/editor/Paragraph";
import Character from "../../../msun-lib-editor-common/src/editor/Character";
import ImageTable from "../../../msun-lib-editor-common/src/editor/ImageTable";
import Table from "../../../msun-lib-editor-common/src/editor/Table";
import Shape from "../../../msun-lib-editor-common/src/editor/shape";

export const R2M = {

  initExtOptions (instance: any) {
    this.deepClone = instance.utils.deepClone;
    this.uuid = instance.utils.getUUID;
    this.useRawDataByConfig = instance.utils.useRawDataByConfig;
    this.serializeCopy = instance.utils.serializeCopy;
    this.TypeJudgment = instance.TypeJudgment;
    this.removeRepeat = instance.utils.removeRepeat;
    this.fontIsEqual = instance.utils.fontIsEqual;
    this.assignDefinedPropertiesToCell = instance.assignDefinedPropertiesToCell;
    this.assignDefinedPropertiesToParagraph = instance.assignDefinedPropertiesToParagraph;
    this.assignDefinedPropertiesToTable = instance.assignDefinedPropertiesToTable;
  },

  /**
   * 纯的 rawData 转换 modelData 不影响任何现有编辑器的东西
   * @param rawData 将要转换成 modelData 的 rawData 数据
   * @returns 返回转化好的数据
   */
  rawData2ModelData (editor: Editor, rawData: any): {rootCell: Cell, headerCell: Cell, footerCell: Cell} {
    const headerCell = editor.createElement("cell", { belong: "header" }) as Cell;
    const rootCell = editor.createElement("cell", { belong: "content" }) as Cell;
    const footerCell = editor.createElement("cell", { belong: "footer" }) as Cell;

    rawData.groups && rawData.groups.forEach((group: any) => {
      rootCell.insertGroup(group);
    });

    rawData.header && rawData.header.forEach((raw: any, index: number, array: []) => {
      this.cellInsertRaw(headerCell, raw, array[index + 1]);
    });

    rawData.content && rawData.content.forEach((raw: any, index: number, array: []) => {
      this.cellInsertRaw(rootCell, raw,
        array[index + 1],
        rawData.groups);
    });

    // 因为内容可能会超出一页,所以页脚需要特殊处理
    rawData.footer && rawData.footer.forEach((raw: any, index: number, array: []) => {
      this.cellInsertRaw(footerCell, raw, array[index + 1]);
    });

    // TODO 这里修改 editor 的水印 形状的属性了，所以会参合在一起 这样不行 需要在后期修改
    // TODO 简化代码
    if (rawData.shapes && rawData.shapes.length) {
      editor.shapes = [...this.getShapesByRawData(editor, rawData.shapes)];
    } else {
      editor.shapes = [];
    }
    if (rawData.waterMarks && rawData.waterMarks.length) {
      editor.waterMarks = [...this.getDataByRawData(editor)];
    } else {
      editor.waterMarks = [];
    }

    return {
      rootCell,
      headerCell,
      footerCell
    };
  },

  reInitRaw ({ editor, rawData, isClearHistory }: {editor: Editor, rawData: any, isClearHistory?: boolean}) {
    editor.fontMap.clear();
    editor.internal.transformData = {
      images: [],
      fields: []
    };
    rawData = this.useRawDataByConfig(rawData);
    // 处理清空批注导致的bug
    if (rawData.meta && rawData.meta.commentsIDSet && Array.isArray(rawData.meta.commentsIDSet)) {
      rawData.meta.commentsIDSet = {};
    }
    if (isClearHistory) {
      editor.history.clear();
    }
    const rawConfig = rawData.config;
    if (rawConfig) {
      // 该判断中 之所以要判断是否为 undefined 是因为不考虑切换病历的情况 就第一次加载的时候 有可能没有就走配置的(后来的数据都加上了应该没这事了)

      if (rawConfig.header_horizontal !== undefined) {
        editor.config.show_header_line = rawConfig.header_horizontal;
      }

      if (rawConfig.rowLineType !== undefined) {
        editor.config.rowLineType = rawConfig.rowLineType;
      } else {
        editor.config.rowLineType = RowLineType.VOID; // 不让配置了 想改 只能通过接口
      }
      if (rawConfig.startPageNumber !== undefined) {
        editor.config.startPageNumber = rawConfig.startPageNumber;
      } else {
        editor.config.startPageNumber = 1;
      }
    }

    // 如果当前是编辑页眉页脚状态，则先退出
    if (editor.is_edit_hf_mode) {
      editor.exitEditHfMode(false);
    }

    editor.contextState.resetFontState();
    editor.pages = [];
    editor.raw = rawData;
    editor.internal.imageSrcObj = rawData.imageSrcObj ?? {};
    editor.document_meta = editor.raw.meta ?? {};

    // 将版本号写入 document_meta 跟处理版本兼容这两行代码就要放在 document_meta 赋值之后生成数据之前，否则可能会没有 versionList 没有版本号
    recordVersionInfo(editor);
    handleCompatibilityProblemByVersion(editor);
    editor.custom_meta = editor.raw.customMeta ?? {};
    if (editor.raw.config) {
      // 设置页面大小和方向
      editor.config.setPageSize(editor.config.page_size_type, editor.raw.config.direction, editor.raw.config.page_info?.page_size);
    } else if (editor.config.page_direction !== "vertical") {
      // 如果文档中没有配置的横向纵向信息，且当前页面方向与默认方向不同，则还原页面方向为纵向
      editor.config.setPageSize(editor.config.page_size_type, "vertical", editor.raw.config.page_info?.page_size);
    }

    const pageSize = editor.config.getPageSize();
    editor.page_size.width = pageSize.width;
    editor.page_size.height = pageSize.height;
    if (editor.raw.fontMap) {
      for (const id in editor.raw.fontMap) {
        const style = editor.raw.fontMap[id];
        editor.fontMap.add(style, id);
      }
    }

    const { headerCell, footerCell, rootCell } = this.rawData2ModelData(editor, editor.raw);

    editor.root_cell = rootCell;
    editor.header_cell = headerCell;
    editor.footer_cell = footerCell;

    if (editor.current_cell.hf_part) {
      editor.current_cell = editor[
        (editor.current_cell.hf_part + "_cell") as "header_cell" | "footer_cell"
      ];
    } else {
      editor.current_cell = editor.root_cell;
    }

    // 初始设置光标位置在开始位置
    editor.selection.setCursorByRootCell("start");

    // 处理浮动模型
    editor.raw.floatModelRaws?.forEach((floatModel: any) => {
      const floatCell = editor.initFloatModel(editor, floatModel.width, floatModel.height, floatModel.originPosition); // new FloatModel(editor, floatModel.width, floatModel.height, floatModel.originPosition); // editor.createFloatModel(floatModel.width, floatModel.height, floatModel.originPosition)!;
      floatModel.data.forEach((it: any, index: number, array: any[]) => {
        floatCell.insert_raw(it, array[index + 1]);
      });
      editor.floatModels.push(floatCell);
    });
    editor.editFloatModelMode = false;
    editor.updateFormatParagraph();
    editor.event.emit("reInitRaw");
  },

  cellInsertRaw (cell: Cell, node: any, nextNode: any, groups?: any[], nextCell?: any) {
    const editor = cell.editor;
    if (node.type === "table") {
      const groupId = groups
        ? cell.findGroupFromGroups(node.id, groups)
        : null;
      const table = (node.name === PACS_TABLE_NAME || this.TypeJudgment.isImageTable(node))
        ? editor.createElement("imageTable", {
          editor,
          id: node.id,
          groupId,
          colSize: node.col_size,
          rowSize: node.row_size,
          minRowSize: node.min_row_size,
          parentCell: cell,
          left: cell.left + cell.padding_left,
          right: cell.right - cell.padding_right,
          top: cell.cursor_position
        }) as ImageTable
        : editor.createElement("table", {
          editor,
          id: node.id,
          groupId,
          colSize: node.col_size,
          rowSize: node.row_size,
          minRowSize: node.min_row_size,
          parentCell: cell,
          left: cell.left + cell.padding_left,
          right: cell.right - cell.padding_right,
          top: cell.cursor_position
        }) as Table;
      this.assignDefinedPropertiesToTable(table, node);
      // 此时的table.row_size 还不是最终 页面上的正确的row_size
      cell.children.push(table);

      cell.paragraph.push(table);
      table.cell_index = cell.cursor_index;

      cell.cursor_index += 1;
      // 插入表格的各个单元格
      for (let i = 0; i < node.cells.length; i++) {
        this.tableInsertRaw(editor, table, node.cells[i], node.cells[i + 1]);
        // table.insert_raw(node.cells[i], node.cells[i + 1]);
      }
      table.sortingCells(); // TODO 加个排序,因为新版的表格拆分要求表格中的单元格顺序必须是对的 而且 tab 键盘跳动也不是按顺序的了 所以要在这里排序
      // 设置单元格的居中对齐或者下对齐
      for (let i = 0; i < table.children.length; i++) {
        const cell = table.children[i];
        // 上对齐为默认值
        if (cell.vertical_align === "top") {
          continue;
        }
        let cursor_position = cell.padding_top;
        // 计算单元格的内容高度
        let children_height = cell.padding_bottom + cell.padding_top;
        cell.children.forEach((row) => {
          children_height += row.height;
        });
        // 计算当前行在居中和下对齐时的位置
        if (cell.vertical_align === "center" && children_height < cell.height) {
          cursor_position = (cell.height - children_height) / 2;
        } else if (cell.vertical_align === "bottom" && children_height < cell.height) {
          cursor_position = cell.height - children_height;
        }
        // 设置单元格内部行的位置
        for (let i = 0; i < cell.children.length; i++) {
          cell.children[i].top = cursor_position;

          cell.children[i].cell_index = i;
          cursor_position += cell.children[i].height;
        }
      }
      cell.updateParaIndex();
    }

    if (node.type === "p") {
      // ↑
      const para_id = node.id ?? this.uuid("para");
      const group_id = groups
        ? cell.findGroupFromGroups(para_id, groups)
        : null;
      const current_paragraph = editor.createElement("paragraph", {
        id: para_id,
        parentCell: cell,
        groupId: group_id
      }) as Paragraph;
      // 当前段落设置各个属性
      this.assignDefinedPropertiesToParagraph(current_paragraph, node);

      // 作用于表格中的对齐方式
      cell.vertical_align = current_paragraph.vertical_align;
      cell.assemblyParaContent(node, current_paragraph, nextNode, null);
      // 此处解决行尾添加复选框样式错误问题，以及单元格内文字删除完后字体样式还原为默认样式问题
      const lastCharacter = current_paragraph.lastCharacter;
      const lastChild = node.children?.[node.children?.length - 1];
      let font;
      if (lastChild && lastChild.font_id) {
        const fontMap = editor.fontMap.get();
        font = fontMap.get(lastChild.font_id);
      }
      const newEnterChar = editor.createElement("char", {
        font: font || cell.editor.fontMap.add(cell.editor.config.default_font_style),
        value: "\n"
      }) as Character; // 段落为空时，换行使用默认样式
      (lastCharacter && lastCharacter.value === "\n") ||
          current_paragraph.characters.push(newEnterChar);
      cell.paragraph.push(current_paragraph);
      // 不能放到p里面，会导致报错，  这个在这里要放到createRow前面
      cell.updateParaIndex();
      current_paragraph.createRow();
      // 到这儿，已经创建完了 Row  每个 Row 的 top 值现在还是 0 通过 updateRowBounding 更新了 top 值
      cell.children.push(...current_paragraph.children);
      cell.cursor_index += current_paragraph.children.length;
    }
    // 全篇只执行一次即可
    if (!nextNode) {
      cell.updateRowBounding(0, nextCell);
    }
  },

  tableInsertRaw (editor: Editor, table: Table, node: any, nextNode: any) {
    const cell = editor.createElement("cell", {
      position: node.pos,
      colspan: node.colspan,
      rowspan: node.rowspan,
      table
    }) as Cell;
    this.assignDefinedPropertiesToCell(cell, node);
    table.children.push(cell);
    table.cellMap.set(cell.id, cell);

    for (let i = 0; i < node.children.length; i++) {
      this.cellInsertRaw(cell, node.children[i], node.children[i + 1], undefined, nextNode);
    }
  },

  getShapesByRawData (editor: Editor, shapes: any) {
    const shapes_list = [];

    for (let i = 0; i < shapes.length; i++) {
      const shape = shapes[i];

      const params: any = {};

      for (const shape_param in shape) {
        params[shape_param] = shape[shape_param];
      }
      shapes_list.push(editor.createElement("shape", { params, shapeType: shape.type }) as Shape);
    }
    return shapes_list;
  },

  /**
   * 通过原始数据获取waterMarks
   * @param waterMarks
   */
  getDataByRawData (editor: Editor) {
    const waterMarks = editor.raw.waterMarks;

    const marks_list: any = [];
    for (let i = 0; i < waterMarks.length; i++) {
      const waterMark = waterMarks[i];
      const params: any = {};

      for (const mark_param in waterMark) {
        params[mark_param] = waterMark[mark_param];
      }
      params.is_edit = false;
      const newWaterMark = editor.createElement("wartermark", { name: params.name, wartermarkType: params.type, mode: params.mode, width: params.width, height: params.height, start: params.start, params: params.params });
      if (params.params.src) {
        editor.imageMap.addOnload(params.params);
      }
      marks_list.push(newWaterMark);
    }
    return marks_list;
  }

};
