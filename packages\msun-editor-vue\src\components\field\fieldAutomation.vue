<template>
  <modal
    class="table-modal"
    :show="show"
    :width="width"
    :freePoint="true"
    :sessionMove="true"
    pointer-events:none
    @cancel="cancel"
  >
    <div class="auto-box">
      <a-table
        id="fieldAutoParent"
        :columns="fieldAutoColumns"
        :data-source="fieldAutomation_list"
        bordered
        :pagination="false"
        :showHeader="true"
        :scroll="{ x: 0, y: 87 }"
        :customRow="clickCustomRow"
      >
        <template
          v-for="(col, i) in ['field', 'condition', 'content', 'action']"
          :slot="col"
          slot-scope="text, record, num"
        >
          <template>
            <div :key="i">
              <div v-if="col === 'field'">文本域内容</div>
              <div v-else-if="col === 'condition'">
                <a-select
                  :style="{ width: '70px' }"
                  :defaultValue="text"
                  :dropdownStyle="{ zIndex: 99999 }"
                  @change="selectChange($event, num, col)"
                  dropdownClassName="select-input"
                >
                  <a-select-option
                    v-for="(item, j) in symbol"
                    :value="item"
                    :key="j"
                    >{{ item }}</a-select-option
                  >
                </a-select>
              </div>
              <div v-else-if="col === 'content'">
                <div
                  v-if="
                    field &&
                    ((field.type === 'select' && field.multi_select === 1) ||
                      (field.type === 'box' && field.box_multi === 1))
                  "
                >
                  <a-select
                    mode="multiple"
                    style="width: 100%"
                    :defaultValue="contentSelect[num]"
                    dropdownClassName="xeditor-input-up"
                    @change="selectHandleChange"
                  >
                    <a-select-option
                      v-for="(item, index) in selectFieldContent"
                      :key="index"
                    >
                      {{ item.text }}
                    </a-select-option>
                  </a-select>
                </div>
                <div
                  v-else-if="judgeCondition(record.condition)"
                  style="display: flex"
                >
                  <a-input-number
                    style="margin-right: 10px"
                    :value="getValue(text.text, 0)"
                    @change="IntervalChange($event, num, 1)"
                  ></a-input-number>
                  <div style="line-height: 32px">一</div>
                  <a-input-number
                    style="margin-left: 10px"
                    :value="getValue(text.text, 1)"
                    @change="IntervalChange($event, num, 2)"
                  ></a-input-number>
                </div>
                <div v-else>
                  <a-input
                    :disabled="record.action === '联动' ? true : false"
                    :id="record.key + i"
                    :value="text.text"
                    @click="clickFieldAutoInput(record.key, i)"
                    @change="fieldAutoHandleChange($event, num)"
                  />
                </div>
              </div>
              <div v-else-if="col === 'action'">
                <a-select
                  :defaultValue="text"
                  dropdownClassName="xeditor-input-up"
                  style="width: 70px"
                  @change="selectChange($event, num, col)"
                >
                  <a-select-option :value="'关联'">关联</a-select-option>
                  <a-select-option :value="'弹窗'">弹窗</a-select-option>
                  <a-select-option :value="'提示'">提示</a-select-option>
                  <a-select-option :value="'联动'">联动</a-select-option>
                  <a-select-option :value="'隐藏'">隐藏</a-select-option>
                  <a-select-option :value="'显示'">显示</a-select-option>
                </a-select>
              </div>
            </div>
          </template>
        </template>

        <template slot="delete" slot-scope="text, record">
          <div class="editable-row-operations">
            <a @click="() => fieldAutomationDeleteRow(record.key)">删除</a>
          </div>
        </template>
      </a-table>
      <div
        v-show="showFieldAutomationSelect"
        class="fieldAutomation-select"
        id="fieldAutoList"
        v-if="
          selectFieldContent.length > 0 &&
          (selectFieldContent[0].text !== '' ||
            selectFieldContent[0].code !== '' ||
            selectFieldContent[0].value)
        "
      >
        <div
          v-for="(item, i) in selectFieldContent"
          class="fieldAutomation-select-div"
          :key="i"
          @click="clickSelectDiv(item)"
        >
          {{ item.text }}
        </div>
      </div>
    </div>
    <div class="auto-spacing">
      <div class="spacing-button" @click="add">添加</div>
    </div>
    <div class="auto-box">
      <a-table
        v-if="
          fieldAutomation_list[focusRowNum].action === '关联' ||
          fieldAutomation_list[focusRowNum].action === '联动' ||
          fieldAutomation_list[focusRowNum].action === '隐藏' ||
          fieldAutomation_list[focusRowNum].action === '显示'
        "
        id="fieldConnectParent"
        :columns="columns"
        :data-source="fieldAutomation_list[focusRowNum].changeFields"
        bordered
        :pagination="false"
        :scroll="{ y: 87 }"
      >
        <template
          v-for="(col, i) in ['name', 'contentChange']"
          :slot="col"
          slot-scope="name, record, num"
        >
          <template>
            <div :key="i" class="editable-row-operations" :id="record.key">
              <div
                v-if="
                  (record.type === 'multiBox' ||
                    record.type === 'multiSelect') &&
                  col === 'contentChange'
                "
              >
                <a-select
                  mode="multiple"
                  style="width: 100%"
                  dropdownClassName="xeditor-input-up"
                  :defaultValue="getChangeText(record.changeText, 1)"
                  @focus="getConnectClickSelect(record.key)"
                  @change="selectConnectHandleChange"
                >
                  <a-select-option
                    v-for="(item, m) in selectContent[num]"
                    :value="item.text"
                    :key="m"
                  >
                    {{ item.text }}
                  </a-select-option>
                </a-select>
              </div>
              <div v-else>
                <a-input
                  ref="fieldConnection"
                  :disabled="
                    (fieldAutomation_list[focusRowNum].action === '联动' ||
                      fieldAutomation_list[focusRowNum].action === '隐藏' ||
                      fieldAutomation_list[focusRowNum].action === '显示') &&
                    col === 'contentChange'
                      ? true
                      : false
                  "
                  :id="record.key + i"
                  :value="
                    col === 'name'
                      ? record.name
                      : getChangeText(record.changeText)
                  "
                  :readOnly="connectReadOnly"
                  @click="clickConnectFieldAutoInput(record.key, i)"
                  @focus="getConnectFocusDom($event, i, num)"
                  @change="(e) => connectHandleChange(e.target.value, num, col)"
                />
              </div>
            </div>
          </template>
        </template>

        <template slot="delete" slot-scope="text, record">
          <div class="editable-row-operations">
            <a @click="() => deleteConnectRow(record.key)">删除</a>
          </div>
        </template>
      </a-table>
      <div v-else style="display: flex; padding: 10px">
        <div class="inputText">文本：</div>

        <a-input
          :value="fieldAutomation_list[focusRowNum].changeFields[0]"
          @change="otherTypeChange($event)"
        ></a-input>
      </div>
      <div
        v-show="showFieldConnectSelect"
        class="fieldAutomation-select"
        id="fieldConnectList"
        v-if="
          selectContent[connectFocusRowNum].length > 0 &&
          (selectContent[connectFocusRowNum].text !== '' ||
            selectContent[connectFocusRowNum].code !== '' ||
            selectContent[connectFocusRowNum].value)
        "
      >
        <div
          v-for="(item, i) in selectContent[connectFocusRowNum]"
          class="fieldAutomation-select-div"
          :key="i"
          @click="clickConnectSelectDiv(item)"
        >
          {{ item.text }}
        </div>
      </div>

      <div slot="editor-modal-footer" class="footer"></div>
    </div>
    <div slot="editor-modal-footer" class="footer">
      <div class="spacing-button add" @click="connectAdd">添加</div>
      <div>
        <a-button type="default" @click="cancel">取消</a-button>
        <a-button type="primary" @click="submit">确定</a-button>
      </div>
    </div>
  </modal>
</template>

<script>
const fieldAutoColumns = [
  {
    title: "当前文本域",
    dataIndex: "field",
    width: "12%",
    scopedSlots: { customRender: "field" },
  },
  {
    title: "条件",
    dataIndex: "condition",
    width: "10%",
    scopedSlots: { customRender: "condition" },
  },
  {
    title: "内容",
    dataIndex: "content",
    width: "25%",
    scopedSlots: { customRender: "content" },
  },

  {
    title: "动作",
    dataIndex: "action",
    width: "10%",
    scopedSlots: { customRender: "action" },
  },

  {
    title: "删除",
    dataIndex: "delete",
    width: "8%",
    scopedSlots: { customRender: "delete" },
  },
];
const columns = [
  {
    title: "关联文本域名称",
    dataIndex: "name",
    width: "25%",
    scopedSlots: { customRender: "name" },
  },
  {
    title: "内容变更为",
    dataIndex: "contentChange",
    width: "65%",
    scopedSlots: { customRender: "contentChange" },
  },
  {
    title: "删除",
    dataIndex: "delete",
    width: "10%",
    scopedSlots: { customRender: "delete" },
  },
];
const initData = [
  {
    key: getUUID(),
    condition: "=",
    content: {
      name: undefined,
      text: "",
    },
    action: "关联",
    changeFields: [
      {
        key: getUUID(),
        name: "",
        type: "",
        changeText: "",
      },
    ],
  },
];
import { getUUID } from "../../assets/js/utils";
import modal from "../common/modal.vue";
// import fieldConnect from "./fieldConnect.vue";
export default {
  name: "fieldAutomation",
  components: { modal },
  data() {
    return {
      columns,
      fieldAutoColumns,
      fieldAutomation_list: [
        {
          key: "",
          condition: "=",
          content: "",
          action: "关联",
          changeFields: [],
        },
      ],
      // focusDom: null,
      showFieldAutomationSelect: false,
      formulaSelect: "=",
      pageDirection: "vertical",
      contentSelect: [],
      selectFieldContent: [],
      connectReadOnly: false,
      fieldContact: "关联",
      focusRowNum: 0,
      symbol: ["=", "≠", ">", "<", ">=", "<=", ">=<=", "><=", ">=<", "><"],
      inputOne: 0,
      inputTwo: 0,
      field_list: [],
      // connectFocusDom: null,
      showFieldConnectSelect: false,
      selectContent: [[]],
      connectFocusRowNum: 0,
      clickCustomRow: (record, index) => {
        return {
          on: {
            click: () => {
              this.focusRowNum = index;
              if (this.judgeCondition(record.condition)) {
                const textList = record.content.text.split(",");
                this.inputOne = textList[0];
                this.inputTwo = textList[1];
              }
            },
          },
        };
      },
    };
  },

  props: {
    show: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
      default: 650,
    },
    title: {
      type: String,
      default: "",
    },
  },
  watch: {
    show(val) {
      if (val) {
        let list = [];
        if (!this.field) {
          this.fieldAutomation_list = initData;
          this.$nextTick(() => {
            this.connectFocusDom = this.$refs.fieldConnection
              ? this.$refs.fieldConnection[0].$el
              : null;
          });
          this.editor.editor.formulaMode(true);
          return;
        }
        if (this.field.type === "select" || this.field.type === "box") {
          this.symbol = ["="];
        }
        if (this.field.automation_list) {
          this.field.automation_list.forEach((e) => {
            list.push({
              key: getUUID(),
              condition: e.condition,
              content: e.content,
              action: e.action,
              changeFields: e.changeFields,
            });
          });
        }
        if (list.length === 0) {
          list = [
            {
              key: getUUID(),
              condition: "=",
              content: {
                name: undefined,
                text: "",
              },
              action: "关联",
              changeFields: [
                {
                  key: getUUID(),
                  name: "",
                  type: "",
                  changeText: "",
                },
              ],
            },
          ];
        }
        //初始化是重置inputOne，inputTwo
        if (this.judgeCondition(list[0].condition)) {
          const splitList = list[0].content.text.split(",");
          this.inputOne = splitList[0];
          this.inputTwo = splitList[1];
        }
        this.fieldAutomation_list = list;
        if (this.field.source_list.length) {
          this.selectFieldContent = [...this.field.source_list];
        } else {
          this.selectFieldContent = [];
        }
        if (this.field.type === "box") {
          this.selectFieldContent = [];
          const boxFields = this.field.group_all_items;
          for (let i = 0; i < boxFields.length; i++) {
            const boxField = boxFields[i];
            const data = {
              key: this.editor.utils.getUUID("choice"),
              text: boxField.text,
              name: boxField.name,
              disabled: boxField.disabled,
            };
            this.selectFieldContent.push(data);
          }
        }
        this.selectContent = [[]];
        //获取内容框的初始值
        for (let i = 0; i < this.fieldAutomation_list.length; i++) {
          const info = this.fieldAutomation_list[i];
          const contentList = info.content.text.split(/,|，/);
          //select需要传入数字数组
          this.contentSelect[i] = this.getInitNum(
            this.selectFieldContent,
            contentList
          );

          this.fieldAutomation_list[i].changeFields.forEach((e, k) => {
            let fieldInfo = "";
            if (e.name) {
              const nameList = e.name.split(/,|，/).filter(Boolean);
              if (nameList.length === 1) {
                fieldInfo = this.editor.editor.getFieldsByName(nameList[0])[0];
                if (fieldInfo) {
                  const type = this.changeType(e.name).type;
                  e.type = type;
                  if (i === 0) {
                    if (fieldInfo.type === "select") {
                      this.selectContent[k].push(...fieldInfo.source_list);
                      this.connectReadOnly = true;
                    } else if (fieldInfo.type === "box") {
                      for (
                        let j = 0;
                        j < fieldInfo.group_all_items.length;
                        j++
                      ) {
                        const ele = fieldInfo.group_all_items[j];
                        this.selectContent[k].push({
                          text: ele.text,
                          name: ele.name,
                        });
                      }
                      this.connectReadOnly = true;
                    } else {
                      e.type = "";
                    }
                  }
                }
              }
            }
          });
        }

        this.$nextTick(() => {
          this.connectFocusDom = this.$refs.fieldConnection
            ? this.$refs.fieldConnection[0].$el
            : null;
        });
        this.editor.editor.formulaMode(true, this.field?.name);
      }
    },
  },

  methods: {
    add() {
      const new_source_list = {
        key: getUUID(),
        condition: "=",
        content: {
          name: undefined,
          text: "",
        },
        action: "关联",
        changeFields: [
          {
            key: getUUID(),
            name: "",
            type: "",
            changeText: "",
          },
        ],
      };

      this.fieldAutomation_list.push(new_source_list);
      const dom = document.getElementsByClassName("ant-table-tbody")[0];
      this.$nextTick(() => {
        //点击添加按钮时滚动到添加的那一行
        document.getElementsByClassName("ant-table-body")[0].scrollTop =
          parseInt(dom?.getBoundingClientRect().height) - 43;
        //关联文本域换为新增的行

        this.focusRowNum = parseInt(
          dom?.getBoundingClientRect().height / 43 - 1
        );
        // this.focusRowNum会改变this.fieldAutomation_list,必须添加this.$nextTick才能更新dom
        this.$nextTick(() => {
          this.connectFocusDom = this.$refs.fieldConnection
            ? this.$refs.fieldConnection[0].$el
            : null;
        });
      });
    },
    connectAdd() {
      const new_source_list = {
        key: getUUID(),
        name: "",
        type: "",
        changeText: "",
      };
      this.fieldAutomation_list[this.focusRowNum].changeFields.push(
        new_source_list
      );
      const dom = document.getElementsByClassName("ant-table-tbody")[1];
      this.$nextTick(() => {
        //点击添加按钮时滚动到添加的那一行
        document.getElementsByClassName("ant-table-body")[1].scrollTop =
          parseInt(dom?.getBoundingClientRect().height) - 43;
        //关联文本域换为新增的行
      });
      // this.focusRowNum=
    },
    restore() {
      this.symbol = [
        "=",
        "≠",
        ">",
        "<",
        ">=",
        "<=",
        ">=<=",
        "><=",
        ">=<",
        "><",
      ];
      this.showFieldConnectSelect = false;
      this.selectFieldContent = [];
    },
    submit() {
      this.$emit("submit", this.fieldAutomation_list);
      this.restore();
      this.editor.editor.formulaMode(false);
    },
    cancel() {
      this.$emit("cancel");
      this.restore();
      this.editor.editor.formulaMode(false);
    },
    otherTypeChange(event) {
      this.fieldAutomation_list[this.focusRowNum].changeFields = [
        event.target.value,
      ];
    },
    getValue(text, num) {
      return text.split(",")[num];
    },
    getInitNum(arr1, arr2) {
      let indexes = [];
      arr1.forEach((item, index) => {
        if (arr2.includes(item.text)) {
          indexes.push(index);
        }
      });

      return indexes;
    },
    clickFieldAutoInput(val, i) {
      if (!(i === 2)) return;
      this.inputDomId = val + i;
      this.showFieldAutomationSelect = !this.showFieldAutomationSelect;
      const parentDiv = document.getElementById("fieldAutoParent");
      const cascadeDiv = document.getElementById("fieldAutoList");
      if (parentDiv && cascadeDiv) {
        const result = this.getClickRowNum(val);
        if (result !== undefined) {
          this.clickSelect = result;
        }
        const div = document.getElementById(this.inputDomId);
        const parentTop = this.getOffsetTop(parentDiv);
        const parentLeft = this.getOffsetLeft(parentDiv);
        const top = this.getOffsetTop(div);
        const left = this.getOffsetLeft(div);
        cascadeDiv.style.cssText = `top:${
          top - parentTop + 85
        }px;z-index:999;left:${left - parentLeft + 11}px`;
      }
    },
    getClickRowNum(val) {
      for (let i = 0; i < this.fieldAutomation_list.length; i++) {
        const key = this.fieldAutomation_list[i].key;
        if (key === val) {
          return i;
        }
      }
    },
    getConnectClickRowNum(val) {
      for (
        let i = 0;
        i < this.fieldAutomation_list[this.focusRowNum].changeFields.length;
        i++
      ) {
        const key =
          this.fieldAutomation_list[this.focusRowNum].changeFields[i].key;
        if (key === val) {
          return i;
        }
      }
    },
    selectChange(text, num, col) {
      if (col === "condition") {
        if (text === "≠") {
          text = "!=";
        }
        this.fieldAutomation_list[num].condition = text;

        if (this.judgeCondition(text)) {
          this.inputOne = 0;
          this.inputTwo = 0;
          this.fieldAutomation_list[num].content.text = "0,0";
        } else {
          this.fieldAutomation_list[num].content.text = "";
        }
      } else if (col === "action") {
        if (text === "弹窗") {
          this.fieldAutomation_list[num].changeFields = [];
        } else if (text === "提示") {
          this.fieldAutomation_list[num].changeFields = [];
        } else if (text === "关联" || text === "隐藏" || text === "显示") {
          this.fieldAutomation_list[num][col] = text;
          this.fieldAutomation_list[num].changeFields = [
            {
              key: getUUID(),
              name: "",
              type: "",
              changeText: "",
            },
          ];
        } else if (text === "联动") {
          this.fieldAutomation_list[num] = {
            key: getUUID(),
            condition: "=",
            content: {
              name: undefined,
              text: "",
            },
            action: "联动",
            changeFields: [
              {
                key: getUUID(),
                name: "",
                type: "",
                changeText: "",
              },
            ],
          };

          this.$nextTick(() => {
            this.connectFocusDom = this.$refs.fieldConnection
              ? this.$refs.fieldConnection[0].$el
              : null;
          });
        }
        this.fieldAutomation_list[num].action = text;
        this.fieldAutomation_list = [...this.fieldAutomation_list];
        this.fieldContact = text;
      }
    },
    selectHandleChange(numList) {
      let name = "";
      let text = "";
      for (let i = 0; i < numList.length; i++) {
        const num = numList[i];
        if (i === numList.length - 1) {
          text += this.selectFieldContent[num].text;
          name += this.selectFieldContent[num].name;
        } else {
          text += this.selectFieldContent[num].text + ",";
          name += this.selectFieldContent[num].name + ",";
        }
      }
      this.fieldAutomation_list[this.focusRowNum].content = {
        name: name,
        text: text,
      };
    },
    fieldAutoHandleChange(event, num) {
      if (this.field.type === "box") return;
      const text = event.target.value;
      this.fieldAutomation_list[num].content = {
        name: undefined,
        text: text,
      };
    },
    IntervalChange(number, num, location) {
      if (location === 1) {
        this.inputOne = number;
      } else {
        this.inputTwo = number;
      }
      this.fieldAutomation_list[num].content = {
        name: undefined,
        text: this.inputOne + "," + this.inputTwo,
      };
    },
    getOffsetTop(el) {
      return el.offsetParent
        ? el.offsetTop + this.getOffsetTop(el.offsetParent)
        : el.offsetTop;
    },
    getOffsetLeft(el) {
      return el.offsetParent
        ? el.offsetLeft + this.getOffsetLeft(el.offsetParent)
        : el.offsetLeft;
    },
    clickSelectDiv(val) {
      this.showFieldAutomationSelect = false;
      this.fieldAutomation_list[this.clickSelect].content = {
        name: val.name,
        text: val.text,
      };
    },

    fieldAutomationDeleteRow(key) {
      if (this.fieldAutomation_list.length === 1) {
        this.fieldAutomation_list = [
          {
            key: getUUID(),
            condition: "=",
            content: {
              name: undefined,
              text: "",
            },
            action: "关联",
            changeFields: [
              {
                key: getUUID(),
                name: "",
                type: "",
                changeText: "",
              },
            ],
          },
        ];
        this.focusRowNum = 0;
        return;
      }
      const result = this.getClickRowNum(key);
      this.fieldAutomation_list = this.fieldAutomation_list.filter(
        (item) => item.key !== key
      );
      if (result) {
        this.focusRowNum = Number(result) - 1;
      } else {
        this.focusRowNum = 0;
      }
    },
    changeType(text) {
      const fieldName = text.split(/,|，/).filter(Boolean);
      let field = null;
      let type = "";
      //如果关联文本域只有一个则内容框类型跟着文本域类型走
      if (fieldName.length === 1) {
        const getField = this.editor.editor.getFieldsByName(fieldName[0])[0];
        if (!getField) return { field: field, type: type };
        if (
          getField.type === "select" ||
          getField.type === "box" ||
          (getField.type === "box" &&
            getField.parent &&
            getField.parent.box_multi === 1)
        ) {
          field = getField;
          type = getField.type;
          if (getField.box_multi === 1) {
            type = "multiBox";
          } else if (getField.multi_select === 1) {
            type = "multiSelect";
          } else if (getField.parent) {
            type = "multiBox";
          }
          if (getField.type === "box" && getField.parent) {
            field = getField.parent;
          }
        }
      }
      return { field: field, type: type };
    },
    getChangeText(text, num) {
      const textList = String(text).split(/,|，/).filter(Boolean);
      if (!this.selectContent[this.connectFocusRowNum].length) return text;

      const list = textList.map(
        (n) => this.selectContent[this.connectFocusRowNum][n].text
      );
      if (num) return list;
      const needText = list.join(",");
      return needText;
    },
    clickConnectFieldAutoInput(val, i) {
      if (i !== 1) {
        this.connectReadOnly = false;
        return;
      }
      this.inputDomId = val + i;
      this.showFieldConnectSelect = !this.showFieldConnectSelect;
      if (!this.selectContent[this.connectFocusRowNum].length) {
        this.showFieldConnectSelect = false;
      }
      const parentDiv = document.getElementById("fieldConnectParent");
      const cascadeDiv = document.getElementById("fieldConnectList");
      if (parentDiv && cascadeDiv) {
        const result = this.getConnectClickRowNum(val);
        if (result !== undefined) {
          this.clickSelect = result;
        }
        const div = document.getElementById(this.inputDomId);
        const parentTop = this.getOffsetTop(parentDiv);
        const parentLeft = this.getOffsetLeft(parentDiv);
        const top = this.getOffsetTop(div);
        const left = this.getOffsetLeft(div);
        cascadeDiv.style.cssText = `top:${
          top - parentTop + 70
        }px;z-index:999;left:${left - parentLeft + 18}px`;
      }
    },
    selectConnectHandleChange(list) {
      const selectList = this.getIntersectionIndices(
        this.selectContent[this.connectFocusRowNum],
        list
      );
      this.fieldAutomation_list[this.focusRowNum].changeFields[
        this.connectFocusRowNum
      ].changeText = selectList.join(",");
    },
    getIntersectionIndices(arr1, arr2) {
      return arr1
        .filter((item) => arr2.indexOf(item.text) !== -1)
        .map((item) => arr1.indexOf(item));
    },
    getConnectClickSelect(key) {
      this.connectFocusRowNum = this.getConnectClickRowNum(key);
    },
    getConnectFocusDom(event, i) {
      if (i === 0) {
        this.connectFocusDom = event.target;
      }
    },
    clickConnectSelectDiv(val) {
      this.showFieldConnectSelect = false;
      const num = this.selectContent[this.connectFocusRowNum].findIndex(
        (e) => e.text === val.text
      );
      this.fieldAutomation_list[this.focusRowNum].changeFields[
        this.connectFocusRowNum
      ].changeText = String(num);
      this.selectContent[this.connectFocusRowNum].text = val.text;
      this.selectContent = [...this.selectContent];
    },
    connectHandleChange(value, num, col) {
      if (col === "name") {
        if (value === this.field?.name) {
          this.$editor.error("关联文本域名称不能包含当前编辑文本域名称");
          return;
        }
        this.fieldAutomation_list[this.focusRowNum].changeFields[num].name =
          value;
        const field = this.changeType(value).field;
        if (field) {
          if (field.type === "select") {
            this.selectContent[this.connectFocusRowNum] = [
              ...field.source_list,
            ];

            this.fieldAutomation_list[this.focusRowNum].changeFields[num].type =
              "select";
            if (field.multi_select) {
              this.fieldAutomation_list[this.focusRowNum].changeFields[
                num
              ].type = "multiSelect";
            }
          } else if (field.type === "box") {
            this.setBoxSelectContent(field);
          }
        } else {
          this.selectContent[num] = [];
          // this.fieldAutomation_list[this.focusRowNum].changeFields[
          //   num
          // ].changeText = "";
          this.fieldAutomation_list[this.focusRowNum].changeFields[num].type =
            "";
        }
      } else if (col === "contentChange") {
        this.fieldAutomation_list[this.focusRowNum].changeFields[
          num
        ].changeText = value;
      }
      this.selectContent = [...this.selectContent];
    },
    deleteConnectRow(key) {
      if (
        this.fieldAutomation_list[this.focusRowNum].changeFields.length === 1
      ) {
        this.fieldAutomation_list[this.focusRowNum].changeFields = [
          {
            key: getUUID(),
            name: "",
            type: "",
            changeText: "",
          },
        ];
        return;
      }
      this.fieldAutomation_list[this.focusRowNum].changeFields =
        this.fieldAutomation_list[this.focusRowNum].changeFields.filter(
          (item) => item.key !== key
        );
    },

    setBoxSelectContent(field) {
      this.selectContent[this.connectFocusRowNum] = [];
      for (let i = 0; i < field.group_all_items.length; i++) {
        const ele = field.group_all_items[i];
        this.selectContent[this.connectFocusRowNum].push({
          text: ele.text,
          name: ele.name,
        });
      }
      if (field.box_multi) {
        this.fieldAutomation_list[this.focusRowNum].changeFields[
          this.connectFocusRowNum
        ].type = "multiBox";
      }
      this.selectContent = [...this.selectContent];
    },
    judgeCondition(condition) {
      switch (condition) {
        case ">=<=":
          return true;
        case "><=":
          return true;
        case ">=<":
          return true;
        case "><":
          return true;
        default:
          return false;
      }
    },
    insertFieldName(fieldInfo) {
      if (
        !(
          this.fieldAutomation_list[this.focusRowNum].action === "关联" ||
          this.fieldAutomation_list[this.focusRowNum].action === "联动" ||
          this.fieldAutomation_list[this.focusRowNum].action === "隐藏" ||
          this.fieldAutomation_list[this.focusRowNum].action === "显示"
        )
      )
        return;
      let name = fieldInfo.name;
      if (fieldInfo.type === "box" && fieldInfo.parent) {
        name = fieldInfo.parent.name;
      }
      //获取当前聚焦的input在整个table的第几行

      const td = this.connectFocusDom.parentNode.parentNode.parentNode;
      const trs = td.closest("table").getElementsByTagName("tr");
      let tdIndex = -1;
      for (var i = 0; i < trs.length; i++) {
        var tds = trs[i].getElementsByTagName("td");
        // 检查当前行的每个单元格是否与目标单元格相同
        for (var j = 0; j < tds.length; j++) {
          if (tds[j] === td) {
            tdIndex = i; // 找到目标行，记录行数
            break; // 结束内层循环
          }
        }
        if (tdIndex !== -1) {
          break; // 结束外层循环
        }
      }
      this.connectFocusDom.focus();
      const focusRowInfo =
        this.fieldAutomation_list[this.focusRowNum].changeFields;
      let field_name = focusRowInfo[tdIndex].name;
      const result = field_name.endsWith(",") || field_name.endsWith("，");
      //文本域自动化名字去重
      const list = field_name.split(/,|，/).filter(Boolean);
      if (!list.includes(name)) {
        if (result) {
          focusRowInfo[tdIndex].name += name + ",";
        } else {
          if (!field_name) {
            focusRowInfo[tdIndex].name += name + ",";
          } else {
            focusRowInfo[tdIndex].name += "," + name + ",";
          }
        }
      } else {
        this.$editor.warning("重复添加文本域名称");
      }
      const field = this.changeType(focusRowInfo[tdIndex].name).field;
      focusRowInfo[tdIndex].type = field ? field.type : "";
      if (field) {
        this.connectReadOnly = false;
        if (field.type === "select") {
          this.selectContent[tdIndex] = [...field.source_list];

          if (field.multi_select) {
            focusRowInfo[tdIndex].type = "multiSelect";
            this.fieldAutomation_list[this.focusRowNum].changeFields[
              tdIndex
            ].type = "multiSelect";
          }
          this.connectReadOnly = true;
        } else if (field.type === "box") {
          this.setBoxSelectContent(field);
          this.connectReadOnly = true;
        }
      } else {
        this.selectContent[tdIndex] = [];
        // this.fieldAutomation_list[this.focusRowNum].changeFields[tdIndex].changeText = "";
      }
      this.fieldAutomation_list = [...this.fieldAutomation_list];
      this.selectContent = [...this.selectContent];
    },
  },
};
</script>
<style>
.select-input .ant-select-dropdown-menu-item {
  padding: 20px 12px;
  line-height: 20px;
}
</style>
<style scoped>
/* .table-modal /deep/ .ant-modal-body {
  height: 200px;
} */

.table-modal /deep/.ant-table-tbody > tr > td {
  padding: 5px;
}

.table-modal /deep/.ant-table-row-cell-break-word {
  padding: 5px;
}

.table-modal /deep/.ant-modal-content {
  top: 50px;
}

.editable-row-operations a {
  margin-left: 15px;
}

.fieldAutomation-select {
  position: absolute;
  border-radius: 4px;
  width: 127px;
  box-shadow: 0px 0px 5px rgb(177, 174, 174);
  background-color: rgb(255, 255, 255);
}

.attention {
  position: absolute;
  bottom: 45px;
  text-align: center;
  color: red;
}

.add {
  margin-top: -11px !important;
}

.auto-box {
  height: 130px;
  border: 1px solid rgb(217, 217, 217);
  padding: 3px;
  border-radius: 5px;
  background-color: white;
}

.ant-select-dropdown /deep/.ant-select-dropdown-menu-item {
  line-height: 10px;
  background-color: red;
}

.fieldAutomation-select-div {
  line-height: 22px;
  padding: 5px 12px;
  font-size: 14px;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.65);
}

.fieldAutomation-select-div:hover {
  background-color: rgb(231, 247, 255);
}

.auto-spacing {
  display: flex;
  padding: 0 32px 0 10px;
}

.spacing-button {
  /* border: 1px solid rgb(217, 217, 217); */
  border-radius: 3px;
  width: 35px;
  text-align: center;
  color: white;
  font-size: 12px;
  height: 18px;
  background-color: rgb(50, 144, 252);
  margin-top: 1px;
  cursor: pointer;
}

.inputText {
  line-height: 32px;
  text-align: center;
  width: 60px;
}

.footer {
  display: flex;
  justify-content: space-between;
  margin-left: 5px;
}

.table-modal
  /deep/.ant-select-selection--multiple
  .ant-select-selection__choice {
  max-width: 80px;
  /* 设置最大宽度 */
  white-space: nowrap;
  /* 文字不换行 */
  overflow: hidden;
  /* 隐藏溢出的内容 */
  text-overflow: ellipsis;
  /* 溢出的文字显示为省略号 */
}
</style>
