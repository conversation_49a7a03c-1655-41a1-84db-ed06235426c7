import BoxField from "./BoxField";
import { widgetType } from "./Definition";
import Editor from "./Editor";
import Element from "./Element";
import Font from "./Font";
import { isWidget } from "./Helper";
import PathUtils, { Path } from "./Path";
import { isField, uuid } from "./Utils";
import EditorHelper from "./EditorHelper";

export default class Widget extends Element {
  id: string
  selected: boolean = false;
  height: number;
  width: number;
  value: string = "";
  level: number = 0;
  color: string = "#ffffff";
  field_id: string | null = null;
  disabled: number = 0; // 是否允许通过点击设置选中状态
  field_position: string = "normal";
  widgetType: widgetType = "checkbox";
  border: string = "solid"; // 该复选框的边线样式
  font: Font;
  params: any;
  selectNum: number | undefined = undefined
  constructor(selected: boolean, height: number, font: Font, widgetType: widgetType = "checkbox", params?: any, id?: string) {
    super(height, height);
    this.selected = selected;
    this.width = this.height = height;
    if (widgetType === "caliper") {
      this.width = this.getCaliperWidth(params);
    }
    this.widgetType = widgetType;
    this.font = font;
    this.id = id ?? uuid("widget");
    this.params = params || {};
  }

  static updateCaliper(caliper: Widget, num: number, height: number, spacing: number, editor: Editor) {
    caliper.params.num = num;
    caliper.params.spacing = spacing;
    caliper.height = height;
    caliper.width = num * spacing + 10;
    editor.refreshDocument(true);
  }

  static insertSingle(editor: Editor, type: widgetType, border: string = "solid", height?: number, params?: any) {
    if (!editor.delSectionRecordStack()) {
      return false;
    }
    const model_path = editor.selection.focus;
    const para_path = editor.modelPath2ParaPath(model_path);

    // 光标行
    const focus_row = editor.selection.getFocusRow();

    const focus_field = editor.selection.getFocusField();
    let r_height = 16;
    if (type === "caliper" && height) {
      r_height = height;
    }
    if (focus_row) {
      const widget = focus_row.paragraph.insertWidget(
        false,
        r_height,
        type,
        para_path[para_path.length - 1],
        border,
        editor,
        focus_field,
        params
      );
      editor.selection.stepForward(1, focus_row);
      editor.update();

      editor.scroll_by_focus();

      editor.render();
      return widget;
    }
  }

  getCaliperWidth(params: any) {
    let width = 100;
    let spacing = 10;
    if (params.num) {
      if (params.num < 1) {
        params.num = 1;
      }
      if (params.spacing) {
        spacing = params.spacing;
      }
      width = params.num * spacing + 10;
    }
    return width;
  }

  // 插入卡尺
  static insertCaliper(editor: Editor, height: number, params: any) {
    const insertPara = editor.selection.getFocusParagraph();
    const model_path = editor.selection.focus;
    const para_path = editor.modelPath2ParaPath(model_path);
    insertPara.insertWidget(false, height, "caliper", para_path[para_path.length - 1], "solid", editor, null, params);
    editor.update();
    editor.render();
  }

  static insert(editor: Editor, params: any) {
    // TODO 不明白调用这个意义是什么 就是为了删除选区吗？？
    if (!editor.delSectionRecordStack()) {
      return false;
    }

    const para_path = editor.selection.para_focus;
    const focus_row = editor.selection.getFocusRow();
    const focus_field = editor.selection.getFocusField();

    if (focus_field && !focus_field.children.length) {
      // 判断children中是否有内容 如果没有 显示的内容就是 placeholder 需要删除
      focus_field.removeParaPlaceholder();
    }

    // 获取传入的信息
    const { isGroup, groupName, hideBorder, isMulti, required, widgetType, deletable, items, automation_list, checkBoxSeparator, dynamic,selectType, border = "solid" } = params;
    const props = {
      id: uuid("box"),
      name: "", // 用于获取或设置复选框状态时使用
      placeholder: "",
      type: "box",
      start_symbol: "[",
      end_symbol: "]",
      readonly: 1,
      deletable: isNaN(deletable) ? 1 : deletable
    };
    if (hideBorder) {
      props.start_symbol = "";
      props.end_symbol = "";
    }

    // 首先插入一个box类型的文本域
    const out_box_field = focus_row.paragraph.insertField(
      para_path[para_path.length - 1],
      focus_field,
      editor.contextState.getFontState(),
      props
    ) as BoxField;
    out_box_field.cascade_list = [];
    out_box_field.automation_list = automation_list;
    out_box_field.meta.dynamic = dynamic
    out_box_field.meta.selectType = selectType
    // 当传入确定分组或者项数大于1时创建分组结构
    if (isGroup || items.length > 1) {
      out_box_field.name = groupName ?? "";
      out_box_field.box_multi = isMulti ? 1 : 0;
      out_box_field.required = required ? 1 : 0;
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        const field = editor.createElement("field", { field_id: uuid("box"), type: "box", style: out_box_field.style, cell: out_box_field.cell }) as BoxField;
        if (isField(field)) {
          let show_field_names = [];
          if (item.field_names) {
            show_field_names = item.field_names.split(/,|，/);
          }
          out_box_field.cell.fields.push(field);
          field.name = item.name;
          field.type = "box";
          field.readonly = 1;
          field.placeholder = "";
          field.start_symbol = "";
          field.end_symbol = "";
          field.box_checked = item.checked ? 1 : 0;
          field.formula_value = item.formula_value;
          field.generateMainProps();
          field.parent = out_box_field;
          out_box_field.cascade_list.push({
            text: item.value,
            show_field_names: show_field_names,
            formula_value: item.formula_value
          });

          // 创建一个小组件对象
          const widget = editor.createElement("widget", { selected: !!item.checked, widgetType: (widgetType === "checkbox" ? "checkbox" : "radio") });
          if (isWidget(widget)) {
            widget.field_id = field.id;
            widget.disabled = item.disabled ? 1 : 0;
            widget.border = border;
            field.children.push(widget);
            field.children.push(...field.textToFieldCharacter(item.value));
          }
          out_box_field.children.push(field);
          // if (i !== items.length - 1) {
          //   out_box_field.children.push(...out_box_field.textToFieldCharacter(" "));
          // }
        }
      }
    } else {
      const item = items[0];
      out_box_field.box_checked = item.checked ? 1 : 0;
      out_box_field.name = item.name;
      let field_names = [];
      if (item.field_names) {
        field_names = item.field_names.split(/,|，/);
      }
      out_box_field.cascade_list.push({
        text: item.value,
        show_field_names: field_names
      });
      const widget = editor.createElement("widget", { selected: !!item.checked, widgetType: (widgetType === "checkbox" ? "checkbox" : "radio") });
      if (isWidget(widget)) {
        widget.field_id = out_box_field.id;
        widget.disabled = item.disabled ? 1 : 0;
        widget.border = border;
        out_box_field.children.push(widget);
        out_box_field.children.push(...out_box_field.textToFieldCharacter(item.value));
      }
    }
    out_box_field.updateChildren();
    if (out_box_field) {
      out_box_field.readonly = 0;
      //TODO tang 直接用传进来的符号插入

      const children = out_box_field.children;
      for (let i = 0; i < children.length - 1; i++) {
        const child = children[i] as BoxField;
        if (child.children) {
          const endParaPath = child.end_para_path_outer;
          const endModelPath = editor.paraPath2ModelPath(endParaPath);
          editor.selection.setCursorPosition(endModelPath);
          if (checkBoxSeparator) {
            EditorHelper.insertText(editor, checkBoxSeparator)
          } else if (checkBoxSeparator === undefined) {
            EditorHelper.insertText(editor, " ")
          }
        }
      }
      out_box_field.readonly = 1;
    }
    const end_para_path: Path = [...out_box_field.end_para_path];
    PathUtils.movePathCharNum(end_para_path);
    editor.selection.setCursorPosition(editor.paraPath2ModelPath(end_para_path));
    editor.update(...editor.getUpdateParamsByContainer(focus_row));
    editor.render();
    return out_box_field;
  }

  static update(editor: Editor, box_field: BoxField, params: any): any {
    const box = box_field.parent_box;
    editor.removeFields([box]);
    const field = editor.insertWidget(params);
    return field
  }

  static attrJudgeUndefinedAssign(newModel: Widget, raw: any) {
    if (newModel.field_id !== undefined) newModel.field_id = raw.field_id;
    if (newModel.widgetType !== undefined) newModel.widgetType = raw.widgetType;
    if (newModel.disabled !== undefined) newModel.disabled = raw.disabled;
    if (newModel.border !== undefined) newModel.border = raw.border;
  }

  contain_vertical(y: number) {
    return this.top <= y && y <= this.bottom;
  }

  contain_horizontal(x: number) {
    return this.left <= x && x <= this.right;
  }

  contain(x: number, y: number) {
    return this.contain_horizontal(x) && this.contain_vertical(y);
  }

  // draw() {
  //   Renderer.drawWidget(this, this.left, this.top, this.height, "red", "blue");
  // }
  copy() {
    const widget = new Widget(this.selected, this.height, this.font, this.widgetType, this.params, this.id);
    widget.field_id = this.field_id;
    widget.field_position = this.field_position;
    widget.disabled = this.disabled;
    widget.border = this.border;
    widget.selectNum = this.selectNum;
    return widget;
  }
}
