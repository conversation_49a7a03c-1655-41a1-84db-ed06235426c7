/**
 * 右键菜单相关逻辑
 * @type {{}}
 */
// import rawData from "../assets/js/rawDatas";
import {
  defaultRightMenuConfig,
  baseMenuList,
  insertTbl,
  fieldPropMethod,
  caliperPropMethod,
  boxTypeField,
  groupMethod,
  convertPDFMethod,
  printContinueMethod,
  alignTypeList,
  listNumRestart,
  insertListAboutBase,
  tableMenuList,
  deleteListAboutTbl,
  insertListAboutTbl,
  headerFooter,
  footerList,
  editorRootCell,
  printOption,
  tableLine,
} from "./rightMenuOption";

const rightMenuMixIn = {
  data() {
    return {
      rightMenuEvent: null, // 右键点击的事件对象
      lock: false,
      currentRightMenuConfig: defaultRightMenuConfig, // 当前的右键菜单配置
      listOfFinalShow: [], // 这才是最终展示的列表

      isShowModal: false, // 展示插入表格弹窗
      isShowSplitCellModal: false,
      isShowLineModal: false, //设置水平线属性

      isShowCellAttr: false, //展示单元格属性弹窗
      isShowPageConfigSetting: false,

      isShowSlashUp: false, // 单元格是否显示左上角开始的斜线
      isShowSlashDown: false, // 单元格是否显示左下角开始的斜线

      isCanNotDelTbl: false, // 该表格 是否不能删除 为true 就是不能删除
      isShowWordModal: false, // 是否展示字体的模态框

      isMarkType: true,
      textIndentNum: 0, // 是否首行缩进
      paraSpacing: 0, // 段间距
      rowDoubleDistance: 0, // 行倍距

      insertOL: false, // 有序列表
      insertUL: false, // 无序列表

      isShowRightClickMenu: false,
      row_num: 1, // 插入表格的行数
      col_num: 1, // 插入表格的列数
      split_row_num: 1,
      split_col_num: 2,
      tableName: "", // 表格name值
      padding_top: 0,
      padding_bottom: 0,
      padding_left: 0,
      padding_right: 0,
      cell_height: 0,
      pagePaddingLeft: 0,
      pagePaddingRight: 0,
      pagePaddingTop: 0,
      pagePaddingBottom: 0,
      pageSizeWidth: 21,
      pageSizeHeight: 14.85,
      pageDirection: "vertical",
      pageType: "A4",
      editableInFormMode: false, // 表格在表单模式下是否可编辑
      tableFiexedStyle: false, // 表格固定样式 不允许拖动表格线
      tableNewPage: false,
      tableSkipMode: false,
      fullPage: false, // 表格线是否撑满一页
      style: {
        width: "100px",
        height: "100px",
        border: "2px solid blue",
        position: "fixed",
        top: 0,
        left: 0,
      },
      inputStyle: {
        position: "fixed",
        visibility: "hidden",
        width: 0,
        maxHeight: "100vh",
        top: 0,
        left: 0,
      },
    };
  },
  methods: {
    imagePreview(e) {
      const files = e.target.files[0];
      if (!e || !window.FileReader) return; // 判断是否支持FileReader
      let reader = new FileReader();
      reader.readAsDataURL(files); // 文件转换
      let _this = this;
      const meta =
        e.target.info && e.target.info.source === "insertLocalMarkableImage"
          ? { imageType: 1 }
          : {};
      reader.onloadend = function (event) {
        if (_this.insertMarkImage) {
          _this.editor.insertWaterMarkImage(event.target.result);
          _this.insertMarkImage = false;
        } else {
          const byteCount = event.total;
          const megabyteCount = byteCount / 1024 / 1024;
          if (_this.editor.config.localImageMaxSize !== undefined) {
            if (megabyteCount <= _this.editor.config.localImageMaxSize) {
              _this.editor.insertImage(event.target.result, {
                meta,
              });
            } else {
              // 压缩图片大小 ↓
              _this.editor
                .compressImage(
                  event.target.result,
                  _this.editor.page_size.width,
                  _this.editor.page_size.height
                )
                .then((res) => {
                  _this.editor.insertImage(res, {
                    meta,
                  });
                });
              // 压缩图片大小 ↑
            }
          } else {
            _this.editor.insertImage(event.target.result, {
              meta,
            });
          }
        }
        if (e.target) {
          e.target.info = undefined;
        }
      };
    },
    // 给该组件定位 height: 右键菜单的高度  width: 右键菜单的宽度 e：点击事件对象
    calcRightClickMenuPosition(e, height, width) {
      const { editor } = this.instance;
      const pageWidth = editor.config.getPageSize().width;
      const offset = editor.internal.view_scale_offset;
      const pageLeft =
        ((parseInt(editor.init_canvas.style.width) - pageWidth) / 2 + offset) *
        editor.viewScale;
      let canvasDom = editor.init_canvas;
      if (editor.waterMark) {
        const container = document.getElementById(this.editorId);
        const childNodes = Array.from(container.childNodes);
        canvasDom = childNodes.find((node) => node.nodeName === "CANVAS");
      }
      const pageRight =
        canvasDom.getBoundingClientRect().left +
        pageLeft +
        pageWidth * editor.viewScale; // 计算的是编辑区域(白色区域)右边框距离窗口最最侧的距离
      let return_left = 0;
      if (e.clientX + width > pageRight) {
        return_left = pageRight - width;
      } else {
        return_left = e.clientX;
      }
      let return_top = 0;
      const canvasDomRect = canvasDom.getBoundingClientRect();
      if (e.clientY + height >= canvasDomRect.bottom) {
        return_top = canvasDomRect.bottom - height - 10;
        return_top =
          return_top < canvasDomRect.top ? canvasDomRect.top : return_top; // 因为有可能 height 很大 而 top 值最小不能小于 canvasDomRect.top
      } else {
        return_top = e.clientY;
      }
      return {
        left: return_left < 0 ? 0 : return_left,
        top: return_top < 0 ? 0 : return_top,
      };
    },
    openFileChange(e) {
      const target = e.target;
      const file = target.files[0];
      const _this = this;
      if (file) {
        this.loading = true;
        this.$nextTick(() => {
          file
            .text()
            .then(function (res) {
              if (_this.openFileType === "insert") {
                _this.insertTemplateData(res);
              } else {
                _this.loadSupportFile(res);
              }
              _this.loading = false;
            })
            .finally(function () {
              target.remove();
            });
        });
      }
    },
    insertTemplateData(res) {
      const initData = this.instance.editor.getRawData();
      try {
        this.instance.editor.insertTemplateData(res);
      } catch (e) {
        this.$error({
          title: "数据格式错误",
        });
        this.editor.reInitRaw(initData);
        this.editor.refreshDocument();
      }
    },
    loadSupportFile(res) {
      try {
        try {
          this.instance.editor.dcXmlDataTrans(res);
        } catch {
          const rawData = this.instance.editor.htmlDataTrans(res);
          this.editor.reInitRaw(rawData);
          this.editor.refreshDocument();
        }
      } catch {
        try {
          const resObj = this.decodeGrf(res);
          if (resObj.DetailGrid || resObj.ReportHeader) {
            this.parseGrfJson(resObj);
            return;
          }
        } catch (e) {
          console.error(e);
        }

        const initData = this.instance.editor.getRawData();
        try {
          let rawData = res;
          try {
            if (this.openFileType === "useConfig") {
              this.editor.reInitRawByConfig(rawData);
            } else {
              this.editor.reInitRaw(rawData);
            }
            this.editor.refreshDocument();
          } catch {
            this.$editor.error("数据格式错误");
            this.editor.reInitRaw(initData);
            this.editor.refreshDocument();
          }
        } catch (e) {
          this.$editor.error({
            msg: "数据格式错误",
          });
        }
      }
    },
    insertLocalImage(info) {
      const input = document.createElement("input");
      input.hidden = "";
      input.type = "file";
      input.accept = "image/*";
      input.info = info;
      input.onchange = this.imagePreview;
      input.click();
    },

    // 根据点击位置 装配右键菜单列表 不考虑有没有配置
    assembleListByPosition(currentInfo, editor, builtInVariable, history) {
      let list = []; // 点在不同位置 应该有的右键菜单选项 不考虑有没有配置
      const isDisabled = editor.selection.getFocusGroup()?.lock;
      if (currentInfo.cell) {
        this.currentClickCell = currentInfo.cell;
        list = [
          ...baseMenuList,
          ...tableMenuList,
          ...insertListAboutTbl,
          ...deleteListAboutTbl,
          ...alignTypeList,
          ...insertListAboutBase,
          ...convertPDFMethod,
          ...printContinueMethod,
          isDisabled ? [] : tableLine,
        ];
      } else {
        // 点在了单元格外边
        list = [
          ...baseMenuList,
          ...insertListAboutBase,
          ...insertTbl,
          ...alignTypeList,
          ...insertListAboutBase,
          ...convertPDFMethod,
          ...printContinueMethod,
        ];
      }
      if (currentInfo.button) {
        list.push({
          value: "按钮编辑",
          key: "buttonEdit",
          icon: "icon-wenbenyu",
          isDisabled: false,
        });
      }
      // 如果是选区状态，判断选区中是否包含文本域，如果存在文本域则也展示文本域属性菜单
      let selectionContainField = false;
      if (!editor.selection.isCollapsed) {
        if (editor.selection.selected_fields_chars.field_chars.length) {
          selectionContainField = true;
        }
      }

      if (
        (currentInfo.field && currentInfo.field.type !== "box") ||
        selectionContainField
      ) {
        list.push(...fieldPropMethod);
      }
      if (currentInfo.element && currentInfo.element.widgetType === "caliper") {
        list.push(...caliperPropMethod);
      }
      if (currentInfo.field && currentInfo.field.type === "box") {
        list.push(...boxTypeField);
        list.filter((item) => {
          if (item.key === "choice") {
            item.isDisabled = true;
          }
        });
      } else {
        list.filter((item) => {
          if (item.key === "choice") {
            item.isDisabled = false;
          }
        });
      }
      if (editor.current_cell === editor.footer_cell) {
        list.push(...footerList);
      }
      const focusParagraph = editor.selection.getFocusParagraph();
      if (focusParagraph.islist && focusParagraph.isOrder) {
        list.push(...listNumRestart);
      }
      const focusGroup = editor.selection.getFocusGroup();
      if (focusGroup) {
        list.push(...groupMethod);
      }
      this.handleListIsDisabled(
        list,
        editor,
        currentInfo,
        builtInVariable,
        focusGroup,
        history,
        isDisabled
      );
      return list;
    },

    handleListIsDisabled(
      list,
      editor,
      currentInfo,
      builtInVariable,
      focusGroup,
      history,
      isDisabled
    ) {
      // 分组锁定的时候 禁用掉的一些操作
      const groupLockDisabled = [
        "insertEmptyParaFromUp",
        "insertEmptyParaFromDown",
        "modifyTableAttr",
        "modifyCellAttr", // 单元格属性
        "setCellsGroup",
        "setFixedTableHeader",
        "cancelFixedTableHeader",
        "disabledDeleteTheTable",
        "allowDeleteTheTable",
        "insertRowFromUp",
        "insertRowFromDown",
        "insertColFromLeft",
        "insertColFromRight",
        "delTblRow",
        "delTblCol",
        "deleteEmptyParaUp",
        "deleteEmptyParaDown",
        "changeTableBgColor",
        "averageRowHeight",
        "averageColWidth",
        "deleteTbl",
        "tableLine",
      ];
      list.forEach((item) => {
        // 判断是否禁用 删除表格上方的空行
        if (item.key === "deleteEmptyParaUp") {
          item.isDisabled = !editor.judgeCanDelEmptyParaOnTblSide(
            builtInVariable.Direction.up
          );
        }
        // 判断是否禁用 删除表格下方的空行
        if (item.key === "deleteEmptyParaDown") {
          item.isDisabled = !editor.judgeCanDelEmptyParaOnTblSide(
            builtInVariable.Direction.down
          );
        }
        // 根据历史堆栈判断 修改是否可以禁用撤销
        if (item.key === "ctrlZ") {
          item.isDisabled = !history.canBeUndo();
        }
        // 根据历史堆栈判断 修改是否可以禁用重做
        if (item.key === "redo") {
          item.isDisabled = !history.canBeRedo();
        }
        // 不是选区的时候 禁用复制 ,剪切
        if (item.key === "copy" || item.key === "cut") {
          item.isDisabled = editor.selection.isCollapsed;
        }

        // 判断是否禁用删除表格
        if (item.key === "deleteTbl") {
          item.isDisabled = currentInfo.table?.isCanNotDelTbl;
        }
        // 判断是否禁用删除表格列
        if (item.key === "delTblCol") {
          item.isDisabled = !editor.judgeIsCanDeleteRowOrCol();
        }
        // 判断是否禁用删除表格行
        if (item.key === "delTblRow") {
          item.isDisabled = currentInfo.table?.isCanNotDelTbl;
        }
        if (
          item.key === "insertColFromLeft" ||
          item.key === "insertColFromRight"
        ) {
          item.isDisabled = !editor.judgeIsCanInsertCol();
        }
        // 判断是否禁用 合并单元格
        if (item.key === "mergeCell") {
          item.isDisabled = !editor.judgeIsCanMergeCell();
        }
        // 判断是否禁用拆分单元格
        // if (item.key === "splitCell") {
        //   // item.isDisabled = !editor.judgeIsCanSplitCell();
        //   const flag = !editor.judgeIsCanSplitCell();
        // }
        // 判断是否禁用删除分组
        if (item.key === "delGroup") {
          item.isDisabled = focusGroup.lock;
        } else if (item.key === "sortGroup") {
          item.isDisabled = !(editor.root_cell.groups.length > 1);
        }

        if (groupLockDisabled.includes(item.key)) {
          item.isDisabled = isDisabled;
        }
      });
    },

    isMobile() {
      return navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      );
    },

    rightClick(e) {
      const canvasDom = e.target;
      const { editor, history, builtInVariable } = this.instance;
      if (this.editor.internal.focus_drag_field) return;
      if (editor.isMobileTerminal()) return;
      if (e.target.contentEditable === "true" && e.target.innerText === "") {
        e.target.style.display = "none";
      } else {
        if (!(canvasDom.nodeName === "CANVAS" || canvasDom.nodeName === "DIV"))
          return;
      }
      if (this.isElectron) {
        e.stopPropagation();
        // const electron = window.electron ?? window.top.electron;
        // electron.rightMenuApi.preventElectronContextMenu();
      }

      // 生成最终右键菜单列表的思路：先根据右键点击的位置生成一个全的菜单list(不分是否二级菜单都是平级的)，然后遍历配置的对象数组config，把里边的值给填全 最后用配置的对象数组config
      this.rightMenuEvent = e;

      const { x, y } = editor.getAbsolutePositionByViewPosition(
        e.offsetX,
        e.offsetY
      );
      const currentInfo = editor.getElementByPoint(x, y);
      this.lock = currentInfo.group ? currentInfo.group.lock : false;
      this.curClickInfo = currentInfo;
      this.hideFieldNeedPanel();

      const { list: up_list, hideSysMenu } = editor.event.emit(
        // 其他项目组 绑定contextmenu这个事件 接收到我传递的两个参数e和currentInfo执行操作 并返回给我一个对象供我使用
        "contextmenu",
        e,
        currentInfo
      );
      if (hideSysMenu) return; // 如果隐藏系统右键菜单 就直接return掉 用人家自己写的了

      // 如果没点击在白色区域内 直接return掉
      if (!(currentInfo.page || e.target.contentEditable === "true")) return;

      const list = this.assembleListByPosition(
        currentInfo,
        editor,
        builtInVariable,
        history
      );

      const configList = Array.isArray(up_list)
        ? up_list
        : this.currentRightMenuConfig;

      let result = [];
      if (configList.length > 0) {
        for (let i = 0; i < configList.length; i++) {
          let obj = {};
          const configItem = configList[i];
          // 如果没有二级菜单
          if (!configItem.children) {
            const option = list.find((i) => i.key === configItem.key);
            if (option) {
              obj = { ...option, ...configItem }; // res 和 item 顺序不能颠倒
            } else if (configItem.handler) {
              obj = { ...configItem };
            }
          } else {
            // 说明有二级菜单
            const children = [];
            const configChildrenList = configItem.children;
            configChildrenList.forEach((item) => {
              let obj = {};
              const res = list.find((i) => i.key === item.key);
              if (res) {
                obj = { ...res, ...item };
                children.push(obj);
              }
            });
            if (children.length > 0) {
              obj.value = configItem.value;
              obj.key = configItem.key || Math.random();
              obj.icon = configItem.icon || "icon-xxx"; // 不给个默认值 就会报错
              obj.isDisabled = false;
              obj.line = configItem.line;
              obj.children = children;
            }
          }
          Object.keys(obj).length > 0 && result.push(obj);
        }
      }
      // 如果 点击在了页眉页脚 并且 禁用或者不显示的时候 右键菜单就啥也不展示
      if (
        currentInfo.isEditHF &&
        (this.editor.is_forbid_edit_hf || !this.editor.show_header_footer)
      ) {
        result = [];
      }
      // 如果 is_forbid_edit_hf 为false 并且点击在了页眉页脚 并且现在页眉页脚也不是编辑状态
      if (
        !this.editor.is_forbid_edit_hf &&
        currentInfo.isEditHF &&
        this.editor.show_header_footer && // 显示页眉页脚的时候 才需要展示 编辑页眉页脚
        !this.editor.is_edit_hf_mode
      ) {
        result = [...headerFooter];
      } else if (editor.is_edit_hf_mode && !currentInfo.isEditHF) {
        // 否则 点击在文档内 并且页眉页脚是编辑状态下的时候
        result = editorRootCell;
      }

      if (editor.waterMark) {
        result = this.waterMarkRightMenu(e, x, y, result, editor);
      }
      if (editor.is_shape_mode) {
        result = this.shapeRightMenu(result);
      }
      // 区域打印的时候 只显示打印的东西并且配置中有的 ↓
      if (editor.print_continue || editor.area_print) {
        const list = [...printOption];
        result = [];
        if (configList.length > 0) {
          for (let i = 0; i < configList.length; i++) {
            let obj = {};
            const configItem = configList[i];
            // 如果没有二级菜单
            if (!configItem.children) {
              const res = list.find((i) => i.key === configItem.key);
              if (res) {
                obj = { ...res, ...configItem }; // res 和 item 顺序不能颠倒
              }
            }
            if (
              Object.keys(obj).length > 0 &&
              editor.print_continue && // 续打模式下 不要区域打印 并且要把续打 改为取消续打
              obj.key !== "area_print"
            ) {
              result.push(obj);
              obj.key === "printContinue" && (obj.value = "取消续打");
            }
            // 区域打印模式下 不要续打 并且只显示取消区域打印
            if (
              Object.keys(obj).length > 0 &&
              editor.area_print &&
              obj.key !== "printContinue"
            ) {
              obj.key === "area_print" && (obj.value = "取消区域打印");
              result.push(obj);
            }
          }
        }
      }
      // 区域打印的时候 只显示打印的东西 ↑

      if (result.length > 0) {
        this.listOfFinalShow = result;
        this.isShowRightClickMenu = true;
        this.$nextTick(() => {
          // 右键菜单的最大高度应该按照父级容器的高度来计算，否则像弹窗内的编辑器，二级菜单就会有问题
          const maxHeight = canvasDom.offsetHeight;
          this.$refs.rightMenu.$el.style.maxHeight = "1000px"; // 先设置一个比较大的值为了能够得到一个装满选项的右键菜单的最大高度
          const height = this.$refs.rightMenu.$el.offsetHeight;
          const width = this.$refs.rightMenu.$el.offsetWidth;
          const { left, top } = this.calcRightClickMenuPosition(
            e,
            height,
            width
          );
          this.$refs.rightMenu.$data.style.left = left + "px";
          this.$refs.rightMenu.$data.style.top = top + "px";
          if (canvasDom.nodeName === "CANVAS") {
            this.$refs.rightMenu.$el.style.maxHeight = maxHeight - 10 + "px"; // 逻辑上应该是总体高度减去 top 值还要能够放的下右键菜单 否则就会有部分内容看不到,滚动条也滚动不过去 // 减 10 是个大概的数 更稳妥 不至于有滚动条了还看不到全量数据
          }
        });
        this.inputStyle.top = e.clientY + "px";
        this.inputStyle.left = e.clientX + "px";
      }
    },
    shapeRightMenu(result) {
      this.$refs.shapeFloat.shapeIndex = 10000;
      this.instance.editor.drawShape("close");
      const item = { key: "shape_mode", value: "关闭图形模式" };
      result.length = 0;
      result.push(
        item,
        { key: "insert_shape_line", value: "插入线" },
        {
          key: "insert_shape_elliptic",
          value: "插入椭圆",
        },
        { key: "insert_shape_cross", value: "插入叉" },
        { key: "insert_shape_rect", value: "插入矩形" },
        { key: "insert_fold_line", value: "插入折线" }
      );

      const shapes = this.editor.shapes;
      const shape = shapes.find((shape) => shape.type === "fold_line");
      if (shape) {
        result.push({ key: "insert_continue_line", value: "继续绘制折线" });
        this.$refs.shapeFloat.$refs.shapeContent.childNodes[4].style.display =
          "block";
      } else {
        this.$refs.shapeFloat.$refs.shapeContent.childNodes[4].style.display =
          "none";
      }
      return result;
    },
    waterMarkRightMenu(e, x, y, result, editor) {
      // 开启了水印模式
      const mark = editor.isInMark(x, y);
      const item = {
        key: "water_mark",
        value: "关闭水印模式",
      };
      result.length = 0;
      this.$refs.waterMarkFloat.waterMarkIndex = 10000;
      if (mark) {
        if (mark.type === "textMark") {
          if (mark.mode === "repeat") {
            if (editor.is_mark_text) {
              result.push(
                item,
                { key: "insert_mark_image", value: "插入水印图片" },
                {
                  key: "insert_custom_mark_image",
                  value: "插入自定义水印图片",
                },
                {
                  key: "close_mark_text",
                  value: "关闭插入水印文字",
                },
                { key: "text_mark_style", value: "水印字体" },
                { key: "single_mark_mode", value: "改为单独模式" }
              );
            } else {
              result.push(
                item,
                { key: "insert_mark_image", value: "插入水印图片" },
                {
                  key: "insert_custom_mark_image",
                  value: "插入自定义水印图片",
                },
                {
                  key: "insert_mark_text",
                  value: "插入水印文字",
                },
                { key: "text_mark_style", value: "水印字体" },
                { key: "single_mark_mode", value: "改为单独模式" }
              );
            }
          } else {
            if (editor.is_mark_text) {
              result.push(
                item,
                { key: "insert_mark_image", value: "插入水印图片" },
                {
                  key: "insert_custom_mark_image",
                  value: "插入自定义水印图片",
                },
                {
                  key: "close_mark_text",
                  value: "关闭插入水印文字",
                },
                { key: "text_mark_style", value: "水印字体" },
                { key: "repeat_mark_mode", value: "改为重复模式" }
              );
            } else {
              result.push(
                item,
                { key: "insert_mark_image", value: "插入水印图片" },
                {
                  key: "insert_custom_mark_image",
                  value: "插入自定义水印图片",
                },
                {
                  key: "insert_mark_text",
                  value: "插入水印文字",
                },
                { key: "text_mark_style", value: "水印字体" },
                { key: "repeat_mark_mode", value: "改为重复模式" }
              );
            }
          }
        } else {
          if (mark.mode === "repeat") {
            if (editor.is_mark_text) {
              result.push(
                item,
                { key: "insert_mark_image", value: "插入水印图片" },
                {
                  key: "insert_custom_mark_image",
                  value: "插入自定义水印图片",
                },
                {
                  key: "close_mark_text",
                  value: "关闭插入水印文字",
                },
                { key: "single_mark_mode", value: "改为单独模式" }
              );
            } else {
              result.push(
                item,
                { key: "insert_mark_image", value: "插入水印图片" },
                {
                  key: "insert_custom_mark_image",
                  value: "插入自定义水印图片",
                },
                {
                  key: "insert_mark_text",
                  value: "插入水印文字",
                },
                {
                  key: "set_mark_prop",
                  value: "设置水印属性",
                },

                { key: "single_mark_mode", value: "改为单独模式" }
              );
            }
          } else {
            if (editor.is_mark_text) {
              result.push(
                item,
                { key: "insert_mark_image", value: "插入水印图片" },
                {
                  key: "insert_custom_mark_image",
                  value: "插入自定义水印图片",
                },
                {
                  key: "close_mark_text",
                  value: "关闭插入水印文字",
                },

                { key: "repeat_mark_mode", value: "改为重复模式" }
              );
            } else {
              result.push(
                item,
                { key: "insert_mark_image", value: "插入水印图片" },
                {
                  key: "insert_custom_mark_image",
                  value: "插入自定义水印图片",
                },
                {
                  key: "insert_mark_text",
                  value: "插入水印文字",
                },
                {
                  key: "set_mark_prop",
                  value: "设置水印属性",
                },
                { key: "repeat_mark_mode", value: "改为重复模式" }
              );
            }
          }
        }
      } else {
        if (editor.is_mark_text) {
          result.push(
            item,
            { key: "insert_mark_image", value: "插入水印图片" },
            {
              key: "insert_custom_mark_image",
              value: "插入自定义水印图片",
            },
            {
              key: "close_mark_text",
              value: "关闭插入水印文字",
            }
          );
        } else {
          result.push(
            item,
            { key: "insert_mark_image", value: "插入水印图片" },
            {
              key: "insert_custom_mark_image",
              value: "插入自定义水印图片",
            },
            {
              key: "insert_mark_text",
              value: "插入水印文字",
            }
          );
        }
      }

      return result;
    },
    closeParaAttrModal() {
      this.isShowParaAttrModal = false;
    },
    handleRightClickMenuSelected(key) {
      const { builtInVariable, copy, cut, paste, editor, history } =
        this.instance;
      switch (key) {
        case "save": // 下载PDF
          this.save();
          break;
        case "printView":
          this.convertPDF("print");
          break;
        case "printCurrentPage":
          this.printCurrentPage();
          break;
        case "shape_mode":
          this.shapeMode();
          break;
        case "insert_shape_line":
          this.$refs.shapeFloat.shapeIndex = 0;
          this.drawShape("line");
          break;
        case "insert_shape_elliptic":
          this.$refs.shapeFloat.shapeIndex = 1;
          this.drawShape("circle");
          break;
        case "insert_shape_cross":
          this.$refs.shapeFloat.shapeIndex = 2;
          this.drawShape("cross");
          break;
        case "insert_shape_rect":
          this.$refs.shapeFloat.shapeIndex = 3;
          this.drawShape("rect");
          break;
        case "insert_fold_line":
          this.$refs.shapeFloat.shapeIndex = 4;
          this.drawShape("fold_line");
          break;
        case "insert_continue_line":
          this.$refs.shapeFloat.shapeIndex = 5;
          this.drawShape("continue_line");
          break;
        case "immediatelyPrint":
          this.immediatelyPrint();
          break;
        case "printContinueImmediate":
          this.printContinueImmediate();
          break;
        case "insert_button":
          this.insertButtonPopup();
          break;
        case "insertFraction":
          this.openFraction();
          break;
        case "buttonEdit":
          this.buttonEdit();
          break;
        case "area_print":
          editor.areaPrint(!editor.area_print);
          break;
        case "add_comment":
          this.openCommentEditModal();
          break;

        case "water_mark":
          this.waterMarkModel();
          break;
        case "insert_mark_image":
          this.insertWaterMarkImage();
          break;
        case "insert_custom_mark_image":
          this.insertCustomWaterMarkImage();
          break;
        case "insert_mark_text":
          this.$refs.waterMarkFloat.waterMarkIndex = 1;
          this.insertWaterMarkText(true);
          break;
        case "close_mark_text":
          this.$refs.waterMarkFloat.waterMarkIndex = 10000;
          this.insertWaterMarkText(false);
          break;
        case "set_mark_prop":
          this.showCustomWaterMark = true;
          break;
        case "repeat_mark_mode":
          this.changeMarkMode("repeat");
          break;
        case "single_mark_mode":
          this.changeMarkMode("single");
          break;
        case "text_mark_style":
          this.showWordModal(true);
          break;
        // case "trace_contrast": //痕迹对比
        //   this.openTraceContrast();
        //   break;
        case "disabledDeleteTheTable": // 禁止删除该表格
          editor.setTblIsCanDelete(true);
          break;
        case "allowDeleteTheTable": // 允许删除该表格
          editor.setTblIsCanDelete(false);
          break;
        case "printContinue": // 插入页码
          this.printContinue();
          break;
        case "insertPageNum": // 插入页码
          this.insertPageInfo();
          break;
        case "searchAndReplace": //查找替换
          this.searchAndReplace();
          break;
        case "convertPDF": // 下载PDF
          this.convertPDF();
          break;
        case "systemPrint": // systemPrint
          this.openSystemPrint(() => {});
          break;
        // case "batchPrint": // systemPrint
        //   this.batchPrint();
        //   break;
        // case "browserPrint": // 浏览器打印
        //   this.openBrowserPrint();
        //   break;
        case "fieldProp": // 文本域属性
          this.showFieldPropWindow();
          break;
        case "redo": // 重做
          history.redo();
          break;
        case "ctrlZ": // 撤销
          history.undo();
          break;
        case "medicalCalcFormula": // 医学公式
          this.openMedicalCalcFormula();
          break;
        case "copy": // 复制
          copy();
          break;
        case "choice": // 插入选择框
          this.showChoiceModal();
          break;
        case "choiceProp": // 选择框属性
          this.showChoiceModal();
          break;
        case "cut": // 剪切
          cut();
          break;
        case "paste": // 粘贴
          paste(false);
          break;
        case "pasteText": // 粘贴
          paste(false, true);
          break;
        case "deleteSelection": // 删除选区内容
          editor.delete_backward();
          break;
        case "italicWaterMark": // 插入倾斜水印
          this.italicWaterMark();
          break;
        case "font": // 字体
          this.showWordModal();
          break;
        case "alignLeft": // 左对齐
          editor.changeAlign("left");
          break;
        case "alignCenter": // 居中对齐
          editor.changeAlign("center");
          break;
        case "alignRight": // 右对齐
          editor.changeAlign("right");
          break;
        case "restartListIndex": // 重新编号
          editor.restartListIndex();
          break;
        case "insertTable": // 插入表格
          this.showInsertTblModal();
          break;
        case "modifyTableAttr": // 表格属性
          this.showTableAttr();
          break;
        case "modifyCellAttr": // 单元格属性
          this.showCellAttr();
          break;
        case "setCellsGroup": // 设置单元格成组
          editor.setCellsGroupBySelection();
          break;
        case "cancelCellsGroup": // 取消单元格成组
          editor.cancelCellsGroupBySelection();
          break;
        case "setFixedTableHeader": // 设置固定表头
          editor.setFixedTableHeader();
          break;
        case "cancelFixedTableHeader": // 设置固定表头
          editor.setFixedTableHeader({ fixed_table_header_num: 0 });
          break;
        case "insertGroup": // 插入分组
          this.showGroupModal();
          break;
        case "sortGroup": // 排序分组
          editor.sortGroup();
          break;
        case "delGroup": // 删除分组
          editor.deleteGroup();
          break;
        case "lockGroup": // 删除分组
          editor.setGroupIslock(!this.lock);
          break;
        case "modifyProperty": // 修改分组属性
          this.showGroupModal(true);
          break;
        case "insertList": // 插入列表
          this.showListModal();
          break;
        case "insertField": // 插入文本域
          this.insertField();
          break;
        case "openFile": // 打开文件
          this.openFile();
          break;
        case "insertLocalImage": // 插入图片
          this.insertLocalImage();
          break;
        case "insertLocalMarkableImage": // 插入可标记图片
          this.insertLocalImage({
            source: "insertLocalMarkableImage",
          });
          break;
        case "insertRadio": // 插入单选框
          editor.insertSimpleWidget("radio");
          break;
        case "insertCheckbox": // 插入复选框
          editor.insertSimpleWidget("checkbox");
          break;
        case "insertCaliper": // 插入卡尺
          this.showCaliperModal();
          break;
        case "caliperProp": // 插入卡尺
          this.showCaliperModal();
          break;
        case "insertFormula": // 插入医学表达式
          this.insertFormula();
          break;
        case "deleteTbl": // 删除表格
          editor.deleteTbl();
          break;
        case "paragraph": // 段落属性
          this.showParaAttrModal();
          break;
        case "delTblRow": // 删除表格行
          editor.deleteRowFromTbl();
          break;
        case "delTblCol": // 删除表格列
          editor.deleteColFromTbl();
          break;
        case "deleteEmptyParaUp": // 删除表格上方空行
          editor.deleteEmtptyParagraphOnTblSide(builtInVariable.Direction.up);
          break;
        case "deleteEmptyParaDown": // 删除表格下方空行
          editor.deleteEmtptyParagraphOnTblSide(builtInVariable.Direction.down);
          break;
        case "changeTableBgColor": // 改变表格内选区背景颜色
          editor.changeTableBgStyle("#ECECEC");
          break;
        case "averageRowHeight":
          editor.averageRowHeight();
          break;
        case "averageColWidth":
          editor.averageColWidth();
          break;
        case "insertEmptyParaFromUp": // 表格上方插入一行空行
          editor.insertEmptyParagraphOnTblSide(builtInVariable.Direction.up);
          break;
        case "insertEmptyParaFromDown": // 表格下方插入一行空行
          editor.insertEmptyParagraphOnTblSide(builtInVariable.Direction.down);
          break;
        case "insertRowFromUp": // 上方插入一行
          editor.internal.sourceOfAddRowOrCol = 1;
          editor.addRowOrColInTbl(builtInVariable.Direction.up);
          break;
        case "insertRowFromDown": // 下方插入一行
          editor.internal.sourceOfAddRowOrCol = 1;
          editor.addRowOrColInTbl(builtInVariable.Direction.down);
          break;
        case "insertColFromLeft": // 左侧插入一列
          editor.internal.sourceOfAddRowOrCol = 1;
          editor.addRowOrColInTbl(builtInVariable.Direction.left);
          break;
        case "insertColFromRight": // 右侧插入一列
          editor.internal.sourceOfAddRowOrCol = 1;
          editor.addRowOrColInTbl(builtInVariable.Direction.right);
          break;
        case "showTableLine": // 展示设置边框的div
          this.showTableLine(true);
          break;
        case "mergeCell": // 合并单元格
          editor.mergeCell();
          break;
        case "splitCell":
          this.showSplitCellModal(); // 解开本地测试的限制 都让弹窗
          // if (this.instance.localTest.useLocal) {
          //   this.showSplitCellModal();
          // } else {
          //   if (editor.judgeIsCanSplitCell()) {
          //     editor.splitCell();
          //   } else {
          //     this.showSplitCellModal();
          //   }
          // }
          break;
        case "setPageConfig": // 页面设置
          this.openPageConfigModal();
          break;
        case "settingTool": // 拆分单元格
          this.openSettingTool();
          break;
        case "insertHorizontalLine": // 插入水平线
          this.openLineModal();
          break;
        case "editorHF": // 编辑页眉页脚
          if (!editor.is_forbid_edit_hf) {
            const offset =
              ((1 - editor.viewScale) *
                parseInt(editor.init_canvas.style.width)) /
              (2 * editor.viewScale);
            editor.internal.dblclick(
              this.rightMenuEvent.offsetX / editor.viewScale - offset,
              this.rightMenuEvent.offsetY / editor.viewScale
            );
          }
          break;
        case "editorRootCell": // 编辑rootCell
          // eslint-disable-next-line no-case-declarations
          const offset =
            ((1 - editor.viewScale) *
              parseInt(editor.init_canvas.style.width)) /
            (2 * editor.viewScale);
          editor.internal.dblclick(
            this.rightMenuEvent.offsetX / editor.viewScale - offset,
            this.rightMenuEvent.offsetY / editor.viewScale
          );
          break;
      }
      if (key.startsWith("tableLine")) {
        if (key === "tableLine_inside_inclined_bottom_line2") {
          editor.cotrolTableLine("inside_inclined_bottom_line", 2);
        } else if (key === "tableLine_inside_inclined_top_line2") {
          editor.cotrolTableLine("inside_inclined_top_line", 2);
        } else {
          editor.cotrolTableLine(key.slice(10));
        }
      }
      this.cancelRightMenu();
      editor.event.emit("menuClick", key);
      editor.focus();
    },
  },
};
export default rightMenuMixIn;
